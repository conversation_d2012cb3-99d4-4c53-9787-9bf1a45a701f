"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_uk_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   uk: () => (/* binding */ uk)\n/* harmony export */ });\n/* harmony import */ var _uk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uk/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatDistance.js\");\n/* harmony import */ var _uk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./uk/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatLong.js\");\n/* harmony import */ var _uk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./uk/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatRelative.js\");\n/* harmony import */ var _uk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./uk/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/localize.js\");\n/* harmony import */ var _uk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./uk/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Ukrainian locale.\n * @language Ukrainian\n * @iso-639-2 ukr\n * <AUTHOR> Korzh [@korzhyk](https://github.com/korzhyk)\n * <AUTHOR> Shcherbyak [@shcherbyakdev](https://github.com/shcherbyakdev)\n */ const uk = {\n    code: \"uk\",\n    formatDistance: _uk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _uk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _uk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _uk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _uk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (uk);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declension(scheme, count) {\n    // scheme for count=1 exists\n    if (scheme.one !== undefined && count === 1) {\n        return scheme.one;\n    }\n    const rem10 = count % 10;\n    const rem100 = count % 100;\n    // 1, 21, 31, ...\n    if (rem10 === 1 && rem100 !== 11) {\n        return scheme.singularNominative.replace(\"{{count}}\", String(count));\n    // 2, 3, 4, 22, 23, 24, 32 ...\n    } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n        return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n    // 5, 6, 7, 8, 9, 10, 11, ...\n    } else {\n        return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n    }\n}\nfunction buildLocalizeTokenFn(scheme) {\n    return (count, options)=>{\n        if (options && options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                if (scheme.future) {\n                    return declension(scheme.future, count);\n                } else {\n                    return \"за \" + declension(scheme.regular, count);\n                }\n            } else {\n                if (scheme.past) {\n                    return declension(scheme.past, count);\n                } else {\n                    return declension(scheme.regular, count) + \" тому\";\n                }\n            }\n        } else {\n            return declension(scheme.regular, count);\n        }\n    };\n}\nconst halfAtMinute = (_, options)=>{\n    if (options && options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"за півхвилини\";\n        } else {\n            return \"півхвилини тому\";\n        }\n    }\n    return \"півхвилини\";\n};\nconst formatDistanceLocale = {\n    lessThanXSeconds: buildLocalizeTokenFn({\n        regular: {\n            one: \"менше секунди\",\n            singularNominative: \"менше {{count}} секунди\",\n            singularGenitive: \"менше {{count}} секунд\",\n            pluralGenitive: \"менше {{count}} секунд\"\n        },\n        future: {\n            one: \"менше, ніж за секунду\",\n            singularNominative: \"менше, ніж за {{count}} секунду\",\n            singularGenitive: \"менше, ніж за {{count}} секунди\",\n            pluralGenitive: \"менше, ніж за {{count}} секунд\"\n        }\n    }),\n    xSeconds: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} секунда\",\n            singularGenitive: \"{{count}} секунди\",\n            pluralGenitive: \"{{count}} секунд\"\n        },\n        past: {\n            singularNominative: \"{{count}} секунду тому\",\n            singularGenitive: \"{{count}} секунди тому\",\n            pluralGenitive: \"{{count}} секунд тому\"\n        },\n        future: {\n            singularNominative: \"за {{count}} секунду\",\n            singularGenitive: \"за {{count}} секунди\",\n            pluralGenitive: \"за {{count}} секунд\"\n        }\n    }),\n    halfAMinute: halfAtMinute,\n    lessThanXMinutes: buildLocalizeTokenFn({\n        regular: {\n            one: \"менше хвилини\",\n            singularNominative: \"менше {{count}} хвилини\",\n            singularGenitive: \"менше {{count}} хвилин\",\n            pluralGenitive: \"менше {{count}} хвилин\"\n        },\n        future: {\n            one: \"менше, ніж за хвилину\",\n            singularNominative: \"менше, ніж за {{count}} хвилину\",\n            singularGenitive: \"менше, ніж за {{count}} хвилини\",\n            pluralGenitive: \"менше, ніж за {{count}} хвилин\"\n        }\n    }),\n    xMinutes: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} хвилина\",\n            singularGenitive: \"{{count}} хвилини\",\n            pluralGenitive: \"{{count}} хвилин\"\n        },\n        past: {\n            singularNominative: \"{{count}} хвилину тому\",\n            singularGenitive: \"{{count}} хвилини тому\",\n            pluralGenitive: \"{{count}} хвилин тому\"\n        },\n        future: {\n            singularNominative: \"за {{count}} хвилину\",\n            singularGenitive: \"за {{count}} хвилини\",\n            pluralGenitive: \"за {{count}} хвилин\"\n        }\n    }),\n    aboutXHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} години\",\n            singularGenitive: \"близько {{count}} годин\",\n            pluralGenitive: \"близько {{count}} годин\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} годину\",\n            singularGenitive: \"приблизно за {{count}} години\",\n            pluralGenitive: \"приблизно за {{count}} годин\"\n        }\n    }),\n    xHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} годину\",\n            singularGenitive: \"{{count}} години\",\n            pluralGenitive: \"{{count}} годин\"\n        }\n    }),\n    xDays: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} день\",\n            singularGenitive: \"{{count}} днi\",\n            pluralGenitive: \"{{count}} днів\"\n        }\n    }),\n    aboutXWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} тижня\",\n            singularGenitive: \"близько {{count}} тижнів\",\n            pluralGenitive: \"близько {{count}} тижнів\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} тиждень\",\n            singularGenitive: \"приблизно за {{count}} тижні\",\n            pluralGenitive: \"приблизно за {{count}} тижнів\"\n        }\n    }),\n    xWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} тиждень\",\n            singularGenitive: \"{{count}} тижні\",\n            pluralGenitive: \"{{count}} тижнів\"\n        }\n    }),\n    aboutXMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} місяця\",\n            singularGenitive: \"близько {{count}} місяців\",\n            pluralGenitive: \"близько {{count}} місяців\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} місяць\",\n            singularGenitive: \"приблизно за {{count}} місяці\",\n            pluralGenitive: \"приблизно за {{count}} місяців\"\n        }\n    }),\n    xMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} місяць\",\n            singularGenitive: \"{{count}} місяці\",\n            pluralGenitive: \"{{count}} місяців\"\n        }\n    }),\n    aboutXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} року\",\n            singularGenitive: \"близько {{count}} років\",\n            pluralGenitive: \"близько {{count}} років\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} рік\",\n            singularGenitive: \"приблизно за {{count}} роки\",\n            pluralGenitive: \"приблизно за {{count}} років\"\n        }\n    }),\n    xYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} рік\",\n            singularGenitive: \"{{count}} роки\",\n            pluralGenitive: \"{{count}} років\"\n        }\n    }),\n    overXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"більше {{count}} року\",\n            singularGenitive: \"більше {{count}} років\",\n            pluralGenitive: \"більше {{count}} років\"\n        },\n        future: {\n            singularNominative: \"більше, ніж за {{count}} рік\",\n            singularGenitive: \"більше, ніж за {{count}} роки\",\n            pluralGenitive: \"більше, ніж за {{count}} років\"\n        }\n    }),\n    almostXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"майже {{count}} рік\",\n            singularGenitive: \"майже {{count}} роки\",\n            pluralGenitive: \"майже {{count}} років\"\n        },\n        future: {\n            singularNominative: \"майже за {{count}} рік\",\n            singularGenitive: \"майже за {{count}} роки\",\n            pluralGenitive: \"майже за {{count}} років\"\n        }\n    })\n};\nconst formatDistance = (token, count, options)=>{\n    options = options || {};\n    return formatDistanceLocale[token](count, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, do MMMM y 'р.'\",\n    long: \"do MMMM y 'р.'\",\n    medium: \"d MMM y 'р.'\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'о' {{time}}\",\n    long: \"{{date}} 'о' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n/* harmony import */ var _toDate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../toDate.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/toDate.js\");\n\n\nconst accusativeWeekdays = [\n    \"неділю\",\n    \"понеділок\",\n    \"вівторок\",\n    \"середу\",\n    \"четвер\",\n    \"п’ятницю\",\n    \"суботу\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 5:\n        case 6:\n            return \"'у минулу \" + weekday + \" о' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'у минулий \" + weekday + \" о' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    return \"'у \" + weekday + \" о' p\";\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 5:\n        case 6:\n            return \"'у наступну \" + weekday + \" о' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'у наступний \" + weekday + \" о' p\";\n    }\n}\nconst lastWeekFormat = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return lastWeek(day);\n    }\n};\nconst nextWeekFormat = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return nextWeek(day);\n    }\n};\nconst formatRelativeLocale = {\n    lastWeek: lastWeekFormat,\n    yesterday: \"'вчора о' p\",\n    today: \"'сьогодні о' p\",\n    tomorrow: \"'завтра о' p\",\n    nextWeek: nextWeekFormat,\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"до н.е.\",\n        \"н.е.\"\n    ],\n    abbreviated: [\n        \"до н. е.\",\n        \"н. е.\"\n    ],\n    wide: [\n        \"до нашої ери\",\n        \"нашої ери\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-й кв.\",\n        \"2-й кв.\",\n        \"3-й кв.\",\n        \"4-й кв.\"\n    ],\n    wide: [\n        \"1-й квартал\",\n        \"2-й квартал\",\n        \"3-й квартал\",\n        \"4-й квартал\"\n    ]\n};\nconst monthValues = {\n    // ДСТУ 3582:2013\n    narrow: [\n        \"С\",\n        \"Л\",\n        \"Б\",\n        \"К\",\n        \"Т\",\n        \"Ч\",\n        \"Л\",\n        \"С\",\n        \"В\",\n        \"Ж\",\n        \"Л\",\n        \"Г\"\n    ],\n    abbreviated: [\n        \"січ.\",\n        \"лют.\",\n        \"берез.\",\n        \"квіт.\",\n        \"трав.\",\n        \"черв.\",\n        \"лип.\",\n        \"серп.\",\n        \"верес.\",\n        \"жовт.\",\n        \"листоп.\",\n        \"груд.\"\n    ],\n    wide: [\n        \"січень\",\n        \"лютий\",\n        \"березень\",\n        \"квітень\",\n        \"травень\",\n        \"червень\",\n        \"липень\",\n        \"серпень\",\n        \"вересень\",\n        \"жовтень\",\n        \"листопад\",\n        \"грудень\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"С\",\n        \"Л\",\n        \"Б\",\n        \"К\",\n        \"Т\",\n        \"Ч\",\n        \"Л\",\n        \"С\",\n        \"В\",\n        \"Ж\",\n        \"Л\",\n        \"Г\"\n    ],\n    abbreviated: [\n        \"січ.\",\n        \"лют.\",\n        \"берез.\",\n        \"квіт.\",\n        \"трав.\",\n        \"черв.\",\n        \"лип.\",\n        \"серп.\",\n        \"верес.\",\n        \"жовт.\",\n        \"листоп.\",\n        \"груд.\"\n    ],\n    wide: [\n        \"січня\",\n        \"лютого\",\n        \"березня\",\n        \"квітня\",\n        \"травня\",\n        \"червня\",\n        \"липня\",\n        \"серпня\",\n        \"вересня\",\n        \"жовтня\",\n        \"листопада\",\n        \"грудня\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"Н\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"нд\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"нед\",\n        \"пон\",\n        \"вів\",\n        \"сер\",\n        \"чтв\",\n        \"птн\",\n        \"суб\"\n    ],\n    wide: [\n        \"неділя\",\n        \"понеділок\",\n        \"вівторок\",\n        \"середа\",\n        \"четвер\",\n        \"п’ятниця\",\n        \"субота\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ніч\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ніч\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"північ\",\n        noon: \"полудень\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"вечір\",\n        night: \"ніч\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"північ\",\n        noon: \"полудень\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const unit = String(options === null || options === void 0 ? void 0 : options.unit);\n    const number = Number(dirtyNumber);\n    let suffix;\n    if (unit === \"date\") {\n        if (number === 3 || number === 23) {\n            suffix = \"-є\";\n        } else {\n            suffix = \"-е\";\n        }\n    } else if (unit === \"minute\" || unit === \"second\" || unit === \"hour\") {\n        suffix = \"-а\";\n    } else {\n        suffix = \"-й\";\n    }\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"any\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|й|є|а|я))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((до )?н\\.?\\s?е\\.?)/i,\n    abbreviated: /^((до )?н\\.?\\s?е\\.?)/i,\n    wide: /^(до нашої ери|нашої ери|наша ера)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^д/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[иі]?й?)? кв.?/i,\n    wide: /^[1234](-?[иі]?й?)? квартал/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[слбктчвжг]/i,\n    abbreviated: /^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\\.?/i,\n    wide: /^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^с/i,\n        /^л/i,\n        /^б/i,\n        /^к/i,\n        /^т/i,\n        /^ч/i,\n        /^л/i,\n        /^с/i,\n        /^в/i,\n        /^ж/i,\n        /^л/i,\n        /^г/i\n    ],\n    any: [\n        /^сі/i,\n        /^лю/i,\n        /^б/i,\n        /^к/i,\n        /^т/i,\n        /^ч/i,\n        /^лип/i,\n        /^се/i,\n        /^в/i,\n        /^ж/i,\n        /^лис/i,\n        /^г/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[нпвсч]/i,\n    short: /^(нд|пн|вт|ср|чт|пт|сб)\\.?/i,\n    abbreviated: /^(нед|пон|вів|сер|че?тв|птн?|суб)\\.?/i,\n    wide: /^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\\W*?ятниц[яі]|субот[аи])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^н/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^н/i,\n        /^п[он]/i,\n        /^в/i,\n        /^с[ер]/i,\n        /^ч/i,\n        /^п\\W*?[ят]/i,\n        /^с[уб]/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n    abbreviated: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n    wide: /^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^дп/i,\n        pm: /^пп/i,\n        midnight: /^півн/i,\n        noon: /^пол/i,\n        morning: /^р/i,\n        afternoon: /^д[ен]/i,\n        evening: /^в/i,\n        night: /^н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/match.js\n"));

/***/ })

}]);