"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_pl_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   pl: () => (/* binding */ pl)\n/* harmony export */ });\n/* harmony import */ var _pl_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pl/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatDistance.js\");\n/* harmony import */ var _pl_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pl/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatLong.js\");\n/* harmony import */ var _pl_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./pl/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatRelative.js\");\n/* harmony import */ var _pl_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pl/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/localize.js\");\n/* harmony import */ var _pl_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pl/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Polish locale.\n * @language Polish\n * @iso-639-2 pol\n * <AUTHOR> Derks [@ertrzyiks](https://github.com/ertrzyiks)\n * <AUTHOR> RAG [@justrag](https://github.com/justrag)\n * <AUTHOR> Grzyb [@mikolajgrzyb](https://github.com/mikolajgrzyb)\n * <AUTHOR> Tokarski [@mutisz](https://github.com/mutisz)\n */ const pl = {\n    code: \"pl\",\n    formatDistance: _pl_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _pl_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _pl_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _pl_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _pl_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pl);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: {\n            regular: \"mniej niż sekunda\",\n            past: \"mniej niż sekundę\",\n            future: \"mniej niż sekundę\"\n        },\n        twoFour: \"mniej niż {{count}} sekundy\",\n        other: \"mniej niż {{count}} sekund\"\n    },\n    xSeconds: {\n        one: {\n            regular: \"sekunda\",\n            past: \"sekundę\",\n            future: \"sekundę\"\n        },\n        twoFour: \"{{count}} sekundy\",\n        other: \"{{count}} sekund\"\n    },\n    halfAMinute: {\n        one: \"pół minuty\",\n        twoFour: \"pół minuty\",\n        other: \"pół minuty\"\n    },\n    lessThanXMinutes: {\n        one: {\n            regular: \"mniej niż minuta\",\n            past: \"mniej niż minutę\",\n            future: \"mniej niż minutę\"\n        },\n        twoFour: \"mniej niż {{count}} minuty\",\n        other: \"mniej niż {{count}} minut\"\n    },\n    xMinutes: {\n        one: {\n            regular: \"minuta\",\n            past: \"minutę\",\n            future: \"minutę\"\n        },\n        twoFour: \"{{count}} minuty\",\n        other: \"{{count}} minut\"\n    },\n    aboutXHours: {\n        one: {\n            regular: \"około godziny\",\n            past: \"około godziny\",\n            future: \"około godzinę\"\n        },\n        twoFour: \"około {{count}} godziny\",\n        other: \"około {{count}} godzin\"\n    },\n    xHours: {\n        one: {\n            regular: \"godzina\",\n            past: \"godzinę\",\n            future: \"godzinę\"\n        },\n        twoFour: \"{{count}} godziny\",\n        other: \"{{count}} godzin\"\n    },\n    xDays: {\n        one: {\n            regular: \"dzień\",\n            past: \"dzień\",\n            future: \"1 dzień\"\n        },\n        twoFour: \"{{count}} dni\",\n        other: \"{{count}} dni\"\n    },\n    aboutXWeeks: {\n        one: \"około tygodnia\",\n        twoFour: \"około {{count}} tygodni\",\n        other: \"około {{count}} tygodni\"\n    },\n    xWeeks: {\n        one: \"tydzień\",\n        twoFour: \"{{count}} tygodnie\",\n        other: \"{{count}} tygodni\"\n    },\n    aboutXMonths: {\n        one: \"około miesiąc\",\n        twoFour: \"około {{count}} miesiące\",\n        other: \"około {{count}} miesięcy\"\n    },\n    xMonths: {\n        one: \"miesiąc\",\n        twoFour: \"{{count}} miesiące\",\n        other: \"{{count}} miesięcy\"\n    },\n    aboutXYears: {\n        one: \"około rok\",\n        twoFour: \"około {{count}} lata\",\n        other: \"około {{count}} lat\"\n    },\n    xYears: {\n        one: \"rok\",\n        twoFour: \"{{count}} lata\",\n        other: \"{{count}} lat\"\n    },\n    overXYears: {\n        one: \"ponad rok\",\n        twoFour: \"ponad {{count}} lata\",\n        other: \"ponad {{count}} lat\"\n    },\n    almostXYears: {\n        one: \"prawie rok\",\n        twoFour: \"prawie {{count}} lata\",\n        other: \"prawie {{count}} lat\"\n    }\n};\nfunction declensionGroup(scheme, count) {\n    if (count === 1) {\n        return scheme.one;\n    }\n    const rem100 = count % 100;\n    // ends with 11-20\n    if (rem100 <= 20 && rem100 > 10) {\n        return scheme.other;\n    }\n    const rem10 = rem100 % 10;\n    // ends with 2, 3, 4\n    if (rem10 >= 2 && rem10 <= 4) {\n        return scheme.twoFour;\n    }\n    return scheme.other;\n}\nfunction declension(scheme, count, time) {\n    const group = declensionGroup(scheme, count);\n    const finalText = typeof group === \"string\" ? group : group[time];\n    return finalText.replace(\"{{count}}\", String(count));\n}\nconst formatDistance = (token, count, options)=>{\n    const scheme = formatDistanceLocale[token];\n    if (!(options === null || options === void 0 ? void 0 : options.addSuffix)) {\n        return declension(scheme, count, \"regular\");\n    }\n    if (options.comparison && options.comparison > 0) {\n        return \"za \" + declension(scheme, count, \"future\");\n    } else {\n        return declension(scheme, count, \"past\") + \" temu\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, do MMMM y\",\n    long: \"do MMMM y\",\n    medium: \"do MMM y\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} {{time}}\",\n    long: \"{{date}} {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n\nconst adjectivesLastWeek = {\n    masculine: \"ostatni\",\n    feminine: \"ostatnia\"\n};\nconst adjectivesThisWeek = {\n    masculine: \"ten\",\n    feminine: \"ta\"\n};\nconst adjectivesNextWeek = {\n    masculine: \"następny\",\n    feminine: \"następna\"\n};\nconst dayGrammaticalGender = {\n    0: \"feminine\",\n    1: \"masculine\",\n    2: \"masculine\",\n    3: \"feminine\",\n    4: \"masculine\",\n    5: \"masculine\",\n    6: \"feminine\"\n};\nfunction dayAndTimeWithAdjective(token, date, baseDate, options) {\n    let adjectives;\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n        adjectives = adjectivesThisWeek;\n    } else if (token === \"lastWeek\") {\n        adjectives = adjectivesLastWeek;\n    } else if (token === \"nextWeek\") {\n        adjectives = adjectivesNextWeek;\n    } else {\n        throw new Error(\"Cannot determine adjectives for token \".concat(token));\n    }\n    const day = date.getDay();\n    const grammaticalGender = dayGrammaticalGender[day];\n    const adjective = adjectives[grammaticalGender];\n    return \"'\".concat(adjective, \"' eeee 'o' p\");\n}\nconst formatRelativeLocale = {\n    lastWeek: dayAndTimeWithAdjective,\n    yesterday: \"'wczoraj o' p\",\n    today: \"'dzisiaj o' p\",\n    tomorrow: \"'jutro o' p\",\n    nextWeek: dayAndTimeWithAdjective,\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(token, date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"p.n.e.\",\n        \"n.e.\"\n    ],\n    abbreviated: [\n        \"p.n.e.\",\n        \"n.e.\"\n    ],\n    wide: [\n        \"przed naszą erą\",\n        \"naszej ery\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"I kw.\",\n        \"II kw.\",\n        \"III kw.\",\n        \"IV kw.\"\n    ],\n    wide: [\n        \"I kwartał\",\n        \"II kwartał\",\n        \"III kwartał\",\n        \"IV kwartał\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"S\",\n        \"L\",\n        \"M\",\n        \"K\",\n        \"M\",\n        \"C\",\n        \"L\",\n        \"S\",\n        \"W\",\n        \"P\",\n        \"L\",\n        \"G\"\n    ],\n    abbreviated: [\n        \"sty\",\n        \"lut\",\n        \"mar\",\n        \"kwi\",\n        \"maj\",\n        \"cze\",\n        \"lip\",\n        \"sie\",\n        \"wrz\",\n        \"paź\",\n        \"lis\",\n        \"gru\"\n    ],\n    wide: [\n        \"styczeń\",\n        \"luty\",\n        \"marzec\",\n        \"kwiecień\",\n        \"maj\",\n        \"czerwiec\",\n        \"lipiec\",\n        \"sierpień\",\n        \"wrzesień\",\n        \"październik\",\n        \"listopad\",\n        \"grudzień\"\n    ]\n};\nconst monthFormattingValues = {\n    narrow: [\n        \"s\",\n        \"l\",\n        \"m\",\n        \"k\",\n        \"m\",\n        \"c\",\n        \"l\",\n        \"s\",\n        \"w\",\n        \"p\",\n        \"l\",\n        \"g\"\n    ],\n    abbreviated: [\n        \"sty\",\n        \"lut\",\n        \"mar\",\n        \"kwi\",\n        \"maj\",\n        \"cze\",\n        \"lip\",\n        \"sie\",\n        \"wrz\",\n        \"paź\",\n        \"lis\",\n        \"gru\"\n    ],\n    wide: [\n        \"stycznia\",\n        \"lutego\",\n        \"marca\",\n        \"kwietnia\",\n        \"maja\",\n        \"czerwca\",\n        \"lipca\",\n        \"sierpnia\",\n        \"września\",\n        \"października\",\n        \"listopada\",\n        \"grudnia\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"N\",\n        \"P\",\n        \"W\",\n        \"Ś\",\n        \"C\",\n        \"P\",\n        \"S\"\n    ],\n    short: [\n        \"nie\",\n        \"pon\",\n        \"wto\",\n        \"śro\",\n        \"czw\",\n        \"pią\",\n        \"sob\"\n    ],\n    abbreviated: [\n        \"niedz.\",\n        \"pon.\",\n        \"wt.\",\n        \"śr.\",\n        \"czw.\",\n        \"pt.\",\n        \"sob.\"\n    ],\n    wide: [\n        \"niedziela\",\n        \"poniedziałek\",\n        \"wtorek\",\n        \"środa\",\n        \"czwartek\",\n        \"piątek\",\n        \"sobota\"\n    ]\n};\nconst dayFormattingValues = {\n    narrow: [\n        \"n\",\n        \"p\",\n        \"w\",\n        \"ś\",\n        \"c\",\n        \"p\",\n        \"s\"\n    ],\n    short: [\n        \"nie\",\n        \"pon\",\n        \"wto\",\n        \"śro\",\n        \"czw\",\n        \"pią\",\n        \"sob\"\n    ],\n    abbreviated: [\n        \"niedz.\",\n        \"pon.\",\n        \"wt.\",\n        \"śr.\",\n        \"czw.\",\n        \"pt.\",\n        \"sob.\"\n    ],\n    wide: [\n        \"niedziela\",\n        \"poniedziałek\",\n        \"wtorek\",\n        \"środa\",\n        \"czwartek\",\n        \"piątek\",\n        \"sobota\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"a\",\n        pm: \"p\",\n        midnight: \"półn.\",\n        noon: \"poł\",\n        morning: \"rano\",\n        afternoon: \"popoł.\",\n        evening: \"wiecz.\",\n        night: \"noc\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"północ\",\n        noon: \"południe\",\n        morning: \"rano\",\n        afternoon: \"popołudnie\",\n        evening: \"wieczór\",\n        night: \"noc\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"północ\",\n        noon: \"południe\",\n        morning: \"rano\",\n        afternoon: \"popołudnie\",\n        evening: \"wieczór\",\n        night: \"noc\"\n    }\n};\nconst dayPeriodFormattingValues = {\n    narrow: {\n        am: \"a\",\n        pm: \"p\",\n        midnight: \"o półn.\",\n        noon: \"w poł.\",\n        morning: \"rano\",\n        afternoon: \"po poł.\",\n        evening: \"wiecz.\",\n        night: \"w nocy\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o północy\",\n        noon: \"w południe\",\n        morning: \"rano\",\n        afternoon: \"po południu\",\n        evening: \"wieczorem\",\n        night: \"w nocy\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o północy\",\n        noon: \"w południe\",\n        morning: \"rano\",\n        afternoon: \"po południu\",\n        evening: \"wieczorem\",\n        night: \"w nocy\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    return String(dirtyNumber);\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: monthFormattingValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\",\n        formattingValues: dayFormattingValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: dayPeriodFormattingValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL3BsL19saWIvbG9jYWxpemUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0U7QUFFaEUsTUFBTUMsWUFBWTtJQUNoQkMsUUFBUTtRQUFDO1FBQVU7S0FBTztJQUMxQkMsYUFBYTtRQUFDO1FBQVU7S0FBTztJQUMvQkMsTUFBTTtRQUFDO1FBQW1CO0tBQWE7QUFDekM7QUFFQSxNQUFNQyxnQkFBZ0I7SUFDcEJILFFBQVE7UUFBQztRQUFLO1FBQUs7UUFBSztLQUFJO0lBQzVCQyxhQUFhO1FBQUM7UUFBUztRQUFVO1FBQVc7S0FBUztJQUNyREMsTUFBTTtRQUFDO1FBQWE7UUFBYztRQUFlO0tBQWE7QUFDaEU7QUFFQSxNQUFNRSxjQUFjO0lBQ2xCSixRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO0tBQUk7SUFDcEVDLGFBQWE7UUFDWDtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVEQyxNQUFNO1FBQ0o7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7QUFDSDtBQUNBLE1BQU1HLHdCQUF3QjtJQUM1QkwsUUFBUTtRQUFDO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztLQUFJO0lBQ3BFQyxhQUFhO1FBQ1g7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFREMsTUFBTTtRQUNKO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0FBQ0g7QUFFQSxNQUFNSSxZQUFZO0lBQ2hCTixRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUMzQ08sT0FBTztRQUFDO1FBQU87UUFBTztRQUFPO1FBQU87UUFBTztRQUFPO0tBQU07SUFDeEROLGFBQWE7UUFBQztRQUFVO1FBQVE7UUFBTztRQUFPO1FBQVE7UUFBTztLQUFPO0lBQ3BFQyxNQUFNO1FBQ0o7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtBQUNIO0FBQ0EsTUFBTU0sc0JBQXNCO0lBQzFCUixRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUMzQ08sT0FBTztRQUFDO1FBQU87UUFBTztRQUFPO1FBQU87UUFBTztRQUFPO0tBQU07SUFDeEROLGFBQWE7UUFBQztRQUFVO1FBQVE7UUFBTztRQUFPO1FBQVE7UUFBTztLQUFPO0lBQ3BFQyxNQUFNO1FBQ0o7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtBQUNIO0FBRUEsTUFBTU8sa0JBQWtCO0lBQ3RCVCxRQUFRO1FBQ05VLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsT0FBTztJQUNUO0lBQ0FoQixhQUFhO1FBQ1hTLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsT0FBTztJQUNUO0lBQ0FmLE1BQU07UUFDSlEsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLE1BQU1DLDRCQUE0QjtJQUNoQ2xCLFFBQVE7UUFDTlUsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7SUFDQWhCLGFBQWE7UUFDWFMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7SUFDQWYsTUFBTTtRQUNKUSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtBQUNGO0FBRUEsTUFBTUUsZ0JBQWdCLENBQUNDLGFBQWFDO0lBQ2xDLE9BQU9DLE9BQU9GO0FBQ2hCO0FBRU8sTUFBTUcsV0FBVztJQUN0Qko7SUFFQUssS0FBSzFCLHdFQUFlQSxDQUFDO1FBQ25CMkIsUUFBUTFCO1FBQ1IyQixjQUFjO0lBQ2hCO0lBRUFDLFNBQVM3Qix3RUFBZUEsQ0FBQztRQUN2QjJCLFFBQVF0QjtRQUNSdUIsY0FBYztRQUNkRSxrQkFBa0IsQ0FBQ0QsVUFBWUEsVUFBVTtJQUMzQztJQUVBRSxPQUFPL0Isd0VBQWVBLENBQUM7UUFDckIyQixRQUFRckI7UUFDUnNCLGNBQWM7UUFDZEksa0JBQWtCekI7UUFDbEIwQix3QkFBd0I7SUFDMUI7SUFFQUMsS0FBS2xDLHdFQUFlQSxDQUFDO1FBQ25CMkIsUUFBUW5CO1FBQ1JvQixjQUFjO1FBQ2RJLGtCQUFrQnRCO1FBQ2xCdUIsd0JBQXdCO0lBQzFCO0lBRUFFLFdBQVduQyx3RUFBZUEsQ0FBQztRQUN6QjJCLFFBQVFoQjtRQUNSaUIsY0FBYztRQUNkSSxrQkFBa0JaO1FBQ2xCYSx3QkFBd0I7SUFDMUI7QUFDRixFQUFFIiwic291cmNlcyI6WyIvVXNlcnMvaGFyc2hsdWhhci9sYWxpdCBkb24vYWxva2l0YS1jYW5kbGVzL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL3BsL19saWIvbG9jYWxpemUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYnVpbGRMb2NhbGl6ZUZuIH0gZnJvbSBcIi4uLy4uL19saWIvYnVpbGRMb2NhbGl6ZUZuLmpzXCI7XG5cbmNvbnN0IGVyYVZhbHVlcyA9IHtcbiAgbmFycm93OiBbXCJwLm4uZS5cIiwgXCJuLmUuXCJdLFxuICBhYmJyZXZpYXRlZDogW1wicC5uLmUuXCIsIFwibi5lLlwiXSxcbiAgd2lkZTogW1wicHJ6ZWQgbmFzesSFIGVyxIVcIiwgXCJuYXN6ZWogZXJ5XCJdLFxufTtcblxuY29uc3QgcXVhcnRlclZhbHVlcyA9IHtcbiAgbmFycm93OiBbXCIxXCIsIFwiMlwiLCBcIjNcIiwgXCI0XCJdLFxuICBhYmJyZXZpYXRlZDogW1wiSSBrdy5cIiwgXCJJSSBrdy5cIiwgXCJJSUkga3cuXCIsIFwiSVYga3cuXCJdLFxuICB3aWRlOiBbXCJJIGt3YXJ0YcWCXCIsIFwiSUkga3dhcnRhxYJcIiwgXCJJSUkga3dhcnRhxYJcIiwgXCJJViBrd2FydGHFglwiXSxcbn07XG5cbmNvbnN0IG1vbnRoVmFsdWVzID0ge1xuICBuYXJyb3c6IFtcIlNcIiwgXCJMXCIsIFwiTVwiLCBcIktcIiwgXCJNXCIsIFwiQ1wiLCBcIkxcIiwgXCJTXCIsIFwiV1wiLCBcIlBcIiwgXCJMXCIsIFwiR1wiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcbiAgICBcInN0eVwiLFxuICAgIFwibHV0XCIsXG4gICAgXCJtYXJcIixcbiAgICBcImt3aVwiLFxuICAgIFwibWFqXCIsXG4gICAgXCJjemVcIixcbiAgICBcImxpcFwiLFxuICAgIFwic2llXCIsXG4gICAgXCJ3cnpcIixcbiAgICBcInBhxbpcIixcbiAgICBcImxpc1wiLFxuICAgIFwiZ3J1XCIsXG4gIF0sXG5cbiAgd2lkZTogW1xuICAgIFwic3R5Y3plxYRcIixcbiAgICBcImx1dHlcIixcbiAgICBcIm1hcnplY1wiLFxuICAgIFwia3dpZWNpZcWEXCIsXG4gICAgXCJtYWpcIixcbiAgICBcImN6ZXJ3aWVjXCIsXG4gICAgXCJsaXBpZWNcIixcbiAgICBcInNpZXJwaWXFhFwiLFxuICAgIFwid3J6ZXNpZcWEXCIsXG4gICAgXCJwYcW6ZHppZXJuaWtcIixcbiAgICBcImxpc3RvcGFkXCIsXG4gICAgXCJncnVkemllxYRcIixcbiAgXSxcbn07XG5jb25zdCBtb250aEZvcm1hdHRpbmdWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wic1wiLCBcImxcIiwgXCJtXCIsIFwia1wiLCBcIm1cIiwgXCJjXCIsIFwibFwiLCBcInNcIiwgXCJ3XCIsIFwicFwiLCBcImxcIiwgXCJnXCJdLFxuICBhYmJyZXZpYXRlZDogW1xuICAgIFwic3R5XCIsXG4gICAgXCJsdXRcIixcbiAgICBcIm1hclwiLFxuICAgIFwia3dpXCIsXG4gICAgXCJtYWpcIixcbiAgICBcImN6ZVwiLFxuICAgIFwibGlwXCIsXG4gICAgXCJzaWVcIixcbiAgICBcIndyelwiLFxuICAgIFwicGHFulwiLFxuICAgIFwibGlzXCIsXG4gICAgXCJncnVcIixcbiAgXSxcblxuICB3aWRlOiBbXG4gICAgXCJzdHljem5pYVwiLFxuICAgIFwibHV0ZWdvXCIsXG4gICAgXCJtYXJjYVwiLFxuICAgIFwia3dpZXRuaWFcIixcbiAgICBcIm1hamFcIixcbiAgICBcImN6ZXJ3Y2FcIixcbiAgICBcImxpcGNhXCIsXG4gICAgXCJzaWVycG5pYVwiLFxuICAgIFwid3J6ZcWbbmlhXCIsXG4gICAgXCJwYcW6ZHppZXJuaWthXCIsXG4gICAgXCJsaXN0b3BhZGFcIixcbiAgICBcImdydWRuaWFcIixcbiAgXSxcbn07XG5cbmNvbnN0IGRheVZhbHVlcyA9IHtcbiAgbmFycm93OiBbXCJOXCIsIFwiUFwiLCBcIldcIiwgXCLFmlwiLCBcIkNcIiwgXCJQXCIsIFwiU1wiXSxcbiAgc2hvcnQ6IFtcIm5pZVwiLCBcInBvblwiLCBcInd0b1wiLCBcIsWbcm9cIiwgXCJjendcIiwgXCJwacSFXCIsIFwic29iXCJdLFxuICBhYmJyZXZpYXRlZDogW1wibmllZHouXCIsIFwicG9uLlwiLCBcInd0LlwiLCBcIsWbci5cIiwgXCJjencuXCIsIFwicHQuXCIsIFwic29iLlwiXSxcbiAgd2lkZTogW1xuICAgIFwibmllZHppZWxhXCIsXG4gICAgXCJwb25pZWR6aWHFgmVrXCIsXG4gICAgXCJ3dG9yZWtcIixcbiAgICBcIsWbcm9kYVwiLFxuICAgIFwiY3p3YXJ0ZWtcIixcbiAgICBcInBpxIV0ZWtcIixcbiAgICBcInNvYm90YVwiLFxuICBdLFxufTtcbmNvbnN0IGRheUZvcm1hdHRpbmdWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wiblwiLCBcInBcIiwgXCJ3XCIsIFwixZtcIiwgXCJjXCIsIFwicFwiLCBcInNcIl0sXG4gIHNob3J0OiBbXCJuaWVcIiwgXCJwb25cIiwgXCJ3dG9cIiwgXCLFm3JvXCIsIFwiY3p3XCIsIFwicGnEhVwiLCBcInNvYlwiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcIm5pZWR6LlwiLCBcInBvbi5cIiwgXCJ3dC5cIiwgXCLFm3IuXCIsIFwiY3p3LlwiLCBcInB0LlwiLCBcInNvYi5cIl0sXG4gIHdpZGU6IFtcbiAgICBcIm5pZWR6aWVsYVwiLFxuICAgIFwicG9uaWVkemlhxYJla1wiLFxuICAgIFwid3RvcmVrXCIsXG4gICAgXCLFm3JvZGFcIixcbiAgICBcImN6d2FydGVrXCIsXG4gICAgXCJwacSFdGVrXCIsXG4gICAgXCJzb2JvdGFcIixcbiAgXSxcbn07XG5cbmNvbnN0IGRheVBlcmlvZFZhbHVlcyA9IHtcbiAgbmFycm93OiB7XG4gICAgYW06IFwiYVwiLFxuICAgIHBtOiBcInBcIixcbiAgICBtaWRuaWdodDogXCJww7PFgm4uXCIsXG4gICAgbm9vbjogXCJwb8WCXCIsXG4gICAgbW9ybmluZzogXCJyYW5vXCIsXG4gICAgYWZ0ZXJub29uOiBcInBvcG/Fgi5cIixcbiAgICBldmVuaW5nOiBcIndpZWN6LlwiLFxuICAgIG5pZ2h0OiBcIm5vY1wiLFxuICB9LFxuICBhYmJyZXZpYXRlZDoge1xuICAgIGFtOiBcIkFNXCIsXG4gICAgcG06IFwiUE1cIixcbiAgICBtaWRuaWdodDogXCJww7PFgm5vY1wiLFxuICAgIG5vb246IFwicG/FgnVkbmllXCIsXG4gICAgbW9ybmluZzogXCJyYW5vXCIsXG4gICAgYWZ0ZXJub29uOiBcInBvcG/FgnVkbmllXCIsXG4gICAgZXZlbmluZzogXCJ3aWVjesOzclwiLFxuICAgIG5pZ2h0OiBcIm5vY1wiLFxuICB9LFxuICB3aWRlOiB7XG4gICAgYW06IFwiQU1cIixcbiAgICBwbTogXCJQTVwiLFxuICAgIG1pZG5pZ2h0OiBcInDDs8WCbm9jXCIsXG4gICAgbm9vbjogXCJwb8WCdWRuaWVcIixcbiAgICBtb3JuaW5nOiBcInJhbm9cIixcbiAgICBhZnRlcm5vb246IFwicG9wb8WCdWRuaWVcIixcbiAgICBldmVuaW5nOiBcIndpZWN6w7NyXCIsXG4gICAgbmlnaHQ6IFwibm9jXCIsXG4gIH0sXG59O1xuXG5jb25zdCBkYXlQZXJpb2RGb3JtYXR0aW5nVmFsdWVzID0ge1xuICBuYXJyb3c6IHtcbiAgICBhbTogXCJhXCIsXG4gICAgcG06IFwicFwiLFxuICAgIG1pZG5pZ2h0OiBcIm8gcMOzxYJuLlwiLFxuICAgIG5vb246IFwidyBwb8WCLlwiLFxuICAgIG1vcm5pbmc6IFwicmFub1wiLFxuICAgIGFmdGVybm9vbjogXCJwbyBwb8WCLlwiLFxuICAgIGV2ZW5pbmc6IFwid2llY3ouXCIsXG4gICAgbmlnaHQ6IFwidyBub2N5XCIsXG4gIH0sXG4gIGFiYnJldmlhdGVkOiB7XG4gICAgYW06IFwiQU1cIixcbiAgICBwbTogXCJQTVwiLFxuICAgIG1pZG5pZ2h0OiBcIm8gcMOzxYJub2N5XCIsXG4gICAgbm9vbjogXCJ3IHBvxYJ1ZG5pZVwiLFxuICAgIG1vcm5pbmc6IFwicmFub1wiLFxuICAgIGFmdGVybm9vbjogXCJwbyBwb8WCdWRuaXVcIixcbiAgICBldmVuaW5nOiBcIndpZWN6b3JlbVwiLFxuICAgIG5pZ2h0OiBcIncgbm9jeVwiLFxuICB9LFxuICB3aWRlOiB7XG4gICAgYW06IFwiQU1cIixcbiAgICBwbTogXCJQTVwiLFxuICAgIG1pZG5pZ2h0OiBcIm8gcMOzxYJub2N5XCIsXG4gICAgbm9vbjogXCJ3IHBvxYJ1ZG5pZVwiLFxuICAgIG1vcm5pbmc6IFwicmFub1wiLFxuICAgIGFmdGVybm9vbjogXCJwbyBwb8WCdWRuaXVcIixcbiAgICBldmVuaW5nOiBcIndpZWN6b3JlbVwiLFxuICAgIG5pZ2h0OiBcIncgbm9jeVwiLFxuICB9LFxufTtcblxuY29uc3Qgb3JkaW5hbE51bWJlciA9IChkaXJ0eU51bWJlciwgX29wdGlvbnMpID0+IHtcbiAgcmV0dXJuIFN0cmluZyhkaXJ0eU51bWJlcik7XG59O1xuXG5leHBvcnQgY29uc3QgbG9jYWxpemUgPSB7XG4gIG9yZGluYWxOdW1iZXIsXG5cbiAgZXJhOiBidWlsZExvY2FsaXplRm4oe1xuICAgIHZhbHVlczogZXJhVmFsdWVzLFxuICAgIGRlZmF1bHRXaWR0aDogXCJ3aWRlXCIsXG4gIH0pLFxuXG4gIHF1YXJ0ZXI6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBxdWFydGVyVmFsdWVzLFxuICAgIGRlZmF1bHRXaWR0aDogXCJ3aWRlXCIsXG4gICAgYXJndW1lbnRDYWxsYmFjazogKHF1YXJ0ZXIpID0+IHF1YXJ0ZXIgLSAxLFxuICB9KSxcblxuICBtb250aDogYnVpbGRMb2NhbGl6ZUZuKHtcbiAgICB2YWx1ZXM6IG1vbnRoVmFsdWVzLFxuICAgIGRlZmF1bHRXaWR0aDogXCJ3aWRlXCIsXG4gICAgZm9ybWF0dGluZ1ZhbHVlczogbW9udGhGb3JtYXR0aW5nVmFsdWVzLFxuICAgIGRlZmF1bHRGb3JtYXR0aW5nV2lkdGg6IFwid2lkZVwiLFxuICB9KSxcblxuICBkYXk6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBkYXlWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgICBmb3JtYXR0aW5nVmFsdWVzOiBkYXlGb3JtYXR0aW5nVmFsdWVzLFxuICAgIGRlZmF1bHRGb3JtYXR0aW5nV2lkdGg6IFwid2lkZVwiLFxuICB9KSxcblxuICBkYXlQZXJpb2Q6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBkYXlQZXJpb2RWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgICBmb3JtYXR0aW5nVmFsdWVzOiBkYXlQZXJpb2RGb3JtYXR0aW5nVmFsdWVzLFxuICAgIGRlZmF1bHRGb3JtYXR0aW5nV2lkdGg6IFwid2lkZVwiLFxuICB9KSxcbn07XG4iXSwibmFtZXMiOlsiYnVpbGRMb2NhbGl6ZUZuIiwiZXJhVmFsdWVzIiwibmFycm93IiwiYWJicmV2aWF0ZWQiLCJ3aWRlIiwicXVhcnRlclZhbHVlcyIsIm1vbnRoVmFsdWVzIiwibW9udGhGb3JtYXR0aW5nVmFsdWVzIiwiZGF5VmFsdWVzIiwic2hvcnQiLCJkYXlGb3JtYXR0aW5nVmFsdWVzIiwiZGF5UGVyaW9kVmFsdWVzIiwiYW0iLCJwbSIsIm1pZG5pZ2h0Iiwibm9vbiIsIm1vcm5pbmciLCJhZnRlcm5vb24iLCJldmVuaW5nIiwibmlnaHQiLCJkYXlQZXJpb2RGb3JtYXR0aW5nVmFsdWVzIiwib3JkaW5hbE51bWJlciIsImRpcnR5TnVtYmVyIiwiX29wdGlvbnMiLCJTdHJpbmciLCJsb2NhbGl6ZSIsImVyYSIsInZhbHVlcyIsImRlZmF1bHRXaWR0aCIsInF1YXJ0ZXIiLCJhcmd1bWVudENhbGxiYWNrIiwibW9udGgiLCJmb3JtYXR0aW5nVmFsdWVzIiwiZGVmYXVsdEZvcm1hdHRpbmdXaWR0aCIsImRheSIsImRheVBlcmlvZCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(p\\.?\\s*n\\.?\\s*e\\.?\\s*|n\\.?\\s*e\\.?\\s*)/i,\n    abbreviated: /^(p\\.?\\s*n\\.?\\s*e\\.?\\s*|n\\.?\\s*e\\.?\\s*)/i,\n    wide: /^(przed\\s*nasz(ą|a)\\s*er(ą|a)|naszej\\s*ery)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^p/i,\n        /^n/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^(I|II|III|IV)\\s*kw\\.?/i,\n    wide: /^(I|II|III|IV)\\s*kwarta(ł|l)/i\n};\nconst parseQuarterPatterns = {\n    narrow: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ],\n    any: [\n        /^I kw/i,\n        /^II kw/i,\n        /^III kw/i,\n        /^IV kw/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[slmkcwpg]/i,\n    abbreviated: /^(sty|lut|mar|kwi|maj|cze|lip|sie|wrz|pa(ź|z)|lis|gru)/i,\n    wide: /^(stycznia|stycze(ń|n)|lutego|luty|marca|marzec|kwietnia|kwiecie(ń|n)|maja|maj|czerwca|czerwiec|lipca|lipiec|sierpnia|sierpie(ń|n)|wrze(ś|s)nia|wrzesie(ń|n)|pa(ź|z)dziernika|pa(ź|z)dziernik|listopada|listopad|grudnia|grudzie(ń|n))/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^s/i,\n        /^l/i,\n        /^m/i,\n        /^k/i,\n        /^m/i,\n        /^c/i,\n        /^l/i,\n        /^s/i,\n        /^w/i,\n        /^p/i,\n        /^l/i,\n        /^g/i\n    ],\n    any: [\n        /^st/i,\n        /^lu/i,\n        /^mar/i,\n        /^k/i,\n        /^maj/i,\n        /^c/i,\n        /^lip/i,\n        /^si/i,\n        /^w/i,\n        /^p/i,\n        /^lis/i,\n        /^g/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[npwścs]/i,\n    short: /^(nie|pon|wto|(ś|s)ro|czw|pi(ą|a)|sob)/i,\n    abbreviated: /^(niedz|pon|wt|(ś|s)r|czw|pt|sob)\\.?/i,\n    wide: /^(niedziela|poniedzia(ł|l)ek|wtorek|(ś|s)roda|czwartek|pi(ą|a)tek|sobota)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^n/i,\n        /^p/i,\n        /^w/i,\n        /^ś/i,\n        /^c/i,\n        /^p/i,\n        /^s/i\n    ],\n    abbreviated: [\n        /^n/i,\n        /^po/i,\n        /^w/i,\n        /^(ś|s)r/i,\n        /^c/i,\n        /^pt/i,\n        /^so/i\n    ],\n    any: [\n        /^n/i,\n        /^po/i,\n        /^w/i,\n        /^(ś|s)r/i,\n        /^c/i,\n        /^pi/i,\n        /^so/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(^a$|^p$|pó(ł|l)n\\.?|o\\s*pó(ł|l)n\\.?|po(ł|l)\\.?|w\\s*po(ł|l)\\.?|po\\s*po(ł|l)\\.?|rano|wiecz\\.?|noc|w\\s*nocy)/i,\n    any: /^(am|pm|pó(ł|l)noc|o\\s*pó(ł|l)nocy|po(ł|l)udnie|w\\s*po(ł|l)udnie|popo(ł|l)udnie|po\\s*po(ł|l)udniu|rano|wieczór|wieczorem|noc|w\\s*nocy)/i\n};\nconst parseDayPeriodPatterns = {\n    narrow: {\n        am: /^a$/i,\n        pm: /^p$/i,\n        midnight: /pó(ł|l)n/i,\n        noon: /po(ł|l)/i,\n        morning: /rano/i,\n        afternoon: /po\\s*po(ł|l)/i,\n        evening: /wiecz/i,\n        night: /noc/i\n    },\n    any: {\n        am: /^am/i,\n        pm: /^pm/i,\n        midnight: /pó(ł|l)n/i,\n        noon: /po(ł|l)/i,\n        morning: /rano/i,\n        afternoon: /po\\s*po(ł|l)/i,\n        evening: /wiecz/i,\n        night: /noc/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/match.js\n"));

/***/ })

}]);