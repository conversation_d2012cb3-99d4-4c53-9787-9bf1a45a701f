(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9559],{32028:(e,t,r)=>{"use strict";r.d(t,{Navigation:()=>x});var s=r(67252),o=r(89688),a=r(40165),i=r(26808),n=r(62182),d=r(78799);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gold:"bg-gradient-to-r from-gold-400 to-gold-600 text-white hover:from-gold-500 hover:to-gold-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef((e,t)=>{let{className:r,variant:o,size:i,asChild:c=!1,...x}=e,h=c?a.DX:"button";return(0,s.jsx)(h,{className:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,d.QP)((0,n.$)(t))}(l({variant:o,size:i,className:r})),ref:t,...x})});function x(){let[e,t]=(0,o.useState)(!1);return(0,s.jsx)("nav",{className:"bg-white/95 backdrop-blur-sm border-b border-gold-200/50 sticky top-0 z-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("a",{href:"/",className:"text-2xl font-bold text-gradient-gold",children:"Alokita Candles"})}),(0,s.jsx)("div",{className:"hidden md:block",children:(0,s.jsxs)("div",{className:"ml-10 flex items-baseline space-x-8",children:[(0,s.jsx)("a",{href:"/",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"Home"}),(0,s.jsx)("a",{href:"/products",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"Products"}),(0,s.jsx)("a",{href:"/categories",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"Categories"}),(0,s.jsx)("a",{href:"/about",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"About"}),(0,s.jsx)("a",{href:"/contact",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"Contact"})]})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,s.jsx)(c,{variant:"outline",size:"sm",children:"Cart (0)"}),(0,s.jsx)(c,{variant:"gold",size:"sm",asChild:!0,children:(0,s.jsx)("a",{href:"/admin",children:"Admin"})})]}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)("button",{onClick:()=>t(!e),className:"text-gray-700 hover:text-gold-600 focus:outline-none focus:text-gold-600",children:(0,s.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gold-200/50",children:[(0,s.jsx)("a",{href:"/",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"Home"}),(0,s.jsx)("a",{href:"/products",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"Products"}),(0,s.jsx)("a",{href:"/categories",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"Categories"}),(0,s.jsx)("a",{href:"/about",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"About"}),(0,s.jsx)("a",{href:"/contact",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"Contact"}),(0,s.jsxs)("div",{className:"flex space-x-2 px-3 py-2",children:[(0,s.jsx)(c,{variant:"outline",size:"sm",className:"flex-1",children:"Cart (0)"}),(0,s.jsx)(c,{variant:"gold",size:"sm",className:"flex-1",asChild:!0,children:(0,s.jsx)("a",{href:"/admin",children:"Admin"})})]})]})})]})})}c.displayName="Button"},52713:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,56319,23)),Promise.resolve().then(r.bind(r,32028))},56319:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[5460,4414,7317,7627,7358],()=>t(52713)),_N_E=e.O()}]);