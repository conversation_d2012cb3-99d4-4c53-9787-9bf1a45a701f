/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/(frontend)/page"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(frontend)/page.tsx */ \"(app-pages-browser)/./src/app/(frontend)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuMF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wX3Nhc3NAMS43Ny40L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZoYXJzaGx1aGFyJTJGbGFsaXQlMjBkb24lMkZhbG9raXRhLWNhbmRsZXMlMkZzcmMlMkZhcHAlMkYoZnJvbnRlbmQpJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBNEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2FwcC8oZnJvbnRlbmQpL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuMF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wX3Nhc3NAMS43Ny40L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyw2TEFBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdCQUFnQjtBQUNwQixJQUFJLGNBQWM7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHIiwic291cmNlcyI6WyIvVXNlcnMvaGFyc2hsdWhhci9sYWxpdCBkb24vYWxva2l0YS1jYW5kbGVzL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuMF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wX3Nhc3NAMS43Ny40L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIFJlYWN0XG4gKiByZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanNcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cblwidXNlIHN0cmljdFwiO1xuXCJwcm9kdWN0aW9uXCIgIT09IHByb2Nlc3MuZW52Lk5PREVfRU5WICYmXG4gIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpIHtcbiAgICAgIGlmIChudWxsID09IHR5cGUpIHJldHVybiBudWxsO1xuICAgICAgaWYgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHJldHVybiB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9DTElFTlRfUkVGRVJFTkNFXG4gICAgICAgICAgPyBudWxsXG4gICAgICAgICAgOiB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBudWxsO1xuICAgICAgaWYgKFwic3RyaW5nXCIgPT09IHR5cGVvZiB0eXBlKSByZXR1cm4gdHlwZTtcbiAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICBjYXNlIFJFQUNUX0ZSQUdNRU5UX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiRnJhZ21lbnRcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9QUk9GSUxFUl9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlByb2ZpbGVyXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1RSSUNUX01PREVfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdHJpY3RNb2RlXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1VTUEVOU0VfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZVwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZUxpc3RcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9BQ1RJVklUWV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIkFjdGl2aXR5XCI7XG4gICAgICB9XG4gICAgICBpZiAoXCJvYmplY3RcIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHN3aXRjaCAoXG4gICAgICAgICAgKFwibnVtYmVyXCIgPT09IHR5cGVvZiB0eXBlLnRhZyAmJlxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgXCJSZWNlaXZlZCBhbiB1bmV4cGVjdGVkIG9iamVjdCBpbiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoKS4gVGhpcyBpcyBsaWtlbHkgYSBidWcgaW4gUmVhY3QuIFBsZWFzZSBmaWxlIGFuIGlzc3VlLlwiXG4gICAgICAgICAgICApLFxuICAgICAgICAgIHR5cGUuJCR0eXBlb2YpXG4gICAgICAgICkge1xuICAgICAgICAgIGNhc2UgUkVBQ1RfUE9SVEFMX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gXCJQb3J0YWxcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0NPTlRFWFRfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAodHlwZS5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIikgKyBcIi5Qcm92aWRlclwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfQ09OU1VNRVJfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAodHlwZS5fY29udGV4dC5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIikgKyBcIi5Db25zdW1lclwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRTpcbiAgICAgICAgICAgIHZhciBpbm5lclR5cGUgPSB0eXBlLnJlbmRlcjtcbiAgICAgICAgICAgIHR5cGUgPSB0eXBlLmRpc3BsYXlOYW1lO1xuICAgICAgICAgICAgdHlwZSB8fFxuICAgICAgICAgICAgICAoKHR5cGUgPSBpbm5lclR5cGUuZGlzcGxheU5hbWUgfHwgaW5uZXJUeXBlLm5hbWUgfHwgXCJcIiksXG4gICAgICAgICAgICAgICh0eXBlID0gXCJcIiAhPT0gdHlwZSA/IFwiRm9yd2FyZFJlZihcIiArIHR5cGUgKyBcIilcIiA6IFwiRm9yd2FyZFJlZlwiKSk7XG4gICAgICAgICAgICByZXR1cm4gdHlwZTtcbiAgICAgICAgICBjYXNlIFJFQUNUX01FTU9fVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIChpbm5lclR5cGUgPSB0eXBlLmRpc3BsYXlOYW1lIHx8IG51bGwpLFxuICAgICAgICAgICAgICBudWxsICE9PSBpbm5lclR5cGVcbiAgICAgICAgICAgICAgICA/IGlubmVyVHlwZVxuICAgICAgICAgICAgICAgIDogZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUudHlwZSkgfHwgXCJNZW1vXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgY2FzZSBSRUFDVF9MQVpZX1RZUEU6XG4gICAgICAgICAgICBpbm5lclR5cGUgPSB0eXBlLl9wYXlsb2FkO1xuICAgICAgICAgICAgdHlwZSA9IHR5cGUuX2luaXQ7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICByZXR1cm4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUoaW5uZXJUeXBlKSk7XG4gICAgICAgICAgICB9IGNhdGNoICh4KSB7fVxuICAgICAgICB9XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgZnVuY3Rpb24gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gICAgICByZXR1cm4gXCJcIiArIHZhbHVlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBjaGVja0tleVN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gICAgICB0cnkge1xuICAgICAgICB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpO1xuICAgICAgICB2YXIgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gITE7XG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9ICEwO1xuICAgICAgfVxuICAgICAgaWYgKEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCkge1xuICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSBjb25zb2xlO1xuICAgICAgICB2YXIgSlNDb21waWxlcl90ZW1wX2NvbnN0ID0gSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0LmVycm9yO1xuICAgICAgICB2YXIgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0JGpzY29tcCQwID1cbiAgICAgICAgICAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgU3ltYm9sICYmXG4gICAgICAgICAgICBTeW1ib2wudG9TdHJpbmdUYWcgJiZcbiAgICAgICAgICAgIHZhbHVlW1N5bWJvbC50b1N0cmluZ1RhZ10pIHx8XG4gICAgICAgICAgdmFsdWUuY29uc3RydWN0b3IubmFtZSB8fFxuICAgICAgICAgIFwiT2JqZWN0XCI7XG4gICAgICAgIEpTQ29tcGlsZXJfdGVtcF9jb25zdC5jYWxsKFxuICAgICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCxcbiAgICAgICAgICBcIlRoZSBwcm92aWRlZCBrZXkgaXMgYW4gdW5zdXBwb3J0ZWQgdHlwZSAlcy4gVGhpcyB2YWx1ZSBtdXN0IGJlIGNvZXJjZWQgdG8gYSBzdHJpbmcgYmVmb3JlIHVzaW5nIGl0IGhlcmUuXCIsXG4gICAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0JGpzY29tcCQwXG4gICAgICAgICk7XG4gICAgICAgIHJldHVybiB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpO1xuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBnZXRUYXNrTmFtZSh0eXBlKSB7XG4gICAgICBpZiAodHlwZSA9PT0gUkVBQ1RfRlJBR01FTlRfVFlQRSkgcmV0dXJuIFwiPD5cIjtcbiAgICAgIGlmIChcbiAgICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIHR5cGUgJiZcbiAgICAgICAgbnVsbCAhPT0gdHlwZSAmJlxuICAgICAgICB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9MQVpZX1RZUEVcbiAgICAgIClcbiAgICAgICAgcmV0dXJuIFwiPC4uLj5cIjtcbiAgICAgIHRyeSB7XG4gICAgICAgIHZhciBuYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgICByZXR1cm4gbmFtZSA/IFwiPFwiICsgbmFtZSArIFwiPlwiIDogXCI8Li4uPlwiO1xuICAgICAgfSBjYXRjaCAoeCkge1xuICAgICAgICByZXR1cm4gXCI8Li4uPlwiO1xuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBnZXRPd25lcigpIHtcbiAgICAgIHZhciBkaXNwYXRjaGVyID0gUmVhY3RTaGFyZWRJbnRlcm5hbHMuQTtcbiAgICAgIHJldHVybiBudWxsID09PSBkaXNwYXRjaGVyID8gbnVsbCA6IGRpc3BhdGNoZXIuZ2V0T3duZXIoKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gVW5rbm93bk93bmVyKCkge1xuICAgICAgcmV0dXJuIEVycm9yKFwicmVhY3Qtc3RhY2stdG9wLWZyYW1lXCIpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBoYXNWYWxpZEtleShjb25maWcpIHtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgXCJrZXlcIikpIHtcbiAgICAgICAgdmFyIGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoY29uZmlnLCBcImtleVwiKS5nZXQ7XG4gICAgICAgIGlmIChnZXR0ZXIgJiYgZ2V0dGVyLmlzUmVhY3RXYXJuaW5nKSByZXR1cm4gITE7XG4gICAgICB9XG4gICAgICByZXR1cm4gdm9pZCAwICE9PSBjb25maWcua2V5O1xuICAgIH1cbiAgICBmdW5jdGlvbiBkZWZpbmVLZXlQcm9wV2FybmluZ0dldHRlcihwcm9wcywgZGlzcGxheU5hbWUpIHtcbiAgICAgIGZ1bmN0aW9uIHdhcm5BYm91dEFjY2Vzc2luZ0tleSgpIHtcbiAgICAgICAgc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gfHxcbiAgICAgICAgICAoKHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duID0gITApLFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICBcIiVzOiBga2V5YCBpcyBub3QgYSBwcm9wLiBUcnlpbmcgdG8gYWNjZXNzIGl0IHdpbGwgcmVzdWx0IGluIGB1bmRlZmluZWRgIGJlaW5nIHJldHVybmVkLiBJZiB5b3UgbmVlZCB0byBhY2Nlc3MgdGhlIHNhbWUgdmFsdWUgd2l0aGluIHRoZSBjaGlsZCBjb21wb25lbnQsIHlvdSBzaG91bGQgcGFzcyBpdCBhcyBhIGRpZmZlcmVudCBwcm9wLiAoaHR0cHM6Ly9yZWFjdC5kZXYvbGluay9zcGVjaWFsLXByb3BzKVwiLFxuICAgICAgICAgICAgZGlzcGxheU5hbWVcbiAgICAgICAgICApKTtcbiAgICAgIH1cbiAgICAgIHdhcm5BYm91dEFjY2Vzc2luZ0tleS5pc1JlYWN0V2FybmluZyA9ICEwO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHByb3BzLCBcImtleVwiLCB7XG4gICAgICAgIGdldDogd2FybkFib3V0QWNjZXNzaW5nS2V5LFxuICAgICAgICBjb25maWd1cmFibGU6ICEwXG4gICAgICB9KTtcbiAgICB9XG4gICAgZnVuY3Rpb24gZWxlbWVudFJlZkdldHRlcldpdGhEZXByZWNhdGlvbldhcm5pbmcoKSB7XG4gICAgICB2YXIgY29tcG9uZW50TmFtZSA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0aGlzLnR5cGUpO1xuICAgICAgZGlkV2FybkFib3V0RWxlbWVudFJlZltjb21wb25lbnROYW1lXSB8fFxuICAgICAgICAoKGRpZFdhcm5BYm91dEVsZW1lbnRSZWZbY29tcG9uZW50TmFtZV0gPSAhMCksXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgXCJBY2Nlc3NpbmcgZWxlbWVudC5yZWYgd2FzIHJlbW92ZWQgaW4gUmVhY3QgMTkuIHJlZiBpcyBub3cgYSByZWd1bGFyIHByb3AuIEl0IHdpbGwgYmUgcmVtb3ZlZCBmcm9tIHRoZSBKU1ggRWxlbWVudCB0eXBlIGluIGEgZnV0dXJlIHJlbGVhc2UuXCJcbiAgICAgICAgKSk7XG4gICAgICBjb21wb25lbnROYW1lID0gdGhpcy5wcm9wcy5yZWY7XG4gICAgICByZXR1cm4gdm9pZCAwICE9PSBjb21wb25lbnROYW1lID8gY29tcG9uZW50TmFtZSA6IG51bGw7XG4gICAgfVxuICAgIGZ1bmN0aW9uIFJlYWN0RWxlbWVudChcbiAgICAgIHR5cGUsXG4gICAgICBrZXksXG4gICAgICBzZWxmLFxuICAgICAgc291cmNlLFxuICAgICAgb3duZXIsXG4gICAgICBwcm9wcyxcbiAgICAgIGRlYnVnU3RhY2ssXG4gICAgICBkZWJ1Z1Rhc2tcbiAgICApIHtcbiAgICAgIHNlbGYgPSBwcm9wcy5yZWY7XG4gICAgICB0eXBlID0ge1xuICAgICAgICAkJHR5cGVvZjogUkVBQ1RfRUxFTUVOVF9UWVBFLFxuICAgICAgICB0eXBlOiB0eXBlLFxuICAgICAgICBrZXk6IGtleSxcbiAgICAgICAgcHJvcHM6IHByb3BzLFxuICAgICAgICBfb3duZXI6IG93bmVyXG4gICAgICB9O1xuICAgICAgbnVsbCAhPT0gKHZvaWQgMCAhPT0gc2VsZiA/IHNlbGYgOiBudWxsKVxuICAgICAgICA/IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcInJlZlwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgICAgIGdldDogZWxlbWVudFJlZkdldHRlcldpdGhEZXByZWNhdGlvbldhcm5pbmdcbiAgICAgICAgICB9KVxuICAgICAgICA6IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcInJlZlwiLCB7IGVudW1lcmFibGU6ICExLCB2YWx1ZTogbnVsbCB9KTtcbiAgICAgIHR5cGUuX3N0b3JlID0ge307XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZS5fc3RvcmUsIFwidmFsaWRhdGVkXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IDBcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnSW5mb1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBudWxsXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z1N0YWNrXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IGRlYnVnU3RhY2tcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnVGFza1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBkZWJ1Z1Rhc2tcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmZyZWV6ZSAmJiAoT2JqZWN0LmZyZWV6ZSh0eXBlLnByb3BzKSwgT2JqZWN0LmZyZWV6ZSh0eXBlKSk7XG4gICAgICByZXR1cm4gdHlwZTtcbiAgICB9XG4gICAgZnVuY3Rpb24ganN4REVWSW1wbChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmLFxuICAgICAgZGVidWdTdGFjayxcbiAgICAgIGRlYnVnVGFza1xuICAgICkge1xuICAgICAgdmFyIGNoaWxkcmVuID0gY29uZmlnLmNoaWxkcmVuO1xuICAgICAgaWYgKHZvaWQgMCAhPT0gY2hpbGRyZW4pXG4gICAgICAgIGlmIChpc1N0YXRpY0NoaWxkcmVuKVxuICAgICAgICAgIGlmIChpc0FycmF5SW1wbChjaGlsZHJlbikpIHtcbiAgICAgICAgICAgIGZvciAoXG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPSAwO1xuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuIDwgY2hpbGRyZW4ubGVuZ3RoO1xuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuKytcbiAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW5baXNTdGF0aWNDaGlsZHJlbl0pO1xuICAgICAgICAgICAgT2JqZWN0LmZyZWV6ZSAmJiBPYmplY3QuZnJlZXplKGNoaWxkcmVuKTtcbiAgICAgICAgICB9IGVsc2VcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgIFwiUmVhY3QuanN4OiBTdGF0aWMgY2hpbGRyZW4gc2hvdWxkIGFsd2F5cyBiZSBhbiBhcnJheS4gWW91IGFyZSBsaWtlbHkgZXhwbGljaXRseSBjYWxsaW5nIFJlYWN0LmpzeHMgb3IgUmVhY3QuanN4REVWLiBVc2UgdGhlIEJhYmVsIHRyYW5zZm9ybSBpbnN0ZWFkLlwiXG4gICAgICAgICAgICApO1xuICAgICAgICBlbHNlIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuKTtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgXCJrZXlcIikpIHtcbiAgICAgICAgY2hpbGRyZW4gPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSk7XG4gICAgICAgIHZhciBrZXlzID0gT2JqZWN0LmtleXMoY29uZmlnKS5maWx0ZXIoZnVuY3Rpb24gKGspIHtcbiAgICAgICAgICByZXR1cm4gXCJrZXlcIiAhPT0gaztcbiAgICAgICAgfSk7XG4gICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPVxuICAgICAgICAgIDAgPCBrZXlzLmxlbmd0aFxuICAgICAgICAgICAgPyBcIntrZXk6IHNvbWVLZXksIFwiICsga2V5cy5qb2luKFwiOiAuLi4sIFwiKSArIFwiOiAuLi59XCJcbiAgICAgICAgICAgIDogXCJ7a2V5OiBzb21lS2V5fVwiO1xuICAgICAgICBkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY2hpbGRyZW4gKyBpc1N0YXRpY0NoaWxkcmVuXSB8fFxuICAgICAgICAgICgoa2V5cyA9XG4gICAgICAgICAgICAwIDwga2V5cy5sZW5ndGggPyBcIntcIiArIGtleXMuam9pbihcIjogLi4uLCBcIikgKyBcIjogLi4ufVwiIDogXCJ7fVwiKSxcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgJ0EgcHJvcHMgb2JqZWN0IGNvbnRhaW5pbmcgYSBcImtleVwiIHByb3AgaXMgYmVpbmcgc3ByZWFkIGludG8gSlNYOlxcbiAgbGV0IHByb3BzID0gJXM7XFxuICA8JXMgey4uLnByb3BzfSAvPlxcblJlYWN0IGtleXMgbXVzdCBiZSBwYXNzZWQgZGlyZWN0bHkgdG8gSlNYIHdpdGhvdXQgdXNpbmcgc3ByZWFkOlxcbiAgbGV0IHByb3BzID0gJXM7XFxuICA8JXMga2V5PXtzb21lS2V5fSB7Li4ucHJvcHN9IC8+JyxcbiAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICAgICAgICBjaGlsZHJlbixcbiAgICAgICAgICAgIGtleXMsXG4gICAgICAgICAgICBjaGlsZHJlblxuICAgICAgICAgICksXG4gICAgICAgICAgKGRpZFdhcm5BYm91dEtleVNwcmVhZFtjaGlsZHJlbiArIGlzU3RhdGljQ2hpbGRyZW5dID0gITApKTtcbiAgICAgIH1cbiAgICAgIGNoaWxkcmVuID0gbnVsbDtcbiAgICAgIHZvaWQgMCAhPT0gbWF5YmVLZXkgJiZcbiAgICAgICAgKGNoZWNrS2V5U3RyaW5nQ29lcmNpb24obWF5YmVLZXkpLCAoY2hpbGRyZW4gPSBcIlwiICsgbWF5YmVLZXkpKTtcbiAgICAgIGhhc1ZhbGlkS2V5KGNvbmZpZykgJiZcbiAgICAgICAgKGNoZWNrS2V5U3RyaW5nQ29lcmNpb24oY29uZmlnLmtleSksIChjaGlsZHJlbiA9IFwiXCIgKyBjb25maWcua2V5KSk7XG4gICAgICBpZiAoXCJrZXlcIiBpbiBjb25maWcpIHtcbiAgICAgICAgbWF5YmVLZXkgPSB7fTtcbiAgICAgICAgZm9yICh2YXIgcHJvcE5hbWUgaW4gY29uZmlnKVxuICAgICAgICAgIFwia2V5XCIgIT09IHByb3BOYW1lICYmIChtYXliZUtleVtwcm9wTmFtZV0gPSBjb25maWdbcHJvcE5hbWVdKTtcbiAgICAgIH0gZWxzZSBtYXliZUtleSA9IGNvbmZpZztcbiAgICAgIGNoaWxkcmVuICYmXG4gICAgICAgIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKFxuICAgICAgICAgIG1heWJlS2V5LFxuICAgICAgICAgIFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGVcbiAgICAgICAgICAgID8gdHlwZS5kaXNwbGF5TmFtZSB8fCB0eXBlLm5hbWUgfHwgXCJVbmtub3duXCJcbiAgICAgICAgICAgIDogdHlwZVxuICAgICAgICApO1xuICAgICAgcmV0dXJuIFJlYWN0RWxlbWVudChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY2hpbGRyZW4sXG4gICAgICAgIHNlbGYsXG4gICAgICAgIHNvdXJjZSxcbiAgICAgICAgZ2V0T3duZXIoKSxcbiAgICAgICAgbWF5YmVLZXksXG4gICAgICAgIGRlYnVnU3RhY2ssXG4gICAgICAgIGRlYnVnVGFza1xuICAgICAgKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gdmFsaWRhdGVDaGlsZEtleXMobm9kZSkge1xuICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIG5vZGUgJiZcbiAgICAgICAgbnVsbCAhPT0gbm9kZSAmJlxuICAgICAgICBub2RlLiQkdHlwZW9mID09PSBSRUFDVF9FTEVNRU5UX1RZUEUgJiZcbiAgICAgICAgbm9kZS5fc3RvcmUgJiZcbiAgICAgICAgKG5vZGUuX3N0b3JlLnZhbGlkYXRlZCA9IDEpO1xuICAgIH1cbiAgICB2YXIgUmVhY3QgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0XCIpLFxuICAgICAgUkVBQ1RfRUxFTUVOVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnRyYW5zaXRpb25hbC5lbGVtZW50XCIpLFxuICAgICAgUkVBQ1RfUE9SVEFMX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QucG9ydGFsXCIpLFxuICAgICAgUkVBQ1RfRlJBR01FTlRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5mcmFnbWVudFwiKSxcbiAgICAgIFJFQUNUX1NUUklDVF9NT0RFX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3RyaWN0X21vZGVcIiksXG4gICAgICBSRUFDVF9QUk9GSUxFUl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnByb2ZpbGVyXCIpO1xuICAgIFN5bWJvbC5mb3IoXCJyZWFjdC5wcm92aWRlclwiKTtcbiAgICB2YXIgUkVBQ1RfQ09OU1VNRVJfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jb25zdW1lclwiKSxcbiAgICAgIFJFQUNUX0NPTlRFWFRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jb250ZXh0XCIpLFxuICAgICAgUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5mb3J3YXJkX3JlZlwiKSxcbiAgICAgIFJFQUNUX1NVU1BFTlNFX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3VzcGVuc2VcIiksXG4gICAgICBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3VzcGVuc2VfbGlzdFwiKSxcbiAgICAgIFJFQUNUX01FTU9fVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5tZW1vXCIpLFxuICAgICAgUkVBQ1RfTEFaWV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmxhenlcIiksXG4gICAgICBSRUFDVF9BQ1RJVklUWV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmFjdGl2aXR5XCIpLFxuICAgICAgUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jbGllbnQucmVmZXJlbmNlXCIpLFxuICAgICAgUmVhY3RTaGFyZWRJbnRlcm5hbHMgPVxuICAgICAgICBSZWFjdC5fX0NMSUVOVF9JTlRFUk5BTFNfRE9fTk9UX1VTRV9PUl9XQVJOX1VTRVJTX1RIRVlfQ0FOTk9UX1VQR1JBREUsXG4gICAgICBoYXNPd25Qcm9wZXJ0eSA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHksXG4gICAgICBpc0FycmF5SW1wbCA9IEFycmF5LmlzQXJyYXksXG4gICAgICBjcmVhdGVUYXNrID0gY29uc29sZS5jcmVhdGVUYXNrXG4gICAgICAgID8gY29uc29sZS5jcmVhdGVUYXNrXG4gICAgICAgIDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgfTtcbiAgICBSZWFjdCA9IHtcbiAgICAgIFwicmVhY3Qtc3RhY2stYm90dG9tLWZyYW1lXCI6IGZ1bmN0aW9uIChjYWxsU3RhY2tGb3JFcnJvcikge1xuICAgICAgICByZXR1cm4gY2FsbFN0YWNrRm9yRXJyb3IoKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIHZhciBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93bjtcbiAgICB2YXIgZGlkV2FybkFib3V0RWxlbWVudFJlZiA9IHt9O1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1N0YWNrID0gUmVhY3RbXCJyZWFjdC1zdGFjay1ib3R0b20tZnJhbWVcIl0uYmluZChcbiAgICAgIFJlYWN0LFxuICAgICAgVW5rbm93bk93bmVyXG4gICAgKSgpO1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1Rhc2sgPSBjcmVhdGVUYXNrKGdldFRhc2tOYW1lKFVua25vd25Pd25lcikpO1xuICAgIHZhciBkaWRXYXJuQWJvdXRLZXlTcHJlYWQgPSB7fTtcbiAgICBleHBvcnRzLkZyYWdtZW50ID0gUkVBQ1RfRlJBR01FTlRfVFlQRTtcbiAgICBleHBvcnRzLmpzeERFViA9IGZ1bmN0aW9uIChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmXG4gICAgKSB7XG4gICAgICB2YXIgdHJhY2tBY3R1YWxPd25lciA9XG4gICAgICAgIDFlNCA+IFJlYWN0U2hhcmVkSW50ZXJuYWxzLnJlY2VudGx5Q3JlYXRlZE93bmVyU3RhY2tzKys7XG4gICAgICByZXR1cm4ganN4REVWSW1wbChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY29uZmlnLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBzZWxmLFxuICAgICAgICB0cmFja0FjdHVhbE93bmVyXG4gICAgICAgICAgPyBFcnJvcihcInJlYWN0LXN0YWNrLXRvcC1mcmFtZVwiKVxuICAgICAgICAgIDogdW5rbm93bk93bmVyRGVidWdTdGFjayxcbiAgICAgICAgdHJhY2tBY3R1YWxPd25lciA/IGNyZWF0ZVRhc2soZ2V0VGFza05hbWUodHlwZSkpIDogdW5rbm93bk93bmVyRGVidWdUYXNrXG4gICAgICApO1xuICAgIH07XG4gIH0pKCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuMF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wX3Nhc3NAMS43Ny40L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSxxUkFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMy4wX3JlYWN0LWRvbUAxOS4xLjBfcmVhY3RAMTkuMS4wX19yZWFjdEAxOS4xLjBfc2Fzc0AxLjc3LjQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(frontend)/page.tsx":
/*!*************************************!*\
  !*** ./src/app/(frontend)/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Mock data for development when database is not available\nconst mockProducts = [\n    {\n        id: '1',\n        name: 'Rose Petal Bliss',\n        price: 899,\n        scent: 'Rose, Jasmine, Peony',\n        isFeatured: true,\n        inStock: true,\n        slug: 'rose-petal-bliss'\n    },\n    {\n        id: '2',\n        name: 'Sandalwood Serenity',\n        price: 1099,\n        scent: 'Sandalwood, Cedar, Vanilla',\n        isFeatured: true,\n        inStock: true,\n        slug: 'sandalwood-serenity'\n    },\n    {\n        id: '3',\n        name: 'Festive Spice',\n        price: 1299,\n        scent: 'Cinnamon, Orange, Clove',\n        isFeatured: true,\n        inStock: true,\n        slug: 'festive-spice'\n    }\n];\nasync function HomePage() {\n    // Use mock data for development (when database is not available)\n    const featuredProducts = mockProducts;\n    // Sample testimonials data (you can move this to CMS later)\n    const testimonials = [\n        {\n            name: \"Priya Sharma\",\n            location: \"Mumbai\",\n            review: \"The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!\",\n            rating: 5\n        },\n        {\n            name: \"Arjun Patel\",\n            location: \"Ahmedabad\",\n            review: \"Amazing quality and the scents are so authentic. Love supporting local artisans!\",\n            rating: 5\n        },\n        {\n            name: \"Kavya Reddy\",\n            location: \"Bangalore\",\n            review: \"Perfect for my meditation space. The woody scent collection is my favorite.\",\n            rating: 4\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"test-tailwind\",\n                children: \"TAILWIND TEST - If this is blue with white text, Tailwind is working!\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                style: {\n                    minHeight: '100vh',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    background: 'linear-gradient(135deg, #fdf2f8 0%, #ffffff 50%, #fef7cd 100%)',\n                    padding: '2rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        maxWidth: '1200px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-gradient-gold\",\n                            style: {\n                                fontSize: 'clamp(2rem, 5vw, 4rem)',\n                                fontWeight: 'bold',\n                                marginBottom: '1.5rem',\n                                lineHeight: '1.2'\n                            },\n                            children: \"Illuminate Your Space with Alokita Candles\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                fontSize: 'clamp(1rem, 2.5vw, 1.5rem)',\n                                color: '#6b7280',\n                                marginBottom: '2rem',\n                                lineHeight: '1.6'\n                            },\n                            children: \"Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '1rem',\n                                justifyContent: 'center',\n                                flexWrap: 'wrap'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        backgroundColor: '#eab308',\n                                        color: 'white',\n                                        padding: '0.75rem 2rem',\n                                        borderRadius: '0.5rem',\n                                        border: 'none',\n                                        fontSize: '1.125rem',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s'\n                                    },\n                                    children: \"Shop Now\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        backgroundColor: 'transparent',\n                                        color: '#374151',\n                                        padding: '0.75rem 2rem',\n                                        borderRadius: '0.5rem',\n                                        border: '2px solid #d1d5db',\n                                        fontSize: '1.125rem',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s'\n                                    },\n                                    children: \"Our Story\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                style: {\n                    padding: '4rem 2rem',\n                    backgroundColor: '#ffffff'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-gradient-gold\",\n                            style: {\n                                fontSize: '2.5rem',\n                                fontWeight: 'bold',\n                                textAlign: 'center',\n                                marginBottom: '3rem'\n                            },\n                            children: \"Featured Candles\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                                gap: '2rem'\n                            },\n                            children: featuredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '0.5rem',\n                                        border: '1px solid #e5e7eb',\n                                        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n                                        overflow: 'hidden',\n                                        transition: 'transform 0.2s, box-shadow 0.2s'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                aspectRatio: '1',\n                                                background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '4rem'\n                                                },\n                                                children: \"\\uD83D\\uDD6F️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '1.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        fontSize: '1.25rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.5rem',\n                                                        color: '#111827'\n                                                    },\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#6b7280',\n                                                        marginBottom: '1rem',\n                                                        fontSize: '0.875rem'\n                                                    },\n                                                    children: product.scent\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'space-between'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                fontSize: '1.5rem',\n                                                                fontWeight: 'bold',\n                                                                color: '#eab308'\n                                                            },\n                                                            children: [\n                                                                \"₹\",\n                                                                product.price.toLocaleString('en-IN')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            style: {\n                                                                backgroundColor: '#eab308',\n                                                                color: 'white',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '0.375rem',\n                                                                border: 'none',\n                                                                fontWeight: '500',\n                                                                cursor: 'pointer',\n                                                                transition: 'background-color 0.2s'\n                                                            },\n                                                            children: \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                style: {\n                    padding: '5rem 2rem',\n                    background: 'linear-gradient(135deg, #fdf2f8 0%, #ffffff 50%, #fef7cd 100%)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: '1fr',\n                            gap: '3rem',\n                            alignItems: 'center'\n                        },\n                        className: \"lg:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-gradient-gold\",\n                                        style: {\n                                            fontSize: '2.5rem',\n                                            fontWeight: 'bold',\n                                            marginBottom: '1.5rem',\n                                            lineHeight: '1.2'\n                                        },\n                                        children: \"Our Story\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: '1.125rem',\n                                            color: '#6b7280',\n                                            marginBottom: '1.5rem',\n                                            lineHeight: '1.7'\n                                        },\n                                        children: \"Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#6b7280',\n                                            marginBottom: '2rem',\n                                            lineHeight: '1.6'\n                                        },\n                                        children: \"Each candle is carefully crafted with premium soy wax and housed in beautiful glass containers that can be repurposed long after the candle has burned.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                            gap: '1rem',\n                                            marginBottom: '2rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                                                    padding: '1.5rem',\n                                                    borderRadius: '0.75rem',\n                                                    border: '1px solid rgba(234, 179, 8, 0.2)',\n                                                    textAlign: 'center',\n                                                    backdropFilter: 'blur(8px)'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '2rem',\n                                                            marginBottom: '0.5rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDF3F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        style: {\n                                                            fontWeight: '600',\n                                                            marginBottom: '0.5rem',\n                                                            color: '#111827'\n                                                        },\n                                                        children: \"Natural Ingredients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            color: '#6b7280'\n                                                        },\n                                                        children: \"Premium soy wax & natural fragrances\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                                                    padding: '1.5rem',\n                                                    borderRadius: '0.75rem',\n                                                    border: '1px solid rgba(234, 179, 8, 0.2)',\n                                                    textAlign: 'center',\n                                                    backdropFilter: 'blur(8px)'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '2rem',\n                                                            marginBottom: '0.5rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDFFA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        style: {\n                                                            fontWeight: '600',\n                                                            marginBottom: '0.5rem',\n                                                            color: '#111827'\n                                                        },\n                                                        children: \"Elegant Glass\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            color: '#6b7280'\n                                                        },\n                                                        children: \"Reusable glass containers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                                                    padding: '1.5rem',\n                                                    borderRadius: '0.75rem',\n                                                    border: '1px solid rgba(234, 179, 8, 0.2)',\n                                                    textAlign: 'center',\n                                                    backdropFilter: 'blur(8px)'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '2rem',\n                                                            marginBottom: '0.5rem'\n                                                        },\n                                                        children: \"✨\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        style: {\n                                                            fontWeight: '600',\n                                                            marginBottom: '0.5rem',\n                                                            color: '#111827'\n                                                        },\n                                                        children: \"Handcrafted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            color: '#6b7280'\n                                                        },\n                                                        children: \"Made with love in Ahmedabad\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            backgroundColor: '#eab308',\n                                            color: 'white',\n                                            padding: '0.75rem 1.5rem',\n                                            borderRadius: '0.5rem',\n                                            border: 'none',\n                                            fontSize: '1rem',\n                                            fontWeight: '500',\n                                            cursor: 'pointer',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                                        },\n                                        children: \"Learn More About Us\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    aspectRatio: '1',\n                                    background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',\n                                    borderRadius: '1rem',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    position: 'relative',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: 'absolute',\n                                            top: '10%',\n                                            left: '10%',\n                                            width: '20px',\n                                            height: '20px',\n                                            backgroundColor: 'rgba(234, 179, 8, 0.3)',\n                                            borderRadius: '50%',\n                                            animation: 'float 3s ease-in-out infinite'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: 'absolute',\n                                            bottom: '20%',\n                                            right: '15%',\n                                            width: '15px',\n                                            height: '15px',\n                                            backgroundColor: 'rgba(236, 72, 153, 0.3)',\n                                            borderRadius: '50%',\n                                            animation: 'float 4s ease-in-out infinite reverse'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '6rem',\n                                            filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))'\n                                        },\n                                        children: \"\\uD83D\\uDD6F️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                style: {\n                    padding: '5rem 2rem',\n                    backgroundColor: '#ffffff'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center',\n                                marginBottom: '3rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gradient-gold\",\n                                    style: {\n                                        fontSize: '2.5rem',\n                                        fontWeight: 'bold',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"What Our Customers Say\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontSize: '1.125rem',\n                                        color: '#6b7280',\n                                        maxWidth: '600px',\n                                        margin: '0 auto'\n                                    },\n                                    children: \"Don't just take our word for it. Here's what our customers have to say about their Alokita Candles experience.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',\n                                gap: '2rem'\n                            },\n                            children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '1rem',\n                                        border: '1px solid #e5e7eb',\n                                        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n                                        padding: '2rem',\n                                        transition: 'all 0.3s ease',\n                                        position: 'relative',\n                                        overflow: 'hidden'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.transform = 'translateY(-4px)';\n                                        e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.15)';\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.transform = 'translateY(0)';\n                                        e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '1rem',\n                                                right: '1rem',\n                                                fontSize: '3rem',\n                                                color: 'rgba(234, 179, 8, 0.1)',\n                                                fontFamily: 'serif'\n                                            },\n                                            children: '\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                marginBottom: '1.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '3.5rem',\n                                                        height: '3.5rem',\n                                                        background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',\n                                                        borderRadius: '50%',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        marginRight: '1rem',\n                                                        border: '2px solid rgba(234, 179, 8, 0.2)'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontWeight: '600',\n                                                            color: '#eab308',\n                                                            fontSize: '1.25rem'\n                                                        },\n                                                        children: testimonial.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            style: {\n                                                                fontWeight: '600',\n                                                                color: '#111827',\n                                                                marginBottom: '0.25rem',\n                                                                fontSize: '1.125rem'\n                                                            },\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: '0.875rem',\n                                                                color: '#6b7280',\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.25rem'\n                                                            },\n                                                            children: [\n                                                                \"\\uD83D\\uDCCD \",\n                                                                testimonial.location\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                marginBottom: '1.5rem',\n                                                gap: '0.25rem'\n                                            },\n                                            children: [\n                                                [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: i < testimonial.rating ? '#fbbf24' : '#e5e7eb',\n                                                            fontSize: '1.25rem'\n                                                        },\n                                                        children: \"★\"\n                                                    }, i, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        marginLeft: '0.5rem',\n                                                        fontSize: '0.875rem',\n                                                        color: '#6b7280',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: [\n                                                        testimonial.rating,\n                                                        \"/5\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            style: {\n                                                color: '#374151',\n                                                fontStyle: 'italic',\n                                                fontSize: '1rem',\n                                                lineHeight: '1.6',\n                                                position: 'relative',\n                                                paddingLeft: '1rem',\n                                                borderLeft: '3px solid #eab308'\n                                            },\n                                            children: [\n                                                '\"',\n                                                testimonial.review,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginTop: '1.5rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.5rem',\n                                                fontSize: '0.875rem',\n                                                color: '#059669',\n                                                fontWeight: '500'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Verified Purchase\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center',\n                                marginTop: '4rem',\n                                padding: '2rem',\n                                background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',\n                                borderRadius: '1rem',\n                                border: '1px solid rgba(234, 179, 8, 0.2)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: '600',\n                                        marginBottom: '1rem',\n                                        color: '#111827'\n                                    },\n                                    children: \"Join thousands of happy customers\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#6b7280',\n                                        marginBottom: '1.5rem',\n                                        fontSize: '1rem'\n                                    },\n                                    children: \"Experience the magic of Alokita Candles in your own home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        backgroundColor: '#eab308',\n                                        color: 'white',\n                                        padding: '0.75rem 2rem',\n                                        borderRadius: '0.5rem',\n                                        border: 'none',\n                                        fontSize: '1rem',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s',\n                                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                                    },\n                                    children: \"Shop Our Collection\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(frontend)/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);