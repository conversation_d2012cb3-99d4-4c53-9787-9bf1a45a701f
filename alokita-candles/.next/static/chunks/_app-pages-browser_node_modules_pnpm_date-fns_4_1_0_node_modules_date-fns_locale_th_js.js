"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_th_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   th: () => (/* binding */ th)\n/* harmony export */ });\n/* harmony import */ var _th_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./th/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatDistance.js\");\n/* harmony import */ var _th_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./th/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatLong.js\");\n/* harmony import */ var _th_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./th/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatRelative.js\");\n/* harmony import */ var _th_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./th/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/localize.js\");\n/* harmony import */ var _th_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./th/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Thai locale.\n * @language Thai\n * @iso-639-2 tha\n * <AUTHOR> Hirunworawongkun [@athivvat](https://github.com/athivvat)\n * <AUTHOR> * <AUTHOR> I. [@nodtem66](https://github.com/nodtem66)\n */ const th = {\n    code: \"th\",\n    formatDistance: _th_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _th_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _th_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _th_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _th_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 0 /* Sunday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (th);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL3RoLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNkQ7QUFDUjtBQUNRO0FBQ1o7QUFDTjtBQUUzQzs7Ozs7Ozs7Q0FRQyxHQUNNLE1BQU1LLEtBQUs7SUFDaEJDLE1BQU07SUFDTk4sZ0JBQWdCQSxxRUFBY0E7SUFDOUJDLFlBQVlBLDZEQUFVQTtJQUN0QkMsZ0JBQWdCQSxxRUFBY0E7SUFDOUJDLFVBQVVBLHlEQUFRQTtJQUNsQkMsT0FBT0EsbURBQUtBO0lBQ1pHLFNBQVM7UUFDUEMsY0FBYyxFQUFFLFVBQVU7UUFDMUJDLHVCQUF1QjtJQUN6QjtBQUNGLEVBQUU7QUFFRixvQ0FBb0M7QUFDcEMsaUVBQWVKLEVBQUVBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvbm9kZV9tb2R1bGVzLy5wbnBtL2RhdGUtZm5zQDQuMS4wL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvdGguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZm9ybWF0RGlzdGFuY2UgfSBmcm9tIFwiLi90aC9fbGliL2Zvcm1hdERpc3RhbmNlLmpzXCI7XG5pbXBvcnQgeyBmb3JtYXRMb25nIH0gZnJvbSBcIi4vdGgvX2xpYi9mb3JtYXRMb25nLmpzXCI7XG5pbXBvcnQgeyBmb3JtYXRSZWxhdGl2ZSB9IGZyb20gXCIuL3RoL19saWIvZm9ybWF0UmVsYXRpdmUuanNcIjtcbmltcG9ydCB7IGxvY2FsaXplIH0gZnJvbSBcIi4vdGgvX2xpYi9sb2NhbGl6ZS5qc1wiO1xuaW1wb3J0IHsgbWF0Y2ggfSBmcm9tIFwiLi90aC9fbGliL21hdGNoLmpzXCI7XG5cbi8qKlxuICogQGNhdGVnb3J5IExvY2FsZXNcbiAqIEBzdW1tYXJ5IFRoYWkgbG9jYWxlLlxuICogQGxhbmd1YWdlIFRoYWlcbiAqIEBpc28tNjM5LTIgdGhhXG4gKiBAYXV0aG9yIEF0aGl3YXQgSGlydW53b3Jhd29uZ2t1biBbQGF0aGl2dmF0XShodHRwczovL2dpdGh1Yi5jb20vYXRoaXZ2YXQpXG4gKiBAYXV0aG9yIFtAaGF3a3VwXShodHRwczovL2dpdGh1Yi5jb20vaGF3a3VwKVxuICogQGF1dGhvciAgSmlyYXdhdCBJLiBbQG5vZHRlbTY2XShodHRwczovL2dpdGh1Yi5jb20vbm9kdGVtNjYpXG4gKi9cbmV4cG9ydCBjb25zdCB0aCA9IHtcbiAgY29kZTogXCJ0aFwiLFxuICBmb3JtYXREaXN0YW5jZTogZm9ybWF0RGlzdGFuY2UsXG4gIGZvcm1hdExvbmc6IGZvcm1hdExvbmcsXG4gIGZvcm1hdFJlbGF0aXZlOiBmb3JtYXRSZWxhdGl2ZSxcbiAgbG9jYWxpemU6IGxvY2FsaXplLFxuICBtYXRjaDogbWF0Y2gsXG4gIG9wdGlvbnM6IHtcbiAgICB3ZWVrU3RhcnRzT246IDAgLyogU3VuZGF5ICovLFxuICAgIGZpcnN0V2Vla0NvbnRhaW5zRGF0ZTogMSxcbiAgfSxcbn07XG5cbi8vIEZhbGxiYWNrIGZvciBtb2R1bGFyaXplZCBpbXBvcnRzOlxuZXhwb3J0IGRlZmF1bHQgdGg7XG4iXSwibmFtZXMiOlsiZm9ybWF0RGlzdGFuY2UiLCJmb3JtYXRMb25nIiwiZm9ybWF0UmVsYXRpdmUiLCJsb2NhbGl6ZSIsIm1hdGNoIiwidGgiLCJjb2RlIiwib3B0aW9ucyIsIndlZWtTdGFydHNPbiIsImZpcnN0V2Vla0NvbnRhaW5zRGF0ZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"น้อยกว่า 1 วินาที\",\n        other: \"น้อยกว่า {{count}} วินาที\"\n    },\n    xSeconds: {\n        one: \"1 วินาที\",\n        other: \"{{count}} วินาที\"\n    },\n    halfAMinute: \"ครึ่งนาที\",\n    lessThanXMinutes: {\n        one: \"น้อยกว่า 1 นาที\",\n        other: \"น้อยกว่า {{count}} นาที\"\n    },\n    xMinutes: {\n        one: \"1 นาที\",\n        other: \"{{count}} นาที\"\n    },\n    aboutXHours: {\n        one: \"ประมาณ 1 ชั่วโมง\",\n        other: \"ประมาณ {{count}} ชั่วโมง\"\n    },\n    xHours: {\n        one: \"1 ชั่วโมง\",\n        other: \"{{count}} ชั่วโมง\"\n    },\n    xDays: {\n        one: \"1 วัน\",\n        other: \"{{count}} วัน\"\n    },\n    aboutXWeeks: {\n        one: \"ประมาณ 1 สัปดาห์\",\n        other: \"ประมาณ {{count}} สัปดาห์\"\n    },\n    xWeeks: {\n        one: \"1 สัปดาห์\",\n        other: \"{{count}} สัปดาห์\"\n    },\n    aboutXMonths: {\n        one: \"ประมาณ 1 เดือน\",\n        other: \"ประมาณ {{count}} เดือน\"\n    },\n    xMonths: {\n        one: \"1 เดือน\",\n        other: \"{{count}} เดือน\"\n    },\n    aboutXYears: {\n        one: \"ประมาณ 1 ปี\",\n        other: \"ประมาณ {{count}} ปี\"\n    },\n    xYears: {\n        one: \"1 ปี\",\n        other: \"{{count}} ปี\"\n    },\n    overXYears: {\n        one: \"มากกว่า 1 ปี\",\n        other: \"มากกว่า {{count}} ปี\"\n    },\n    almostXYears: {\n        one: \"เกือบ 1 ปี\",\n        other: \"เกือบ {{count}} ปี\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            if (token === \"halfAMinute\") {\n                return \"ใน\" + result;\n            } else {\n                return \"ใน \" + result;\n            }\n        } else {\n            return result + \"ที่ผ่านมา\";\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"วันEEEEที่ do MMMM y\",\n    long: \"do MMMM y\",\n    medium: \"d MMM y\",\n    short: \"dd/MM/yyyy\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss น. zzzz\",\n    long: \"H:mm:ss น. z\",\n    medium: \"H:mm:ss น.\",\n    short: \"H:mm น.\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'เวลา' {{time}}\",\n    long: \"{{date}} 'เวลา' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"medium\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"eeee'ที่แล้วเวลา' p\",\n    yesterday: \"'เมื่อวานนี้เวลา' p\",\n    today: \"'วันนี้เวลา' p\",\n    tomorrow: \"'พรุ่งนี้เวลา' p\",\n    nextWeek: \"eeee 'เวลา' p\",\n    other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL3RoL19saWIvZm9ybWF0UmVsYXRpdmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE1BQU1BLHVCQUF1QjtJQUMzQkMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLE9BQU87SUFDUEMsVUFBVTtJQUNWQyxVQUFVO0lBQ1ZDLE9BQU87QUFDVDtBQUVPLE1BQU1DLGlCQUFpQixDQUFDQyxPQUFPQyxPQUFPQyxXQUFXQyxXQUN0RFgsb0JBQW9CLENBQUNRLE1BQU0sQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS90aC9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGZvcm1hdFJlbGF0aXZlTG9jYWxlID0ge1xuICBsYXN0V2VlazogXCJlZWVlJ+C4l+C4teC5iOC5geC4peC5ieC4p+C5gOC4p+C4peC4sicgcFwiLFxuICB5ZXN0ZXJkYXk6IFwiJ+C5gOC4oeC4t+C5iOC4reC4p+C4suC4meC4meC4teC5ieC5gOC4p+C4peC4sicgcFwiLFxuICB0b2RheTogXCIn4Lin4Lix4LiZ4LiZ4Li14LmJ4LmA4Lin4Lil4LiyJyBwXCIsXG4gIHRvbW9ycm93OiBcIifguJ7guKPguLjguYjguIfguJnguLXguYnguYDguKfguKXguLInIHBcIixcbiAgbmV4dFdlZWs6IFwiZWVlZSAn4LmA4Lin4Lil4LiyJyBwXCIsXG4gIG90aGVyOiBcIlBcIixcbn07XG5cbmV4cG9ydCBjb25zdCBmb3JtYXRSZWxhdGl2ZSA9ICh0b2tlbiwgX2RhdGUsIF9iYXNlRGF0ZSwgX29wdGlvbnMpID0+XG4gIGZvcm1hdFJlbGF0aXZlTG9jYWxlW3Rva2VuXTtcbiJdLCJuYW1lcyI6WyJmb3JtYXRSZWxhdGl2ZUxvY2FsZSIsImxhc3RXZWVrIiwieWVzdGVyZGF5IiwidG9kYXkiLCJ0b21vcnJvdyIsIm5leHRXZWVrIiwib3RoZXIiLCJmb3JtYXRSZWxhdGl2ZSIsInRva2VuIiwiX2RhdGUiLCJfYmFzZURhdGUiLCJfb3B0aW9ucyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"B\",\n        \"คศ\"\n    ],\n    abbreviated: [\n        \"BC\",\n        \"ค.ศ.\"\n    ],\n    wide: [\n        \"ปีก่อนคริสตกาล\",\n        \"คริสต์ศักราช\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"Q1\",\n        \"Q2\",\n        \"Q3\",\n        \"Q4\"\n    ],\n    wide: [\n        \"ไตรมาสแรก\",\n        \"ไตรมาสที่สอง\",\n        \"ไตรมาสที่สาม\",\n        \"ไตรมาสที่สี่\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"อา.\",\n        \"จ.\",\n        \"อ.\",\n        \"พ.\",\n        \"พฤ.\",\n        \"ศ.\",\n        \"ส.\"\n    ],\n    short: [\n        \"อา.\",\n        \"จ.\",\n        \"อ.\",\n        \"พ.\",\n        \"พฤ.\",\n        \"ศ.\",\n        \"ส.\"\n    ],\n    abbreviated: [\n        \"อา.\",\n        \"จ.\",\n        \"อ.\",\n        \"พ.\",\n        \"พฤ.\",\n        \"ศ.\",\n        \"ส.\"\n    ],\n    wide: [\n        \"อาทิตย์\",\n        \"จันทร์\",\n        \"อังคาร\",\n        \"พุธ\",\n        \"พฤหัสบดี\",\n        \"ศุกร์\",\n        \"เสาร์\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"ม.ค.\",\n        \"ก.พ.\",\n        \"มี.ค.\",\n        \"เม.ย.\",\n        \"พ.ค.\",\n        \"มิ.ย.\",\n        \"ก.ค.\",\n        \"ส.ค.\",\n        \"ก.ย.\",\n        \"ต.ค.\",\n        \"พ.ย.\",\n        \"ธ.ค.\"\n    ],\n    abbreviated: [\n        \"ม.ค.\",\n        \"ก.พ.\",\n        \"มี.ค.\",\n        \"เม.ย.\",\n        \"พ.ค.\",\n        \"มิ.ย.\",\n        \"ก.ค.\",\n        \"ส.ค.\",\n        \"ก.ย.\",\n        \"ต.ค.\",\n        \"พ.ย.\",\n        \"ธ.ค.\"\n    ],\n    wide: [\n        \"มกราคม\",\n        \"กุมภาพันธ์\",\n        \"มีนาคม\",\n        \"เมษายน\",\n        \"พฤษภาคม\",\n        \"มิถุนายน\",\n        \"กรกฎาคม\",\n        \"สิงหาคม\",\n        \"กันยายน\",\n        \"ตุลาคม\",\n        \"พฤศจิกายน\",\n        \"ธันวาคม\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"เช้า\",\n        afternoon: \"บ่าย\",\n        evening: \"เย็น\",\n        night: \"กลางคืน\"\n    },\n    abbreviated: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"เช้า\",\n        afternoon: \"บ่าย\",\n        evening: \"เย็น\",\n        night: \"กลางคืน\"\n    },\n    wide: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"เช้า\",\n        afternoon: \"บ่าย\",\n        evening: \"เย็น\",\n        night: \"กลางคืน\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"ตอนเช้า\",\n        afternoon: \"ตอนกลางวัน\",\n        evening: \"ตอนเย็น\",\n        night: \"ตอนกลางคืน\"\n    },\n    abbreviated: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"ตอนเช้า\",\n        afternoon: \"ตอนกลางวัน\",\n        evening: \"ตอนเย็น\",\n        night: \"ตอนกลางคืน\"\n    },\n    wide: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"ตอนเช้า\",\n        afternoon: \"ตอนกลางวัน\",\n        evening: \"ตอนเย็น\",\n        night: \"ตอนกลางคืน\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    return String(dirtyNumber);\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^\\d+/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^([bB]|[aA]|คศ)/i,\n    abbreviated: /^([bB]\\.?\\s?[cC]\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?|ค\\.?ศ\\.?)/i,\n    wide: /^(ก่อนคริสตกาล|คริสต์ศักราช|คริสตกาล)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^[bB]/i,\n        /^(^[aA]|ค\\.?ศ\\.?|คริสตกาล|คริสต์ศักราช|)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^q[1234]/i,\n    wide: /^ไตรมาส(ที่)? ?[1234]/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /(1|แรก|หนึ่ง)/i,\n        /(2|สอง)/i,\n        /(3|สาม)/i,\n        /(4|สี่)/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?)/i,\n    abbreviated: /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?')/i,\n    wide: /^(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)/i\n};\nconst parseMonthPatterns = {\n    wide: [\n        /^มก/i,\n        /^กุม/i,\n        /^มี/i,\n        /^เม/i,\n        /^พฤษ/i,\n        /^มิ/i,\n        /^กรก/i,\n        /^ส/i,\n        /^กัน/i,\n        /^ต/i,\n        /^พฤศ/i,\n        /^ธ/i\n    ],\n    any: [\n        /^ม\\.?ค\\.?/i,\n        /^ก\\.?พ\\.?/i,\n        /^มี\\.?ค\\.?/i,\n        /^เม\\.?ย\\.?/i,\n        /^พ\\.?ค\\.?/i,\n        /^มิ\\.?ย\\.?/i,\n        /^ก\\.?ค\\.?/i,\n        /^ส\\.?ค\\.?/i,\n        /^ก\\.?ย\\.?/i,\n        /^ต\\.?ค\\.?/i,\n        /^พ\\.?ย\\.?/i,\n        /^ธ\\.?ค\\.?/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n    short: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n    abbreviated: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n    wide: /^(อาทิตย์|จันทร์|อังคาร|พุธ|พฤหัสบดี|ศุกร์|เสาร์)/i\n};\nconst parseDayPatterns = {\n    wide: [\n        /^อา/i,\n        /^จั/i,\n        /^อั/i,\n        /^พุธ/i,\n        /^พฤ/i,\n        /^ศ/i,\n        /^เส/i\n    ],\n    any: [\n        /^อา/i,\n        /^จ/i,\n        /^อ/i,\n        /^พ(?!ฤ)/i,\n        /^พฤ/i,\n        /^ศ/i,\n        /^ส/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    any: /^(ก่อนเที่ยง|หลังเที่ยง|เที่ยงคืน|เที่ยง|(ตอน.*?)?.*(เที่ยง|เช้า|บ่าย|เย็น|กลางคืน))/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^ก่อนเที่ยง/i,\n        pm: /^หลังเที่ยง/i,\n        midnight: /^เที่ยงคืน/i,\n        noon: /^เที่ยง/i,\n        morning: /เช้า/i,\n        afternoon: /บ่าย/i,\n        evening: /เย็น/i,\n        night: /กลางคืน/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/th/_lib/match.js\n"));

/***/ })

}]);