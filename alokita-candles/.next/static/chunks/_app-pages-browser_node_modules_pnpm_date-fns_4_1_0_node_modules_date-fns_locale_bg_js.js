"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_bg_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bg: () => (/* binding */ bg),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _bg_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bg/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatDistance.js\");\n/* harmony import */ var _bg_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bg/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatLong.js\");\n/* harmony import */ var _bg_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bg/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatRelative.js\");\n/* harmony import */ var _bg_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bg/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/localize.js\");\n/* harmony import */ var _bg_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./bg/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Bulgarian locale.\n * @language Bulgarian\n * @iso-639-2 bul\n * <AUTHOR> Stoynov [@arvigeus](https://github.com/arvigeus)\n * <AUTHOR> Ovedenski [@fintara](https://github.com/fintara)\n */ const bg = {\n    code: \"bg\",\n    formatDistance: _bg_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _bg_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _bg_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _bg_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _bg_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bg);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"по-малко от секунда\",\n        other: \"по-малко от {{count}} секунди\"\n    },\n    xSeconds: {\n        one: \"1 секунда\",\n        other: \"{{count}} секунди\"\n    },\n    halfAMinute: \"половин минута\",\n    lessThanXMinutes: {\n        one: \"по-малко от минута\",\n        other: \"по-малко от {{count}} минути\"\n    },\n    xMinutes: {\n        one: \"1 минута\",\n        other: \"{{count}} минути\"\n    },\n    aboutXHours: {\n        one: \"около час\",\n        other: \"около {{count}} часа\"\n    },\n    xHours: {\n        one: \"1 час\",\n        other: \"{{count}} часа\"\n    },\n    xDays: {\n        one: \"1 ден\",\n        other: \"{{count}} дни\"\n    },\n    aboutXWeeks: {\n        one: \"около седмица\",\n        other: \"около {{count}} седмици\"\n    },\n    xWeeks: {\n        one: \"1 седмица\",\n        other: \"{{count}} седмици\"\n    },\n    aboutXMonths: {\n        one: \"около месец\",\n        other: \"около {{count}} месеца\"\n    },\n    xMonths: {\n        one: \"1 месец\",\n        other: \"{{count}} месеца\"\n    },\n    aboutXYears: {\n        one: \"около година\",\n        other: \"около {{count}} години\"\n    },\n    xYears: {\n        one: \"1 година\",\n        other: \"{{count}} години\"\n    },\n    overXYears: {\n        one: \"над година\",\n        other: \"над {{count}} години\"\n    },\n    almostXYears: {\n        one: \"почти година\",\n        other: \"почти {{count}} години\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"след \" + result;\n        } else {\n            return \"преди \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, dd MMMM yyyy\",\n    long: \"dd MMMM yyyy\",\n    medium: \"dd MMM yyyy\",\n    short: \"dd.MM.yyyy\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    any: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n/* harmony import */ var _toDate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../toDate.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/toDate.js\");\n\n\n// Adapted from the `ru` translation\nconst weekdays = [\n    \"неделя\",\n    \"понеделник\",\n    \"вторник\",\n    \"сряда\",\n    \"четвъртък\",\n    \"петък\",\n    \"събота\"\n];\nfunction lastWeek(day) {\n    const weekday = weekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 6:\n            return \"'миналата \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n            return \"'миналия \" + weekday + \" в' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = weekdays[day];\n    if (day === 2 /* Tue */ ) {\n        return \"'във \" + weekday + \" в' p\";\n    } else {\n        return \"'в \" + weekday + \" в' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = weekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 6:\n            return \"'следващата \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n            return \"'следващия \" + weekday + \" в' p\";\n    }\n}\nconst lastWeekFormatToken = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return lastWeek(day);\n    }\n};\nconst nextWeekFormatToken = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return nextWeek(day);\n    }\n};\nconst formatRelativeLocale = {\n    lastWeek: lastWeekFormatToken,\n    yesterday: \"'вчера в' p\",\n    today: \"'днес в' p\",\n    tomorrow: \"'утре в' p\",\n    nextWeek: nextWeekFormatToken,\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"пр.н.е.\",\n        \"н.е.\"\n    ],\n    abbreviated: [\n        \"преди н. е.\",\n        \"н. е.\"\n    ],\n    wide: [\n        \"преди новата ера\",\n        \"новата ера\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-во тримес.\",\n        \"2-ро тримес.\",\n        \"3-то тримес.\",\n        \"4-то тримес.\"\n    ],\n    wide: [\n        \"1-во тримесечие\",\n        \"2-ро тримесечие\",\n        \"3-то тримесечие\",\n        \"4-то тримесечие\"\n    ]\n};\nconst monthValues = {\n    abbreviated: [\n        \"яну\",\n        \"фев\",\n        \"мар\",\n        \"апр\",\n        \"май\",\n        \"юни\",\n        \"юли\",\n        \"авг\",\n        \"сеп\",\n        \"окт\",\n        \"ное\",\n        \"дек\"\n    ],\n    wide: [\n        \"януари\",\n        \"февруари\",\n        \"март\",\n        \"април\",\n        \"май\",\n        \"юни\",\n        \"юли\",\n        \"август\",\n        \"септември\",\n        \"октомври\",\n        \"ноември\",\n        \"декември\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"Н\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"нд\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"нед\",\n        \"пон\",\n        \"вто\",\n        \"сря\",\n        \"чет\",\n        \"пет\",\n        \"съб\"\n    ],\n    wide: [\n        \"неделя\",\n        \"понеделник\",\n        \"вторник\",\n        \"сряда\",\n        \"четвъртък\",\n        \"петък\",\n        \"събота\"\n    ]\n};\nconst dayPeriodValues = {\n    wide: {\n        am: \"преди обяд\",\n        pm: \"след обяд\",\n        midnight: \"в полунощ\",\n        noon: \"на обяд\",\n        morning: \"сутринта\",\n        afternoon: \"следобед\",\n        evening: \"вечерта\",\n        night: \"през нощта\"\n    }\n};\nfunction isFeminine(unit) {\n    return unit === \"year\" || unit === \"week\" || unit === \"minute\" || unit === \"second\";\n}\nfunction isNeuter(unit) {\n    return unit === \"quarter\";\n}\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n    const suffix = isNeuter(unit) ? neuter : isFeminine(unit) ? feminine : masculine;\n    return number + \"-\" + suffix;\n}\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const number = Number(dirtyNumber);\n    const unit = options === null || options === void 0 ? void 0 : options.unit;\n    if (number === 0) {\n        return numberWithSuffix(0, unit, \"ев\", \"ева\", \"ево\");\n    } else if (number % 1000 === 0) {\n        return numberWithSuffix(number, unit, \"ен\", \"на\", \"но\");\n    } else if (number % 100 === 0) {\n        return numberWithSuffix(number, unit, \"тен\", \"тна\", \"тно\");\n    }\n    const rem100 = number % 100;\n    if (rem100 > 20 || rem100 < 10) {\n        switch(rem100 % 10){\n            case 1:\n                return numberWithSuffix(number, unit, \"ви\", \"ва\", \"во\");\n            case 2:\n                return numberWithSuffix(number, unit, \"ри\", \"ра\", \"ро\");\n            case 7:\n            case 8:\n                return numberWithSuffix(number, unit, \"ми\", \"ма\", \"мо\");\n        }\n    }\n    return numberWithSuffix(number, unit, \"ти\", \"та\", \"то\");\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((пр)?н\\.?\\s?е\\.?)/i,\n    abbreviated: /^((пр)?н\\.?\\s?е\\.?)/i,\n    wide: /^(преди новата ера|новата ера|нова ера)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^п/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[врт]?o?)? тримес.?/i,\n    wide: /^[1234](-?[врт]?о?)? тримесечие/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[нпвсч]/i,\n    short: /^(нд|пн|вт|ср|чт|пт|сб)/i,\n    abbreviated: /^(нед|пон|вто|сря|чет|пет|съб)/i,\n    wide: /^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^н/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^н[ед]/i,\n        /^п[он]/i,\n        /^вт/i,\n        /^ср/i,\n        /^ч[ет]/i,\n        /^п[ет]/i,\n        /^с[ъб]/i\n    ]\n};\nconst matchMonthPatterns = {\n    abbreviated: /^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,\n    wide: /^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i\n};\nconst parseMonthPatterns = {\n    any: [\n        /^я/i,\n        /^ф/i,\n        /^мар/i,\n        /^ап/i,\n        /^май/i,\n        /^юн/i,\n        /^юл/i,\n        /^ав/i,\n        /^се/i,\n        /^окт/i,\n        /^но/i,\n        /^де/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    any: /^(преди о|след о|в по|на о|през|веч|сут|следо)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^преди о/i,\n        pm: /^след о/i,\n        midnight: /^в пол/i,\n        noon: /^на об/i,\n        morning: /^сут/i,\n        afternoon: /^следо/i,\n        evening: /^веч/i,\n        night: /^през н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/match.js\n"));

/***/ })

}]);