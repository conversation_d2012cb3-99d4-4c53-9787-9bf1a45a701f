"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_sk_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   sk: () => (/* binding */ sk)\n/* harmony export */ });\n/* harmony import */ var _sk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sk/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatDistance.js\");\n/* harmony import */ var _sk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sk/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatLong.js\");\n/* harmony import */ var _sk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sk/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatRelative.js\");\n/* harmony import */ var _sk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sk/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/localize.js\");\n/* harmony import */ var _sk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./sk/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Slovak locale.\n * @language Slovak\n * @iso-639-2 slk\n * <AUTHOR> Suscak [@mareksuscak](https://github.com/mareksuscak)\n */ const sk = {\n    code: \"sk\",\n    formatDistance: _sk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _sk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _sk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _sk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _sk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sk);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL3NrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNkQ7QUFDUjtBQUNRO0FBQ1o7QUFDTjtBQUUzQzs7Ozs7O0NBTUMsR0FDTSxNQUFNSyxLQUFLO0lBQ2hCQyxNQUFNO0lBQ05OLGdCQUFnQkEscUVBQWNBO0lBQzlCQyxZQUFZQSw2REFBVUE7SUFDdEJDLGdCQUFnQkEscUVBQWNBO0lBQzlCQyxVQUFVQSx5REFBUUE7SUFDbEJDLE9BQU9BLG1EQUFLQTtJQUNaRyxTQUFTO1FBQ1BDLGNBQWMsRUFBRSxVQUFVO1FBQzFCQyx1QkFBdUI7SUFDekI7QUFDRixFQUFFO0FBRUYsb0NBQW9DO0FBQ3BDLGlFQUFlSixFQUFFQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvaGFyc2hsdWhhci9sYWxpdCBkb24vYWxva2l0YS1jYW5kbGVzL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL3NrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZvcm1hdERpc3RhbmNlIH0gZnJvbSBcIi4vc2svX2xpYi9mb3JtYXREaXN0YW5jZS5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0TG9uZyB9IGZyb20gXCIuL3NrL19saWIvZm9ybWF0TG9uZy5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0UmVsYXRpdmUgfSBmcm9tIFwiLi9zay9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzXCI7XG5pbXBvcnQgeyBsb2NhbGl6ZSB9IGZyb20gXCIuL3NrL19saWIvbG9jYWxpemUuanNcIjtcbmltcG9ydCB7IG1hdGNoIH0gZnJvbSBcIi4vc2svX2xpYi9tYXRjaC5qc1wiO1xuXG4vKipcbiAqIEBjYXRlZ29yeSBMb2NhbGVzXG4gKiBAc3VtbWFyeSBTbG92YWsgbG9jYWxlLlxuICogQGxhbmd1YWdlIFNsb3Zha1xuICogQGlzby02MzktMiBzbGtcbiAqIEBhdXRob3IgTWFyZWsgU3VzY2FrIFtAbWFyZWtzdXNjYWtdKGh0dHBzOi8vZ2l0aHViLmNvbS9tYXJla3N1c2NhaylcbiAqL1xuZXhwb3J0IGNvbnN0IHNrID0ge1xuICBjb2RlOiBcInNrXCIsXG4gIGZvcm1hdERpc3RhbmNlOiBmb3JtYXREaXN0YW5jZSxcbiAgZm9ybWF0TG9uZzogZm9ybWF0TG9uZyxcbiAgZm9ybWF0UmVsYXRpdmU6IGZvcm1hdFJlbGF0aXZlLFxuICBsb2NhbGl6ZTogbG9jYWxpemUsXG4gIG1hdGNoOiBtYXRjaCxcbiAgb3B0aW9uczoge1xuICAgIHdlZWtTdGFydHNPbjogMSAvKiBNb25kYXkgKi8sXG4gICAgZmlyc3RXZWVrQ29udGFpbnNEYXRlOiA0LFxuICB9LFxufTtcblxuLy8gRmFsbGJhY2sgZm9yIG1vZHVsYXJpemVkIGltcG9ydHM6XG5leHBvcnQgZGVmYXVsdCBzaztcbiJdLCJuYW1lcyI6WyJmb3JtYXREaXN0YW5jZSIsImZvcm1hdExvbmciLCJmb3JtYXRSZWxhdGl2ZSIsImxvY2FsaXplIiwibWF0Y2giLCJzayIsImNvZGUiLCJvcHRpb25zIiwid2Vla1N0YXJ0c09uIiwiZmlyc3RXZWVrQ29udGFpbnNEYXRlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declensionGroup(scheme, count) {\n    if (count === 1 && scheme.one) {\n        return scheme.one;\n    }\n    if (count >= 2 && count <= 4 && scheme.twoFour) {\n        return scheme.twoFour;\n    }\n    // if count === null || count === 0 || count >= 5\n    return scheme.other;\n}\nfunction declension(scheme, count, time) {\n    const group = declensionGroup(scheme, count);\n    const finalText = group[time];\n    return finalText.replace(\"{{count}}\", String(count));\n}\nfunction extractPreposition(token) {\n    const result = [\n        \"lessThan\",\n        \"about\",\n        \"over\",\n        \"almost\"\n    ].filter(function(preposition) {\n        return !!token.match(new RegExp(\"^\" + preposition));\n    });\n    return result[0];\n}\nfunction prefixPreposition(preposition) {\n    let translation = \"\";\n    if (preposition === \"almost\") {\n        translation = \"takmer\";\n    }\n    if (preposition === \"about\") {\n        translation = \"približne\";\n    }\n    return translation.length > 0 ? translation + \" \" : \"\";\n}\nfunction suffixPreposition(preposition) {\n    let translation = \"\";\n    if (preposition === \"lessThan\") {\n        translation = \"menej než\";\n    }\n    if (preposition === \"over\") {\n        translation = \"viac než\";\n    }\n    return translation.length > 0 ? translation + \" \" : \"\";\n}\nfunction lowercaseFirstLetter(string) {\n    return string.charAt(0).toLowerCase() + string.slice(1);\n}\nconst formatDistanceLocale = {\n    xSeconds: {\n        one: {\n            present: \"sekunda\",\n            past: \"sekundou\",\n            future: \"sekundu\"\n        },\n        twoFour: {\n            present: \"{{count}} sekundy\",\n            past: \"{{count}} sekundami\",\n            future: \"{{count}} sekundy\"\n        },\n        other: {\n            present: \"{{count}} sekúnd\",\n            past: \"{{count}} sekundami\",\n            future: \"{{count}} sekúnd\"\n        }\n    },\n    halfAMinute: {\n        other: {\n            present: \"pol minúty\",\n            past: \"pol minútou\",\n            future: \"pol minúty\"\n        }\n    },\n    xMinutes: {\n        one: {\n            present: \"minúta\",\n            past: \"minútou\",\n            future: \"minútu\"\n        },\n        twoFour: {\n            present: \"{{count}} minúty\",\n            past: \"{{count}} minútami\",\n            future: \"{{count}} minúty\"\n        },\n        other: {\n            present: \"{{count}} minút\",\n            past: \"{{count}} minútami\",\n            future: \"{{count}} minút\"\n        }\n    },\n    xHours: {\n        one: {\n            present: \"hodina\",\n            past: \"hodinou\",\n            future: \"hodinu\"\n        },\n        twoFour: {\n            present: \"{{count}} hodiny\",\n            past: \"{{count}} hodinami\",\n            future: \"{{count}} hodiny\"\n        },\n        other: {\n            present: \"{{count}} hodín\",\n            past: \"{{count}} hodinami\",\n            future: \"{{count}} hodín\"\n        }\n    },\n    xDays: {\n        one: {\n            present: \"deň\",\n            past: \"dňom\",\n            future: \"deň\"\n        },\n        twoFour: {\n            present: \"{{count}} dni\",\n            past: \"{{count}} dňami\",\n            future: \"{{count}} dni\"\n        },\n        other: {\n            present: \"{{count}} dní\",\n            past: \"{{count}} dňami\",\n            future: \"{{count}} dní\"\n        }\n    },\n    xWeeks: {\n        one: {\n            present: \"týždeň\",\n            past: \"týždňom\",\n            future: \"týždeň\"\n        },\n        twoFour: {\n            present: \"{{count}} týždne\",\n            past: \"{{count}} týždňami\",\n            future: \"{{count}} týždne\"\n        },\n        other: {\n            present: \"{{count}} týždňov\",\n            past: \"{{count}} týždňami\",\n            future: \"{{count}} týždňov\"\n        }\n    },\n    xMonths: {\n        one: {\n            present: \"mesiac\",\n            past: \"mesiacom\",\n            future: \"mesiac\"\n        },\n        twoFour: {\n            present: \"{{count}} mesiace\",\n            past: \"{{count}} mesiacmi\",\n            future: \"{{count}} mesiace\"\n        },\n        other: {\n            present: \"{{count}} mesiacov\",\n            past: \"{{count}} mesiacmi\",\n            future: \"{{count}} mesiacov\"\n        }\n    },\n    xYears: {\n        one: {\n            present: \"rok\",\n            past: \"rokom\",\n            future: \"rok\"\n        },\n        twoFour: {\n            present: \"{{count}} roky\",\n            past: \"{{count}} rokmi\",\n            future: \"{{count}} roky\"\n        },\n        other: {\n            present: \"{{count}} rokov\",\n            past: \"{{count}} rokmi\",\n            future: \"{{count}} rokov\"\n        }\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    const preposition = extractPreposition(token) || \"\";\n    const key = lowercaseFirstLetter(token.substring(preposition.length));\n    const scheme = formatDistanceLocale[key];\n    if (!(options === null || options === void 0 ? void 0 : options.addSuffix)) {\n        return prefixPreposition(preposition) + suffixPreposition(preposition) + declension(scheme, count, \"present\");\n    }\n    if (options.comparison && options.comparison > 0) {\n        return prefixPreposition(preposition) + \"o \" + suffixPreposition(preposition) + declension(scheme, count, \"future\");\n    } else {\n        return prefixPreposition(preposition) + \"pred \" + suffixPreposition(preposition) + declension(scheme, count, \"past\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1986\nconst dateFormats = {\n    full: \"EEEE d. MMMM y\",\n    long: \"d. MMMM y\",\n    medium: \"d. M. y\",\n    short: \"d. M. y\"\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#2149\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1994\nconst dateTimeFormats = {\n    full: \"{{date}}, {{time}}\",\n    long: \"{{date}}, {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1308\nconst accusativeWeekdays = [\n    \"nedeľu\",\n    \"pondelok\",\n    \"utorok\",\n    \"stredu\",\n    \"štvrtok\",\n    \"piatok\",\n    \"sobotu\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        /* Sun */ case 3:\n        /* Wed */ case 6 /* Sat */ :\n            return \"'minulú \" + weekday + \" o' p\";\n        default:\n            return \"'minulý' eeee 'o' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    if (day === 4 /* Thu */ ) {\n        return \"'vo' eeee 'o' p\";\n    } else {\n        return \"'v \" + weekday + \" o' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        /* Sun */ case 4:\n        /* Wed */ case 6 /* Sat */ :\n            return \"'budúcu \" + weekday + \" o' p\";\n        default:\n            return \"'budúci' eeee 'o' p\";\n    }\n}\nconst formatRelativeLocale = {\n    lastWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return lastWeek(day);\n        }\n    },\n    yesterday: \"'včera o' p\",\n    today: \"'dnes o' p\",\n    tomorrow: \"'zajtra o' p\",\n    nextWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return nextWeek(day);\n        }\n    },\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1772\nconst eraValues = {\n    narrow: [\n        \"pred Kr.\",\n        \"po Kr.\"\n    ],\n    abbreviated: [\n        \"pred Kr.\",\n        \"po Kr.\"\n    ],\n    wide: [\n        \"pred Kristom\",\n        \"po Kristovi\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1780\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"Q1\",\n        \"Q2\",\n        \"Q3\",\n        \"Q4\"\n    ],\n    wide: [\n        \"1. štvrťrok\",\n        \"2. štvrťrok\",\n        \"3. štvrťrok\",\n        \"4. štvrťrok\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1804\nconst monthValues = {\n    narrow: [\n        \"j\",\n        \"f\",\n        \"m\",\n        \"a\",\n        \"m\",\n        \"j\",\n        \"j\",\n        \"a\",\n        \"s\",\n        \"o\",\n        \"n\",\n        \"d\"\n    ],\n    abbreviated: [\n        \"jan\",\n        \"feb\",\n        \"mar\",\n        \"apr\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"aug\",\n        \"sep\",\n        \"okt\",\n        \"nov\",\n        \"dec\"\n    ],\n    wide: [\n        \"január\",\n        \"február\",\n        \"marec\",\n        \"apríl\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"august\",\n        \"september\",\n        \"október\",\n        \"november\",\n        \"december\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"j\",\n        \"f\",\n        \"m\",\n        \"a\",\n        \"m\",\n        \"j\",\n        \"j\",\n        \"a\",\n        \"s\",\n        \"o\",\n        \"n\",\n        \"d\"\n    ],\n    abbreviated: [\n        \"jan\",\n        \"feb\",\n        \"mar\",\n        \"apr\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"aug\",\n        \"sep\",\n        \"okt\",\n        \"nov\",\n        \"dec\"\n    ],\n    wide: [\n        \"januára\",\n        \"februára\",\n        \"marca\",\n        \"apríla\",\n        \"mája\",\n        \"júna\",\n        \"júla\",\n        \"augusta\",\n        \"septembra\",\n        \"októbra\",\n        \"novembra\",\n        \"decembra\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1876\nconst dayValues = {\n    narrow: [\n        \"n\",\n        \"p\",\n        \"u\",\n        \"s\",\n        \"š\",\n        \"p\",\n        \"s\"\n    ],\n    short: [\n        \"ne\",\n        \"po\",\n        \"ut\",\n        \"st\",\n        \"št\",\n        \"pi\",\n        \"so\"\n    ],\n    abbreviated: [\n        \"ne\",\n        \"po\",\n        \"ut\",\n        \"st\",\n        \"št\",\n        \"pi\",\n        \"so\"\n    ],\n    wide: [\n        \"nedeľa\",\n        \"pondelok\",\n        \"utorok\",\n        \"streda\",\n        \"štvrtok\",\n        \"piatok\",\n        \"sobota\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1932\nconst dayPeriodValues = {\n    narrow: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"poln.\",\n        noon: \"pol.\",\n        morning: \"ráno\",\n        afternoon: \"pop.\",\n        evening: \"več.\",\n        night: \"noc\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"poln.\",\n        noon: \"pol.\",\n        morning: \"ráno\",\n        afternoon: \"popol.\",\n        evening: \"večer\",\n        night: \"noc\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"polnoc\",\n        noon: \"poludnie\",\n        morning: \"ráno\",\n        afternoon: \"popoludnie\",\n        evening: \"večer\",\n        night: \"noc\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o poln.\",\n        noon: \"nap.\",\n        morning: \"ráno\",\n        afternoon: \"pop.\",\n        evening: \"več.\",\n        night: \"v n.\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o poln.\",\n        noon: \"napol.\",\n        morning: \"ráno\",\n        afternoon: \"popol.\",\n        evening: \"večer\",\n        night: \"v noci\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o polnoci\",\n        noon: \"napoludnie\",\n        morning: \"ráno\",\n        afternoon: \"popoludní\",\n        evening: \"večer\",\n        night: \"v noci\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)\\.?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(pred Kr\\.|pred n\\. l\\.|po Kr\\.|n\\. l\\.)/i,\n    abbreviated: /^(pred Kr\\.|pred n\\. l\\.|po Kr\\.|n\\. l\\.)/i,\n    wide: /^(pred Kristom|pred na[šs][íi]m letopo[čc]tom|po Kristovi|n[áa][šs]ho letopo[čc]tu)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^pr/i,\n        /^(po|n)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^q[1234]/i,\n    wide: /^[1234]\\. [šs]tvr[ťt]rok/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmasond]/i,\n    abbreviated: /^(jan|feb|mar|apr|m[áa]j|j[úu]n|j[úu]l|aug|sep|okt|nov|dec)/i,\n    wide: /^(janu[áa]ra?|febru[áa]ra?|(marec|marca)|apr[íi]la?|m[áa]ja?|j[úu]na?|j[úu]la?|augusta?|(september|septembra)|(okt[óo]ber|okt[óo]bra)|(november|novembra)|(december|decembra))/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^ja/i,\n        /^f/i,\n        /^mar/i,\n        /^ap/i,\n        /^m[áa]j/i,\n        /^j[úu]n/i,\n        /^j[úu]l/i,\n        /^au/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[npusšp]/i,\n    short: /^(ne|po|ut|st|št|pi|so)/i,\n    abbreviated: /^(ne|po|ut|st|št|pi|so)/i,\n    wide: /^(nede[ľl]a|pondelok|utorok|streda|[šs]tvrtok|piatok|sobota])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^n/i,\n        /^p/i,\n        /^u/i,\n        /^s/i,\n        /^š/i,\n        /^p/i,\n        /^s/i\n    ],\n    any: [\n        /^n/i,\n        /^po/i,\n        /^u/i,\n        /^st/i,\n        /^(št|stv)/i,\n        /^pi/i,\n        /^so/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(am|pm|(o )?poln\\.?|(nap\\.?|pol\\.?)|r[áa]no|pop\\.?|ve[čc]\\.?|(v n\\.?|noc))/i,\n    abbreviated: /^(am|pm|(o )?poln\\.?|(napol\\.?|pol\\.?)|r[áa]no|pop\\.?|ve[čc]er|(v )?noci?)/i,\n    any: /^(am|pm|(o )?polnoci?|(na)?poludnie|r[áa]no|popoludn(ie|í|i)|ve[čc]er|(v )?noci?)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^am/i,\n        pm: /^pm/i,\n        midnight: /poln/i,\n        noon: /^(nap|(na)?pol(\\.|u))/i,\n        morning: /^r[áa]no/i,\n        afternoon: /^pop/i,\n        evening: /^ve[čc]/i,\n        night: /^(noc|v n\\.)/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/match.js\n"));

/***/ })

}]);