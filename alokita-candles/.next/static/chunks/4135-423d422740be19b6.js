(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4135],{452:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(26520);t.default={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>(0,n._)`{passingSchemas: ${e.passing}}`},code(e){let{gen:t,schema:r,parentSchema:i,it:o}=e;if(!Array.isArray(r))throw Error("ajv implementation error");if(o.opts.discriminator&&i.discriminator)return;let s=t.let("valid",!1),l=t.let("passing",null),u=t.name("_valid");e.setParams({passing:l}),t.block(function(){r.forEach((r,i)=>{let c;(0,a.alwaysValidSchema)(o,r)?t.var(u,!0):c=e.subschema({keyword:"oneOf",schemaProp:i,compositeRule:!0},u),i>0&&t.if((0,n._)`${u} && ${s}`).assign(s,!1).assign(l,(0,n._)`[${l}, ${i}]`).else(),t.if(u,()=>{t.assign(s,!0),t.assign(l,i),c&&e.mergeEvaluated(c,n.Name)})})}),e.result(s,()=>e.reset(),()=>e.error(!0))}}},796:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(26520);t.default={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){let{gen:t,schema:r,it:a}=e;if((0,n.alwaysValidSchema)(a,r))return void e.fail();let i=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},i),e.failResult(i,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}}},1044:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,enUS:()=>c});let n={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};var a=r(37028);let i={date:(0,a.k)({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:(0,a.k)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,a.k)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};var s=r(26666);let l={ordinalNumber:(e,t)=>{let r=Number(e),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:(0,s.o)({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:(0,s.o)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,s.o)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:(0,s.o)({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:(0,s.o)({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};var u=r(28906);let c={code:"en-US",formatDistance:(e,t,r)=>{let a,i=n[e];if(a="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),null==r?void 0:r.addSuffix)if(r.comparison&&r.comparison>0)return"in "+a;else return a+" ago";return a},formatLong:i,formatRelative:(e,t,r,n)=>o[e],localize:l,match:{ordinalNumber:(0,r(52536).K)({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,u.A)({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:(0,u.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,u.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,u.A)({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,u.A)({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},d=c},1408:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(26520);function i(e,t){let r=e.schema[t];return void 0!==r&&!(0,a.alwaysValidSchema)(e,r)}t.default={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>(0,n.str)`must match "${e.ifClause}" schema`,params:({params:e})=>(0,n._)`{failingKeyword: ${e.ifClause}}`},code(e){let{gen:t,parentSchema:r,it:o}=e;void 0===r.then&&void 0===r.else&&(0,a.checkStrictMode)(o,'"if" without "then" and "else" is ignored');let s=i(o,"then"),l=i(o,"else");if(!s&&!l)return;let u=t.let("valid",!0),c=t.name("_valid");if(function(){let t=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},c);e.mergeEvaluated(t)}(),e.reset(),s&&l){let r=t.let("ifClause");e.setParams({ifClause:r}),t.if(c,d("then",r),d("else",r))}else s?t.if(c,d("then")):t.if((0,n.not)(c),d("else"));function d(r,a){return()=>{let i=e.subschema({keyword:r},c);t.assign(u,c),e.mergeValidEvaluated(i,u),a?t.assign(a,(0,n._)`${r}`):e.setParams({ifClause:r})}}e.pass(u,()=>e.error(!0))}}},2927:(e,t,r)=>{"use strict";r.d(t,{NT:()=>a});let n=({req:{user:e}})=>!!e,a={access:{create:n,delete:n,read:n,unlock:n,update:n},admin:{components:{},custom:{},enableRichTextLink:!0,enableRichTextRelationship:!0,pagination:{defaultLimit:10,limits:[5,10,25,50,100]},useAsTitle:"id"},auth:!1,custom:{},endpoints:[],fields:[],hooks:{afterChange:[],afterDelete:[],afterForgotPassword:[],afterLogin:[],afterLogout:[],afterMe:[],afterOperation:[],afterRead:[],afterRefresh:[],beforeChange:[],beforeDelete:[],beforeLogin:[],beforeOperation:[],beforeRead:[],beforeValidate:[],me:[],refresh:[]},indexes:[],timestamps:!0,upload:!1,versions:!1}},3537:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},useLinkStatus:function(){return b}});let n=r(58272),a=r(67252),i=n._(r(89688)),o=r(85864),s=r(58838),l=r(78377),u=r(81469),c=r(93894),d=r(95148);r(16799);let f=r(33073),p=r(39217),m=r(38669);function g(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}function y(e){let t,r,n,[o,y]=(0,i.useOptimistic)(f.IDLE_LINK_STATUS),b=(0,i.useRef)(null),{href:w,as:x,children:$,prefetch:k=null,passHref:S,replace:_,shallow:j,scroll:E,onClick:C,onMouseEnter:P,onTouchStart:N,legacyBehavior:A=!1,onNavigate:T,ref:O,unstable_dynamicOnHover:D,...M}=e;t=$,A&&("string"==typeof t||"number"==typeof t)&&(t=(0,a.jsx)("a",{children:t}));let R=i.default.useContext(s.AppRouterContext),L=!1!==k,I=null===k?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:F,as:B}=i.default.useMemo(()=>{let e=g(w);return{href:e,as:x?g(x):e}},[w,x]);A&&(r=i.default.Children.only(t));let z=A?r&&"object"==typeof r&&r.ref:O,V=i.default.useCallback(e=>(null!==R&&(b.current=(0,f.mountLinkInstance)(e,F,R,I,L,y)),()=>{b.current&&((0,f.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,f.unmountPrefetchableInstance)(e)}),[L,F,R,I,y]),U={ref:(0,u.useMergedRef)(V,z),onClick(e){A||"function"!=typeof C||C(e),A&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),R&&(e.defaultPrevented||function(e,t,r,n,a,o,s){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){a&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,m.dispatchNavigateAction)(r||t,a?"replace":"push",null==o||o,n.current)})}}(e,F,B,b,_,E,T))},onMouseEnter(e){A||"function"!=typeof P||P(e),A&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),R&&L&&(0,f.onNavigationIntent)(e.currentTarget,!0===D)},onTouchStart:function(e){A||"function"!=typeof N||N(e),A&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),R&&L&&(0,f.onNavigationIntent)(e.currentTarget,!0===D)}};return(0,c.isAbsoluteUrl)(B)?U.href=B:A&&!S&&("a"!==r.type||"href"in r.props)||(U.href=(0,d.addBasePath)(B)),n=A?i.default.cloneElement(r,U):(0,a.jsx)("a",{...M,...U,children:t}),(0,a.jsx)(v.Provider,{value:o,children:n})}r(30911);let v=(0,i.createContext)(f.IDLE_LINK_STATUS),b=()=>(0,i.useContext)(v);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4110:(e,t,r)=>{"use strict";r.d(t,{AutosaveCell:()=>c,m:()=>u});var n=r(47092),a=r(67252),i=r(83548),o=r(71464),s=r(56156),l=r(89688);let u=(e,t,r,n,o)=>(0,a.jsx)(l.Fragment,{children:(null==e?void 0:e.id)===t?(0,a.jsx)(i.Pill,{pillStyle:o,size:"small",children:r}):(0,a.jsx)(i.Pill,{size:"small",children:n})}),c=e=>{let t,r,l=(0,n.c)(16),{latestDraftVersion:c,latestPublishedVersion:d,rowData:f}=e;l[0]!==f?(t=void 0===f?{autosave:void 0,publishedLocale:void 0,version:void 0}:f,l[0]=f,l[1]=t):t=l[1];let p=t,{i18n:m,t:g}=(0,o.d)(),{config:y}=(0,s.b)(),{localization:v}=y,b=(null==p?void 0:p.publishedLocale)||void 0,w=null==p?void 0:p.version._status;if(l[2]!==(null==m?void 0:m.language)||l[3]!==c||l[4]!==d||l[5]!==v||l[6]!==b||l[7]!==p||l[8]!==w||l[9]!==g){let e,t=null,{currentLabel:n,latestVersion:o,pillStyle:s,previousLabel:f}={draft:{currentLabel:g("version:currentDraft"),latestVersion:c,pillStyle:void 0,previousLabel:g("version:draft")},published:{currentLabel:g("version:currentPublishedVersion"),latestVersion:d,pillStyle:"success",previousLabel:g("version:previouslyPublished")}}[w]||{};if(v&&(null==v?void 0:v.locales)&&b){var x;let e,r=Array.isArray(b)?b[0]:b;l[11]!==r?(e=e=>e.code===r,l[11]=r,l[12]=e):e=l[12];let n=v.locales.find(e),o=(null==n||null==(x=n.label)?void 0:x[null==m?void 0:m.language])||(null==n?void 0:n.label);o&&(t=(0,a.jsx)(i.Pill,{children:o}))}l[13]!==(null==p?void 0:p.autosave)||l[14]!==g?(e=(null==p?void 0:p.autosave)&&(0,a.jsx)(i.Pill,{children:g("version:autosave")}),l[13]=null==p?void 0:p.autosave,l[14]=g,l[15]=e):e=l[15],r=(0,a.jsxs)("div",{className:"".concat("autosave-cell","__items"),children:[e,w&&u(p,o,n,f,s),t]}),l[2]=null==m?void 0:m.language,l[3]=c,l[4]=d,l[5]=v,l[6]=b,l[7]=p,l[8]=w,l[9]=g,l[10]=r}else r=l[10];return r}},4256:(e,t,r)=>{"use strict";r.d(t,{VersionsViewClient:()=>u});var n=r(47092),a=r(67252),i=r(83548),o=r(71464),s=r(98034),l=r(89688);let u=e=>{let t,r,u=(0,n.c)(13),{baseClass:c,columns:d,paginationLimits:f}=e,{data:p,handlePageChange:m,handlePerPageChange:g}=(0,i.useListQuery)(),y=(0,s.useSearchParams)();u[0]!==y?(t=y.get("limit"),u[0]=y,u[1]=t):t=u[1];let v=t,{i18n:b}=(0,o.d)(),w=(null==p?void 0:p.totalDocs)||0,x=!p;return u[2]!==c||u[3]!==d||u[4]!==p||u[5]!==m||u[6]!==g||u[7]!==b||u[8]!==v||u[9]!==f||u[10]!==x||u[11]!==w?(r=(0,a.jsxs)(l.Fragment,{children:[(0,a.jsx)(i.LoadingOverlayToggle,{name:"versions",show:x}),0===w&&(0,a.jsx)("div",{className:"".concat(c,"__no-versions"),children:b.t("version:noFurtherVersionsFound")}),w>0&&(0,a.jsxs)(l.Fragment,{children:[(0,a.jsx)(i.Table,{columns:d,data:null==p?void 0:p.docs}),(0,a.jsxs)("div",{className:"".concat(c,"__page-controls"),children:[(0,a.jsx)(i.Pagination,{hasNextPage:p.hasNextPage,hasPrevPage:p.hasPrevPage,limit:p.limit,nextPage:p.nextPage,numberOfNeighbors:1,onChange:m,page:p.page,prevPage:p.prevPage,totalPages:p.totalPages}),(null==p?void 0:p.totalDocs)>0&&(0,a.jsxs)(l.Fragment,{children:[(0,a.jsxs)("div",{className:"".concat(c,"__page-info"),children:[p.page*p.limit-(p.limit-1),"-",p.totalPages>1&&p.totalPages!==p.page?p.limit*p.page:p.totalDocs," ",b.t("general:of")," ",p.totalDocs]}),(0,a.jsx)(i.PerPage,{handleChange:g,limit:v?Number(v):10,limits:f})]})]})]})]}),u[2]=c,u[3]=d,u[4]=p,u[5]=m,u[6]=g,u[7]=b,u[8]=v,u[9]=f,u[10]=x,u[11]=w,u[12]=r):r=u[12],r}},4424:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;let n=r(55150),a=r(25421),i=r(12208),o=r(59228),s=r(26520),l=r(62597);class u{constructor(e){var t;let r;this.refs={},this.dynamicAnchors={},"object"==typeof e.schema&&(r=e.schema),this.schema=e.schema,this.schemaId=e.schemaId,this.root=e.root||this,this.baseId=null!=(t=e.baseId)?t:(0,o.normalizeId)(null==r?void 0:r[e.schemaId||"$id"]),this.schemaPath=e.schemaPath,this.localRefs=e.localRefs,this.meta=e.meta,this.$async=null==r?void 0:r.$async,this.refs={}}}function c(e){let t,r,s=f.call(this,e);if(s)return s;let u=(0,o.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:c,lines:d}=this.opts.code,{ownProperties:p}=this.opts,m=new n.CodeGen(this.scope,{es5:c,lines:d,ownProperties:p});e.$async&&(t=m.scopeValue("Error",{ref:a.default,code:(0,n._)`require("ajv/dist/runtime/validation_error").default`}));let g=m.scopeName("validate");e.validateName=g;let y={gen:m,allErrors:this.opts.allErrors,data:i.default.data,parentData:i.default.parentData,parentDataProperty:i.default.parentDataProperty,dataNames:[i.default.data],dataPathArr:[n.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:m.scopeValue("schema",!0===this.opts.code.source?{ref:e.schema,code:(0,n.stringify)(e.schema)}:{ref:e.schema}),validateName:g,ValidationError:t,schema:e.schema,schemaEnv:e,rootId:u,baseId:e.baseId||u,schemaPath:n.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:(0,n._)`""`,opts:this.opts,self:this};try{this._compilations.add(e),(0,l.validateFunctionCode)(y),m.optimize(this.opts.code.optimize);let t=m.toString();r=`${m.scopeRefs(i.default.scope)}return ${t}`,this.opts.code.process&&(r=this.opts.code.process(r,e));let a=Function(`${i.default.self}`,`${i.default.scope}`,r)(this,this.scope.get());if(this.scope.value(g,{ref:a}),a.errors=null,a.schema=e.schema,a.schemaEnv=e,e.$async&&(a.$async=!0),!0===this.opts.code.source&&(a.source={validateName:g,validateCode:t,scopeValues:m._values}),this.opts.unevaluated){let{props:e,items:t}=y;a.evaluated={props:e instanceof n.Name?void 0:e,items:t instanceof n.Name?void 0:t,dynamicProps:e instanceof n.Name,dynamicItems:t instanceof n.Name},a.source&&(a.source.evaluated=(0,n.stringify)(a.evaluated))}return e.validate=a,e}catch(t){throw delete e.validate,delete e.validateName,r&&this.logger.error("Error compiling schema, function code:",r),t}finally{this._compilations.delete(e)}}function d(e){return(0,o.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:c.call(this,e)}function f(e){for(let n of this._compilations){var t,r;if(t=n,r=e,t.schema===r.schema&&t.root===r.root&&t.baseId===r.baseId)return n}}function p(e,t){let r;for(;"string"==typeof(r=this.refs[t]);)t=r;return r||this.schemas[t]||m.call(this,e,t)}function m(e,t){let r=this.opts.uriResolver.parse(t),n=(0,o._getFullPath)(this.opts.uriResolver,r),a=(0,o.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===a)return y.call(this,r,e);let i=(0,o.normalizeId)(n),s=this.refs[i]||this.schemas[i];if("string"==typeof s){let t=m.call(this,e,s);if("object"!=typeof(null==t?void 0:t.schema))return;return y.call(this,r,t)}if("object"==typeof(null==s?void 0:s.schema)){if(s.validate||c.call(this,s),i===(0,o.normalizeId)(t)){let{schema:t}=s,{schemaId:r}=this.opts,n=t[r];return n&&(a=(0,o.resolveUrl)(this.opts.uriResolver,a,n)),new u({schema:t,schemaId:r,root:e,baseId:a})}return y.call(this,r,s)}}t.SchemaEnv=u,t.compileSchema=c,t.resolveRef=function(e,t,r){var n;r=(0,o.resolveUrl)(this.opts.uriResolver,t,r);let a=e.refs[r];if(a)return a;let i=p.call(this,e,r);if(void 0===i){let a=null==(n=e.localRefs)?void 0:n[r],{schemaId:o}=this.opts;a&&(i=new u({schema:a,schemaId:o,root:e,baseId:t}))}if(void 0!==i)return e.refs[r]=d.call(this,i)},t.getCompilingSchema=f,t.resolveSchema=m;let g=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function y(e,{baseId:t,schema:r,root:n}){var a;let i;if((null==(a=e.fragment)?void 0:a[0])!=="/")return;for(let n of e.fragment.slice(1).split("/")){if("boolean"==typeof r)return;let e=r[(0,s.unescapeFragment)(n)];if(void 0===e)return;let a="object"==typeof(r=e)&&r[this.opts.schemaId];!g.has(n)&&a&&(t=(0,o.resolveUrl)(this.opts.uriResolver,t,a))}if("boolean"!=typeof r&&r.$ref&&!(0,s.schemaHasRulesButRef)(r,this.RULES)){let e=(0,o.resolveUrl)(this.opts.uriResolver,t,r.$ref);i=m.call(this,n,e)}let{schemaId:l}=this.opts;if((i=i||new u({schema:r,schemaId:l,root:n,baseId:t})).schema!==i.root.schema)return i}},4624:(e,t,r)=>{"use strict";r.d(t,{$:()=>g});var n=r(47092),a=r(67252),i=r(71464),o=r(56156),s=r(83548),l=r(93653),u=r(89688);function c(e,t){return JSON.stringify(e)!==JSON.stringify(t)}var d=r(60122);function f({comparison:e,config:t,fields:r,locales:n,parentIsLocalized:a,version:i}){let o=0;return r.forEach(r=>{if("name"in r&&"id"===r.name)return;let s=r.type;switch(s){case"array":case"blocks":if(n&&(0,l.Cd)({field:r,parentIsLocalized:a}))n.forEach(s=>{let l=e?.[r.name]?.[s]??[],u=i?.[r.name]?.[s]??[];o+=p({comparisonRows:l,config:t,field:r,locales:n,parentIsLocalized:a||r.localized,versionRows:u})});else{let s=e?.[r.name]??[],l=i?.[r.name]??[];o+=p({comparisonRows:s,config:t,field:r,locales:n,parentIsLocalized:a||r.localized,versionRows:l})}break;case"checkbox":case"code":case"date":case"email":case"join":case"json":case"number":case"point":case"radio":case"relationship":case"richText":case"select":case"text":case"textarea":case"upload":n&&(0,l.Cd)({field:r,parentIsLocalized:a})?n.forEach(t=>{c(i?.[r.name]?.[t],e?.[r.name]?.[t])&&o++}):c(i?.[r.name],e?.[r.name])&&o++;break;case"collapsible":case"row":o+=f({comparison:e,config:t,fields:r.fields,locales:n,parentIsLocalized:a||r.localized,version:i});break;case"group":(0,l.jY)(r)?n&&(0,l.Cd)({field:r,parentIsLocalized:a})?n.forEach(s=>{o+=f({comparison:e?.[r.name]?.[s],config:t,fields:r.fields,locales:n,parentIsLocalized:a||r.localized,version:i?.[r.name]?.[s]})}):o+=f({comparison:e?.[r.name],config:t,fields:r.fields,locales:n,parentIsLocalized:a||r.localized,version:i?.[r.name]}):o+=f({comparison:e,config:t,fields:r.fields,locales:n,parentIsLocalized:a||r.localized,version:i});break;case"tabs":r.tabs.forEach(r=>{"name"in r&&n&&r.localized?n.forEach(s=>{o+=f({comparison:e?.[r.name]?.[s],config:t,fields:r.fields,locales:n,parentIsLocalized:a||r.localized,version:i?.[r.name]?.[s]})}):"name"in r?o+=f({comparison:e?.[r.name],config:t,fields:r.fields,locales:n,parentIsLocalized:a||r.localized,version:i?.[r.name]}):o+=f({comparison:e,config:t,fields:r.fields,locales:n,parentIsLocalized:a||r.localized,version:i})});break;case"ui":break;default:throw Error(`Unexpected field.type in countChangedFields : ${String(s)}`)}}),o}function p({comparisonRows:e=[],config:t,field:r,locales:n,parentIsLocalized:a,versionRows:i=[]}){let o=0,s=0;for(;e[s]||i[s];){let l=e?.[s]||{},u=i?.[s]||{},{fields:c}=(0,d.G)({baseVersionField:{type:"text",fields:[],path:"",schemaPath:""},comparisonRow:l,config:t,field:r,row:s,versionRow:u});o+=f({comparison:l,config:t,fields:c,locales:n,parentIsLocalized:a||r.localized,version:u}),s++}return o}let m="diff-collapser",g=e=>{let t,r=(0,n.c)(21),{children:c,comparison:d,field:g,fields:y,initCollapsed:v,isIterable:b,label:w,locales:x,parentIsLocalized:$,version:k}=e,S=void 0!==b&&b,{t:_}=(0,i.d)(),[j,E]=(0,u.useState)(void 0!==v&&v),{config:C}=(0,o.b)();if(r[0]!==c||r[1]!==d||r[2]!==C||r[3]!==g||r[4]!==y||r[5]!==j||r[6]!==S||r[7]!==w||r[8]!==x||r[9]!==$||r[10]!==_||r[11]!==k){let e,n,i;if(S){let t,n;if(!(0,l.eE)(g)&&!(0,l.MT)(g))throw Error("DiffCollapser: field must be an array or blocks field when isIterable is true");r[13]!==d?(t=null!=d?d:[],r[13]=d,r[14]=t):t=r[14];let a=t;r[15]!==k?(n=null!=k?k:[],r[15]=k,r[16]=n):n=r[16];let i=n;if(!Array.isArray(a)||!Array.isArray(i))throw Error("DiffCollapser: comparison and version must be arrays when isIterable is true");e=p({comparisonRows:a,config:C,field:g,locales:x,parentIsLocalized:$,versionRows:i})}else e=f({comparison:d,config:C,fields:y,locales:x,parentIsLocalized:$,version:k});let o=j&&"".concat(m,"__content--is-collapsed");r[17]!==o?(n=["".concat(m,"__content"),o].filter(Boolean),r[17]=o,r[18]=n):n=r[18];let u=n.join(" ");r[19]!==j?(i=()=>E(!j),r[19]=j,r[20]=i):i=r[20],t=(0,a.jsxs)("div",{className:m,children:[(0,a.jsxs)(s.FieldDiffLabel,{children:[(0,a.jsx)("button",{"aria-label":j?"Expand":"Collapse",className:"".concat(m,"__toggle-button"),onClick:i,type:"button",children:(0,a.jsx)(s.ChevronIcon,{direction:j?"right":"down"})}),(0,a.jsx)("span",{className:"".concat(m,"__label"),children:w}),e>0&&(0,a.jsx)(s.Pill,{className:"".concat(m,"__field-change-count"),pillStyle:"light-gray",size:"small",children:_("version:changedFieldsCount",{count:e})})]}),(0,a.jsx)("div",{className:u,children:c})]}),r[0]=c,r[1]=d,r[2]=C,r[3]=g,r[4]=y,r[5]=j,r[6]=S,r[7]=w,r[8]=x,r[9]=$,r[10]=_,r[11]=k,r[12]=t}else t=r[12];return t}},6114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(62597),a=r(37742),i=r(26520),o=r(34283);t.default={keyword:"properties",type:"object",schemaType:"object",code(e){let{gen:t,schema:r,parentSchema:s,data:l,it:u}=e;"all"===u.opts.removeAdditional&&void 0===s.additionalProperties&&o.default.code(new n.KeywordCxt(u,o.default,"additionalProperties"));let c=(0,a.allSchemaProperties)(r);for(let e of c)u.definedProperties.add(e);u.opts.unevaluated&&c.length&&!0!==u.props&&(u.props=i.mergeEvaluated.props(t,(0,i.toHash)(c),u.props));let d=c.filter(e=>!(0,i.alwaysValidSchema)(u,r[e]));if(0===d.length)return;let f=t.name("valid");for(let n of d){var p;(p=n,u.opts.useDefaults&&!u.compositeRule&&void 0!==r[p].default)?m(n):(t.if((0,a.propertyInData)(t,l,n,u.opts.ownProperties)),m(n),u.allErrors||t.else().var(f,!0),t.endIf()),e.it.definedProperties.add(n),e.ok(f)}function m(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},f)}}}},7430:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(26520);t.default={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>(0,n._)`{propertyName: ${e.propertyName}}`},code(e){let{gen:t,schema:r,data:i,it:o}=e;if((0,a.alwaysValidSchema)(o,r))return;let s=t.name("valid");t.forIn("key",i,r=>{e.setParams({propertyName:r}),e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:!0},s),t.if((0,n.not)(s),()=>{e.error(!0),o.allErrors||t.break()})}),e.ok(s)}}},8253:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateTuple=void 0;let n=r(55150),a=r(26520),i=r(37742);function o(e,t,r=e.schema){let{gen:i,parentSchema:s,data:l,keyword:u,it:c}=e;(function(e){let{opts:n,errSchemaPath:i}=c,o=r.length,s=o===e.minItems&&(o===e.maxItems||!1===e[t]);if(n.strictTuples&&!s){let e=`"${u}" is ${o}-tuple, but minItems or maxItems/${t} are not specified or different at path "${i}"`;(0,a.checkStrictMode)(c,e,n.strictTuples)}})(s),c.opts.unevaluated&&r.length&&!0!==c.items&&(c.items=a.mergeEvaluated.items(i,r.length,c.items));let d=i.name("valid"),f=i.const("len",(0,n._)`${l}.length`);r.forEach((t,r)=>{(0,a.alwaysValidSchema)(c,t)||(i.if((0,n._)`${f} > ${r}`,()=>e.subschema({keyword:u,schemaProp:r,dataProp:r},d)),e.ok(d))})}t.validateTuple=o,t.default={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){let{schema:t,it:r}=e;if(Array.isArray(t))return o(e,"additionalItems",t);r.items=!0,(0,a.alwaysValidSchema)(r,t)||e.ok((0,i.validateArray)(e))}}},9688:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});var n=r(63165),a=r(65726);function i(e,t){var r,i,o,s,l,u,c,d;let f=(0,n.q)(),p=null!=(d=null!=(c=null!=(u=null!=(l=null==t?void 0:t.weekStartsOn)?l:null==t||null==(i=t.locale)||null==(r=i.options)?void 0:r.weekStartsOn)?u:f.weekStartsOn)?c:null==(s=f.locale)||null==(o=s.options)?void 0:o.weekStartsOn)?d:0,m=(0,a.a)(e,null==t?void 0:t.in),g=m.getDay();return m.setDate(m.getDate()-(7*(g<p)+g-p)),m.setHours(0,0,0,0),m}},10166:(e,t)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=i(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=i(t,r));return t}(r)))}return e}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0===(r=(function(){return a}).apply(t,[]))||(e.exports=r)}()},11111:(e,t,r)=>{"use strict";r.d(t,{J7:()=>a,Ay:()=>th});var n,a,i,o,s={};r.r(s),r.d(s,{Diff:()=>p,applyPatch:()=>V,applyPatches:()=>U,canonicalize:()=>I,convertChangesToDMP:()=>eu,convertChangesToXML:()=>ec,createPatch:()=>G,createTwoFilesPatch:()=>W,diffArrays:()=>B,diffChars:()=>g,diffCss:()=>P,diffJson:()=>L,diffLines:()=>S,diffSentences:()=>E,diffTrimmedLines:()=>_,diffWords:()=>x,diffWordsWithSpace:()=>$,formatPatch:()=>H,merge:()=>J,parsePatch:()=>z,reversePatch:()=>el,structuredPatch:()=>q});var l=r(67252),u=r(10166),c=r(89688),d=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function f(e,t){if(e.length!==t.length)return!1;for(var r,n,a=0;a<e.length;a++)if(!((r=e[a])===(n=t[a])||d(r)&&d(n))&&1)return!1;return!0}function p(){}p.prototype={diff:function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=n.callback;"function"==typeof n&&(a=n,n={}),this.options=n;var i=this;function o(e){return a?(setTimeout(function(){a(void 0,e)},0),!0):e}e=this.castInput(e),t=this.castInput(t),e=this.removeEmpty(this.tokenize(e));var s=(t=this.removeEmpty(this.tokenize(t))).length,l=e.length,u=1,c=s+l;n.maxEditLength&&(c=Math.min(c,n.maxEditLength));var d=null!=(r=n.timeout)?r:1/0,f=Date.now()+d,p=[{oldPos:-1,lastComponent:void 0}],m=this.extractCommon(p[0],t,e,0);if(p[0].oldPos+1>=l&&m+1>=s)return o([{value:this.join(t),count:t.length}]);var g=-1/0,y=1/0;function v(){for(var r=Math.max(g,-u);r<=Math.min(y,u);r+=2){var n=void 0,a=p[r-1],c=p[r+1];a&&(p[r-1]=void 0);var d=!1;if(c){var f=c.oldPos-r;d=c&&0<=f&&f<s}var v=a&&a.oldPos+1<l;if(!d&&!v){p[r]=void 0;continue}if(n=!v||d&&a.oldPos+1<c.oldPos?i.addToPath(c,!0,void 0,0):i.addToPath(a,void 0,!0,1),m=i.extractCommon(n,t,e,r),n.oldPos+1>=l&&m+1>=s)return o(function(e,t,r,n,a){for(var i,o=[];t;)o.push(t),i=t.previousComponent,delete t.previousComponent,t=i;o.reverse();for(var s=0,l=o.length,u=0,c=0;s<l;s++){var d=o[s];if(d.removed){if(d.value=e.join(n.slice(c,c+d.count)),c+=d.count,s&&o[s-1].added){var f=o[s-1];o[s-1]=o[s],o[s]=f}}else{if(!d.added&&a){var p=r.slice(u,u+d.count);p=p.map(function(e,t){var r=n[c+t];return r.length>e.length?r:e}),d.value=e.join(p)}else d.value=e.join(r.slice(u,u+d.count));u+=d.count,d.added||(c+=d.count)}}var m=o[l-1];return l>1&&"string"==typeof m.value&&(m.added||m.removed)&&e.equals("",m.value)&&(o[l-2].value+=m.value,o.pop()),o}(i,n.lastComponent,t,e,i.useLongestToken));p[r]=n,n.oldPos+1>=l&&(y=Math.min(y,r-1)),m+1>=s&&(g=Math.max(g,r+1))}u++}if(a)!function e(){setTimeout(function(){if(u>c||Date.now()>f)return a();v()||e()},0)}();else for(;u<=c&&Date.now()<=f;){var b=v();if(b)return b}},addToPath:function(e,t,r,n){var a=e.lastComponent;return a&&a.added===t&&a.removed===r?{oldPos:e.oldPos+n,lastComponent:{count:a.count+1,added:t,removed:r,previousComponent:a.previousComponent}}:{oldPos:e.oldPos+n,lastComponent:{count:1,added:t,removed:r,previousComponent:a}}},extractCommon:function(e,t,r,n){for(var a=t.length,i=r.length,o=e.oldPos,s=o-n,l=0;s+1<a&&o+1<i&&this.equals(t[s+1],r[o+1]);)s++,o++,l++;return l&&(e.lastComponent={count:l,previousComponent:e.lastComponent}),e.oldPos=o,s},equals:function(e,t){return this.options.comparator?this.options.comparator(e,t):e===t||this.options.ignoreCase&&e.toLowerCase()===t.toLowerCase()},removeEmpty:function(e){for(var t=[],r=0;r<e.length;r++)e[r]&&t.push(e[r]);return t},castInput:function(e){return e},tokenize:function(e){return e.split("")},join:function(e){return e.join("")}};var m=new p;function g(e,t,r){return m.diff(e,t,r)}function y(e,t){if("function"==typeof e)t.callback=e;else if(e)for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t}var v=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,b=/\S/,w=new p;function x(e,t,r){return r=y(r,{ignoreWhitespace:!0}),w.diff(e,t,r)}function $(e,t,r){return w.diff(e,t,r)}w.equals=function(e,t){return this.options.ignoreCase&&(e=e.toLowerCase(),t=t.toLowerCase()),e===t||this.options.ignoreWhitespace&&!b.test(e)&&!b.test(t)},w.tokenize=function(e){for(var t=e.split(/([^\S\r\n]+|[()[\]{}'"\r\n]|\b)/),r=0;r<t.length-1;r++)!t[r+1]&&t[r+2]&&v.test(t[r])&&v.test(t[r+2])&&(t[r]+=t[r+2],t.splice(r+1,2),r--);return t};var k=new p;function S(e,t,r){return k.diff(e,t,r)}function _(e,t,r){var n=y(r,{ignoreWhitespace:!0});return k.diff(e,t,n)}k.tokenize=function(e){this.options.stripTrailingCr&&(e=e.replace(/\r\n/g,"\n"));var t=[],r=e.split(/(\n|\r\n)/);r[r.length-1]||r.pop();for(var n=0;n<r.length;n++){var a=r[n];n%2&&!this.options.newlineIsToken?t[t.length-1]+=a:(this.options.ignoreWhitespace&&(a=a.trim()),t.push(a))}return t};var j=new p;function E(e,t,r){return j.diff(e,t,r)}j.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)};var C=new p;function P(e,t,r){return C.diff(e,t,r)}function N(e){return(N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function O(e){return function(e){if(Array.isArray(e))return D(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return D(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return D(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}C.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)};var M=Object.prototype.toString,R=new p;function L(e,t,r){return R.diff(e,t,r)}function I(e,t,r,n,a){for(t=t||[],r=r||[],n&&(e=n(a,e)),i=0;i<t.length;i+=1)if(t[i]===e)return r[i];if("[object Array]"===M.call(e)){for(t.push(e),o=Array(e.length),r.push(o),i=0;i<e.length;i+=1)o[i]=I(e[i],t,r,n,a);return t.pop(),r.pop(),o}if(e&&e.toJSON&&(e=e.toJSON()),"object"===N(e)&&null!==e){t.push(e),o={},r.push(o);var i,o,s,l=[];for(s in e)e.hasOwnProperty(s)&&l.push(s);for(l.sort(),i=0;i<l.length;i+=1)o[s=l[i]]=I(e[s],t,r,n,s);t.pop(),r.pop()}else o=e;return o}R.useLongestToken=!0,R.tokenize=k.tokenize,R.castInput=function(e){var t=this.options,r=t.undefinedReplacement,n=t.stringifyReplacer,a=void 0===n?function(e,t){return void 0===t?r:t}:n;return"string"==typeof e?e:JSON.stringify(I(e,null,null,a),a,"  ")},R.equals=function(e,t){return p.prototype.equals.call(R,e.replace(/,([\r\n])/g,"$1"),t.replace(/,([\r\n])/g,"$1"))};var F=new p;function B(e,t,r){return F.diff(e,t,r)}function z(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.split(/\r\n|[\n\v\f\r\x85]/),n=e.match(/\r\n|[\n\v\f\r\x85]/g)||[],a=[],i=0;function o(e){var t=/^(---|\+\+\+)\s+(.*)$/.exec(r[i]);if(t){var n="---"===t[1]?"old":"new",a=t[2].split("	",2),o=a[0].replace(/\\\\/g,"\\");/^".*"$/.test(o)&&(o=o.substr(1,o.length-2)),e[n+"FileName"]=o,e[n+"Header"]=(a[1]||"").trim(),i++}}for(;i<r.length;)!function(){var e={};for(a.push(e);i<r.length;){var s=r[i];if(/^(\-\-\-|\+\+\+|@@)\s/.test(s))break;var l=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(s);l&&(e.index=l[1]),i++}for(o(e),o(e),e.hunks=[];i<r.length;){var u=r[i];if(/^(Index:|diff|\-\-\-|\+\+\+)\s/.test(u))break;if(/^@@/.test(u))e.hunks.push(function(){var e=i,a=r[i++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),o={oldStart:+a[1],oldLines:void 0===a[2]?1:+a[2],newStart:+a[3],newLines:void 0===a[4]?1:+a[4],lines:[],linedelimiters:[]};0===o.oldLines&&(o.oldStart+=1),0===o.newLines&&(o.newStart+=1);for(var s=0,l=0;i<r.length&&(0!==r[i].indexOf("--- ")||!(i+2<r.length)||0!==r[i+1].indexOf("+++ ")||0!==r[i+2].indexOf("@@"));i++){var u=0==r[i].length&&i!=r.length-1?" ":r[i][0];if("+"===u||"-"===u||" "===u||"\\"===u)o.lines.push(r[i]),o.linedelimiters.push(n[i]||"\n"),"+"===u?s++:"-"===u?l++:" "===u&&(s++,l++);else break}if(s||1!==o.newLines||(o.newLines=0),l||1!==o.oldLines||(o.oldLines=0),t.strict){if(s!==o.newLines)throw Error("Added line count did not match for hunk at line "+(e+1));if(l!==o.oldLines)throw Error("Removed line count did not match for hunk at line "+(e+1))}return o}());else if(u&&t.strict)throw Error("Unknown line "+(i+1)+" "+JSON.stringify(u));else i++}}();return a}function V(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof t&&(t=z(t)),Array.isArray(t)){if(t.length>1)throw Error("applyPatch only works with a single input.");t=t[0]}for(var n,a,i=e.split(/\r\n|[\n\v\f\r\x85]/),o=e.match(/\r\n|[\n\v\f\r\x85]/g)||[],s=t.hunks,l=r.compareLine||function(e,t,r,n){return t===n},u=0,c=r.fuzzFactor||0,d=0,f=0,p=0;p<s.length;p++){for(var m=s[p],g=i.length-m.oldLines,y=0,v=f+m.oldStart-1,b=function(e,t,r){var n=!0,a=!1,i=!1,o=1;return function s(){if(n&&!i){if(a?o++:n=!1,e+o<=r)return o;i=!0}if(!a)return(i||(n=!0),t<=e-o)?-o++:(a=!0,s())}}(v,d,g);void 0!==y;y=b())if(function(e,t){for(var r=0;r<e.lines.length;r++){var n=e.lines[r],a=n.length>0?n[0]:" ",o=n.length>0?n.substr(1):n;if(" "===a||"-"===a){if(!l(t+1,i[t],a,o)&&++u>c)return!1;t++}}return!0}(m,v+y)){m.offset=f+=y;break}if(void 0===y)return!1;d=m.offset+m.oldStart+m.oldLines}for(var w=0,x=0;x<s.length;x++){var $=s[x],k=$.oldStart+$.offset+w-1;w+=$.newLines-$.oldLines;for(var S=0;S<$.lines.length;S++){var _=$.lines[S],j=_.length>0?_[0]:" ",E=_.length>0?_.substr(1):_,C=$.linedelimiters&&$.linedelimiters[S]||"\n";if(" "===j)k++;else if("-"===j)i.splice(k,1),o.splice(k,1);else if("+"===j)i.splice(k,0,E),o.splice(k,0,C),k++;else if("\\"===j){var P=$.lines[S-1]?$.lines[S-1][0]:null;"+"===P?n=!0:"-"===P&&(a=!0)}}}if(n)for(;!i[i.length-1];)i.pop(),o.pop();else a&&(i.push(""),o.push("\n"));for(var N=0;N<i.length-1;N++)i[N]=i[N]+o[N];return i.join("")}function U(e,t){"string"==typeof e&&(e=z(e));var r=0;!function n(){var a=e[r++];if(!a)return t.complete();t.loadFile(a,function(e,r){if(e)return t.complete(e);var i=V(r,a,t);t.patched(a,i,function(e){if(e)return t.complete(e);n()})})}()}function q(e,t,r,n,a,i,o){o||(o={}),void 0===o.context&&(o.context=4);var s=S(r,n,o);if(s){s.push({value:"",lines:[]});for(var l=[],u=0,c=0,d=[],f=1,p=1,m=0;m<s.length;m++)!function(e){var t=s[e],a=t.lines||t.value.replace(/\n$/,"").split("\n");if(t.lines=a,t.added||t.removed){if(!u){var i=s[e-1];u=f,c=p,i&&(d=o.context>0?g(i.lines.slice(-o.context)):[],u-=d.length,c-=d.length)}(m=d).push.apply(m,O(a.map(function(e){return(t.added?"+":"-")+e}))),t.added?p+=a.length:f+=a.length}else{if(u)if(a.length<=2*o.context&&e<s.length-2)(y=d).push.apply(y,O(g(a)));else{var m,y,v,b=Math.min(a.length,o.context);(v=d).push.apply(v,O(g(a.slice(0,b))));var w={oldStart:u,oldLines:f-u+b,newStart:c,newLines:p-c+b,lines:d};if(e>=s.length-2&&a.length<=o.context){var x=/\n$/.test(r),$=/\n$/.test(n),k=0==a.length&&d.length>w.oldLines;!x&&k&&r.length>0&&d.splice(w.oldLines,0,"\\ No newline at end of file"),(x||k)&&$||d.push("\\ No newline at end of file")}l.push(w),u=0,c=0,d=[]}f+=a.length,p+=a.length}}(m);return{oldFileName:e,newFileName:t,oldHeader:a,newHeader:i,hunks:l}}function g(e){return e.map(function(e){return" "+e})}}function H(e){if(Array.isArray(e))return e.map(H).join("\n");var t=[];e.oldFileName==e.newFileName&&t.push("Index: "+e.oldFileName),t.push("==================================================================="),t.push("--- "+e.oldFileName+(void 0===e.oldHeader?"":"	"+e.oldHeader)),t.push("+++ "+e.newFileName+(void 0===e.newHeader?"":"	"+e.newHeader));for(var r=0;r<e.hunks.length;r++){var n=e.hunks[r];0===n.oldLines&&(n.oldStart-=1),0===n.newLines&&(n.newStart-=1),t.push("@@ -"+n.oldStart+","+n.oldLines+" +"+n.newStart+","+n.newLines+" @@"),t.push.apply(t,n.lines)}return t.join("\n")+"\n"}function W(e,t,r,n,a,i,o){return H(q(e,t,r,n,a,i,o))}function G(e,t,r,n,a,i){return W(e,e,t,r,n,a,i)}function K(e,t){if(t.length>e.length)return!1;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return!1;return!0}function J(e,t,r){e=Y(e,r),t=Y(t,r);var n={};(e.index||t.index)&&(n.index=e.index||t.index),(e.newFileName||t.newFileName)&&(Q(e)?Q(t)?(n.oldFileName=X(n,e.oldFileName,t.oldFileName),n.newFileName=X(n,e.newFileName,t.newFileName),n.oldHeader=X(n,e.oldHeader,t.oldHeader),n.newHeader=X(n,e.newHeader,t.newHeader)):(n.oldFileName=e.oldFileName,n.newFileName=e.newFileName,n.oldHeader=e.oldHeader,n.newHeader=e.newHeader):(n.oldFileName=t.oldFileName||e.oldFileName,n.newFileName=t.newFileName||e.newFileName,n.oldHeader=t.oldHeader||e.oldHeader,n.newHeader=t.newHeader||e.newHeader)),n.hunks=[];for(var a=0,i=0,o=0,s=0;a<e.hunks.length||i<t.hunks.length;){var l=e.hunks[a]||{oldStart:1/0},u=t.hunks[i]||{oldStart:1/0};if(Z(l,u))n.hunks.push(ee(l,o)),a++,s+=l.newLines-l.oldLines;else if(Z(u,l))n.hunks.push(ee(u,s)),i++,o+=u.newLines-u.oldLines;else{var c={oldStart:Math.min(l.oldStart,u.oldStart),oldLines:0,newStart:Math.min(l.newStart+o,u.oldStart+s),newLines:0,lines:[]};(function(e,t,r,n,a){var i,o,s,l={offset:t,lines:r,index:0},u={offset:n,lines:a,index:0};for(en(e,l,u),en(e,u,l);l.index<l.lines.length&&u.index<u.lines.length;){var c,d,f=l.lines[l.index],p=u.lines[u.index];("-"===f[0]||"+"===f[0])&&("-"===p[0]||"+"===p[0])?function(e,t,r){var n,a,i,o=ei(t),s=ei(r);if(eo(o)&&eo(s)){if(K(o,s)&&es(r,o,o.length-s.length))return(n=e.lines).push.apply(n,O(o));if(K(s,o)&&es(t,s,s.length-o.length))return(a=e.lines).push.apply(a,O(s))}else if(o.length===s.length&&K(o,s))return(i=e.lines).push.apply(i,O(o));er(e,o,s)}(e,l,u):"+"===f[0]&&" "===p[0]?(c=e.lines).push.apply(c,O(ei(l))):"+"===p[0]&&" "===f[0]?(d=e.lines).push.apply(d,O(ei(u))):"-"===f[0]&&" "===p[0]?et(e,l,u):"-"===p[0]&&" "===f[0]?et(e,u,l,!0):f===p?(e.lines.push(f),l.index++,u.index++):er(e,ei(l),ei(u))}ea(e,l),ea(e,u),o=(i=function e(t){var r=0,n=0;return t.forEach(function(t){if("string"!=typeof t){var a=e(t.mine),i=e(t.theirs);void 0!==r&&(a.oldLines===i.oldLines?r+=a.oldLines:r=void 0),void 0!==n&&(a.newLines===i.newLines?n+=a.newLines:n=void 0)}else void 0!==n&&("+"===t[0]||" "===t[0])&&n++,void 0!==r&&("-"===t[0]||" "===t[0])&&r++}),{oldLines:r,newLines:n}}(e.lines)).oldLines,s=i.newLines,void 0!==o?e.oldLines=o:delete e.oldLines,void 0!==s?e.newLines=s:delete e.newLines})(c,l.oldStart,l.lines,u.oldStart,u.lines),i++,a++,n.hunks.push(c)}}return n}function Y(e,t){if("string"==typeof e){if(/^@@/m.test(e)||/^Index:/m.test(e))return z(e)[0];if(!t)throw Error("Must provide a base reference or pass in a patch");return q(void 0,void 0,t,e)}return e}function Q(e){return e.newFileName&&e.newFileName!==e.oldFileName}function X(e,t,r){return t===r?t:(e.conflict=!0,{mine:t,theirs:r})}function Z(e,t){return e.oldStart<t.oldStart&&e.oldStart+e.oldLines<t.oldStart}function ee(e,t){return{oldStart:e.oldStart,oldLines:e.oldLines,newStart:e.newStart+t,newLines:e.newLines,lines:e.lines}}function et(e,t,r,n){var a,i=ei(t),o=function(e,t){for(var r=[],n=[],a=0,i=!1,o=!1;a<t.length&&e.index<e.lines.length;){var s=e.lines[e.index],l=t[a];if("+"===l[0])break;if(i=i||" "!==s[0],n.push(l),a++,"+"===s[0])for(o=!0;"+"===s[0];)r.push(s),s=e.lines[++e.index];l.substr(1)===s.substr(1)?(r.push(s),e.index++):o=!0}if("+"===(t[a]||"")[0]&&i&&(o=!0),o)return r;for(;a<t.length;)n.push(t[a++]);return{merged:n,changes:r}}(r,i);o.merged?(a=e.lines).push.apply(a,O(o.merged)):er(e,n?o:i,n?i:o)}function er(e,t,r){e.conflict=!0,e.lines.push({conflict:!0,mine:t,theirs:r})}function en(e,t,r){for(;t.offset<r.offset&&t.index<t.lines.length;){var n=t.lines[t.index++];e.lines.push(n),t.offset++}}function ea(e,t){for(;t.index<t.lines.length;){var r=t.lines[t.index++];e.lines.push(r)}}function ei(e){for(var t=[],r=e.lines[e.index][0];e.index<e.lines.length;){var n=e.lines[e.index];if("-"===r&&"+"===n[0]&&(r="+"),r===n[0])t.push(n),e.index++;else break}return t}function eo(e){return e.reduce(function(e,t){return e&&"-"===t[0]},!0)}function es(e,t,r){for(var n=0;n<r;n++){var a=t[t.length-r+n].substr(1);if(e.lines[e.index+n]!==" "+a)return!1}return e.index+=r,!0}function el(e){return Array.isArray(e)?e.map(el).reverse():T(T({},e),{},{oldFileName:e.newFileName,oldHeader:e.newHeader,newFileName:e.oldFileName,newHeader:e.oldHeader,hunks:e.hunks.map(function(e){return{oldLines:e.newLines,oldStart:e.newStart,newLines:e.oldLines,newStart:e.oldStart,linedelimiters:e.linedelimiters,lines:e.lines.map(function(e){return e.startsWith("-")?"+".concat(e.slice(1)):e.startsWith("+")?"-".concat(e.slice(1)):e})}})})}function eu(e){for(var t,r=[],n=0;n<e.length;n++)r.push([(t=e[n]).added?1:t.removed?-1:0,t.value]);return r}function ec(e){for(var t=[],r=0;r<e.length;r++){var n=e[r];n.added?t.push("<ins>"):n.removed&&t.push("<del>"),t.push(function(e){var t=e;return(t=(t=(t=t.replace(/&/g,"&amp;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;")).replace(/"/g,"&quot;")}(n.value)),n.added?t.push("</ins>"):n.removed&&t.push("</del>")}return t.join("")}F.tokenize=function(e){return e.slice()},F.join=F.removeEmpty=function(e){return e},!function(e){e[e.DEFAULT=0]="DEFAULT",e[e.ADDED=1]="ADDED",e[e.REMOVED=2]="REMOVED",e[e.CHANGED=3]="CHANGED"}(n||(n={})),function(e){e.CHARS="diffChars",e.WORDS="diffWords",e.WORDS_WITH_SPACE="diffWordsWithSpace",e.LINES="diffLines",e.TRIMMED_LINES="diffTrimmedLines",e.SENTENCES="diffSentences",e.CSS="diffCss",e.JSON="diffJson"}(a||(a={}));let ed=e=>""===e?[]:e.replace(/\n$/,"").split("\n"),ef=(e,t,r=a.CHARS)=>{let i=("string"==typeof r?s[r]:r)(e,t),o={left:[],right:[]};return i.forEach(({added:e,removed:t,value:r})=>{let a={};return e&&(a.type=n.ADDED,a.value=r,o.right.push(a)),t&&(a.type=n.REMOVED,a.value=r,o.left.push(a)),t||e||(a.type=n.DEFAULT,a.value=r,o.right.push(a),o.left.push(a)),a}),o},eh=(e,t,r=!1,i=a.CHARS,o=0,s=[])=>{let l=[];l="string"==typeof e&&"string"==typeof t?S(e,t,{newlineIsToken:!1,ignoreWhitespace:!1,ignoreCase:!1}):L(e,t);let u=o,c=o,d=[],f=0,p=[],m=[],g=(e,t,a,o,d)=>ed(e).map((e,y)=>{let v={},b={};if(!m.includes(`${t}-${y}`)&&(!d||0===y)){if(a||o){let a=!0;if(o){v.lineNumber=c+=1,v.type=n.REMOVED,v.value=e||" ";let o=l[t+1];if(o?.added){let n=ed(o.value)[y];if(n){let{value:o,lineNumber:s,type:l}=g(n,t,!0,!1,!0)[0].right;if(m.push(`${t+1}-${y}`),b.lineNumber=s,v.value===o)a=!1,b.type=0,v.type=0,b.value=o;else if(b.type=l,r)b.value=o;else{let t=ef(e,o,i);b.value=t.right,v.value=t.left}}}}else b.lineNumber=u+=1,b.type=n.ADDED,b.value=e;!a||d||p.includes(f)||p.push(f)}else c+=1,u+=1,v.lineNumber=c,v.type=n.DEFAULT,v.value=e,b.lineNumber=u,b.type=n.DEFAULT,b.value=e;return(s?.includes(`L-${v.lineNumber}`)||s?.includes(`R-${b.lineNumber}`)&&!p.includes(f))&&p.push(f),d||(f+=1),{right:b,left:v}}}).filter(Boolean);return l.forEach(({added:e,removed:t,value:r},n)=>{d=[...d,...g(r,n,e,t)]}),{lineInformation:d,diffLines:p}};function ep(){return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",width:"16",height:"16",children:[(0,l.jsx)("title",{children:"expand"}),(0,l.jsx)("path",{d:"m8.177.677 2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25a.75.75 0 0 1-1.5 0V4H5.104a.25.25 0 0 1-.177-.427L7.823.677a.25.25 0 0 1 .354 0ZM7.25 10.75a.75.75 0 0 1 1.5 0V12h2.146a.25.25 0 0 1 .177.427l-2.896 2.896a.25.25 0 0 1-.354 0l-2.896-2.896A.25.25 0 0 1 5.104 12H7.25v-1.25Zm-5-2a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z"})]})}var em=function(){function e(e){var t=this;this._insertTag=function(e){var r;r=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,r),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(((t=document.createElement("style")).setAttribute("data-emotion",this.key),void 0!==this.nonce&&t.setAttribute("nonce",this.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t));var t,r=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(r);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else r.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach(function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),this.tags=[],this.ctr=0},e}(),eg=Math.abs,ey=String.fromCharCode,ev=Object.assign;function eb(e,t,r){return e.replace(t,r)}function ew(e,t){return e.indexOf(t)}function ex(e,t){return 0|e.charCodeAt(t)}function e$(e,t,r){return e.slice(t,r)}function ek(e){return e.length}function eS(e,t){return t.push(e),e}var e_=1,ej=1,eE=0,eC=0,eP=0,eN="";function eA(e,t,r,n,a,i,o){return{value:e,root:t,parent:r,type:n,props:a,children:i,line:e_,column:ej,length:o,return:""}}function eT(e,t){return ev(eA("",null,null,"",null,null,0),e,{length:-e.length},t)}function eO(){return eP=eC<eE?ex(eN,eC++):0,ej++,10===eP&&(ej=1,e_++),eP}function eD(){return ex(eN,eC)}function eM(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function eR(e){return e_=ej=1,eE=ek(eN=e),eC=0,[]}function eL(e){var t,r;return(t=eC-1,r=function e(t){for(;eO();)switch(eP){case t:return eC;case 34:case 39:34!==t&&39!==t&&e(eP);break;case 40:41===t&&e(t);break;case 92:eO()}return eC}(91===e?e+2:40===e?e+1:e),e$(eN,t,r)).trim()}var eI="-ms-",eF="-moz-",eB="-webkit-",ez="comm",eV="rule",eU="decl",eq="@keyframes";function eH(e,t){for(var r="",n=e.length,a=0;a<n;a++)r+=t(e[a],a,e,t)||"";return r}function eW(e,t,r,n){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case eU:return e.return=e.return||e.value;case ez:return"";case eq:return e.return=e.value+"{"+eH(e.children,n)+"}";case eV:e.value=e.props.join(",")}return ek(r=eH(e.children,n))?e.return=e.value+"{"+r+"}":""}function eG(e,t,r,n,a,i,o,s,l,u,c){for(var d=a-1,f=0===a?i:[""],p=f.length,m=0,g=0,y=0;m<n;++m)for(var v=0,b=e$(e,d+1,d=eg(g=o[m])),w=e;v<p;++v)(w=(g>0?f[v]+" "+b:eb(b,/&\f/g,f[v])).trim())&&(l[y++]=w);return eA(e,t,r,0===a?eV:s,l,u,c)}function eK(e,t,r,n){return eA(e,t,r,eU,e$(e,0,n),e$(e,n+1,-1),n)}var eJ=function(e,t,r){for(var n=0,a=0;n=a,a=eD(),38===n&&12===a&&(t[r]=1),!eM(a);)eO();return e$(eN,e,eC)},eY=function(e,t){var r=-1,n=44;do switch(eM(n)){case 0:38===n&&12===eD()&&(t[r]=1),e[r]+=eJ(eC-1,t,r);break;case 2:e[r]+=eL(n);break;case 4:if(44===n){e[++r]=58===eD()?"&\f":"",t[r]=e[r].length;break}default:e[r]+=ey(n)}while(n=eO());return e},eQ=function(e,t){var r;return r=eY(eR(e),t),eN="",r},eX=new WeakMap,eZ=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,r=e.parent,n=e.column===r.column&&e.line===r.line;"rule"!==r.type;)if(!(r=r.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||eX.get(r))&&!n){eX.set(e,!0);for(var a=[],i=eQ(t,a),o=r.props,s=0,l=0;s<i.length;s++)for(var u=0;u<o.length;u++,l++)e.props[l]=a[s]?i[s].replace(/&\f/g,o[u]):o[u]+" "+i[s]}}},e0=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}},e1=[function(e,t,r,n){if(e.length>-1&&!e.return)switch(e.type){case eU:e.return=function e(t,r){switch(45^ex(t,0)?(((r<<2^ex(t,0))<<2^ex(t,1))<<2^ex(t,2))<<2^ex(t,3):0){case 5103:return eB+"print-"+t+t;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return eB+t+t;case 5349:case 4246:case 4810:case 6968:case 2756:return eB+t+eF+t+eI+t+t;case 6828:case 4268:return eB+t+eI+t+t;case 6165:return eB+t+eI+"flex-"+t+t;case 5187:return eB+t+eb(t,/(\w+).+(:[^]+)/,eB+"box-$1$2"+eI+"flex-$1$2")+t;case 5443:return eB+t+eI+"flex-item-"+eb(t,/flex-|-self/,"")+t;case 4675:return eB+t+eI+"flex-line-pack"+eb(t,/align-content|flex-|-self/,"")+t;case 5548:return eB+t+eI+eb(t,"shrink","negative")+t;case 5292:return eB+t+eI+eb(t,"basis","preferred-size")+t;case 6060:return eB+"box-"+eb(t,"-grow","")+eB+t+eI+eb(t,"grow","positive")+t;case 4554:return eB+eb(t,/([^-])(transform)/g,"$1"+eB+"$2")+t;case 6187:return eb(eb(eb(t,/(zoom-|grab)/,eB+"$1"),/(image-set)/,eB+"$1"),t,"")+t;case 5495:case 3959:return eb(t,/(image-set\([^]*)/,eB+"$1$`$1");case 4968:return eb(eb(t,/(.+:)(flex-)?(.*)/,eB+"box-pack:$3"+eI+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+eB+t+t;case 4095:case 3583:case 4068:case 2532:return eb(t,/(.+)-inline(.+)/,eB+"$1$2")+t;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(ek(t)-1-r>6)switch(ex(t,r+1)){case 109:if(45!==ex(t,r+4))break;case 102:return eb(t,/(.+:)(.+)-([^]+)/,"$1"+eB+"$2-$3$1"+eF+(108==ex(t,r+3)?"$3":"$2-$3"))+t;case 115:return~ew(t,"stretch")?e(eb(t,"stretch","fill-available"),r)+t:t}break;case 4949:if(115!==ex(t,r+1))break;case 6444:switch(ex(t,ek(t)-3-(~ew(t,"!important")&&10))){case 107:return eb(t,":",":"+eB)+t;case 101:return eb(t,/(.+:)([^;!]+)(;|!.+)?/,"$1"+eB+(45===ex(t,14)?"inline-":"")+"box$3$1"+eB+"$2$3$1"+eI+"$2box$3")+t}break;case 5936:switch(ex(t,r+11)){case 114:return eB+t+eI+eb(t,/[svh]\w+-[tblr]{2}/,"tb")+t;case 108:return eB+t+eI+eb(t,/[svh]\w+-[tblr]{2}/,"tb-rl")+t;case 45:return eB+t+eI+eb(t,/[svh]\w+-[tblr]{2}/,"lr")+t}return eB+t+eI+t+t}return t}(e.value,e.length);break;case eq:return eH([eT(e,{value:eb(e.value,"@","@"+eB)})],n);case eV:if(e.length){var a,i;return a=e.props,i=function(t){var r;switch(r=t,(r=/(::plac\w+|:read-\w+)/.exec(r))?r[0]:r){case":read-only":case":read-write":return eH([eT(e,{props:[eb(t,/:(read-\w+)/,":"+eF+"$1")]})],n);case"::placeholder":return eH([eT(e,{props:[eb(t,/:(plac\w+)/,":"+eB+"input-$1")]}),eT(e,{props:[eb(t,/:(plac\w+)/,":"+eF+"$1")]}),eT(e,{props:[eb(t,/:(plac\w+)/,eI+"input-$1")]})],n)}return""},a.map(i).join("")}}}],e2=function(e){var t,r,n,a,i,o=e.key;if("css"===o){var s=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(s,function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))})}var l=e.stylisPlugins||e1,u={},c=[];a=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+o+' "]'),function(e){for(var t=e.getAttribute("data-emotion").split(" "),r=1;r<t.length;r++)u[t[r]]=!0;c.push(e)});var d=(r=(t=[eZ,e0].concat(l,[eW,(n=function(e){i.insert(e)},function(e){!e.root&&(e=e.return)&&n(e)})])).length,function(e,n,a,i){for(var o="",s=0;s<r;s++)o+=t[s](e,n,a,i)||"";return o}),f=function(e){var t,r;return eH((r=function e(t,r,n,a,i,o,s,l,u){for(var c,d=0,f=0,p=s,m=0,g=0,y=0,v=1,b=1,w=1,x=0,$="",k=i,S=o,_=a,j=$;b;)switch(y=x,x=eO()){case 40:if(108!=y&&58==ex(j,p-1)){-1!=ew(j+=eb(eL(x),"&","&\f"),"&\f")&&(w=-1);break}case 34:case 39:case 91:j+=eL(x);break;case 9:case 10:case 13:case 32:j+=function(e){for(;eP=eD();)if(eP<33)eO();else break;return eM(e)>2||eM(eP)>3?"":" "}(y);break;case 92:j+=function(e,t){for(var r;--t&&eO()&&!(eP<48)&&!(eP>102)&&(!(eP>57)||!(eP<65))&&(!(eP>70)||!(eP<97)););return r=eC+(t<6&&32==eD()&&32==eO()),e$(eN,e,r)}(eC-1,7);continue;case 47:switch(eD()){case 42:case 47:eS((c=function(e,t){for(;eO();)if(e+eP===57)break;else if(e+eP===84&&47===eD())break;return"/*"+e$(eN,t,eC-1)+"*"+ey(47===e?e:eO())}(eO(),eC),eA(c,r,n,ez,ey(eP),e$(c,2,-2),0)),u);break;default:j+="/"}break;case 123*v:l[d++]=ek(j)*w;case 125*v:case 59:case 0:switch(x){case 0:case 125:b=0;case 59+f:-1==w&&(j=eb(j,/\f/g,"")),g>0&&ek(j)-p&&eS(g>32?eK(j+";",a,n,p-1):eK(eb(j," ","")+";",a,n,p-2),u);break;case 59:j+=";";default:if(eS(_=eG(j,r,n,d,f,i,l,$,k=[],S=[],p),o),123===x)if(0===f)e(j,r,_,_,k,o,p,l,S);else switch(99===m&&110===ex(j,3)?100:m){case 100:case 108:case 109:case 115:e(t,_,_,a&&eS(eG(t,_,_,0,0,i,l,$,i,k=[],p),S),i,S,p,l,a?k:S);break;default:e(j,_,_,_,[""],S,0,l,S)}}d=f=g=0,v=w=1,$=j="",p=s;break;case 58:p=1+ek(j),g=y;default:if(v<1){if(123==x)--v;else if(125==x&&0==v++&&125==(eP=eC>0?ex(eN,--eC):0,ej--,10===eP&&(ej=1,e_--),eP))continue}switch(j+=ey(x),x*v){case 38:w=f>0?1:(j+="\f",-1);break;case 44:l[d++]=(ek(j)-1)*w,w=1;break;case 64:45===eD()&&(j+=eL(eO())),m=eD(),f=p=ek($=j+=function(e){for(;!eM(eD());)eO();return e$(eN,e,eC)}(eC)),x++;break;case 45:45===y&&2==ek(j)&&(v=0)}}return o}("",null,null,null,[""],t=eR(t=e),0,[0],t),eN="",r),d)},p={key:o,sheet:new em({key:o,container:a,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:u,registered:{},insert:function(e,t,r,n){i=r,f(e?e+"{"+t.styles+"}":t.styles),n&&(p.inserted[t.name]=!0)}};return p.sheet.hydrate(c),p},e5={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},e6=/[A-Z]|^ms/g,e8=/_EMO_([^_]+?)_([^]*?)_EMO_/g,e4=function(e){return 45===e.charCodeAt(1)},e3=function(e){return null!=e&&"boolean"!=typeof e},e7=function(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}(function(e){return e4(e)?e:e.replace(e6,"-$&").toLowerCase()}),e9=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(e8,function(e,t,r){return i={name:t,styles:r,next:i},t})}return 1===e5[e]||e4(e)||"number"!=typeof t||0===t?t:t+"px"};function te(e,t,r){if(null==r)return"";if(void 0!==r.__emotion_styles)return r;switch(typeof r){case"boolean":return"";case"object":if(1===r.anim)return i={name:r.name,styles:r.styles,next:i},r.name;if(void 0!==r.styles){var n=r.next;if(void 0!==n)for(;void 0!==n;)i={name:n.name,styles:n.styles,next:i},n=n.next;return r.styles+";"}return function(e,t,r){var n="";if(Array.isArray(r))for(var a=0;a<r.length;a++)n+=te(e,t,r[a])+";";else for(var i in r){var o=r[i];if("object"!=typeof o)null!=t&&void 0!==t[o]?n+=i+"{"+t[o]+"}":e3(o)&&(n+=e7(i)+":"+e9(i,o)+";");else if(Array.isArray(o)&&"string"==typeof o[0]&&(null==t||void 0===t[o[0]]))for(var s=0;s<o.length;s++)e3(o[s])&&(n+=e7(i)+":"+e9(i,o[s])+";");else{var l=te(e,t,o);switch(i){case"animation":case"animationName":n+=e7(i)+":"+l+";";break;default:n+=i+"{"+l+"}"}}}return n}(e,t,r);case"function":if(void 0!==e){var a=i,o=r(e);return i=a,te(e,t,o)}}if(null==t)return r;var s=t[r];return void 0!==s?s:r}var tt=/label:\s*([^\s;{]+)\s*(;|$)/g;function tr(e,t,r){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var n,a=!0,o="";i=void 0;var s=e[0];null==s||void 0===s.raw?(a=!1,o+=te(r,t,s)):o+=s[0];for(var l=1;l<e.length;l++)o+=te(r,t,e[l]),a&&(o+=s[l]);tt.lastIndex=0;for(var u="";null!==(n=tt.exec(o));)u+="-"+n[1];return{name:function(e){for(var t,r=0,n=0,a=e.length;a>=4;++n,a-=4)t=(65535&(t=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))*0x5bd1e995+((t>>>16)*59797<<16),t^=t>>>24,r=(65535&t)*0x5bd1e995+((t>>>16)*59797<<16)^(65535&r)*0x5bd1e995+((r>>>16)*59797<<16);switch(a){case 3:r^=(255&e.charCodeAt(n+2))<<16;case 2:r^=(255&e.charCodeAt(n+1))<<8;case 1:r^=255&e.charCodeAt(n),r=(65535&r)*0x5bd1e995+((r>>>16)*59797<<16)}return r^=r>>>13,(((r=(65535&r)*0x5bd1e995+((r>>>16)*59797<<16))^r>>>15)>>>0).toString(36)}(o)+u,styles:o,next:i}}function tn(e,t,r){var n="";return r.split(" ").forEach(function(r){void 0!==e[r]?t.push(e[r]+";"):r&&(n+=r+" ")}),n}var ta=function(e,t,r){var n=e.key+"-"+t.name;!1===r&&void 0===e.registered[n]&&(e.registered[n]=t.styles)},ti=function(e,t,r){ta(e,t,r);var n=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a=t;do e.insert(t===a?"."+n:"",a,e.sheet,!0),a=a.next;while(void 0!==a)}};function to(e,t){if(void 0===e.inserted[t.name])return e.insert("",t,e.sheet,!0)}function ts(e,t,r){var n=[],a=tn(e,n,r);return n.length<2?r:a+t(n)}var tl=function(e){var t=e2(e);t.sheet.speedy=function(e){this.isSpeedy=e},t.compat=!0;var r=function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];var a=tr(r,t.registered,void 0);return ti(t,a,!1),t.key+"-"+a.name};return{css:r,cx:function(){for(var e=arguments.length,n=Array(e),a=0;a<e;a++)n[a]=arguments[a];return ts(t.registered,r,tu(n))},injectGlobal:function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];var a=tr(r,t.registered);to(t,a)},keyframes:function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];var a=tr(r,t.registered),i="animation-"+a.name;return to(t,{name:a.name,styles:"@keyframes "+i+"{"+a.styles+"}"}),i},hydrate:function(e){e.forEach(function(e){t.inserted[e]=!0})},flush:function(){t.registered={},t.inserted={},t.sheet.flush()},sheet:t.sheet,cache:t,getRegisteredStyles:tn.bind(null,t.registered),merge:ts.bind(null,t.registered,r)}},tu=function e(t){for(var r="",n=0;n<t.length;n++){var a=t[n];if(null!=a){var i=void 0;switch(typeof a){case"boolean":break;case"object":if(Array.isArray(a))i=e(a);else for(var o in i="",a)a[o]&&o&&(i&&(i+=" "),i+=o);break;default:i=a}i&&(r&&(r+=" "),r+=i)}}return r};let tc=(e,t=!1,r="")=>{let{variables:n={},...a}=e,i={light:{diffViewerBackground:"#fff",diffViewerColor:"#212529",addedBackground:"#e6ffed",addedColor:"#24292e",removedBackground:"#ffeef0",removedColor:"#24292e",changedBackground:"#fffbdd",wordAddedBackground:"#acf2bd",wordRemovedBackground:"#fdb8c0",addedGutterBackground:"#cdffd8",removedGutterBackground:"#ffdce0",gutterBackground:"#f7f7f7",gutterBackgroundDark:"#f3f1f1",highlightBackground:"#fffbdd",highlightGutterBackground:"#fff5b1",codeFoldGutterBackground:"#dbedff",codeFoldBackground:"#f1f8ff",emptyLineBackground:"#fafbfc",gutterColor:"#212529",addedGutterColor:"#212529",removedGutterColor:"#212529",codeFoldContentColor:"#212529",diffViewerTitleBackground:"#fafbfc",diffViewerTitleColor:"#212529",diffViewerTitleBorderColor:"#eee",...n.light||{}},dark:{diffViewerBackground:"#2e303c",diffViewerColor:"#FFF",addedBackground:"#044B53",addedColor:"white",removedBackground:"#632F34",removedColor:"white",changedBackground:"#3e302c",wordAddedBackground:"#055d67",wordRemovedBackground:"#7d383f",addedGutterBackground:"#034148",removedGutterBackground:"#632b30",gutterBackground:"#2c2f3a",gutterBackgroundDark:"#262933",highlightBackground:"#2a3967",highlightGutterBackground:"#2d4077",codeFoldGutterBackground:"#262831",codeFoldBackground:"#262831",emptyLineBackground:"#363946",gutterColor:"#666c87",addedGutterColor:"#8c8c8c",removedGutterColor:"#8c8c8c",codeFoldContentColor:"#656a8b",diffViewerTitleBackground:"#2f323e",diffViewerTitleColor:"#757a9b",diffViewerTitleBorderColor:"#353846",...n.dark||{}}},o=t?i.dark:i.light,{css:s,cx:l}=tl({key:"react-diff",nonce:r}),u=s({width:"auto",label:"content"}),c=s({label:"split-view"}),d=s({background:o.diffViewerTitleBackground,color:o.diffViewerTitleColor,padding:"0.5em 1em",display:"flex",alignItems:"center",gap:"0.5em",fontFamily:"monospace",fill:o.diffViewerTitleColor}),f=s({width:"100%",minWidth:"1000px",overflowX:"auto",tableLayout:"fixed",background:o.diffViewerBackground,pre:{margin:0,whiteSpace:"pre-wrap",lineHeight:"1.6em",width:"fit-content"},label:"diff-container",borderCollapse:"collapse"}),p=s({overflow:"hidden",width:"100%"}),m=s({color:o.diffViewerColor,whiteSpace:"pre-wrap",fontFamily:"monospace",lineBreak:"anywhere",textDecoration:"none",label:"content-text"}),g=s({userSelect:"none",label:"unselectable"}),y=s({background:"transparent",border:"none",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",margin:0,label:"all-expand-button",":hover":{fill:o.addedGutterColor},":focus":{outline:`1px ${o.addedGutterColor} solid`}}),v=s({background:o.diffViewerTitleBackground,padding:"0.5em",lineHeight:"1.4em",height:"2.4em",overflow:"hidden",width:"50%",borderBottom:`1px solid ${o.diffViewerTitleBorderColor}`,label:"title-block",":last-child":{borderLeft:`1px solid ${o.diffViewerTitleBorderColor}`},[`.${m}`]:{color:o.diffViewerTitleColor}}),b=s({color:o.gutterColor,label:"line-number"}),w=s({background:o.removedBackground,color:o.removedColor,pre:{color:o.removedColor},[`.${b}`]:{color:o.removedGutterColor},label:"diff-removed"}),x=s({background:o.addedBackground,color:o.addedColor,pre:{color:o.addedColor},[`.${b}`]:{color:o.addedGutterColor},label:"diff-added"}),$=s({background:o.changedBackground,[`.${b}`]:{color:o.gutterColor},label:"diff-changed"}),k=s({padding:2,display:"inline-flex",borderRadius:4,wordBreak:"break-all",label:"word-diff"}),S=s({background:o.wordAddedBackground,textDecoration:"none",label:"word-added"}),_=s({background:o.wordRemovedBackground,textDecoration:"none",label:"word-removed"}),j=s({backgroundColor:o.codeFoldGutterBackground,label:"code-fold-gutter",minWidth:"50px",width:"50px"}),E=s({padding:""}),C=s({background:o.codeFoldBackground,cursor:"pointer",display:"inline",margin:0,border:"none",label:"code-fold-expand-button"}),P=s({color:o.codeFoldContentColor,fontFamily:"monospace",label:"code-fold-content"}),N=s({display:"block",width:"10px",height:"10px",backgroundColor:"#ddd",borderWidth:"1px",borderStyle:"solid",borderColor:o.diffViewerTitleBorderColor}),A=s({backgroundColor:o.wordAddedBackground}),T=s({backgroundColor:o.wordRemovedBackground}),O=s({backgroundColor:o.codeFoldBackground,height:40,fontSize:14,alignItems:"center",userSelect:"none",fontWeight:700,label:"code-fold",a:{textDecoration:"underline !important",cursor:"pointer",pre:{display:"inline"}}}),D=s({backgroundColor:o.emptyLineBackground,label:"empty-line"}),M=s({width:28,paddingLeft:10,paddingRight:10,userSelect:"none",label:"marker",[`&.${x}`]:{pre:{color:o.addedColor}},[`&.${w}`]:{pre:{color:o.removedColor}}}),R=s({background:o.highlightBackground,label:"highlighted-line",[`.${S}, .${_}`]:{backgroundColor:"initial"}}),L=s({label:"highlighted-gutter"}),I=s({userSelect:"none",minWidth:50,width:"50px",padding:"0 10px",whiteSpace:"nowrap",label:"gutter",textAlign:"right",background:o.gutterBackground,"&:hover":{cursor:"pointer",background:o.gutterBackgroundDark,pre:{opacity:1}},pre:{opacity:.5},[`&.${x}`]:{background:o.addedGutterBackground},[`&.${w}`]:{background:o.removedGutterBackground},[`&.${L}`]:{background:o.highlightGutterBackground,"&:hover":{background:o.highlightGutterBackground}}}),F=s({"&:hover":{background:o.gutterBackground,cursor:"initial"},label:"empty-gutter"}),B={diffContainer:f,diffRemoved:w,diffAdded:x,diffChanged:$,splitView:c,marker:M,highlightedGutter:L,highlightedLine:R,gutter:I,line:s({verticalAlign:"baseline",label:"line",textDecoration:"none"}),lineContent:p,wordDiff:k,wordAdded:S,summary:d,block:N,blockAddition:A,blockDeletion:T,wordRemoved:_,noSelect:g,codeFoldGutter:j,codeFoldExpandButton:C,codeFoldContentContainer:E,codeFold:O,emptyGutter:F,emptyLine:D,lineNumber:b,contentText:m,content:u,column:s({}),codeFoldContent:P,titleBlock:v,allExpandButton:y},z=Object.keys(a).reduce((e,t)=>({...e,...{[t]:s(a[t])}}),{});return Object.keys(B).reduce((e,t)=>({...e,...{[t]:z[t]?l(B[t],z[t]):B[t]}}),{})};function td(){return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",width:"16",height:"16",children:[(0,l.jsx)("title",{children:"fold"}),(0,l.jsx)("path",{d:"M10.896 2H8.75V.75a.75.75 0 0 0-1.5 0V2H5.104a.25.25 0 0 0-.177.427l2.896 2.896a.25.25 0 0 0 .354 0l2.896-2.896A.25.25 0 0 0 10.896 2ZM8.75 15.25a.75.75 0 0 1-1.5 0V14H5.104a.25.25 0 0 1-.177-.427l2.896-2.896a.25.25 0 0 1 .354 0l2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25Zm-6.5-6.5a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z"})]})}!function(e){e.LEFT="L",e.RIGHT="R"}(o||(o={}));class tf extends c.Component{styles;static defaultProps={oldValue:"",newValue:"",splitView:!0,highlightLines:[],disableWordDiff:!1,compareMethod:a.CHARS,styles:{},hideLineNumbers:!1,extraLinesSurroundingDiff:3,showDiffOnly:!0,useDarkTheme:!1,linesOffset:0,nonce:""};constructor(e){super(e),this.state={expandedBlocks:[],noSelect:void 0}}resetCodeBlocks=()=>this.state.expandedBlocks.length>0&&(this.setState({expandedBlocks:[]}),!0);onBlockExpand=e=>{let t=this.state.expandedBlocks.slice();t.push(e),this.setState({expandedBlocks:t})};computeStyles=(function(e,t){void 0===t&&(t=f);var r=null;function n(){for(var n=[],a=0;a<arguments.length;a++)n[a]=arguments[a];if(r&&r.lastThis===this&&t(n,r.lastArgs))return r.lastResult;var i=e.apply(this,n);return r={lastResult:i,lastArgs:n,lastThis:this},i}return n.clear=function(){r=null},n})(tc);onLineNumberClickProxy=e=>this.props.onLineNumberClick?t=>this.props.onLineNumberClick(e,t):()=>{};renderWordDiff=(e,t)=>e.map((e,r)=>{let a=t?t(e.value):e.value;if("string"==typeof a)return e.type===n.ADDED?(0,l.jsx)("ins",{className:u(this.styles.wordDiff,{[this.styles.wordAdded]:e.type===n.ADDED}),children:a},r):e.type===n.REMOVED?(0,l.jsx)("del",{className:u(this.styles.wordDiff,{[this.styles.wordRemoved]:e.type===n.REMOVED}),children:a},r):(0,l.jsx)("span",{className:u(this.styles.wordDiff),children:a},r)});renderLine=(e,t,r,a,i,s)=>{let c,d=`${r}-${e}`,f=`${s}-${i}`,p=this.props.highlightLines.includes(d)||this.props.highlightLines.includes(f),m=t===n.ADDED,g=t===n.REMOVED,y=t===n.CHANGED,v=Array.isArray(a);c=v?this.renderWordDiff(a,this.props.renderContent):this.props.renderContent?this.props.renderContent(a):a;let b="div";return m&&!v?b="ins":g&&!v&&(b="del"),(0,l.jsxs)(l.Fragment,{children:[!this.props.hideLineNumbers&&(0,l.jsx)("td",{onClick:e&&this.onLineNumberClickProxy(d),className:u(this.styles.gutter,{[this.styles.emptyGutter]:!e,[this.styles.diffAdded]:m,[this.styles.diffRemoved]:g,[this.styles.diffChanged]:y,[this.styles.highlightedGutter]:p}),children:(0,l.jsx)("pre",{className:this.styles.lineNumber,children:e})}),!this.props.splitView&&!this.props.hideLineNumbers&&(0,l.jsx)("td",{onClick:i&&this.onLineNumberClickProxy(f),className:u(this.styles.gutter,{[this.styles.emptyGutter]:!i,[this.styles.diffAdded]:m,[this.styles.diffRemoved]:g,[this.styles.diffChanged]:y,[this.styles.highlightedGutter]:p}),children:(0,l.jsx)("pre",{className:this.styles.lineNumber,children:i})}),this.props.renderGutter?this.props.renderGutter({lineNumber:e,type:t,prefix:r,value:a,additionalLineNumber:i,additionalPrefix:s,styles:this.styles}):null,(0,l.jsx)("td",{className:u(this.styles.marker,{[this.styles.emptyLine]:!c,[this.styles.diffAdded]:m,[this.styles.diffRemoved]:g,[this.styles.diffChanged]:y,[this.styles.highlightedLine]:p}),children:(0,l.jsxs)("pre",{children:[m&&"+",g&&"-"]})}),(0,l.jsx)("td",{className:u(this.styles.content,{[this.styles.emptyLine]:!c,[this.styles.diffAdded]:m,[this.styles.diffRemoved]:g,[this.styles.diffChanged]:y,[this.styles.highlightedLine]:p,left:r===o.LEFT,right:r===o.RIGHT}),onMouseDown:()=>{let e=document.getElementsByClassName(r===o.LEFT?"right":"left");for(let t=0;t<e.length;t++)e.item(t).classList.add(this.styles.noSelect)},title:m&&!v?"Added line":g&&!v?"Removed line":void 0,children:(0,l.jsx)(b,{className:this.styles.contentText,children:c})})]})};renderSplitView=({left:e,right:t},r)=>(0,l.jsxs)("tr",{className:this.styles.line,children:[this.renderLine(e.lineNumber,e.type,o.LEFT,e.value),this.renderLine(t.lineNumber,t.type,o.RIGHT,t.value)]},r);renderInlineView=({left:e,right:t},r)=>{let a;return e.type===n.REMOVED&&t.type===n.ADDED?(0,l.jsxs)(c.Fragment,{children:[(0,l.jsx)("tr",{className:this.styles.line,children:this.renderLine(e.lineNumber,e.type,o.LEFT,e.value,null)}),(0,l.jsx)("tr",{className:this.styles.line,children:this.renderLine(null,t.type,o.RIGHT,t.value,t.lineNumber,o.RIGHT)})]},r):(e.type===n.REMOVED&&(a=this.renderLine(e.lineNumber,e.type,o.LEFT,e.value,null)),e.type===n.DEFAULT&&(a=this.renderLine(e.lineNumber,e.type,o.LEFT,e.value,t.lineNumber,o.RIGHT)),t.type===n.ADDED&&(a=this.renderLine(null,t.type,o.RIGHT,t.value,t.lineNumber)),(0,l.jsx)("tr",{className:this.styles.line,children:a},r))};onBlockClickProxy=e=>()=>this.onBlockExpand(e);renderSkippedLineIndicator=(e,t,r,n)=>{let{hideLineNumbers:a,splitView:i}=this.props,o=this.props.codeFoldMessageRenderer?this.props.codeFoldMessageRenderer(e,r,n):(0,l.jsxs)("span",{className:this.styles.codeFoldContent,children:["Expand ",e," lines ..."]}),s=(0,l.jsx)("td",{className:this.styles.codeFoldContentContainer,children:(0,l.jsx)("button",{type:"button",className:this.styles.codeFoldExpandButton,onClick:this.onBlockClickProxy(t),tabIndex:0,children:o})}),d=!i&&!a;return(0,l.jsxs)("tr",{className:this.styles.codeFold,children:[!a&&(0,l.jsx)("td",{className:this.styles.codeFoldGutter}),this.props.renderGutter?(0,l.jsx)("td",{className:this.styles.codeFoldGutter}):null,(0,l.jsx)("td",{className:u({[this.styles.codeFoldGutter]:d})}),d?(0,l.jsxs)(c.Fragment,{children:[(0,l.jsx)("td",{}),s]}):(0,l.jsxs)(c.Fragment,{children:[s,this.props.renderGutter?(0,l.jsx)("td",{}):null,(0,l.jsx)("td",{}),(0,l.jsx)("td",{}),a?null:(0,l.jsx)("td",{})]})]},`${r}-${n}`)};renderDiff=()=>{let{oldValue:e,newValue:t,splitView:r,disableWordDiff:n,compareMethod:a,linesOffset:i}=this.props,{lineInformation:o,diffLines:s}=eh(e,t,n,a,i,this.props.alwaysShowLines),{lineBlocks:u,blocks:d}=function(e,t,r){let n,a=0,i={},o=[];return e.forEach((e,s)=>{let l=t.some(e=>e>=s-r&&e<=s+r);l||void 0!==n?!l&&n?(n.endLine=s,n.lines++,i[s]=n.index):n=void 0:(n={index:a,startLine:s,endLine:s,lines:1},o.push(n),i[s]=n.index,a++)}),{lineBlocks:i,blocks:o}}(o,s,this.props.extraLinesSurroundingDiff<0?0:Math.round(this.props.extraLinesSurroundingDiff));return{diffNodes:o.map((e,t)=>{if(this.props.showDiffOnly){let r=u[t];if(void 0!==r){let n=d[r].endLine===t;if(!this.state.expandedBlocks.includes(r)&&n)return(0,l.jsx)(c.Fragment,{children:this.renderSkippedLineIndicator(d[r].lines,r,e.left.lineNumber,e.right.lineNumber)},t);if(!this.state.expandedBlocks.includes(r))return null}}return r?this.renderSplitView(e,t):this.renderInlineView(e,t)}),blocks:d,lineInformation:o}};render=()=>{let{oldValue:e,newValue:t,useDarkTheme:r,leftTitle:i,rightTitle:o,splitView:s,compareMethod:c,hideLineNumbers:d,nonce:f}=this.props;if("string"==typeof c&&c!==a.JSON&&("string"!=typeof e||"string"!=typeof t))throw Error('"oldValue" and "newValue" should be strings');this.styles=this.computeStyles(this.props.styles,r,f);let p=this.renderDiff(),m=3,g=4;d&&(m-=1,g-=1),this.props.renderGutter&&(m+=1,g+=1);let y=0,v=0;for(let e of p.lineInformation)e.left.type===n.ADDED&&v++,e.right.type===n.ADDED&&v++,e.left.type===n.REMOVED&&y++,e.right.type===n.REMOVED&&y++;let b=y+v,w=Math.round(v/b*100),x=[];for(let e=0;e<5;e++)w>20*e?x.push((0,l.jsx)("span",{className:u(this.styles.block,this.styles.blockAddition)},e)):x.push((0,l.jsx)("span",{className:u(this.styles.block,this.styles.blockDeletion)},e));let $=this.state.expandedBlocks.length===p.blocks.length;return(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:this.styles.summary,role:"banner",children:[(0,l.jsx)("button",{type:"button",className:this.styles.allExpandButton,onClick:()=>{this.setState({expandedBlocks:$?[]:p.blocks.map(e=>e.index)})},children:$?(0,l.jsx)(td,{}):(0,l.jsx)(ep,{})})," ",b,(0,l.jsx)("div",{style:{display:"flex",gap:"1px"},children:x}),this.props.summary?(0,l.jsx)("span",{children:this.props.summary}):null]}),(0,l.jsx)("table",{className:u(this.styles.diffContainer,{[this.styles.splitView]:s}),onMouseUp:()=>{let e=document.getElementsByClassName("right");for(let t=0;t<e.length;t++)e.item(t).classList.remove(this.styles.noSelect);let t=document.getElementsByClassName("left");for(let e=0;e<t.length;e++)t.item(e).classList.remove(this.styles.noSelect)},children:(0,l.jsxs)("tbody",{children:[(0,l.jsxs)("tr",{children:[this.props.hideLineNumbers?null:(0,l.jsx)("td",{width:"50px"}),s||this.props.hideLineNumbers?null:(0,l.jsx)("td",{width:"50px"}),this.props.renderGutter?(0,l.jsx)("td",{width:"50px"}):null,(0,l.jsx)("td",{width:"28px"}),(0,l.jsx)("td",{width:"100%"}),s?(0,l.jsxs)(l.Fragment,{children:[this.props.hideLineNumbers?null:(0,l.jsx)("td",{width:"50px"}),this.props.renderGutter?(0,l.jsx)("td",{width:"50px"}):null,(0,l.jsx)("td",{width:"28px"}),(0,l.jsx)("td",{width:"100%"})]}):null]}),i||o?(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{colSpan:s?m:g,className:u(this.styles.titleBlock,this.styles.column),children:i?(0,l.jsx)("pre",{className:this.styles.contentText,children:i}):null}),s?(0,l.jsx)("th",{colSpan:m,className:u(this.styles.titleBlock,this.styles.column),children:o?(0,l.jsx)("pre",{className:this.styles.contentText,children:o}):null}):null]}):null,p.diffNodes]})})]})}}let th=tf},11226:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(37699),a=r(55150),i=r(26520),o=r(37549);t.default={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>(0,a.str)`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>(0,a._)`{i: ${e}, j: ${t}}`},code(e){let{gen:t,data:r,$data:s,schema:l,parentSchema:u,schemaCode:c,it:d}=e;if(!s&&!l)return;let f=t.let("valid"),p=u.items?(0,n.getSchemaTypes)(u.items):[];e.block$data(f,function(){let s=t.let("i",(0,a._)`${r}.length`),l=t.let("j");e.setParams({i:s,j:l}),t.assign(f,!0),t.if((0,a._)`${s} > 1`,()=>(p.length>0&&!p.some(e=>"object"===e||"array"===e)?function(i,o){let s=t.name("item"),l=(0,n.checkDataTypes)(p,s,d.opts.strictNumbers,n.DataType.Wrong),u=t.const("indices",(0,a._)`{}`);t.for((0,a._)`;${i}--;`,()=>{t.let(s,(0,a._)`${r}[${i}]`),t.if(l,(0,a._)`continue`),p.length>1&&t.if((0,a._)`typeof ${s} == "string"`,(0,a._)`${s} += "_"`),t.if((0,a._)`typeof ${u}[${s}] == "number"`,()=>{t.assign(o,(0,a._)`${u}[${s}]`),e.error(),t.assign(f,!1).break()}).code((0,a._)`${u}[${s}] = ${i}`)})}:function(n,s){let l=(0,i.useFunc)(t,o.default),u=t.name("outer");t.label(u).for((0,a._)`;${n}--;`,()=>t.for((0,a._)`${s} = ${n}; ${s}--;`,()=>t.if((0,a._)`${l}(${r}[${n}], ${r}[${s}])`,()=>{e.error(),t.assign(f,!1).break(u)})))})(s,l))},(0,a._)`${c} === false`),e.ok(f)}}},11659:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.regexpCode=t.getEsmExportName=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r,t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class n extends r{constructor(e){if(super(),!t.IDENTIFIER.test(e))throw Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}t.Name=n;class a extends r{constructor(e){super(),this._items="string"==typeof e?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;let e=this._items[0];return""===e||'""'===e}get str(){var e;return null!=(e=this._str)?e:this._str=this._items.reduce((e,t)=>`${e}${t}`,"")}get names(){var e;return null!=(e=this._names)?e:this._names=this._items.reduce((e,t)=>(t instanceof n&&(e[t.str]=(e[t.str]||0)+1),e),{})}}function i(e,...t){let r=[e[0]],n=0;for(;n<t.length;)l(r,t[n]),r.push(e[++n]);return new a(r)}t._Code=a,t.nil=new a(""),t._=i;let o=new a("+");function s(e,...t){let r=[u(e[0])],i=0;for(;i<t.length;)r.push(o),l(r,t[i]),r.push(o,u(e[++i]));return function(e){let t=1;for(;t<e.length-1;){if(e[t]===o){var r,a;let i=(r=e[t-1],'""'===(a=e[t+1])?r:'""'===r?a:"string"==typeof r?a instanceof n||'"'!==r[r.length-1]?void 0:"string"!=typeof a?`${r.slice(0,-1)}${a}"`:'"'===a[0]?r.slice(0,-1)+a.slice(1):void 0:"string"!=typeof a||'"'!==a[0]||r instanceof n?void 0:`"${r}${a.slice(1)}`);if(void 0!==i){e.splice(t-1,3,i);continue}e[t++]="+"}t++}}(r),new a(r)}function l(e,t){var r;t instanceof a?e.push(...t._items):t instanceof n?e.push(t):e.push("number"==typeof(r=t)||"boolean"==typeof r||null===r?r:u(Array.isArray(r)?r.join(","):r))}function u(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.str=s,t.addCodeArg=l,t.strConcat=function(e,t){return t.emptyStr()?e:e.emptyStr()?t:s`${e}${t}`},t.stringify=function(e){return new a(u(e))},t.safeStringify=u,t.getProperty=function(e){return"string"==typeof e&&t.IDENTIFIER.test(e)?new a(`.${e}`):i`[${e}]`},t.getEsmExportName=function(e){if("string"==typeof e&&t.IDENTIFIER.test(e))return new a(`${e}`);throw Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)},t.regexpCode=function(e){return new a(e.toString())}},12036:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(55032),a=r(93653);let i=function e(t,r){let{i18n:i,keepPresentationalFields:o,labelPrefix:s,moveSubFieldsToTop:l=!1,pathPrefix:u}="boolean"==typeof r?{keepPresentationalFields:r}:r??{};return t.reduce((t,c)=>{if("group"===c.type&&"fields"in c)if(l){let r="name"in c&&"string"==typeof c.name&&!!c.name,a="name"in c?c.name:void 0,d="label"in c&&c.label&&i?(0,n.sC)(c.label,i):void 0,f=s?`${s} > ${d??a}`:d??a,p="name"in c&&c.name?u?`${u}-${c.name}`:c.name:u;t.push(c,...e(c.fields,{i18n:i,keepPresentationalFields:o,labelPrefix:r?f:s,moveSubFieldsToTop:l,pathPrefix:r?p:u}))}else t.push(c);else if("tabs"===c.type&&"tabs"in c)return[...t,...c.tabs.reduce((t,c)=>{if(!(0,a.pz)(c))return[...t,...e(c.fields,r)];if(!l)return[...t,{...c,type:"tab"}];{let r="label"in c&&c.label&&i?(0,n.sC)(c.label,i):void 0,a=s?`${s} > ${r??c.name}`:r??c.name,d=c.name?u?`${u}-${c.name}`:c.name:u;return[...t,...e(c.fields,{i18n:i,keepPresentationalFields:o,labelPrefix:a,moveSubFieldsToTop:l,pathPrefix:d})]}},[])];else if((0,a.sd)(c)&&["collapsible","row"].includes(c.type))t.push(...e(c.fields,r));else if((0,a.Z7)(c)||o&&(0,a.aO)(c)){if("id"===c.name&&void 0!==s)return t;let e="label"in c&&c.label&&i?(0,n.sC)(c.label,i):void 0,r="name"in c?c.name:void 0,a=void 0!==u||void 0!==s;t.push({...c,...l&&a&&{accessor:u&&r?`${u}-${r}`:r??"",labelWithPrefix:s?`${s} > ${e??r}`:e??r}})}return t},[])}},12208:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150);t.default={data:new n.Name("data"),valCxt:new n.Name("valCxt"),instancePath:new n.Name("instancePath"),parentData:new n.Name("parentData"),parentDataProperty:new n.Name("parentDataProperty"),rootData:new n.Name("rootData"),dynamicAnchors:new n.Name("dynamicAnchors"),vErrors:new n.Name("vErrors"),errors:new n.Name("errors"),this:new n.Name("this"),self:new n.Name("self"),scope:new n.Name("scope"),json:new n.Name("json"),jsonPos:new n.Name("jsonPos"),jsonLen:new n.Name("jsonLen"),jsonPart:new n.Name("jsonPart")}},12637:(e,t,r)=>{"use strict";r.d(t,{ShouldRenderTabs:()=>a});var n=r(83548);let a=e=>{let{children:t}=e,{id:r,collectionSlug:a,globalSlug:i}=(0,n.useDocumentInfo)();return a&&("create"!==r?r:null)||i?t:null}},13337:(e,t,r)=>{"use strict";r.d(t,{NavHamburger:()=>o});var n=r(47092),a=r(67252),i=r(83548);r(89688);let o=e=>{let t,r,o=(0,n.c)(6),{baseClass:s}=e,{navOpen:l,setNavOpen:u}=(0,i.useNav)(),c="".concat(s,"__mobile-close");o[0]!==u?(t=()=>{u(!1)},o[0]=u,o[1]=t):t=o[1];let d=l?void 0:-1;return o[2]!==c||o[3]!==t||o[4]!==d?(r=(0,a.jsx)("button",{className:c,onClick:t,tabIndex:d,type:"button",children:(0,a.jsx)(i.Hamburger,{isActive:!0})}),o[2]=c,o[3]=t,o[4]=d,o[5]=r):r=o[5],r}},13376:(e,t,r)=>{"use strict";r.d(t,{APIViewClient:()=>y});var n=r(67252),a=r(83548),i=r(71464),o=r(56156),s=r(98034),l=r(89688),u=r(47092);let c=e=>{let t,r=(0,u.c)(6),{localeOptions:o,onChange:s}=e,{t:l}=(0,i.d)();if(r[0]!==o||r[1]!==s||r[2]!==l){let e;r[4]!==s?(e=e=>s(e),r[4]=s,r[5]=e):e=r[5],t=(0,n.jsx)(a.SelectField,{field:{name:"locale",label:l("general:locale"),options:o},onChange:e,path:"locale"}),r[0]=o,r[1]=s,r[2]=l,r[3]=t}else t=r[3];return t},d={leftCurlyBracket:"{",leftSquareBracket:"[",rightCurlyBracket:"}",rightSquareBracket:"]"},f="query-inspector",p=e=>{let{type:t,comma:r=!1,position:a}=e,i="object"===t?d.rightCurlyBracket:d.rightSquareBracket,o="object"===t?d.leftCurlyBracket:d.leftSquareBracket;return(0,n.jsxs)("span",{className:"".concat(f,"__bracket ").concat(f,"__bracket--position-").concat(a),children:["end"===a?i:o,"end"===a&&r?",":null]})},m=e=>{let t,r=(0,u.c)(2),{isEmpty:i,object:o,objectKey:s,parentType:c,trailingComma:d}=e,g=void 0!==i&&i,y=void 0===c?"object":c,v=void 0!==d&&d,b=o?Object.keys(o):[],w=b.length,[x,$]=l.useState(!0),k="object"===y||"array"===y;return r[0]!==x?(t=()=>$(!x),r[0]=x,r[1]=t):t=r[1],(0,n.jsxs)("li",{className:k?"".concat(f,"__row-line--nested"):"",children:[(0,n.jsxs)("button",{"aria-label":"toggle",className:"".concat(f,"__list-toggle ").concat(g?"".concat(f,"__list-toggle--empty"):""),onClick:t,type:"button",children:[g?null:(0,n.jsx)(a.ChevronIcon,{className:"".concat(f,"__toggle-row-icon ").concat(f,"__toggle-row-icon--").concat(x?"open":"closed")}),(0,n.jsxs)("span",{children:[s&&'"'.concat(s,'": '),(0,n.jsx)(p,{position:"start",type:y}),g?(0,n.jsx)(p,{comma:v,position:"end",type:y}):null]})]}),(0,n.jsx)("ul",{className:"".concat(f,"__json-children ").concat(k?"".concat(f,"__json-children--nested"):""),children:x&&b.map((e,t)=>{let r,a=o[e],i=t===w-1;if(null===a?r="null":a instanceof Date?(r="date",a=a.toISOString()):r=Array.isArray(a)?"array":"object"==typeof a?"object":"number"==typeof a?"number":"boolean"==typeof a?"boolean":"string","object"===r||"array"===r)return(0,n.jsx)(m,{isEmpty:0===a.length||0===Object.keys(a).length,object:a,objectKey:"object"===y?e:void 0,parentType:r,trailingComma:!i},"".concat(e,"-").concat(t));if("date"===r||"string"===r||"null"===r||"number"===r||"boolean"===r){let o=!!("object"===y&&e),l=["".concat(f,"__row-line"),"".concat(f,"__value-type--").concat(r),"".concat(f,"__row-line--").concat(s?"nested":"top")].filter(Boolean).join(" ");return(0,n.jsxs)("li",{className:l,children:[o?(0,n.jsx)("span",{children:'"'.concat(e,'": ')}):null,(0,n.jsx)("span",{className:"".concat(f,"__value"),children:JSON.stringify(a)}),i?"":","]},"".concat(e,"-").concat(t))}})}),!g&&(0,n.jsx)("span",{className:k?"".concat(f,"__bracket--nested"):"",children:(0,n.jsx)(p,{comma:v,position:"end",type:y})})]})},g="query-inspector",y=()=>{var e,t,r,u;let{id:d,collectionSlug:f,globalSlug:p,initialData:y}=(0,a.useDocumentInfo)(),v=(0,s.useSearchParams)(),{i18n:b,t:w}=(0,i.d)(),{code:x}=(0,a.useLocale)(),{config:{defaultDepth:$,localization:k,routes:{api:S},serverURL:_},getEntityConfig:j}=(0,o.b)(),E=j({collectionSlug:f}),C=j({globalSlug:p}),P=k&&k.locales.map(e=>({label:e.label,value:e.code})),N=!1,A="";E&&(N=!!(null==(r=E.versions)?void 0:r.drafts),A="/".concat(f,"/").concat(d)),C&&(N=!!(null==(u=C.versions)?void 0:u.drafts),A="/globals/".concat(p));let[T,O]=l.useState(y),[D,M]=l.useState("true"===v.get("draft")),[R,L]=l.useState((null==v?void 0:v.get("locale"))||x),[I,F]=l.useState(v.get("depth")||$.toString()),[B,z]=l.useState(!0),[V,U]=l.useState(!1),q=new URLSearchParams({depth:I,draft:String(D),locale:R}).toString(),H="".concat(_).concat(S).concat(A,"?").concat(q);return l.useEffect(()=>{(async()=>{try{let e=await fetch(H,{credentials:B?"include":"omit",headers:{"Accept-Language":b.language},method:"GET"});try{let t=await e.json();O(t)}catch(e){a.toast.error("Error parsing response"),console.error(e)}}catch(e){a.toast.error("Error making request"),console.error(e)}})()},[b.language,H,B]),(0,n.jsxs)(a.Gutter,{className:[g,V&&"".concat(g,"--fullscreen")].filter(Boolean).join(" "),right:!1,children:[(0,n.jsx)(a.SetDocumentStepNav,{collectionSlug:f,globalLabel:null==C?void 0:C.label,globalSlug:p,id:d,pluralLabel:E?null==E||null==(e=E.labels)?void 0:e.plural:void 0,useAsTitle:E?null==E||null==(t=E.admin)?void 0:t.useAsTitle:void 0,view:"API"}),(0,n.jsxs)("div",{className:"".concat(g,"__configuration"),children:[(0,n.jsxs)("div",{className:"".concat(g,"__api-url"),children:[(0,n.jsxs)("span",{className:"".concat(g,"__label"),children:["API URL ",(0,n.jsx)(a.CopyToClipboard,{value:H})]}),(0,n.jsx)("a",{href:H,rel:"noopener noreferrer",target:"_blank",children:H})]}),(0,n.jsx)(a.Form,{initialState:{authenticated:{initialValue:B||!1,valid:!0,value:B||!1},depth:{initialValue:Number(I||0),valid:!0,value:Number(I||0)},draft:{initialValue:D||!1,valid:!0,value:D||!1},locale:{initialValue:R,valid:!0,value:R}},children:(0,n.jsxs)("div",{className:"".concat(g,"__form-fields"),children:[(0,n.jsxs)("div",{className:"".concat(g,"__filter-query-checkboxes"),children:[N&&(0,n.jsx)(a.CheckboxField,{field:{name:"draft",label:w("version:draft")},onChange:()=>M(!D),path:"draft"}),(0,n.jsx)(a.CheckboxField,{field:{name:"authenticated",label:w("authentication:authenticated")},onChange:()=>z(!B),path:"authenticated"})]}),P&&(0,n.jsx)(c,{localeOptions:P,onChange:L}),(0,n.jsx)(a.NumberField,{field:{name:"depth",admin:{step:1},label:w("general:depth"),max:10,min:0},onChange:e=>F(null==e?void 0:e.toString()),path:"depth"})]})})]}),(0,n.jsxs)("div",{className:"".concat(g,"__results-wrapper"),children:[(0,n.jsx)("div",{className:"".concat(g,"__toggle-fullscreen-button-container"),children:(0,n.jsx)("button",{"aria-label":"toggle fullscreen",className:"".concat(g,"__toggle-fullscreen-button"),onClick:()=>U(!V),type:"button",children:(0,n.jsx)(a.MinimizeMaximizeIcon,{isMinimized:!V})})}),(0,n.jsx)("div",{className:"".concat(g,"__results"),children:(0,n.jsx)(m,{object:T})})]})]})}},13401:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(26520);t.default={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){void 0===t.if&&(0,n.checkStrictMode)(r,`"${e}" without "if" is ignored`)}}},13492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n={src:"/_next/static/media/payload-favicon-dark.c322d81c.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAJ1BMVEVMaXEyMjIzMzM1NTU2NjYzMzMzMzMzMzMyMjIzMzM1NTUyMjIzMzMrTeFiAAAADXRSTlMAg8EpEmvzdo/kIEOg8UAOkgAAAAlwSFlzAAAuIwAALiMBeKU/dgAAADZJREFUeJw1y7kBwDAIBME9PglQ//U68uQDuJkDESvVDbIOTB9k1ssTemRyxekB3yFuSZXw9w8kbAD/5SnsMwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}},15352:(e,t,r)=>{"use strict";r.d(t,{KS:()=>function e(t){if("object"!=typeof t||null===t||!("$$typeof"in t)||"symbol"!=typeof t.$$typeof){if("object"!=typeof t||null===t)return t;if(Array.isArray(t))return t.map(t=>"object"!=typeof t||null===t?t:e(t));if(t instanceof Date)return new Date(t);let r={};for(let n in t){let a=t[n];r[n]="object"!=typeof a||null===a?a:e(a)}return r}}});var n=r(86709).hp;function a(e){return e instanceof n?n.from(e):new e.constructor(e.buffer.slice(),e.byteOffset,e.length)}let i=new Map;i.set(Date,e=>new Date(e)),i.set(Map,(e,t)=>new Map(s(Array.from(e),t))),i.set(Set,(e,t)=>new Set(s(Array.from(e),t))),i.set(RegExp,e=>new RegExp(e.source,e.flags));let o=null;function s(e,t){let r=Object.keys(e),n=Array(r.length);for(let s=0;s<r.length;s++){let l=r[s],u=e[l];"object"!=typeof u||null===u?n[l]=u:u instanceof RegExp?n[l]=new RegExp(u.source,u.flags):u.constructor!==Object&&(o=i.get(u.constructor))?n[l]=o(u,t):ArrayBuffer.isView(u)?n[l]=a(u):n[l]=t(u)}return n}let l=e=>{if("object"!=typeof e||null===e)return e;if(Array.isArray(e))return s(e,l);if(e instanceof RegExp)return new RegExp(e.source,e.flags);if(e.constructor!==Object&&(o=i.get(e.constructor)))return o(e,l);let t={};for(let r in e){if(!1===Object.hasOwnProperty.call(e,r))continue;let n=e[r];"object"!=typeof n||null===n?t[r]=n:n instanceof RegExp?t[r]=new RegExp(n.source,n.flags):n.constructor!==Object&&(o=i.get(n.constructor))?t[r]=o(n,l):ArrayBuffer.isView(n)?t[r]=a(n):t[r]=l(n)}return t}},15540:(e,t,r)=>{"use strict";r.d(t,{Y:()=>a,d:()=>i});let n=e=>0===Object.keys(e).length,a=(e,t)=>n(t)?e:n(e)?t:("and"in e&&e.and?e.and.push(t):("or"in e,e={and:[e,t]}),e),i=({collectionConfig:e,search:t,where:r={}})=>{if(t){let i={...r||{}},o=(e.admin.listSearchableFields||[e.admin?.useAsTitle||"id"]).map(e=>({[e]:{like:t}}));o.length>0&&(i=a(i,{or:o})),n(i)||(r=i)}return r}},16100:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n={src:"/_next/static/media/payload-favicon-light.b8a65007.png",height:32,width:32,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEVMaXH///////////////////////////////////////////////////////////////////+kpd7DAAAAEnRSTlMAdo/15mvEgxwpDQSgS8ayQj0ZIUK/AAAACXBIWXMAAC4jAAAuIwF4pT92AAAAOklEQVR4nC3LQQKAIAhFwaeAH9Sy7n/ZNs1+gBqjgMxL8j4Ja4v0hu7jD1toE0EXzQvqTWY3yQL+vj4zgwFqjTrzFAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8}},16625:(e,t,r)=>{"use strict";r.d(t,{j:()=>function e(t,r){var a,i;if(t===r)return!0;if(t&&r&&(a=t.constructor)===r.constructor){if(a===Date)return t.getTime()===r.getTime();if(a===RegExp)return t.toString()===r.toString();if(a===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!a||"object"==typeof t){for(a in i=0,t)if(n.call(t,a)&&++i&&!n.call(r,a)||!(a in r)||!e(t[a],r[a]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r}});var n=Object.prototype.hasOwnProperty},19466:(e,t,r)=>{"use strict";r.d(t,{DefaultVersionView:()=>C});var n=r(67252),a=r(56156),i=r(71464),o=r(83548),s=r(73272),l=r(98034),u=r(89688),c=r(55032),d=r(45311);let f="restore-version",p="restore-version",m=e=>{var t;let r,{className:m,collectionSlug:g,globalSlug:y,label:v,originalDocID:b,status:w,versionDate:x,versionID:$}=e,{config:{routes:{admin:k,api:S},serverURL:_},getEntityConfig:j}=(0,a.b)(),E=j({collectionSlug:g}),{toggleModal:C}=(0,o.useModal)(),P=(0,l.useRouter)(),{i18n:N,t:A}=(0,i.d)(),[T,O]=(0,u.useState)(!1),{startRouteTransition:D}=(0,o.useRouteTransition)(),M=A("version:aboutToRestoreGlobal",{label:(0,c.sC)(v,N),versionDate:x}),R="".concat(_).concat(S),L="draft"!==w&&(null==E||null==(t=E.versions)?void 0:t.drafts);g&&(R="".concat(R,"/").concat(g,"/versions/").concat($,"?draft=").concat(T),r=(0,d.Q)({adminRoute:k,path:"/collections/".concat(g,"/").concat(b)})),y&&(R="".concat(R,"/globals/").concat(y,"/versions/").concat($,"?draft=").concat(T),r=(0,d.Q)({adminRoute:k,path:"/globals/".concat(y)}));let I=(0,u.useCallback)(async()=>{let e=await s.zG.post(R,{headers:{"Accept-Language":N.language}});if(200===e.status){let t=await e.json();return o.toast.success(t.message),D(()=>P.push(r))}o.toast.error(A("version:problemRestoringVersion"))},[R,r,A,N,P,D]);return(0,n.jsxs)(u.Fragment,{children:[(0,n.jsx)("div",{className:[f,m].filter(Boolean).join(" "),children:(0,n.jsx)(o.Button,{buttonStyle:"pill",className:[L&&"".concat(f,"__button")].filter(Boolean).join(" "),onClick:()=>C(p),size:"small",SubMenuPopupContent:L?()=>(0,n.jsx)(o.PopupList.ButtonGroup,{children:(0,n.jsx)(o.PopupList.Button,{onClick:()=>[O(!0),C(p)],children:A("version:restoreAsDraft")})}):null,children:A("version:restoreThisVersion")})}),(0,n.jsx)(o.ConfirmationModal,{body:M,confirmingLabel:A("version:restoring"),heading:A("version:confirmVersionRestoration"),modalSlug:p,onConfirm:I})]})};var g=r(96900),y=r(4110);let v="compare-version",b=[],w=e=>{let{baseURL:t,draftsEnabled:r,latestDraftVersion:l,latestPublishedVersion:c,onChange:d,parentID:f,value:p,versionID:m}=e,{config:{admin:{dateFormat:w},localization:x}}=(0,a.b)(),{hasPublishedDoc:$}=(0,o.useDocumentInfo)(),[k,S]=(0,u.useState)(b),[_,j]=(0,u.useState)(1),[E,C]=(0,u.useState)(""),{i18n:P,t:N}=(0,i.d)(),A=u.useRef(!1),T=(0,u.useCallback)(async e=>{let{lastLoadedPage:a}=e;if(A.current)return;let i={depth:0,limit:10,page:a,where:{and:[{id:{not_equals:m}}]}};f&&i.where.and.push({parent:{equals:f}}),x&&r&&i.where.and.push({snapshot:{not_equals:!0}});let u=(0,g.A)(i),d=await fetch("".concat(t,"?").concat(u),{credentials:"include",headers:{"Accept-Language":P.language}});if(d.ok){let e=await d.json();if(e.docs.length>0){let t={draft:{currentLabel:N("version:currentDraft"),latestVersion:l,pillStyle:void 0,previousLabel:N("version:draft")},published:{currentLabel:N("version:currentPublishedVersion"),latestVersion:$?c:void 0,pillStyle:"success",previousLabel:N("version:previouslyPublished")}},r=e.docs.map(e=>{let r=e.version._status,a=null,i=e.publishedLocale||void 0,{currentLabel:l,latestVersion:u,pillStyle:c,previousLabel:d}=t[r]||{};if(x&&(null==x?void 0:x.locales)&&i){var f;let e=Array.isArray(i)?i[0]:i,t=x.locales.find(t=>t.code===e),r=(null==t||null==(f=t.label)?void 0:f[null==P?void 0:P.language])||(null==t?void 0:t.label);r&&(a=(0,n.jsx)(o.Pill,{children:r}))}return{label:(0,n.jsxs)("div",{children:[(0,s.Yq)({date:e.updatedAt,i18n:P,pattern:w}),"\xa0\xa0",(0,y.m)(e,u,l,d,c),a]}),value:e.id}});S(e=>[...e,...r]),e.hasNextPage||(A.current=!0),j(e.page)}}else C(N("error:unspecific"))},[w,t,f,m,N,P,l,c]);(0,u.useEffect)(()=>{P.dateFNS&&T({lastLoadedPage:1})},[T,P.dateFNS]);let O=k.filter((e,t,r)=>r.findIndex(t=>t.value===e.value)===t);return(0,u.useEffect)(()=>{O.length>0&&!p&&d(O[0])},[O,p,d]),(0,n.jsxs)("div",{className:[o.fieldBaseClass,v,E&&"error-loading"].filter(Boolean).join(" "),children:[(0,n.jsx)("div",{className:"".concat(v,"__label"),children:N("version:compareVersion")}),!E&&(0,n.jsx)(o.ReactSelect,{isClearable:!1,isSearchable:!1,onChange:d,onMenuScrollToBottom:()=>{T({lastLoadedPage:_+1})},options:O,placeholder:N("version:selectVersionToCompare"),value:p}),E&&(0,n.jsx)("div",{className:"".concat(v,"__error-loading"),children:E})]})};var x=r(47092);let $="select-version-locales",k=e=>{let t,r,a=(0,x.c)(8),{onChange:s,options:l,value:u}=e,{t:c}=(0,i.d)(),{code:d}=(0,o.useLocale)();a[0]!==d?(t=e=>e.map(e=>"string"==typeof e.label?e:"string"!=typeof e.label&&e.label[d]?{label:e.label[d],value:e.value}:void 0),a[0]=d,a[1]=t):t=a[1];let f=t;return a[2]!==f||a[3]!==s||a[4]!==l||a[5]!==c||a[6]!==u?(r=(0,n.jsxs)("div",{className:$,children:[(0,n.jsx)("div",{className:"".concat($,"__label"),children:c("version:showLocales")}),(0,n.jsx)(o.ReactSelect,{isMulti:!0,onChange:s,options:f(l),placeholder:c("version:selectLocales"),value:f(u)})]}),a[2]=f,a[3]=s,a[4]=l,a[5]=c,a[6]=u,a[7]=r):r=a[7],r};var S=r(20985),_=r(93653);let j=e=>{let t,r,n=(0,x.c)(14),{id:l,collectionConfig:f,collectionSlug:p,doc:m,fields:g,globalConfig:y,globalSlug:v}=e,{config:b}=(0,a.b)(),{setStepNav:w}=(0,o.useStepNav)(),{i18n:$,t:k}=(0,i.d)(),S=(0,o.useLocale)();return n[0]!==f||n[1]!==p||n[2]!==b||n[3]!==m||n[4]!==g||n[5]!==y||n[6]!==v||n[7]!==$||n[8]!==l||n[9]!==S||n[10]!==w||n[11]!==k?(t=()=>{let e=[],{admin:t,routes:r}=b,{dateFormat:n}=t,{admin:a}=r;if(p&&f){var i,o,u;let t="",r=(null==f||null==(i=f.admin)?void 0:i.useAsTitle)||"id",y=null==f||null==(o=f.labels)?void 0:o.plural,v=m.version?m.version:m;if(v)if("id"!==r){let e=g.find(e=>{let t="name"in e?e.name:void 0;return!!((0,_.Z7)(e)&&t===r)});t=e&&v[r]?"localized"in e&&e.localized?null==(u=v[r])?void 0:u[S.code]:v[r]:"[".concat(k("general:untitled"),"]")}else t=m.id;e=[{label:(0,c.sC)(y,$),url:(0,d.Q)({adminRoute:a,path:"/collections/".concat(p)})},{label:t,url:(0,d.Q)({adminRoute:a,path:"/collections/".concat(p,"/").concat(l)})},{label:"Versions",url:(0,d.Q)({adminRoute:a,path:"/collections/".concat(p,"/").concat(l,"/versions")})},{label:(null==m?void 0:m.createdAt)?(0,s.Yq)({date:m.createdAt,i18n:$,pattern:n}):""}]}v&&y&&(e=[{label:y.label,url:(0,d.Q)({adminRoute:a,path:"/globals/".concat(y.slug)})},{label:"Versions",url:(0,d.Q)({adminRoute:a,path:"/globals/".concat(y.slug,"/versions")})},{label:(null==m?void 0:m.createdAt)?(0,s.Yq)({date:m.createdAt,i18n:$,pattern:n}):""}]),w(e)},r=[b,w,p,v,m,l,S,k,$,f,g,y],n[0]=f,n[1]=p,n[2]=b,n[3]=m,n[4]=g,n[5]=y,n[6]=v,n[7]=$,n[8]=l,n[9]=S,n[10]=w,n[11]=k,n[12]=t,n[13]=r):(t=n[12],r=n[13]),(0,u.useEffect)(t,r),null},E="view-version",C=e=>{var t,r,c;let{canUpdate:d,doc:f,latestDraftVersion:p,latestPublishedVersion:g,modifiedOnly:y,RenderedDiff:v,selectedLocales:b,versionID:x}=e,{config:$,getEntityConfig:_}=(0,a.b)(),C=(0,u.useMemo)(()=>$.localization?$.localization.locales.map(e=>({label:e.label,value:e.code})):[],[$.localization]),{i18n:P}=(0,i.d)(),{id:N,collectionSlug:A,globalSlug:T}=(0,o.useDocumentInfo)(),{startRouteTransition:O}=(0,o.useRouteTransition)(),[D]=(0,u.useState)(()=>_({collectionSlug:A})),[M]=(0,u.useState)(()=>_({globalSlug:T})),[R,L]=(0,u.useState)(b),[I,F]=(0,u.useState)(),B=(0,l.useRouter)(),z=(0,l.usePathname)(),V=(0,l.useSearchParams)(),[U,q]=(0,u.useState)(y);(0,u.useEffect)(()=>{let e=new URLSearchParams(Array.from(V.entries()));I?e.set("compareValue",null==I?void 0:I.value):e.delete("compareValue"),R?e.set("localeCodes",JSON.stringify(R.map(e=>e.value))):e.delete("localeCodes"),!1===U?e.set("modifiedOnly","false"):e.delete("modifiedOnly");let t=e.toString(),r=t?"?".concat(t):"";O(()=>B.push("".concat(z).concat(r)))},[I,z,B,V,R,U,O]);let{admin:{dateFormat:H},localization:W,routes:{api:G},serverURL:K}=$,J=(null==f?void 0:f.updatedAt)?(0,s.Yq)({date:f.updatedAt,i18n:P,pattern:H}):"",Y="".concat(K).concat(G,"/").concat(T?"globals/":"").concat(A||T,"/versions"),Q=!!(null==(t=D||M)?void 0:t.versions.drafts);return(0,n.jsxs)("main",{className:E,children:[(0,n.jsx)(j,{collectionConfig:D,collectionSlug:A,doc:f,fields:null==(r=D||M)?void 0:r.fields,globalConfig:M,globalSlug:T,id:N}),(0,n.jsxs)(o.Gutter,{className:"".concat(E,"__wrap"),children:[(0,n.jsxs)("div",{className:"".concat(E,"__header-wrap"),children:[(0,n.jsx)("p",{className:"".concat(E,"__created-at"),children:P.t("version:versionCreatedOn",{version:P.t((null==f?void 0:f.autosave)?"version:autosavedVersion":"version:version")})}),(0,n.jsxs)("header",{className:"".concat(E,"__header"),children:[(0,n.jsx)("h2",{children:J}),d&&(0,n.jsx)(m,{className:"".concat(E,"__restore"),collectionSlug:A,globalSlug:T,label:(null==D?void 0:D.labels.singular)||(null==M?void 0:M.label),originalDocID:N,status:null==f||null==(c=f.version)?void 0:c._status,versionDate:J,versionID:x}),(0,n.jsx)("span",{className:"".concat(E,"__modifiedCheckBox"),children:(0,n.jsx)(o.CheckboxInput,{checked:U,id:"modifiedOnly",label:P.t("version:modifiedOnly"),onToggle:function(){q(!U)}})})]})]}),(0,n.jsxs)("div",{className:"".concat(E,"__controls"),children:[(0,n.jsx)(w,{baseURL:Y,draftsEnabled:Q,latestDraftVersion:p,latestPublishedVersion:g,onChange:F,parentID:N,value:I,versionID:x}),W&&(0,n.jsx)(k,{onChange:L,options:C,value:R})]}),(0,n.jsx)(S.a,{value:{selectedLocales:R.map(e=>e.value)},children:(null==f?void 0:f.version)&&v})]})]})}},19516:(e,t)=>{"use strict";function r(e,t){return t.rules.some(t=>n(e,t))}function n(e,t){var r;return void 0!==e[t.keyword]||(null==(r=t.definition.implements)?void 0:r.some(t=>void 0!==e[t]))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0,t.schemaHasRulesForType=function({schema:e,self:t},n){let a=t.RULES.types[n];return a&&!0!==a&&r(e,a)},t.shouldUseGroup=r,t.shouldUseRule=n},19697:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=n.operators,i={maximum:{okStr:"<=",ok:a.LTE,fail:a.GT},minimum:{okStr:">=",ok:a.GTE,fail:a.LT},exclusiveMaximum:{okStr:"<",ok:a.LT,fail:a.GTE},exclusiveMinimum:{okStr:">",ok:a.GT,fail:a.LTE}};t.default={keyword:Object.keys(i),type:"number",schemaType:"number",$data:!0,error:{message:({keyword:e,schemaCode:t})=>(0,n.str)`must be ${i[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>(0,n._)`{comparison: ${i[e].okStr}, limit: ${t}}`},code(e){let{keyword:t,data:r,schemaCode:a}=e;e.fail$data((0,n._)`${r} ${i[t].fail} ${a} || isNaN(${r})`)}}},19811:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(19697),a=r(58084),i=r(47756),o=r(95665),s=r(24529),l=r(98232),u=r(44316),c=r(11226),d=r(64360),f=r(89964);t.default=[n.default,a.default,i.default,o.default,s.default,l.default,u.default,c.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},d.default,f.default]},20985:(e,t,r)=>{"use strict";r.d(t,{I:()=>i,a:()=>a});var n=r(89688);let a=(0,n.createContext)({selectedLocales:[]}),i=()=>(0,n.use)(a)},21352:(e,t,r)=>{"use strict";r.d(t,{LivePreviewClient:()=>I});var n=r(47092),a=r(67252),i=r(83548),o=r(56156),s=r(71464),l=r(73272),u=r(98034),c=r(45311),d=r(89688);let f=(0,d.createContext)({appIsReady:!1,breakpoint:void 0,breakpoints:void 0,fieldSchemaJSON:void 0,iframeHasLoaded:!1,iframeRef:void 0,isPopupOpen:!1,measuredDeviceSize:{height:0,width:0},openPopupWindow:()=>{},popupRef:void 0,previewWindowType:"iframe",setAppIsReady:()=>{},setBreakpoint:()=>{},setHeight:()=>{},setIframeHasLoaded:()=>{},setMeasuredDeviceSize:()=>{},setPreviewWindowType:()=>{},setSize:()=>{},setToolbarPosition:()=>{},setWidth:()=>{},setZoom:()=>{},size:{height:0,width:0},toolbarPosition:{x:0,y:0},url:void 0,zoom:1}),p=()=>(0,d.use)(f);var m=r(67617),g=r(93653);let y=(e,t)=>e.reduce((e,r)=>{let n=e;switch(r.type){case"array":e.push({name:r.name,type:r.type,fields:y([...r.fields,{name:"id",type:"text"}],t)});break;case"blocks":e.push({name:r.name,type:r.type,blocks:(r.blockReferences??r.blocks).reduce((e,r)=>{let n="string"==typeof r?t.blocksMap[r]:r;return e[n.slug]={fields:y([...n.fields,{name:"id",type:"text"}],t)},e},{})});break;case"collapsible":case"row":n=n.concat(y(r.fields,t));break;case"group":(0,g.Z7)(r)?e.push({name:r.name,type:r.type,fields:y(r.fields,t)}):n=n.concat(y(r.fields,t));break;case"relationship":case"upload":e.push({name:r.name,type:r.type,hasMany:"hasMany"in r&&!!r.hasMany,relationTo:r.relationTo});break;case"tabs":{let e=[];r.tabs.forEach(n=>{if("name"in n)return void e.push({name:n.name,type:r.type,fields:y(n.fields,t)});e=e.concat(y(n.fields,t))}),n=n.concat(e);break}default:"name"in r&&e.push({name:r.name,type:r.type})}return n},[]),v=({collisionRect:e,droppableContainers:t,...r})=>{let n=t.find(({id:e})=>"live-preview-area"===e),a=(0,m.Qo)({...r,collisionRect:e,droppableContainers:[n]});if(0===a.length)return a;let i=n?.rect?.current;if(e.top>=i.top&&e.left>=i.left&&e.bottom<=i.bottom&&e.right<=i.right)return a},b=(e,t)=>{switch(t.type){case"height":return{...e,height:t.value};case"width":return{...e,width:t.value};default:return{...e,...t?.value||{}}}},w=e=>{let{breakpoints:t,children:r,fieldSchema:n,isPopupOpen:i,openPopupWindow:s,popupRef:l,url:u}=e,[c,p]=(0,d.useState)("iframe"),[g,w]=(0,d.useState)(!1),[x,$]=(0,d.useState)(!1),k=d.useRef(null),[S,_]=(0,d.useState)(!1),{config:j}=(0,o.b)(),[E,C]=(0,d.useState)(1),[P,N]=(0,d.useState)({x:0,y:0}),[A,T]=d.useReducer(b,{height:0,width:0}),[O,D]=(0,d.useState)({height:0,width:0}),[M,R]=d.useState("responsive"),[L]=(0,d.useState)(()=>y(n,j)),I=(0,d.useCallback)(e=>{T({type:"width",value:e})},[T]),F=(0,d.useCallback)(e=>{T({type:"height",value:e})},[T]);(0,d.useEffect)(()=>{let e=null==t?void 0:t.find(e=>e.name===M);e&&"responsive"!==M&&"custom"!==M&&"number"==typeof(null==e?void 0:e.width)&&"number"==typeof(null==e?void 0:e.height)&&T({type:"reset",value:{height:e.height,width:e.width}})},[M,t]),(0,d.useEffect)(()=>{let e=e=>{(null==u?void 0:u.startsWith(e.origin))&&e.data&&"object"==typeof e.data&&"payload-live-preview"===e.data.type&&e.data.ready&&w(!0)};return window.addEventListener("message",e),$(!0),()=>{window.removeEventListener("message",e)}},[u,x]);let B=(0,d.useCallback)(e=>{w(!1),p(e),"popup"===e&&s()},[s]);return(0,d.useEffect)(()=>{(i?"popup":"iframe")!==c&&B("iframe")},[c,i,B]),(0,a.jsx)(f,{value:{appIsReady:g,breakpoint:M,breakpoints:t,fieldSchemaJSON:L,iframeHasLoaded:S,iframeRef:k,isPopupOpen:i,measuredDeviceSize:O,openPopupWindow:s,popupRef:l,previewWindowType:c,setAppIsReady:w,setBreakpoint:R,setHeight:F,setIframeHasLoaded:_,setMeasuredDeviceSize:D,setPreviewWindowType:B,setSize:T,setToolbarPosition:N,setWidth:I,setZoom:C,size:A,toolbarPosition:P,url:u,zoom:E},children:(0,a.jsx)(m.Mp,{collisionDetection:v,onDragEnd:e=>{e.over&&"live-preview-area"===e.over.id&&N({x:P.x+e.delta.x,y:P.y+e.delta.y})},children:x&&r})})};var x=r(94808);let $=e=>{let{children:t}=e,r=d.useRef(null),n=d.useRef(null),{breakpoint:o,setMeasuredDeviceSize:s,size:l,zoom:u}=p(),{size:c}=(0,i.useResize)(r.current),{size:f}=(0,i.useResize)(n.current),m=!1;(0,d.useEffect)(()=>{c&&s(c)},[c,s]);let g="0",y="0";if(o&&"responsive"!==o&&(g="-50%",l&&c&&"number"==typeof u&&"number"==typeof l.width&&"number"==typeof l.height&&"number"==typeof c.width&&"number"==typeof c.height)){y="0 auto";let e=l.width/u,t=c.width*u,r=e-l.width;if(t>f.width)if(u>1){let e=c.width-f.width;g=e<0?"".concat(e/2,"px"):"0"}else g="0";else if(u>=1)g="".concat(r/2,"px");else{let e=f.width-t;g="".concat(e/2,"px"),y="0"}}let v=u?"".concat(100/u,"%"):"100%",b=u?"".concat(100/u,"%"):"100%";return"responsive"!==o&&(v="".concat((null==l?void 0:l.width)/("number"==typeof u?u:1),"px"),b="".concat((null==l?void 0:l.height)/("number"==typeof u?u:1),"px")),(0,a.jsx)("div",{ref:n,style:{height:"100%",width:"100%"},children:(0,a.jsx)("div",{ref:r,style:{height:b,margin:y,transform:"translate3d(".concat(g,", 0, 0)"),width:v},children:t})})},k=e=>{let{ref:t,setIframeHasLoaded:r,url:n}=e,{zoom:i}=p();return(0,a.jsx)("iframe",{className:"live-preview-iframe",onLoad:()=>{r(!0)},ref:t,src:n,style:{transform:"number"==typeof i?"scale(".concat(i,") "):void 0},title:n})},S=e=>{let t,r,i,o,s=(0,n.c)(18),{axis:l}=e,{breakpoint:u,measuredDeviceSize:c,setBreakpoint:f,setSize:m,size:g,zoom:y}=p(),[v,b]=d.useState(("x"===l?null==c?void 0:c.width:null==c?void 0:c.height)||0);s[0]!==l||s[1]!==(null==c?void 0:c.height)||s[2]!==(null==c?void 0:c.width)||s[3]!==f||s[4]!==m||s[5]!==y?(t=e=>{let t=Number(e.target.value);t<0&&(t=0),b(t),f("custom"),m({type:"reset",value:{height:"y"===l?t:Number(null==c?void 0:c.height.toFixed(0))*y,width:"x"===l?t:Number(null==c?void 0:c.width.toFixed(0))*y}})},s[0]=l,s[1]=null==c?void 0:c.height,s[2]=null==c?void 0:c.width,s[3]=f,s[4]=m,s[5]=y,s[6]=t):t=s[6];let w=t;s[7]!==l||s[8]!==u||s[9]!==c||s[10]!==g||s[11]!==y?(r=()=>{"responsive"===u&&c&&("x"===l?b(Number(c.width.toFixed(0))*y):b(Number(c.height.toFixed(0))*y)),"responsive"!==u&&g&&b("x"===l?g.width:g.height)},i=[u,l,c,g,y],s[7]=l,s[8]=u,s[9]=c,s[10]=g,s[11]=y,s[12]=r,s[13]=i):(r=s[12],i=s[13]),(0,d.useEffect)(r,i);let x="x"===l?"live-preview-width":"live-preview-height",$=v||0;return s[14]!==w||s[15]!==x||s[16]!==$?(o=(0,a.jsx)("input",{className:"toolbar-input",min:0,name:x,onChange:w,step:1,type:"number",value:$}),s[14]=w,s[15]=x,s[16]=$,s[17]=o):o=s[17],o},_="live-preview-toolbar-controls",j=[50,75,100,125,150,200],E=()=>{let e,t=(0,n.c)(14),{breakpoint:r,breakpoints:o,setBreakpoint:l,setPreviewWindowType:u,setZoom:c,url:f,zoom:m}=p(),{t:g}=(0,s.d)();if(t[0]!==r||t[1]!==o||t[2]!==l||t[3]!==u||t[4]!==c||t[5]!==g||t[6]!==f||t[7]!==m){var y,v;let n,p,b={label:g("general:custom"),value:"custom"};t[9]!==c||t[10]!==m?(n=e=>{let{close:t}=e;return(0,a.jsx)(i.PopupList.ButtonGroup,{children:(0,a.jsx)(d.Fragment,{children:j.map(e=>(0,a.jsxs)(i.PopupList.Button,{active:100*m==e,onClick:()=>{c(e/100),t()},children:[e,"%"]},e))})})},t[9]=c,t[10]=m,t[11]=n):n=t[11],t[12]!==u?(p=e=>{e.preventDefault(),u("popup")},t[12]=u,t[13]=p):p=t[13],e=(0,a.jsxs)("div",{className:_,children:[(null==o?void 0:o.length)>0&&(0,a.jsx)(i.Popup,{button:(0,a.jsxs)(d.Fragment,{children:[(0,a.jsx)("span",{children:null!=(v=null==(y=o.find(e=>e.name==r))?void 0:y.label)?v:b.label}),(0,a.jsx)(i.ChevronIcon,{className:"".concat(_,"__chevron")})]}),className:"".concat(_,"__breakpoint"),horizontalAlign:"right",render:e=>{let{close:t}=e;return(0,a.jsx)(i.PopupList.ButtonGroup,{children:(0,a.jsxs)(d.Fragment,{children:[o.map(e=>(0,a.jsx)(i.PopupList.Button,{active:e.name==r,onClick:()=>{l(e.name),t()},children:e.label},e.name)),"custom"===r&&(0,a.jsx)(i.PopupList.Button,{active:r==b.value,onClick:()=>{l(b.value),t()},children:b.label})]})})},showScrollbar:!0,verticalAlign:"bottom"}),(0,a.jsxs)("div",{className:"".concat(_,"__device-size"),children:[(0,a.jsx)(S,{axis:"x"}),(0,a.jsx)("span",{className:"".concat(_,"__size-divider"),children:(0,a.jsx)(s.a,{})}),(0,a.jsx)(S,{axis:"y"})]}),(0,a.jsx)(i.Popup,{button:(0,a.jsxs)(d.Fragment,{children:[(0,a.jsxs)("span",{children:[100*m,"%"]}),(0,a.jsx)(i.ChevronIcon,{className:"".concat(_,"__chevron")})]}),className:"".concat(_,"__zoom"),horizontalAlign:"right",render:n,showScrollbar:!0,verticalAlign:"bottom"}),(0,a.jsx)("a",{className:"".concat(_,"__external"),href:f,onClick:p,type:"button",children:(0,a.jsx)(i.LinkIcon,{})})]}),t[0]=r,t[1]=o,t[2]=l,t[3]=u,t[4]=c,t[5]=g,t[6]=f,t[7]=m,t[8]=e}else e=t[8];return e},C="live-preview-toolbar",P=e=>{let t,r,o,s=(0,n.c)(10),{toolbarPosition:l}=p();s[0]===Symbol.for("react.memo_cache_sentinel")?(t={id:"live-preview-toolbar"},s[0]=t):t=s[0];let{attributes:u,listeners:c,setNodeRef:d,transform:f}=(0,m.PM)(t);s[1]===Symbol.for("react.memo_cache_sentinel")?(r=[C,"".concat(C,"--draggable")],s[1]=r):r=s[1];let g="".concat(l.x,"px"),y="".concat(l.y,"px");return s[2]!==u||s[3]!==c||s[4]!==e||s[5]!==d||s[6]!==g||s[7]!==y||s[8]!==f?(o=(0,a.jsxs)("div",{className:r.join(" "),style:{left:g,top:y,...f?{transform:f?"translate3d(".concat((null==f?void 0:f.x)||0,"px, ").concat((null==f?void 0:f.y)||0,"px, 0)"):void 0}:{}},children:[(0,a.jsx)("button",{...c,...u,className:"".concat(C,"__drag-handle"),ref:d,type:"button",children:(0,a.jsx)(i.DragHandleIcon,{})}),(0,a.jsx)(E,{...e})]}),s[2]=u,s[3]=c,s[4]=e,s[5]=d,s[6]=g,s[7]=y,s[8]=f,s[9]=o):o=s[9],o},N=e=>(0,a.jsx)("div",{className:[C,"".concat(C,"--static")].join(" "),children:(0,a.jsx)(E,{...e})}),A=e=>{let{draggable:t}=e;return t?(0,a.jsx)(P,{...e}):(0,a.jsx)(N,{...e})},T="live-preview-window",O=e=>{let t,r,s,l,u=(0,n.c)(41),{appIsReady:c,iframeHasLoaded:f,iframeRef:m,popupRef:g,previewWindowType:y,setIframeHasLoaded:v,url:b}=p(),w=(0,i.useLocale)(),{mostRecentUpdate:S}=(0,i.useDocumentEvents)(),{breakpoint:_,fieldSchemaJSON:j}=p(),E=d.useRef(void 0),[C]=(0,i.useAllFormFields)();if(u[0]!==c||u[1]!==j||u[2]!==C||u[3]!==m||u[4]!==w||u[5]!==S||u[6]!==g||u[7]!==y||u[8]!==b?(t=()=>{if(C&&window&&"postMessage"in window&&c){let t=(0,x.r)(C,!0),r=!E.current||E.current!==y;E.current=y;let n={type:"payload-live-preview",data:t,externallyUpdatedRelationship:S,fieldSchemaJSON:r?j:void 0,locale:w.code};if("popup"===y&&g.current&&g.current.postMessage(n,b),"iframe"===y&&m.current){var e;null==(e=m.current.contentWindow)||e.postMessage(n,b)}}},u[0]=c,u[1]=j,u[2]=C,u[3]=m,u[4]=w,u[5]=S,u[6]=g,u[7]=y,u[8]=b,u[9]=t):t=u[9],u[10]!==c||u[11]!==j||u[12]!==C||u[13]!==f||u[14]!==m||u[15]!==w||u[16]!==S||u[17]!==g||u[18]!==y||u[19]!==v||u[20]!==b?(r=[C,b,f,y,g,c,m,v,j,S,w],u[10]=c,u[11]=j,u[12]=C,u[13]=f,u[14]=m,u[15]=w,u[16]=S,u[17]=g,u[18]=y,u[19]=v,u[20]=b,u[21]=r):r=u[21],(0,d.useEffect)(t,r),u[22]!==m||u[23]!==g||u[24]!==y||u[25]!==b?(s=()=>{let e={type:"payload-document-event"};if("popup"===y&&g.current&&g.current.postMessage(e,b),"iframe"===y&&m.current){var t;null==(t=m.current.contentWindow)||t.postMessage(e,b)}},u[22]=m,u[23]=g,u[24]=y,u[25]=b,u[26]=s):s=u[26],u[27]!==m||u[28]!==S||u[29]!==g||u[30]!==y||u[31]!==b?(l=[S,m,g,y,b],u[27]=m,u[28]=S,u[29]=g,u[30]=y,u[31]=b,u[32]=l):l=u[32],(0,d.useEffect)(s,l),"iframe"===y){let t,r,n=_&&"responsive"!==_&&"".concat(T,"--has-breakpoint");u[33]!==n?(t=[T,n].filter(Boolean),u[33]=n,u[34]=t):t=u[34];let i=t.join(" ");return u[35]!==m||u[36]!==e||u[37]!==v||u[38]!==i||u[39]!==b?(r=(0,a.jsx)("div",{className:i,children:(0,a.jsxs)("div",{className:"".concat(T,"__wrapper"),children:[(0,a.jsx)(A,{...e}),(0,a.jsx)("div",{className:"".concat(T,"__main"),children:(0,a.jsx)($,{children:b?(0,a.jsx)(k,{ref:m,setIframeHasLoaded:v,url:b}):(0,a.jsx)(o.d,{height:"100%"})})})]})}),u[35]=m,u[36]=e,u[37]=v,u[38]=i,u[39]=b,u[40]=r):r=u[40],r}},D=e=>{let t,r,a,i,s,l,u=(0,n.c)(15),{eventType:c,onMessage:f,url:p}=e,m=(0,d.useRef)(!1),[g,y]=(0,d.useState)(!1),{config:v}=(0,o.b)(),{serverURL:b}=v,w=(0,d.useRef)(null);u[0]!==c||u[1]!==g||u[2]!==f||u[3]!==b||u[4]!==p?(t=()=>{let e=async e=>{var t,r;e.origin===window.location.origin&&e.origin===p&&e.origin===b&&("function"!=typeof f||(null==(t=e.data)?void 0:t.type)!==c||m.current||(m.current=!0,await f(null==(r=e.data)?void 0:r.searchParams),m.current=!1))};return g&&w.current&&window.addEventListener("message",e,!1),()=>{window.removeEventListener("message",e)}},r=[f,c,p,b,g],u[0]=c,u[1]=g,u[2]=f,u[3]=b,u[4]=p,u[5]=t,u[6]=r):(t=u[5],r=u[6]),(0,d.useEffect)(t,r),u[7]!==p?(a=e=>{e&&e.preventDefault();let t={height:700,left:"auto",menubar:"no",popup:"yes",toolbar:"no",top:"auto",width:800},r=Object.entries(t).reduce((e,r)=>{let[n,a]=r,i=e;if("auto"===a){if("top"===n){let e=Math.round(window.innerHeight/2-t.height/2);i+="top=".concat(e,",")}else if("left"===n){let e=Math.round(window.innerWidth/2-t.width/2);i+="left=".concat(e,",")}return i}return i+"".concat(n,"=").concat(a,",")},"").slice(0,-1);w.current=window.open(p,"_blank",r),y(!0)},u[7]=p,u[8]=a):a=u[8];let x=a;return u[9]!==g?(i=()=>{let e;return g?e=setInterval(function(){w.current.closed&&(clearInterval(e),y(!1))},1e3):clearInterval(e),()=>{e&&clearInterval(e)}},s=[g,w],u[9]=g,u[10]=i,u[11]=s):(i=u[10],s=u[11]),(0,d.useEffect)(i,s),u[12]!==g||u[13]!==x?(l={isPopupOpen:g,openPopupWindow:x,popupRef:w},u[12]=g,u[13]=x,u[14]=l):l=u[14],l},M="live-preview",R=e=>{try{return new URL(e,window.location.origin).href}catch(t){return e}},L=e=>{var t,r,n,f,m,g,y,v,b;let{BeforeDocumentControls:w,collectionConfig:x,config:$,Description:k,fields:S,globalConfig:_,PreviewButton:j,PublishButton:E,SaveButton:C,SaveDraftButton:P,schemaPath:N}=e,{id:A,action:T,AfterDocument:D,AfterFields:R,apiURL:L,BeforeFields:I,collectionSlug:F,currentEditor:B,disableActions:z,disableLeaveWithoutSaving:V,docPermissions:U,documentIsLocked:q,getDocPermissions:H,getDocPreferences:W,globalSlug:G,hasPublishPermission:K,hasSavePermission:J,incrementVersionCount:Y,initialData:Q,initialState:X,isEditing:Z,isInitializing:ee,lastUpdateTime:et,setCurrentEditor:er,setDocumentIsLocked:en,unlockDocument:ea,updateDocumentEditor:ei,updateSavedDocumentData:eo}=(0,i.useDocumentInfo)(),{onSave:es}=(0,i.useDocumentDrawerContext)(),el=A?"update":"create",{config:{admin:{user:eu},routes:{admin:ec}}}=(0,o.b)(),ed=(0,u.useRouter)(),ef=(0,u.useSearchParams)().get("locale"),{t:eh}=(0,s.d)(),{previewWindowType:ep}=p(),{refreshCookieAsync:em,user:eg}=(0,i.useAuth)(),{reportUpdate:ey}=(0,i.useDocumentEvents)(),{resetUploadEdits:ev}=(0,i.useUploadEdits)(),{getFormState:eb}=(0,i.useServerFunctions)(),{startRouteTransition:ew}=(0,i.useRouteTransition)(),ex=x||_,e$=(null==x?void 0:x.slug)||(null==_?void 0:_.slug),ek=(0,i.useEditDepth)(),eS=(null==ex?void 0:ex.lockDocuments)===void 0||(null==ex?void 0:ex.lockDocuments),e_=!1!==eS,ej="object"==typeof eS?eS.duration:300,eE=!!((null==x||null==(t=x.versions)?void 0:t.drafts)&&(null==x||null==(n=x.versions)||null==(r=n.drafts)?void 0:r.autosave)||(null==_||null==(f=_.versions)?void 0:f.drafts)&&(null==_||null==(g=_.versions)||null==(m=g.drafts)?void 0:m.autosave)),eC=void 0!==V?!V:!eE,[eP,eN]=(0,d.useState)(!1),[eA,eT]=(0,d.useState)(!1),eO=(0,d.useRef)(null),eD=(0,d.useRef)(null),[eM,eR]=(0,d.useState)(Date.now()),eL=et+1e3*ej,eI=Date.now()>eL,eF=(0,d.useRef)({hasShownLockedModal:!1,isLocked:!1,user:null}),eB=(0,d.useCallback)(async e=>{var t,r;let n=(0,l.wN)(eD);if(ey({id:A,entitySlug:e$,updatedAt:(null==e||null==(t=e.result)?void 0:t.updatedAt)||new Date().toISOString()}),eg&&F===eu&&A===eg.id&&em(),Y(),"function"==typeof eo&&eo((null==e?void 0:e.doc)||{}),"function"==typeof es&&es({...e,operation:A?"update":"create"}),!Z&&ek<2){let t=(0,c.Q)({adminRoute:ec,path:"/collections/".concat(F,"/").concat(null==e||null==(r=e.doc)?void 0:r.id).concat(ef?"?locale=".concat(ef):"")});ew(()=>ed.push(t))}else ev();if(await H(e),(A||G)&&!eE){let t=await W(),{state:r}=await eb({id:A,collectionSlug:F,data:(null==e?void 0:e.doc)||(null==e?void 0:e.result),docPermissions:U,docPreferences:t,globalSlug:G,operation:el,renderAllFields:!0,returnLockStatus:!1,schemaPath:e$,signal:n.signal,skipValidation:!0});return e_&&en(!1),eD.current=null,r}},[ec,F,ek,U,e$,H,W,eb,G,A,Y,Z,e_,ef,es,el,em,ey,ev,ed,en,eo,ew,eg,eu,eE]),ez=(0,d.useCallback)(async e=>{let{formState:t,submitted:r}=e,n=(0,l.wN)(eO),a=Date.now(),i=a-eM,o=e_&&i>=1e4;o&&eR(a);let s=await W(),{lockedState:u,state:c}=await eb({id:A,collectionSlug:F,docPermissions:U,docPreferences:s,formState:t,globalSlug:G,operation:el,returnLockStatus:!!e_,schemaPath:N,signal:n.signal,skipValidation:!r,updateLastEdited:o});if(en(!0),e_){var d,f,p,m,g;let e="object"==typeof(null==(d=eF.current)?void 0:d.user)?null==(p=eF.current)||null==(f=p.user)?void 0:f.id:null==(m=eF.current)?void 0:m.user;if(u){let t="string"==typeof u.user||"number"==typeof u.user?u.user:u.user.id;eF.current&&t===e||(e===eg.id&&t!==eg.id&&(eT(!0),eF.current.hasShownLockedModal=!0),eF.current=eF.current={hasShownLockedModal:(null==(g=eF.current)?void 0:g.hasShownLockedModal)||!1,isLocked:!0,user:u.user},er(u.user))}}return eO.current=null,c},[eM,e_,W,eb,A,F,U,G,el,N,en,null==eg?void 0:eg.id,er]);(0,d.useEffect)(()=>()=>{if(!e_)return;let e=window.location.pathname,t=A||G,r=["preview","api","versions"].some(t=>e.includes(t));if(t&&q&&!r){var n,a,i,o;("object"==typeof(null==(n=eF.current)?void 0:n.user)?(null==(i=eF.current)||null==(a=i.user)?void 0:a.id)===(null==eg?void 0:eg.id):(null==(o=eF.current)?void 0:o.user)===(null==eg?void 0:eg.id))&&(ea(A,null!=F?F:G),en(!1),er(null))}eT(!1)},[F,G,A,ea,eg,er,e_,q,en]),(0,d.useEffect)(()=>{let e=eO.current,t=eD.current;return()=>{(0,l.eS)(e),(0,l.eS)(t)}});let eV=q&&B&&("object"==typeof B?B.id!==(null==eg?void 0:eg.id):B!==(null==eg?void 0:eg.id))&&!eP&&!eA&&!(null==(y=eF.current)?void 0:y.hasShownLockedModal)&&!eI;return(0,a.jsx)(i.OperationProvider,{operation:el,children:(0,a.jsxs)(i.Form,{action:T,className:"".concat(M,"__form"),disabled:eP||!J,initialState:X,isInitializing:ee,method:A?"PATCH":"POST",onChange:[ez],onSuccess:eB,children:[e_&&eV&&!eP&&(0,a.jsx)(i.DocumentLocked,{handleGoBack:()=>(0,l.yF)({adminRoute:ec,collectionSlug:F,router:ed}),isActive:eV,onReadOnly:()=>{eN(!0),eT(!1)},onTakeOver:()=>(0,l.UG)(A,F,G,eg,!1,ei,er,eF,e_),updatedAt:et,user:B}),e_&&eA&&(0,a.jsx)(i.DocumentTakeOver,{handleBackToDashboard:()=>(0,l.n_)({adminRoute:ec,router:ed}),isActive:eA,onReadOnly:()=>{eN(!0),eT(!1)}}),!eP&&eC&&(0,a.jsx)(i.LeaveWithoutSaving,{}),(0,a.jsx)(i.SetDocumentStepNav,{collectionSlug:F,globalLabel:null==_?void 0:_.label,globalSlug:G,id:A,pluralLabel:x?null==x||null==(v=x.labels)?void 0:v.plural:void 0,useAsTitle:x?null==x||null==(b=x.admin)?void 0:b.useAsTitle:void 0,view:eh("general:livePreview")}),(0,a.jsx)(i.SetDocumentTitle,{collectionConfig:x,config:$,fallback:(null==A?void 0:A.toString())||"",globalConfig:_}),(0,a.jsx)(i.DocumentControls,{apiURL:L,BeforeDocumentControls:w,customComponents:{PreviewButton:j,PublishButton:E,SaveButton:C,SaveDraftButton:P},data:Q,disableActions:z,hasPublishPermission:K,hasSavePermission:J,id:A,isEditing:Z,onTakeOver:()=>(0,l.UG)(A,F,G,eg,!0,ei,er,eF,e_,eN),permissions:U,readOnlyForIncomingUser:eP,slug:(null==x?void 0:x.slug)||(null==_?void 0:_.slug),user:B}),(0,a.jsxs)("div",{className:[M,"popup"===ep&&"".concat(M,"--detached")].filter(Boolean).join(" "),children:[(0,a.jsxs)("div",{className:["".concat(M,"__main"),"popup"===ep&&"".concat(M,"__main--popup-open")].filter(Boolean).join(" "),children:[(0,a.jsx)(i.DocumentFields,{AfterFields:R,BeforeFields:I,Description:k,docPermissions:U,fields:S,forceSidebarWrap:!0,readOnly:eP||!J,schemaPathSegments:[F||G]}),D]}),(0,a.jsx)(O,{collectionSlug:F,globalSlug:G})]})]})})},I=e=>{let t,r,s,l=(0,n.c)(23),{breakpoints:u,url:c}=e,{collectionSlug:f,globalSlug:p}=(0,i.useDocumentInfo)(),{config:m,config:g,getEntityConfig:y}=(0,o.b)(),{routes:v,serverURL:b}=g,{api:x}=v;l[0]!==c?(t=c.startsWith("http://")||c.startsWith("https://")?c:R(c),l[0]=c,l[1]=t):t=l[1];let $=t;l[2]!==$?(r={eventType:"payload-live-preview",url:$},l[2]=$,l[3]=r):r=l[3];let{isPopupOpen:k,openPopupWindow:S,popupRef:_}=D(r);if(l[4]!==x||l[5]!==u||l[6]!==f||l[7]!==m||l[8]!==y||l[9]!==p||l[10]!==k||l[11]!==S||l[12]!==_||l[13]!==e.BeforeDocumentControls||l[14]!==e.Description||l[15]!==e.PreviewButton||l[16]!==e.PublishButton||l[17]!==e.SaveButton||l[18]!==e.SaveDraftButton||l[19]!==e.Upload||l[20]!==b||l[21]!==$){var j;let t=y({collectionSlug:f}),r=y({globalSlug:p}),n=f||p;s=(0,a.jsx)(d.Fragment,{children:(0,a.jsx)(w,{breakpoints:u,fieldSchema:(null==t?void 0:t.fields)||(null==r?void 0:r.fields),isPopupOpen:k,openPopupWindow:S,popupRef:_,url:$,children:(0,a.jsx)(L,{apiRoute:x,BeforeDocumentControls:e.BeforeDocumentControls,collectionConfig:t,config:m,Description:e.Description,fields:null==(j=t||r)?void 0:j.fields,globalConfig:r,PreviewButton:e.PreviewButton,PublishButton:e.PublishButton,SaveButton:e.SaveButton,SaveDraftButton:e.SaveDraftButton,schemaPath:n,serverURL:b,Upload:e.Upload})})}),l[4]=x,l[5]=u,l[6]=f,l[7]=m,l[8]=y,l[9]=p,l[10]=k,l[11]=S,l[12]=_,l[13]=e.BeforeDocumentControls,l[14]=e.Description,l[15]=e.PreviewButton,l[16]=e.PublishButton,l[17]=e.SaveButton,l[18]=e.SaveDraftButton,l[19]=e.Upload,l[20]=b,l[21]=$,l[22]=s}else s=l[22];return s}},22711:(e,t,r)=>{"use strict";r.d(t,{_:()=>n});let n=(e,t)=>e.size===t.size&&[...e].every(e=>t.has(e))},23530:(e,t,r)=>{"use strict";var n,a;n=r(29553),a=r(88769),n.version,n.renderToString,t.qV=n.renderToStaticMarkup,a.renderToReadableStream,a.resume&&a.resume},24529:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150);t.default={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message:({keyword:e,schemaCode:t})=>(0,n.str)`must NOT have ${"maxProperties"===e?"more":"fewer"} than ${t} properties`,params:({schemaCode:e})=>(0,n._)`{limit: ${e}}`},code(e){let{keyword:t,data:r,schemaCode:a}=e,i="maxProperties"===t?n.operators.GT:n.operators.LT;e.fail$data((0,n._)`Object.keys(${r}).length ${i} ${a}`)}}},25184:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n={src:"/_next/static/media/payload-favicon.7c819288.svg",height:1024,width:1024,blurWidth:0,blurHeight:0}},25421:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super("validation failed"),this.errors=e,this.ajv=this.validation=!0}}t.default=r},25506:(e,t,r)=>{"use strict";r.d(t,{C5:()=>o,Rp:()=>s,wJ:()=>i,Qq:()=>a,Xh:()=>l}),r(94273);var n=r(81510);r(62293),n.default,n.default;let a=(e,{hasMany:t,maxLength:r,maxRows:n,minLength:a,minRows:i,req:{payload:{config:o},t:s},required:l})=>{let c;if(!l&&!e)return!0;if(!0===t){let t=u(e,{maxRows:n,minRows:i,required:l,t:s});if("string"==typeof t)return t}for(let t of("number"==typeof o?.defaultMaxTextLength&&(c=o.defaultMaxTextLength),"number"==typeof r&&(c=r),Array.isArray(e)?e:[e])){let e=t?.length||0;if("number"==typeof c&&e>c)return s("validation:shorterThanMax",{label:s("general:value"),maxLength:c,stringValue:t});if("number"==typeof a&&e<a)return s("validation:longerThanMin",{label:s("general:value"),minLength:a,stringValue:t})}return!l||!!("string"==typeof e||Array.isArray(e))&&e?.length!==0||s("validation:required")},i=(e,{maxLength:t,minLength:r=3,req:{payload:{config:n},t:a},required:i})=>{let o;return("number"==typeof n?.defaultMaxTextLength&&(o=n.defaultMaxTextLength),"number"==typeof t&&(o=t),e&&o&&e.length>o)?a("validation:shorterThanMax",{maxLength:o}):e&&r&&e.length<r?a("validation:longerThanMin",{minLength:r}):!i||!!e||a("validation:required")},o=(e,{req:{t},required:r,siblingData:n})=>r&&!e?t("validation:required"):!e||e===n.password||t("fields:passwordsDoNotMatch"),s=(e,{collectionSlug:t,req:{payload:{collections:r,config:n},t:a},required:i,siblingData:o})=>{if(t){let i=r?.[t]?.config??n.collections.find(({slug:e})=>e===t);if(i.auth.loginWithUsername&&!i.auth.loginWithUsername?.requireUsername&&!i.auth.loginWithUsername?.requireEmail&&!e&&!o?.username)return a("validation:required")}return(!e||!!/^(?!.*\.\.)[\w!#$%&'*+/=?^`{|}~-](?:[\w!#$%&'*+/=?^`{|}~.-]*[\w!#$%&'*+/=?^`{|}~-])?@[a-z0-9](?:[a-z0-9-]*[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]*[a-z0-9])?)*\.[a-z]{2,}$/i.test(e))&&(!!e||!i)||a("validation:emailAddress")},l=(e,{collectionSlug:t,req:{payload:{collections:r,config:n},t:a},required:i,siblingData:o})=>{let s;if(t){let i=r?.[t]?.config??n.collections.find(({slug:e})=>e===t);if(i.auth.loginWithUsername&&!i.auth.loginWithUsername?.requireUsername&&!i.auth.loginWithUsername?.requireEmail&&!e&&!o?.email)return a("validation:required")}return("number"==typeof n?.defaultMaxTextLength&&(s=n.defaultMaxTextLength),e&&s&&e.length>s)?a("validation:shorterThanMax",{maxLength:s}):!!e||!i||a("validation:required")},u=(e,t)=>{let{maxRows:r,minRows:n,required:a,t:i}=t,o=Array.isArray(e)?e.length:e||0;return!a&&0===o||(n&&o<n?i("validation:requiresAtLeast",{count:n,label:i("general:rows")}):r&&o>r?i("validation:requiresNoMoreThan",{count:r,label:i("general:rows")}):!a||!!o||i("validation:requiresAtLeast",{count:1,label:i("general:row")}))}},26520:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0;let a=r(55150),i=r(11659);function o(e,t=e.schema){let{opts:r,self:n}=e;if(!r.strictSchema||"boolean"==typeof t)return;let a=n.RULES.keywords;for(let r in t)a[r]||m(e,`unknown keyword: "${r}"`)}function s(e,t){if("boolean"==typeof e)return!e;for(let r in e)if(t[r])return!0;return!1}function l(e){return"number"==typeof e?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}function u(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function c({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:n}){return(i,o,s,l)=>{let u=void 0===s?o:s instanceof a.Name?(o instanceof a.Name?e(i,o,s):t(i,o,s),s):o instanceof a.Name?(t(i,s,o),o):r(o,s);return l!==a.Name||u instanceof a.Name?u:n(i,u)}}function d(e,t){if(!0===t)return e.var("props",!0);let r=e.var("props",(0,a._)`{}`);return void 0!==t&&f(e,r,t),r}function f(e,t,r){Object.keys(r).forEach(r=>e.assign((0,a._)`${t}${(0,a.getProperty)(r)}`,!0))}t.toHash=function(e){let t={};for(let r of e)t[r]=!0;return t},t.alwaysValidSchema=function(e,t){return"boolean"==typeof t?t:0===Object.keys(t).length||(o(e,t),!s(t,e.self.RULES.all))},t.checkUnknownRules=o,t.schemaHasRules=s,t.schemaHasRulesButRef=function(e,t){if("boolean"==typeof e)return!e;for(let r in e)if("$ref"!==r&&t.all[r])return!0;return!1},t.schemaRefOrVal=function({topSchemaRef:e,schemaPath:t},r,n,i){if(!i){if("number"==typeof r||"boolean"==typeof r)return r;if("string"==typeof r)return(0,a._)`${r}`}return(0,a._)`${e}${t}${(0,a.getProperty)(n)}`},t.unescapeFragment=function(e){return u(decodeURIComponent(e))},t.escapeFragment=function(e){return encodeURIComponent(l(e))},t.escapeJsonPointer=l,t.unescapeJsonPointer=u,t.eachItem=function(e,t){if(Array.isArray(e))for(let r of e)t(r);else t(e)},t.mergeEvaluated={props:c({mergeNames:(e,t,r)=>e.if((0,a._)`${r} !== true && ${t} !== undefined`,()=>{e.if((0,a._)`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,(0,a._)`${r} || {}`).code((0,a._)`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if((0,a._)`${r} !== true`,()=>{!0===t?e.assign(r,!0):(e.assign(r,(0,a._)`${r} || {}`),f(e,r,t))}),mergeValues:(e,t)=>!0===e||{...e,...t},resultToName:d}),items:c({mergeNames:(e,t,r)=>e.if((0,a._)`${r} !== true && ${t} !== undefined`,()=>e.assign(r,(0,a._)`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if((0,a._)`${r} !== true`,()=>e.assign(r,!0===t||(0,a._)`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>!0===e||Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})},t.evaluatedPropsToName=d,t.setEvaluated=f;let p={};function m(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,!0===r)throw Error(t);e.self.logger.warn(t)}}t.useFunc=function(e,t){return e.scopeValue("func",{ref:t,code:p[t.code]||(p[t.code]=new i._Code(t.code))})},function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"}(n||(t.Type=n={})),t.getErrorPath=function(e,t,r){if(e instanceof a.Name){let i=t===n.Num;return r?i?(0,a._)`"[" + ${e} + "]"`:(0,a._)`"['" + ${e} + "']"`:i?(0,a._)`"/" + ${e}`:(0,a._)`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,a.getProperty)(e).toString():"/"+l(e)},t.checkStrictMode=m},26666:(e,t,r)=>{"use strict";function n(e){return(t,r)=>{let n;if("formatting"===((null==r?void 0:r.context)?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,a=(null==r?void 0:r.width)?String(r.width):t;n=e.formattingValues[a]||e.formattingValues[t]}else{let t=e.defaultWidth,a=(null==r?void 0:r.width)?String(r.width):e.defaultWidth;n=e.values[a]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}r.d(t,{o:()=>n})},27571:(e,t)=>{"use strict";t.byteLength=function(e){var t=l(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,i=l(e),o=i[0],s=i[1],u=new a((o+s)*3/4-s),c=0,d=s>0?o-4:o;for(r=0;r<d;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===s&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,u[c++]=255&t),1===s&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u},t.fromByteArray=function(e){for(var t,n=e.length,a=n%3,i=[],o=0,s=n-a;o<s;o+=16383)i.push(function(e,t,n){for(var a,i=[],o=t;o<n;o+=3)a=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),i.push(r[a>>18&63]+r[a>>12&63]+r[a>>6&63]+r[63&a]);return i.join("")}(e,o,o+16383>s?s:o+16383));return 1===a?i.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===a&&i.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),i.join("")};for(var r=[],n=[],a="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,s=i.length;o<s;++o)r[o]=i[o],n[i.charCodeAt(o)]=o;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},27861:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(53791),a=r(97129);function i({folderFieldName:e,isUpload:t,relationTo:r,useAsTitle:i,value:o}){let s={id:o?.id,_folderOrDocumentTitle:String(i&&o?.[i]||o.id),createdAt:o?.createdAt,folderID:o?.[e],updatedAt:o?.updatedAt};return t&&(s.filename=o.filename,s.mimeType=o.mimeType,s.url=(0,n.w)(o.mimeType)?(0,a.k)({sizes:o.sizes,targetSizeMax:520,targetSizeMin:300,url:o.url,width:o.width}):void 0),{itemKey:`${r}-${o.id}`,relationTo:r,value:s}}},28438:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var n=r(94808),a=r(43011);let i=(e,t)=>{let r;if(!e)return null;if(-1===t.indexOf("."))return(0,n.r)(e,!0);let i={},o=t.split(".");return r=Number.isNaN(Number(o[o.length-1]))?t.substring(0,t.lastIndexOf(".")+1):`${t}.`,Object.keys(e).forEach(t=>{e[t].disableFormData||0!==t.indexOf(r)||(i[t.replace(r,"")]=e[t].value)}),(0,a.s)(i)}},28705:(e,t,r)=>{"use strict";r.d(t,{Row:()=>i});var n=r(67252);r(89688);var a=r(73063);let i=e=>{let{baseVersionField:t}=e;return(0,n.jsx)("div",{className:"row-diff",children:(0,n.jsx)(a.RenderVersionFieldsToDiff,{versionFields:t.fields})})}},28906:(e,t,r)=>{"use strict";function n(e){return function(t){let r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=n.width,i=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;let s=o[0],l=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(l)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(l,e=>e.test(s)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(l,e=>e.test(s));return r=e.valueCallback?e.valueCallback(u):u,{value:r=n.valueCallback?n.valueCallback(r):r,rest:t.slice(s.length)}}}r.d(t,{A:()=>n})},29311:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRules=t.isJSONType=void 0;let r=new Set(["string","number","integer","boolean","null","object","array"]);t.isJSONType=function(e){return"string"==typeof e&&r.has(e)},t.getRules=function(){let e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}},29694:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateAdditionalItems=void 0;let n=r(55150),a=r(26520);function i(e,t){let{gen:r,schema:i,data:o,keyword:s,it:l}=e;l.items=!0;let u=r.const("len",(0,n._)`${o}.length`);if(!1===i)e.setParams({len:t.length}),e.pass((0,n._)`${u} <= ${t.length}`);else if("object"==typeof i&&!(0,a.alwaysValidSchema)(l,i)){let i=r.var("valid",(0,n._)`${u} <= ${t.length}`);r.if((0,n.not)(i),()=>{var o;return o=i,void r.forRange("i",t.length,u,t=>{e.subschema({keyword:s,dataProp:t,dataPropType:a.Type.Num},o),l.allErrors||r.if((0,n.not)(o),()=>r.break())})}),e.ok(i)}}t.validateAdditionalItems=i,t.default={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>(0,n.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,n._)`{limit: ${e}}`},code(e){let{parentSchema:t,it:r}=e,{items:n}=t;if(!Array.isArray(n))return void(0,a.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas');i(e,n)}}},30911:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},31370:(e,t,r)=>{"use strict";var n=r(89688).__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;t.c=function(e){return n.H.useMemoCache(e)}},31432:(e,t,r)=>{"use strict";r.d(t,{FolderTableCellClient:()=>l});var n=r(67252),a=r(89688),i=r(56156),o=r(71464),s=r(83548);let l=e=>{let{collectionSlug:t,data:r,docTitle:l,folderFieldName:u}=e,c=r.id,d=null==r?void 0:r[u],{config:f}=(0,i.b)(),{t:p}=(0,o.d)(),[m,g]=a.useState(()=>d?"".concat(p("general:loading"),"..."):p("folder:noFolder")),[y,v]=a.useState(d),b=a.useRef(!1),w=a.useCallback(async e=>{let{id:r,name:n}=e;try{await fetch("".concat(f.routes.api,"/").concat(t,"/").concat(c),{body:JSON.stringify({[u]:r}),credentials:"include",headers:{"Content-Type":"application/json"},method:"PATCH"}),v(r),g(n||p("folder:noFolder"))}catch(e){console.error("Error moving document to folder",e)}},[f.routes.api,t,c,p]);return(0,a.useEffect)(()=>{let e=async()=>{try{let e=await fetch("".concat(f.routes.api,"/").concat(f.folders.slug).concat(d?"/".concat(d):""),{credentials:"include",headers:{"Content-Type":"application/json"},method:"GET"}),t=await e.json();g((null==t?void 0:t.name)||p("folder:noFolder"))}catch(e){console.error("Error moving document to folder",e)}};b.current||(e(),b.current=!0)},[]),(0,n.jsx)(s.MoveDocToFolderButton,{buttonProps:{size:"small"},collectionSlug:t,docData:r,docID:c,docTitle:l,fromFolderID:y,fromFolderName:m,modalSlug:"move-doc-to-folder-cell--".concat(c),onConfirm:w,skipConfirmModal:!1})}},31979:(e,t,r)=>{"use strict";r.d(t,{NavHamburger:()=>o});var n=r(47092),a=r(67252),i=r(83548);r(89688);let o=()=>{let e,t=(0,n.c)(2),{navOpen:r}=(0,i.useNav)();return t[0]!==r?(e=(0,a.jsx)(i.Hamburger,{closeIcon:"collapse",isActive:r}),t[0]=r,t[1]=e):e=t[1],e}},33151:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(64227);n.code='require("ajv/dist/runtime/uri").default',t.default=n},34283:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(37742),a=r(55150),i=r(12208),o=r(26520);t.default={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>(0,a._)`{additionalProperty: ${e.additionalProperty}}`},code(e){let{gen:t,schema:r,parentSchema:s,data:l,errsCount:u,it:c}=e;if(!u)throw Error("ajv implementation error");let{allErrors:d,opts:f}=c;if(c.props=!0,"all"!==f.removeAdditional&&(0,o.alwaysValidSchema)(c,r))return;let p=(0,n.allSchemaProperties)(s.properties),m=(0,n.allSchemaProperties)(s.patternProperties);function g(e){t.code((0,a._)`delete ${l}[${e}]`)}function y(n){if("all"===f.removeAdditional||f.removeAdditional&&!1===r)return void g(n);if(!1===r){e.setParams({additionalProperty:n}),e.error(),d||t.break();return}if("object"==typeof r&&!(0,o.alwaysValidSchema)(c,r)){let r=t.name("valid");"failing"===f.removeAdditional?(v(n,r,!1),t.if((0,a.not)(r),()=>{e.reset(),g(n)})):(v(n,r),d||t.if((0,a.not)(r),()=>t.break()))}}function v(t,r,n){let a={keyword:"additionalProperties",dataProp:t,dataPropType:o.Type.Str};!1===n&&Object.assign(a,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(a,r)}t.forIn("key",l,r=>{p.length||m.length?t.if(function(r){let i;if(p.length>8){let e=(0,o.schemaRefOrVal)(c,s.properties,"properties");i=(0,n.isOwnProperty)(t,e,r)}else i=p.length?(0,a.or)(...p.map(e=>(0,a._)`${r} === ${e}`)):a.nil;return m.length&&(i=(0,a.or)(i,...m.map(t=>(0,a._)`${(0,n.usePattern)(e,t)}.test(${r})`))),(0,a.not)(i)}(r),()=>y(r)):y(r)}),e.ok((0,a._)`${u} === ${i.default.errors}`)}}},36006:(e,t,r)=>{"use strict";let{HEX:n}=r(39758),a=/^(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)$/u;function i(e){if(3>l(e,"."))return{host:e,isIPV4:!1};let[t]=e.match(a)||[];return t?{host:function(e,t){let r="",n=!0,a=e.length;for(let i=0;i<a;i++){let o=e[i];"0"===o&&n?(i+1<=a&&"."===e[i+1]||i+1===a)&&(r+=o,n=!1):(n=o===t,r+=o)}return r}(t,"."),isIPV4:!0}:{host:e,isIPV4:!1}}function o(e,t=!1){let r="",a=!0;for(let t of e){if(void 0===n[t])return;"0"!==t&&!0===a&&(a=!1),a||(r+=t)}return t&&0===r.length&&(r="0"),r}function s(e){if(2>l(e,":"))return{host:e,isIPV6:!1};let t=function(e){let t=0,r={error:!1,address:"",zone:""},n=[],a=[],i=!1,s=!1,l=!1;function u(){if(a.length){if(!1===i){let e=o(a);if(void 0===e)return r.error=!0,!1;n.push(e)}a.length=0}return!0}for(let o=0;o<e.length;o++){let c=e[o];if("["!==c&&"]"!==c)if(":"===c){if(!0===s&&(l=!0),!u())break;if(t++,n.push(":"),t>7){r.error=!0;break}o-1>=0&&":"===e[o-1]&&(s=!0);continue}else if("%"===c){if(!u())break;i=!0}else{a.push(c);continue}}return a.length&&(i?r.zone=a.join(""):l?n.push(a.join("")):n.push(o(a))),r.address=n.join(""),r}(e);if(t.error)return{host:e,isIPV6:!1};{let e=t.address,r=t.address;return t.zone&&(e+="%"+t.zone,r+="%25"+t.zone),{host:e,escapedHost:r,isIPV6:!0}}}function l(e,t){let r=0;for(let n=0;n<e.length;n++)e[n]===t&&r++;return r}let u=/^\.\.?\//u,c=/^\/\.(?:\/|$)/u,d=/^\/\.\.(?:\/|$)/u,f=/^\/?(?:.|\n)*?(?=\/|$)/u;e.exports={recomposeAuthority:function(e){let t=[];if(void 0!==e.userinfo&&(t.push(e.userinfo),t.push("@")),void 0!==e.host){let r=unescape(e.host),n=i(r);if(n.isIPV4)r=n.host;else{let t=s(n.host);r=!0===t.isIPV6?`[${t.escapedHost}]`:e.host}t.push(r)}return("number"==typeof e.port||"string"==typeof e.port)&&(t.push(":"),t.push(String(e.port))),t.length?t.join(""):void 0},normalizeComponentEncoding:function(e,t){let r=!0!==t?escape:unescape;return void 0!==e.scheme&&(e.scheme=r(e.scheme)),void 0!==e.userinfo&&(e.userinfo=r(e.userinfo)),void 0!==e.host&&(e.host=r(e.host)),void 0!==e.path&&(e.path=r(e.path)),void 0!==e.query&&(e.query=r(e.query)),void 0!==e.fragment&&(e.fragment=r(e.fragment)),e},removeDotSegments:function(e){let t=[];for(;e.length;)if(e.match(u))e=e.replace(u,"");else if(e.match(c))e=e.replace(c,"/");else if(e.match(d))e=e.replace(d,"/"),t.pop();else if("."===e||".."===e)e="";else{let r=e.match(f);if(r){let n=r[0];e=e.slice(n.length),t.push(n)}else throw Error("Unexpected dot segment condition")}return t.join("")},normalizeIPv4:i,normalizeIPv6:s,stringArrayToHexStripped:o}},36279:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(72431),a=r(19811),i=r(54417),o=r(60283),s=r(40546);t.default=[n.default,a.default,(0,i.default)(),o.default,s.metadataVocabulary,s.contentVocabulary]},37028:(e,t,r)=>{"use strict";function n(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}r.d(t,{k:()=>n})},37549:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(54297);n.code='require("ajv/dist/runtime/equal").default',t.default=n},37699:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0;let a=r(29311),i=r(19516),o=r(95439),s=r(55150),l=r(26520);function u(e){let t=Array.isArray(e)?e:e?[e]:[];if(t.every(a.isJSONType))return t;throw Error("type must be JSONType or JSONType[]: "+t.join(","))}!function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"}(n||(t.DataType=n={})),t.getSchemaTypes=function(e){let t=u(e.type);if(t.includes("null")){if(!1===e.nullable)throw Error("type: null contradicts nullable: false")}else{if(!t.length&&void 0!==e.nullable)throw Error('"nullable" cannot be used without "type"');!0===e.nullable&&t.push("null")}return t},t.getJSONTypes=u,t.coerceAndCheckDataType=function(e,t){var r,a;let{gen:o,data:l,opts:u}=e,d=(r=t,(a=u.coerceTypes)?r.filter(e=>c.has(e)||"array"===a&&"array"===e):[]),p=t.length>0&&!(0===d.length&&1===t.length&&(0,i.schemaHasRulesForType)(e,t[0]));if(p){let r=f(t,l,u.strictNumbers,n.Wrong);o.if(r,()=>{d.length?function(e,t,r){let{gen:n,data:a,opts:i}=e,o=n.let("dataType",(0,s._)`typeof ${a}`),l=n.let("coerced",(0,s._)`undefined`);for(let e of("array"===i.coerceTypes&&n.if((0,s._)`${o} == 'object' && Array.isArray(${a}) && ${a}.length == 1`,()=>n.assign(a,(0,s._)`${a}[0]`).assign(o,(0,s._)`typeof ${a}`).if(f(t,a,i.strictNumbers),()=>n.assign(l,a))),n.if((0,s._)`${l} !== undefined`),r))(c.has(e)||"array"===e&&"array"===i.coerceTypes)&&function(e){switch(e){case"string":n.elseIf((0,s._)`${o} == "number" || ${o} == "boolean"`).assign(l,(0,s._)`"" + ${a}`).elseIf((0,s._)`${a} === null`).assign(l,(0,s._)`""`);return;case"number":n.elseIf((0,s._)`${o} == "boolean" || ${a} === null
              || (${o} == "string" && ${a} && ${a} == +${a})`).assign(l,(0,s._)`+${a}`);return;case"integer":n.elseIf((0,s._)`${o} === "boolean" || ${a} === null
              || (${o} === "string" && ${a} && ${a} == +${a} && !(${a} % 1))`).assign(l,(0,s._)`+${a}`);return;case"boolean":n.elseIf((0,s._)`${a} === "false" || ${a} === 0 || ${a} === null`).assign(l,!1).elseIf((0,s._)`${a} === "true" || ${a} === 1`).assign(l,!0);return;case"null":n.elseIf((0,s._)`${a} === "" || ${a} === 0 || ${a} === false`),n.assign(l,null);return;case"array":n.elseIf((0,s._)`${o} === "string" || ${o} === "number"
              || ${o} === "boolean" || ${a} === null`).assign(l,(0,s._)`[${a}]`)}}(e);n.else(),m(e),n.endIf(),n.if((0,s._)`${l} !== undefined`,()=>{n.assign(a,l),function({gen:e,parentData:t,parentDataProperty:r},n){e.if((0,s._)`${t} !== undefined`,()=>e.assign((0,s._)`${t}[${r}]`,n))}(e,l)})}(e,t,d):m(e)})}return p};let c=new Set(["string","number","integer","boolean","null"]);function d(e,t,r,a=n.Correct){let i,o=a===n.Correct?s.operators.EQ:s.operators.NEQ;switch(e){case"null":return(0,s._)`${t} ${o} null`;case"array":i=(0,s._)`Array.isArray(${t})`;break;case"object":i=(0,s._)`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":i=l((0,s._)`!(${t} % 1) && !isNaN(${t})`);break;case"number":i=l();break;default:return(0,s._)`typeof ${t} ${o} ${e}`}return a===n.Correct?i:(0,s.not)(i);function l(e=s.nil){return(0,s.and)((0,s._)`typeof ${t} == "number"`,e,r?(0,s._)`isFinite(${t})`:s.nil)}}function f(e,t,r,n){let a;if(1===e.length)return d(e[0],t,r,n);let i=(0,l.toHash)(e);if(i.array&&i.object){let e=(0,s._)`typeof ${t} != "object"`;a=i.null?e:(0,s._)`!${t} || ${e}`,delete i.null,delete i.array,delete i.object}else a=s.nil;for(let e in i.number&&delete i.integer,i)a=(0,s.and)(a,d(e,t,r,n));return a}t.checkDataType=d,t.checkDataTypes=f;let p={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>"string"==typeof e?(0,s._)`{type: ${e}}`:(0,s._)`{type: ${t}}`};function m(e){let t=function(e){let{gen:t,data:r,schema:n}=e,a=(0,l.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:a,schemaValue:a,parentSchema:n,params:{},it:e}}(e);(0,o.reportError)(t,p)}t.reportTypeError=m},37742:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;let n=r(55150),a=r(26520),i=r(12208),o=r(26520);function s(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:(0,n._)`Object.prototype.hasOwnProperty`})}function l(e,t,r){return(0,n._)`${s(e)}.call(${t}, ${r})`}function u(e,t,r,a){let i=(0,n._)`${t}${(0,n.getProperty)(r)} === undefined`;return a?(0,n.or)(i,(0,n.not)(l(e,t,r))):i}function c(e){return e?Object.keys(e).filter(e=>"__proto__"!==e):[]}t.checkReportMissingProp=function(e,t){let{gen:r,data:a,it:i}=e;r.if(u(r,a,t,i.opts.ownProperties),()=>{e.setParams({missingProperty:(0,n._)`${t}`},!0),e.error()})},t.checkMissingProp=function({gen:e,data:t,it:{opts:r}},a,i){return(0,n.or)(...a.map(a=>(0,n.and)(u(e,t,a,r.ownProperties),(0,n._)`${i} = ${a}`)))},t.reportMissingProp=function(e,t){e.setParams({missingProperty:t},!0),e.error()},t.hasPropFunc=s,t.isOwnProperty=l,t.propertyInData=function(e,t,r,a){let i=(0,n._)`${t}${(0,n.getProperty)(r)} !== undefined`;return a?(0,n._)`${i} && ${l(e,t,r)}`:i},t.noPropertyInData=u,t.allSchemaProperties=c,t.schemaProperties=function(e,t){return c(t).filter(r=>!(0,a.alwaysValidSchema)(e,t[r]))},t.callValidateCode=function({schemaCode:e,data:t,it:{gen:r,topSchemaRef:a,schemaPath:o,errorPath:s},it:l},u,c,d){let f=d?(0,n._)`${e}, ${t}, ${a}${o}`:t,p=[[i.default.instancePath,(0,n.strConcat)(i.default.instancePath,s)],[i.default.parentData,l.parentData],[i.default.parentDataProperty,l.parentDataProperty],[i.default.rootData,i.default.rootData]];l.opts.dynamicRef&&p.push([i.default.dynamicAnchors,i.default.dynamicAnchors]);let m=(0,n._)`${f}, ${r.object(...p)}`;return c!==n.nil?(0,n._)`${u}.call(${c}, ${m})`:(0,n._)`${u}(${m})`};let d=(0,n._)`new RegExp`;t.usePattern=function({gen:e,it:{opts:t}},r){let a=t.unicodeRegExp?"u":"",{regExp:i}=t.code,s=i(r,a);return e.scopeValue("pattern",{key:s.toString(),ref:s,code:(0,n._)`${"new RegExp"===i.code?d:(0,o.useFunc)(e,i)}(${r}, ${a})`})},t.validateArray=function(e){let{gen:t,data:r,keyword:i,it:o}=e,s=t.name("valid");if(o.allErrors){let e=t.let("valid",!0);return l(()=>t.assign(e,!1)),e}return t.var(s,!0),l(()=>t.break()),s;function l(o){let l=t.const("len",(0,n._)`${r}.length`);t.forRange("i",0,l,r=>{e.subschema({keyword:i,dataProp:r,dataPropType:a.Type.Num},s),t.if((0,n.not)(s),o)})}},t.validateUnion=function(e){let{gen:t,schema:r,keyword:i,it:o}=e;if(!Array.isArray(r))throw Error("ajv implementation error");if(r.some(e=>(0,a.alwaysValidSchema)(o,e))&&!o.opts.unevaluated)return;let s=t.let("valid",!1),l=t.name("_valid");t.block(()=>r.forEach((r,a)=>{let o=e.subschema({keyword:i,schemaProp:a,compositeRule:!0},l);t.assign(s,(0,n._)`${s} || ${l}`),e.mergeValidEvaluated(o,l)||t.if((0,n.not)(s))})),e.result(s,()=>e.reset(),()=>e.error(!0))}},39217:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(93894),a=r(68407);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,a.hasBasePath)(r.pathname)}catch(e){return!1}}},39476:(e,t,r)=>{"use strict";r.d(t,{_P:()=>i,my:()=>n,w4:()=>a});let n=6048e5,a=864e5,i=Symbol.for("constructDateFrom")},39758:e=>{"use strict";e.exports={HEX:{0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15}}},40496:(e,t,r)=>{"use strict";r.d(t,{w:()=>a});var n=r(39476);function a(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&n._P in e?e[n._P](t):e instanceof Date?new e.constructor(t):new Date(t)}},40546:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.contentVocabulary=t.metadataVocabulary=void 0,t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]},42657:(e,t,r)=>{"use strict";async function n(e){return new Promise(t=>{setTimeout(t,e)})}r.d(t,{u:()=>n})},43011:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(e,t)=>{let r=(t=t||{}).delimiter||".",a=t.overwrite||!1,i=t.recursive||!1,o={};if(function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}(e)||"[object Object]"!==Object.prototype.toString.call(e))return e;let s=e=>{let r=Number(e);return isNaN(r)||-1!==e.indexOf(".")||t.object?e:r};return Object.keys(e).sort((e,t)=>e.length-t.length).forEach(l=>{let u=l.split(r),c=s(u.shift()),d=s(u[0]),f=o;for(;void 0!==d;){if("__proto__"===c)return;let e=Object.prototype.toString.call(f[c]),r="[object Object]"===e||"[object Array]"===e;if(!a&&!r&&void 0!==f[c])return;(!a||r)&&(a||null!=f[c])||(f[c]="number"!=typeof d||t.object?{}:[]),f=f[c],u.length>0&&(c=s(u.shift()),d=s(u[0]))}f[c]=i?n(e[l],t):e[l]}),o}},43556:(e,t,r)=>{"use strict";r.d(t,{Select:()=>m});var n=r(47092),a=r(67252),i=r(55032),o=r(71464),s=r(83548);r(89688);var l=r(47104),u=r(11111);let c=e=>{let{comparisonToRender:t,diffMethod:r,diffStyles:n,placeholder:i,versionToRender:o}=e;return(0,a.jsx)(u.Ay,{compareMethod:u.J7[r],hideLineNumbers:!0,newValue:void 0!==o?o:i,oldValue:t,showDiffOnly:!1,splitView:!0,styles:n})},d="select-diff",f=(e,t,r)=>r&&Array.isArray(e)?e.map(e=>t.find(t=>("string"==typeof t?t:t.value)===e)||String(e)):t.find(t=>("string"==typeof t?t:t.value)===e)||String(e),p=(e,t)=>{if(Array.isArray(e))return e.map(e=>{if("string"==typeof e)return e;let r=(0,i.sC)(e.label,t);return"string"==typeof r?r:e.value}).join(", ");if("string"==typeof e)return e;let r=(0,i.sC)(e.label,t);return"string"==typeof r?r:e.value},m=e=>{let t,r=(0,n.c)(11),{comparisonValue:u,diffMethod:m,field:g,locale:y,versionValue:v}=e,{i18n:b}=(0,o.d)(),w="";v==u&&(w="[".concat(b.t("general:noValue"),"]"));let x="options"in g&&g.options;if(r[0]!==u||r[1]!==m||r[2]!==g||r[3]!==b||r[4]!==y||r[5]!==x||r[6]!==w||r[7]!==v){let e,n=void 0!==u?p(f("string"==typeof u?u:JSON.stringify(u),x,g.hasMany),b):w,o=void 0!==v?p(f("string"==typeof v?v:JSON.stringify(v),x,g.hasMany),b):w;r[9]!==y?(e=y&&(0,a.jsx)("span",{className:"".concat(d,"__locale-label"),children:y}),r[9]=y,r[10]=e):e=r[10],t=(0,a.jsxs)("div",{className:d,children:[(0,a.jsxs)(s.FieldDiffLabel,{children:[e,"label"in g&&(0,i.sC)(g.label||"",b)]}),(0,a.jsx)(c,{comparisonToRender:n,diffMethod:m,diffStyles:l.C,placeholder:w,versionToRender:o})]}),r[0]=u,r[1]=m,r[2]=g,r[3]=b,r[4]=y,r[5]=x,r[6]=w,r[7]=v,r[8]=t}else t=r[8];return t}},43773:(e,t,r)=>{"use strict";r.d(t,{NavWrapper:()=>o});var n=r(47092),a=r(67252),i=r(83548);r(89688);let o=e=>{let t,r,o=(0,n.c)(11),{baseClass:s,children:l}=e,{hydrated:u,navOpen:c,navRef:d,shouldAnimate:f}=(0,i.useNav)(),p=c&&"".concat(s,"--nav-open"),m=f&&"".concat(s,"--nav-animate"),g=u&&"".concat(s,"--nav-hydrated");o[0]!==s||o[1]!==p||o[2]!==m||o[3]!==g?(t=[s,p,m,g].filter(Boolean),o[0]=s,o[1]=p,o[2]=m,o[3]=g,o[4]=t):t=o[4];let y=t.join(" "),v=!c||void 0,b="".concat(s,"__scroll");return o[5]!==l||o[6]!==d||o[7]!==y||o[8]!==v||o[9]!==b?(r=(0,a.jsx)("aside",{className:y,inert:v,children:(0,a.jsx)("div",{className:b,ref:d,children:l})}),o[5]=l,o[6]=d,o[7]=y,o[8]=v,o[9]=b,o[10]=r):r=o[10],r}},44316:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150);t.default={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message:({keyword:e,schemaCode:t})=>(0,n.str)`must NOT have ${"maxItems"===e?"more":"fewer"} than ${t} items`,params:({schemaCode:e})=>(0,n._)`{limit: ${e}}`},code(e){let{keyword:t,data:r,schemaCode:a}=e,i="maxItems"===t?n.operators.GT:n.operators.LT;e.fail$data((0,n._)`${r}.length ${i} ${a}`)}}},45311:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});let n=e=>{let{adminRoute:t,basePath:r="",path:n,serverURL:a}=e,i=n||"";if(t){if("/"!==t)return`${a||""}${r}${t}${i}`;else if(!i)return`${a||""}${r}${t}`}return`${a||""}${r}${i}`}},47058:(e,t,r)=>{"use strict";r.d(t,{q:()=>f});var n=r(81224);let a=Object.prototype.hasOwnProperty,i=Array.isArray,o={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:n.D4,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},s=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},l=function(e,t){let r,l={__proto__:null},u=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,c=t.parameterLimit===1/0?void 0:t.parameterLimit,d=u.split(t.delimiter,c),f=-1,p=t.charset;if(t.charsetSentinel)for(r=0;r<d.length;++r)0===d[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===d[r]?p="utf-8":"utf8=%26%2310003%3B"===d[r]&&(p="iso-8859-1"),f=r,r=d.length);for(r=0;r<d.length;++r){let e,u;if(r===f)continue;let c=d[r],m=c.indexOf("]="),g=-1===m?c.indexOf("="):m+1;-1===g?(e=t.decoder(c,o.decoder,p,"key"),u=t.strictNullHandling?null:""):(e=t.decoder(c.slice(0,g),o.decoder,p,"key"),u=n.F7(s(c.slice(g+1),t),function(e){return t.decoder(e,o.decoder,p,"value")})),u&&t.interpretNumericEntities&&"iso-8859-1"===p&&(u=u.replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),c.indexOf("[]=")>-1&&(u=i(u)?[u]:u);let y=a.call(l,e);y&&"combine"===t.duplicates?l[e]=n.kg(l[e],u):y&&"last"!==t.duplicates||(l[e]=u)}return l},u=function(e,t,r,n){let a=n?t:s(t,r);for(let t=e.length-1;t>=0;--t){let n,i=e[t];if("[]"===i&&r.parseArrays)n=r.allowEmptyArrays&&""===a?[]:[].concat(a);else{n=r.plainObjects?Object.create(null):{};let e="["===i.charAt(0)&&"]"===i.charAt(i.length-1)?i.slice(1,-1):i,t=r.decodeDotInKeys?e.replace(/%2E/g,"."):e,o=parseInt(t,10);r.parseArrays||""!==t?!isNaN(o)&&i!==t&&String(o)===t&&o>=0&&r.parseArrays&&o<=r.arrayLimit?(n=[])[o]=a:"__proto__"!==t&&(n[t]=a):n={0:a}}a=n}return a},c=function(e,t,r,n){if(!e)return;let i=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,o=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(i),l=s?i.slice(0,s.index):i,c=[];if(l){if(!r.plainObjects&&a.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}let d=0;for(;r.depth>0&&null!==(s=o.exec(i))&&d<r.depth;){if(d+=1,!r.plainObjects&&a.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(s[1])}return s&&c.push("["+i.slice(s.index)+"]"),u(c,t,r,n)},d=function(e){if(!e)return o;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let t=void 0===e.charset?o.charset:e.charset,r=void 0===e.duplicates?o.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||o.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:o.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:o.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:o.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:o.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:o.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:o.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:o.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:o.decoder,delimiter:"string"==typeof e.delimiter||n.gd(e.delimiter)?e.delimiter:o.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:o.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:o.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:o.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:o.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:o.strictNullHandling}};function f(e,t){let r=d(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};let a="string"==typeof e?l(e,r):e,i=r.plainObjects?Object.create(null):{},o=Object.keys(a);for(let t=0;t<o.length;++t){let s=o[t],l=c(s,a[s],r,"string"==typeof e);i=n.h1(i,l,r)}return!0===r.allowSparse?i:n.oE(i)}},47092:(e,t,r)=>{"use strict";e.exports=r(31370)},47104:(e,t,r)=>{"use strict";r.d(t,{C:()=>n});let n={diffContainer:{minWidth:"unset"},variables:{dark:{addedBackground:"var(--theme-success-900)",addedColor:"var(--theme-success-100)",diffViewerBackground:"transparent",diffViewerColor:"var(--theme-text)",emptyLineBackground:"var(--theme-elevation-50)",removedBackground:"var(--theme-error-900)",removedColor:"var(--theme-error-100)",wordAddedBackground:"var(--theme-success-800)",wordRemovedBackground:"var(--theme-error-800)"},light:{addedBackground:"var(--theme-success-100)",addedColor:"var(--theme-success-900)",diffViewerBackground:"transparent",diffViewerColor:"var(--theme-text)",emptyLineBackground:"var(--theme-elevation-50)",removedBackground:"var(--theme-error-100)",removedColor:"var(--theme-error-900)",wordAddedBackground:"var(--theme-success-200)",wordRemovedBackground:"var(--theme-error-200)"}},wordAdded:{color:"var(--theme-success-600)"},wordRemoved:{color:"var(--theme-error-600)",textDecorationLine:"line-through"}}},47235:()=>{},47756:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(26520),i=r(59526);t.default={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message:({keyword:e,schemaCode:t})=>(0,n.str)`must NOT have ${"maxLength"===e?"more":"fewer"} than ${t} characters`,params:({schemaCode:e})=>(0,n._)`{limit: ${e}}`},code(e){let{keyword:t,data:r,schemaCode:o,it:s}=e,l="maxLength"===t?n.operators.GT:n.operators.LT,u=!1===s.opts.unicode?(0,n._)`${r}.length`:(0,n._)`${(0,a.useFunc)(e.gen,i.default)}(${r})`;e.fail$data((0,n._)`${u} ${l} ${o}`)}}},48148:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0;let n=r(55150),a=r(26520);t.getSubschema=function(e,{keyword:t,schemaProp:r,schema:i,schemaPath:o,errSchemaPath:s,topSchemaRef:l}){if(void 0!==t&&void 0!==i)throw Error('both "keyword" and "schema" passed, only one allowed');if(void 0!==t){let i=e.schema[t];return void 0===r?{schema:i,schemaPath:(0,n._)`${e.schemaPath}${(0,n.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:i[r],schemaPath:(0,n._)`${e.schemaPath}${(0,n.getProperty)(t)}${(0,n.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,a.escapeFragment)(r)}`}}if(void 0!==i){if(void 0===o||void 0===s||void 0===l)throw Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:i,schemaPath:o,topSchemaRef:l,errSchemaPath:s}}throw Error('either "keyword" or "schema" must be passed')},t.extendSubschemaData=function(e,t,{dataProp:r,dataPropType:i,data:o,dataTypes:s,propertyName:l}){if(void 0!==o&&void 0!==r)throw Error('both "data" and "dataProp" passed, only one allowed');let{gen:u}=t;if(void 0!==r){let{errorPath:o,dataPathArr:s,opts:l}=t;c(u.let("data",(0,n._)`${t.data}${(0,n.getProperty)(r)}`,!0)),e.errorPath=(0,n.str)`${o}${(0,a.getErrorPath)(r,i,l.jsPropertySyntax)}`,e.parentDataProperty=(0,n._)`${r}`,e.dataPathArr=[...s,e.parentDataProperty]}function c(r){e.data=r,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,r]}void 0!==o&&(c(o instanceof n.Name?o:u.let("data",o,!0)),void 0!==l&&(e.propertyName=l)),s&&(e.dataTypes=s)},t.extendSubschemaMode=function(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:a,allErrors:i}){void 0!==n&&(e.compositeRule=n),void 0!==a&&(e.createErrors=a),void 0!==i&&(e.allErrors=i),e.jtdDiscriminator=t,e.jtdMetadata=r}},48416:(e,t,r)=>{"use strict";r.d(t,{r:()=>n});let n=e=>"string"==typeof e||"number"==typeof e?e:e.id},49380:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(59228);class a extends Error{constructor(e,t,r,a){super(a||`can't resolve reference ${r} from id ${t}`),this.missingRef=(0,n.resolveUrl)(e,t,r),this.missingSchema=(0,n.normalizeId)((0,n.getFullPath)(e,this.missingRef))}}t.default=a},50969:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignDefaults=void 0;let n=r(55150),a=r(26520);function i(e,t,r){let{gen:i,compositeRule:o,data:s,opts:l}=e;if(void 0===r)return;let u=(0,n._)`${s}${(0,n.getProperty)(t)}`;if(o)return void(0,a.checkStrictMode)(e,`default is ignored for: ${u}`);let c=(0,n._)`${u} === undefined`;"empty"===l.useDefaults&&(c=(0,n._)`${c} || ${u} === null || ${u} === ""`),i.if(c,(0,n._)`${u} = ${(0,n.stringify)(r)}`)}t.assignDefaults=function(e,t){let{properties:r,items:n}=e.schema;if("object"===t&&r)for(let t in r)i(e,t,r[t].default);else"array"===t&&Array.isArray(n)&&n.forEach((t,r)=>i(e,r,t.default))}},52536:(e,t,r)=>{"use strict";function n(e){return function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.match(e.matchPattern);if(!n)return null;let a=n[0],i=t.match(e.parsePattern);if(!i)return null;let o=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:o=r.valueCallback?r.valueCallback(o):o,rest:t.slice(a.length)}}}r.d(t,{K:()=>n})},53791:(e,t,r)=>{"use strict";function n(e){return["image/jpeg","image/png","image/gif","image/svg+xml","image/webp","image/avif","image/jxl"].indexOf(e)>-1}r.d(t,{w:()=>n})},54201:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,_J:()=>o,j1:()=>s});let n=String.prototype.replace,a=/%20/g,i={RFC1738:"RFC1738",RFC3986:"RFC3986"},o={RFC1738:function(e){return n.call(e,a,"+")},RFC3986:function(e){return String(e)}},s=i.RFC1738;i.RFC3986;let l=i.RFC3986},54297:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(a=n;0!=a--;)if(!e(t[a],r[a]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(i=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(a=n;0!=a--;)if(!Object.prototype.hasOwnProperty.call(r,i[a]))return!1;for(a=n;0!=a--;){var n,a,i,o=i[a];if(!e(t[o],r[o]))return!1}return!0}return t!=t&&r!=r}},54417:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(29694),a=r(68297),i=r(8253),o=r(92061),s=r(60054),l=r(88414),u=r(7430),c=r(34283),d=r(6114),f=r(90446),p=r(796),m=r(87710),g=r(452),y=r(62919),v=r(1408),b=r(13401);t.default=function(e=!1){let t=[p.default,m.default,g.default,y.default,v.default,b.default,u.default,c.default,l.default,d.default,f.default];return e?t.push(a.default,o.default):t.push(n.default,i.default),t.push(s.default),t}},54982:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0;let n=r(55150),a=r(12208),i=r(37742),o=r(95439);function s(e){let{gen:t,data:r,it:a}=e;t.if(a.parentData,()=>t.assign(r,(0,n._)`${a.parentData}[${a.parentDataProperty}]`))}function l(e,t,r){if(void 0===r)throw Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword","function"==typeof r?{ref:r}:{ref:r,code:(0,n.stringify)(r)})}t.macroKeywordCode=function(e,t){let{gen:r,keyword:a,schema:i,parentSchema:o,it:s}=e,u=t.macro.call(s.self,i,o,s),c=l(r,a,u);!1!==s.opts.validateSchema&&s.self.validateSchema(u,!0);let d=r.name("valid");e.subschema({schema:u,schemaPath:n.nil,errSchemaPath:`${s.errSchemaPath}/${a}`,topSchemaRef:c,compositeRule:!0},d),e.pass(d,()=>e.error(!0))},t.funcKeywordCode=function(e,t){var r;let{gen:u,keyword:c,schema:d,parentSchema:f,$data:p,it:m}=e;!function({schemaEnv:e},t){if(t.async&&!e.$async)throw Error("async keyword in sync schema")}(m,t);let g=l(u,c,!p&&t.compile?t.compile.call(m.self,d,f,m):t.validate),y=u.let("valid");function v(r=t.async?(0,n._)`await `:n.nil){let o=m.opts.passContext?a.default.this:a.default.self,s=!("compile"in t&&!p||!1===t.schema);u.assign(y,(0,n._)`${r}${(0,i.callValidateCode)(e,g,o,s)}`,t.modifying)}function b(e){var r;u.if((0,n.not)(null!=(r=t.valid)?r:y),e)}e.block$data(y,function(){if(!1===t.errors)v(),t.modifying&&s(e),b(()=>e.error());else{let r=t.async?function(){let e=u.let("ruleErrs",null);return u.try(()=>v((0,n._)`await `),t=>u.assign(y,!1).if((0,n._)`${t} instanceof ${m.ValidationError}`,()=>u.assign(e,(0,n._)`${t}.errors`),()=>u.throw(t))),e}():function(){let e=(0,n._)`${g}.errors`;return u.assign(e,null),v(n.nil),e}();t.modifying&&s(e),b(()=>(function(e,t){let{gen:r}=e;r.if((0,n._)`Array.isArray(${t})`,()=>{r.assign(a.default.vErrors,(0,n._)`${a.default.vErrors} === null ? ${t} : ${a.default.vErrors}.concat(${t})`).assign(a.default.errors,(0,n._)`${a.default.vErrors}.length`),(0,o.extendErrors)(e)},()=>e.error())})(e,r))}}),e.ok(null!=(r=t.valid)?r:y)},t.validSchemaType=function(e,t,r=!1){return!t.length||t.some(t=>"array"===t?Array.isArray(e):"object"===t?e&&"object"==typeof e&&!Array.isArray(e):typeof e==t||r&&void 0===e)},t.validateKeywordUsage=function({schema:e,opts:t,self:r,errSchemaPath:n},a,i){if(Array.isArray(a.keyword)?!a.keyword.includes(i):a.keyword!==i)throw Error("ajv implementation error");let o=a.dependencies;if(null==o?void 0:o.some(t=>!Object.prototype.hasOwnProperty.call(e,t)))throw Error(`parent schema must have dependencies of ${i}: ${o.join(",")}`);if(a.validateSchema&&!a.validateSchema(e[i])){let e=`keyword "${i}" value is invalid at path "${n}": `+r.errorsText(a.validateSchema.errors);if("log"===t.validateSchema)r.logger.error(e);else throw Error(e)}}},55032:(e,t,r)=>{"use strict";r.d(t,{sC:()=>a,pP:()=>n,t:()=>u});let n=async e=>{let t;switch(e){case"ar":t=(await r.e(586).then(r.bind(r,10586))).ar;break;case"az":t=(await r.e(7811).then(r.bind(r,17811))).az;break;case"bg":t=(await r.e(2195).then(r.bind(r,92195))).bg;break;case"ca":t=(await r.e(4741).then(r.bind(r,74741))).ca;break;case"cs":t=(await r.e(1301).then(r.bind(r,61301))).cs;break;case"da":t=(await r.e(1673).then(r.bind(r,41673))).da;break;case"de":t=(await r.e(7100).then(r.bind(r,97100))).de;break;case"en-US":t=(await Promise.resolve().then(r.bind(r,1044))).enUS;break;case"es":t=(await r.e(794).then(r.bind(r,60794))).es;break;case"et":t=(await r.e(1287).then(r.bind(r,81287))).et;break;case"fa-IR":t=(await r.e(1140).then(r.bind(r,51140))).faIR;break;case"fr":t=(await r.e(2268).then(r.bind(r,42268))).fr;break;case"he":t=(await r.e(1893).then(r.bind(r,81893))).he;break;case"hr":t=(await r.e(7960).then(r.bind(r,57960))).hr;break;case"hu":t=(await r.e(5822).then(r.bind(r,85822))).hu;break;case"it":t=(await r.e(4325).then(r.bind(r,14325))).it;break;case"ja":t=(await r.e(3855).then(r.bind(r,63855))).ja;break;case"ko":t=(await r.e(735).then(r.bind(r,50735))).ko;break;case"lt":t=(await r.e(8848).then(r.bind(r,38848))).lt;break;case"nb":t=(await r.e(2564).then(r.bind(r,92564))).nb;break;case"nl":t=(await r.e(3272).then(r.bind(r,3272))).nl;break;case"pl":t=(await r.e(6845).then(r.bind(r,96845))).pl;break;case"pt":t=(await r.e(6077).then(r.bind(r,16077))).pt;break;case"ro":t=(await r.e(4979).then(r.bind(r,44979))).ro;break;case"rs":t=(await r.e(5595).then(r.bind(r,75595))).sr;break;case"rs-Latin":t=(await r.e(5337).then(r.bind(r,65337))).srLatn;break;case"ru":t=(await r.e(4003).then(r.bind(r,84003))).ru;break;case"sk":t=(await r.e(804).then(r.bind(r,10804))).sk;break;case"sl-SI":t=(await r.e(5494).then(r.bind(r,35494))).sl;break;case"sv":t=(await r.e(716).then(r.bind(r,60716))).sv;break;case"th":t=(await r.e(8158).then(r.bind(r,98158))).th;break;case"tr":t=(await r.e(5044).then(r.bind(r,5044))).tr;break;case"uk":t=(await r.e(9888).then(r.bind(r,49888))).uk;break;case"vi":t=(await r.e(7254).then(r.bind(r,27254))).vi;break;case"zh-CN":t=(await r.e(2724).then(r.bind(r,22724))).zhCN;break;case"zh-TW":t=(await r.e(1772).then(r.bind(r,21772))).zhTW}return t?.default?t.default:t},a=(e,t)=>{if("object"==typeof e&&!Object.prototype.hasOwnProperty.call(e,"$$typeof")){if(e[t.language])return e[t.language];let r=[];"string"==typeof t.fallbackLanguage?r=[t.fallbackLanguage]:Array.isArray(t.fallbackLanguage)&&(r=t.fallbackLanguage);let n=r.find(t=>e[t]);return n&&e[n]?e[n]:e[Object.keys(e)[0]]}return"function"==typeof e?e({i18n:void 0,t:t.t}):e},i=["authentication:account","authentication:accountOfCurrentUser","authentication:accountVerified","authentication:alreadyActivated","authentication:alreadyLoggedIn","authentication:apiKey","authentication:authenticated","authentication:backToLogin","authentication:beginCreateFirstUser","authentication:changePassword","authentication:checkYourEmailForPasswordReset","authentication:confirmGeneration","authentication:confirmPassword","authentication:createFirstUser","authentication:emailNotValid","authentication:usernameNotValid","authentication:emailOrUsername","authentication:emailSent","authentication:emailVerified","authentication:enableAPIKey","authentication:failedToUnlock","authentication:forceUnlock","authentication:forgotPassword","authentication:forgotPasswordEmailInstructions","authentication:forgotPasswordUsernameInstructions","authentication:forgotPasswordQuestion","authentication:generate","authentication:generateNewAPIKey","authentication:generatingNewAPIKeyWillInvalidate","authentication:logBackIn","authentication:loggedOutInactivity","authentication:loggedOutSuccessfully","authentication:loggingOut","authentication:login","authentication:logOut","authentication:loggedIn","authentication:loggedInChangePassword","authentication:logout","authentication:logoutUser","authentication:logoutSuccessful","authentication:newAPIKeyGenerated","authentication:newPassword","authentication:passed","authentication:passwordResetSuccessfully","authentication:resetPassword","authentication:stayLoggedIn","authentication:successfullyRegisteredFirstUser","authentication:successfullyUnlocked","authentication:username","authentication:unableToVerify","authentication:tokenRefreshSuccessful","authentication:verified","authentication:verifiedSuccessfully","authentication:verify","authentication:verifyUser","authentication:youAreInactive","error:autosaving","error:correctInvalidFields","error:deletingTitle","error:emailOrPasswordIncorrect","error:usernameOrPasswordIncorrect","error:loadingDocument","error:invalidRequestArgs","error:invalidFileType","error:logoutFailed","error:noMatchedField","error:notAllowedToAccessPage","error:previewing","error:unableToDeleteCount","error:unableToReindexCollection","error:unableToUpdateCount","error:unauthorized","error:unauthorizedAdmin","error:unknown","error:unspecific","error:unverifiedEmail","error:userEmailAlreadyRegistered","error:usernameAlreadyRegistered","error:tokenNotProvided","error:unPublishingDocument","error:problemUploadingFile","fields:addLabel","fields:addLink","fields:addNew","fields:addNewLabel","fields:addRelationship","fields:addUpload","fields:block","fields:blocks","fields:blockType","fields:chooseBetweenCustomTextOrDocument","fields:customURL","fields:chooseDocumentToLink","fields:openInNewTab","fields:enterURL","fields:internalLink","fields:chooseFromExisting","fields:linkType","fields:textToDisplay","fields:collapseAll","fields:editLink","fields:editRelationship","fields:itemsAndMore","fields:labelRelationship","fields:latitude","fields:linkedTo","fields:longitude","fields:passwordsDoNotMatch","fields:removeRelationship","fields:removeUpload","fields:saveChanges","fields:searchForBlock","fields:selectFieldsToEdit","fields:showAll","fields:swapRelationship","fields:swapUpload","fields:toggleBlock","fields:uploadNewLabel","folder:byFolder","folder:browseByFolder","folder:deleteFolder","folder:folders","folder:folderName","folder:itemsMovedToFolder","folder:itemsMovedToRoot","folder:itemHasBeenMoved","folder:itemHasBeenMovedToRoot","folder:moveFolder","folder:movingFromFolder","folder:moveItemsToFolderConfirmation","folder:moveItemsToRootConfirmation","folder:moveItemToFolderConfirmation","folder:moveItemToRootConfirmation","folder:noFolder","folder:newFolder","folder:renameFolder","folder:searchByNameInFolder","folder:selectFolderForItem","general:all","general:aboutToDeleteCount","general:aboutToDelete","general:addBelow","general:addFilter","general:adminTheme","general:allCollections","general:and","general:anotherUser","general:anotherUserTakenOver","general:applyChanges","general:ascending","general:automatic","general:backToDashboard","general:cancel","general:changesNotSaved","general:close","general:collapse","general:collections","general:confirmMove","general:yes","general:no","general:columns","general:columnToSort","general:confirm","general:confirmCopy","general:confirmDeletion","general:confirmDuplication","general:confirmReindex","general:confirmReindexAll","general:confirmReindexDescription","general:confirmReindexDescriptionAll","general:copied","general:clearAll","general:copy","general:copyWarning","general:copying","general:create","general:created","general:createdAt","general:createNew","general:createNewLabel","general:creating","general:creatingNewLabel","general:currentlyEditing","general:custom","general:dark","general:dashboard","general:delete","general:deletedSuccessfully","general:deletedCountSuccessfully","general:deleting","general:descending","general:depth","general:deselectAllRows","general:document","general:documentLocked","general:documents","general:duplicate","general:duplicateWithoutSaving","general:edit","general:editAll","general:editing","general:editingLabel","general:editingTakenOver","general:editLabel","general:editedSince","general:email","general:emailAddress","general:enterAValue","general:error","general:errors","general:fallbackToDefaultLocale","general:false","general:filters","general:filterWhere","general:globals","general:goBack","general:isEditing","general:item","general:items","general:language","general:lastModified","general:leaveAnyway","general:leaveWithoutSaving","general:light","general:livePreview","general:loading","general:locale","general:menu","general:moreOptions","general:move","general:moveConfirm","general:moveCount","general:moveDown","general:moveUp","general:moving","general:movingCount","general:name","general:next","general:noDateSelected","general:noFiltersSet","general:noLabel","general:none","general:noOptions","general:noResults","general:notFound","general:nothingFound","general:noUpcomingEventsScheduled","general:noValue","general:of","general:open","general:only","general:or","general:order","general:overwriteExistingData","general:pageNotFound","general:password","general:payloadSettings","general:perPage","general:previous","general:reindex","general:reindexingAll","general:remove","general:rename","general:reset","general:resetPreferences","general:resetPreferencesDescription","general:resettingPreferences","general:row","general:rows","general:save","general:schedulePublishFor","general:saving","general:searchBy","general:select","general:selectAll","general:selectAllRows","general:selectedCount","general:selectLabel","general:selectValue","general:showAllLabel","general:sorryNotFound","general:sort","general:sortByLabelDirection","general:stayOnThisPage","general:submissionSuccessful","general:submit","general:submitting","general:success","general:successfullyCreated","general:successfullyDuplicated","general:successfullyReindexed","general:takeOver","general:thisLanguage","general:time","general:timezone","general:titleDeleted","general:import","general:export","general:allLocales","general:true","general:upcomingEvents","general:users","general:user","general:username","general:unauthorized","general:unsavedChanges","general:unsavedChangesDuplicate","general:untitled","general:updatedAt","general:updatedLabelSuccessfully","general:updatedCountSuccessfully","general:updateForEveryone","general:updatedSuccessfully","general:updating","general:value","general:viewReadOnly","general:uploading","general:uploadingBulk","general:welcome","localization:localeToPublish","localization:copyToLocale","localization:copyFromTo","localization:selectLocaleToCopy","localization:cannotCopySameLocale","localization:copyFrom","localization:copyTo","operators:equals","operators:exists","operators:isNotIn","operators:isIn","operators:contains","operators:isLike","operators:isNotLike","operators:isNotEqualTo","operators:near","operators:isGreaterThan","operators:isLessThan","operators:isGreaterThanOrEqualTo","operators:isLessThanOrEqualTo","operators:within","operators:intersects","upload:addFile","upload:addFiles","upload:bulkUpload","upload:crop","upload:cropToolDescription","upload:dragAndDrop","upload:editImage","upload:fileToUpload","upload:filesToUpload","upload:focalPoint","upload:focalPointDescription","upload:height","upload:pasteURL","upload:previewSizes","upload:selectCollectionToBrowse","upload:selectFile","upload:setCropArea","upload:setFocalPoint","upload:sizesFor","upload:sizes","upload:width","upload:fileName","upload:fileSize","upload:noFile","upload:download","validation:emailAddress","validation:enterNumber","validation:fieldHasNo","validation:greaterThanMax","validation:invalidInput","validation:invalidSelection","validation:invalidSelections","validation:lessThanMin","validation:limitReached","validation:longerThanMin","validation:notValidDate","validation:required","validation:requiresAtLeast","validation:requiresNoMoreThan","validation:requiresTwoNumbers","validation:shorterThanMax","validation:trueOrFalse","validation:timezoneRequired","validation:username","validation:validUploadID","version:aboutToPublishSelection","version:aboutToRestore","version:aboutToRestoreGlobal","version:aboutToRevertToPublished","version:aboutToUnpublish","version:aboutToUnpublishSelection","version:autosave","version:autosavedSuccessfully","version:autosavedVersion","version:changed","version:changedFieldsCount","version:confirmRevertToSaved","version:compareVersion","version:confirmPublish","version:confirmUnpublish","version:confirmVersionRestoration","version:currentDraft","version:currentPublishedVersion","version:draft","version:draftSavedSuccessfully","version:lastSavedAgo","version:modifiedOnly","version:noFurtherVersionsFound","version:noRowsFound","version:noRowsSelected","version:preview","version:previouslyPublished","version:problemRestoringVersion","version:publish","version:publishAllLocales","version:publishChanges","version:published","version:publishIn","version:publishing","version:restoreAsDraft","version:restoredSuccessfully","version:restoreThisVersion","version:restoring","version:reverting","version:revertToPublished","version:saveDraft","version:scheduledSuccessfully","version:schedulePublish","version:selectLocales","version:selectVersionToCompare","version:showLocales","version:status","version:type","version:unpublish","version:unpublishing","version:versionCreatedOn","version:versionID","version:version","version:versions","version:viewingVersion","version:viewingVersionGlobal","version:viewingVersions","version:viewingVersionsGlobal"],o=(e,t)=>"client"===t?function e(t){let r={};return Object.keys(t).sort().forEach(n=>{"object"==typeof t[n]?r[n]=e(t[n]):r[n]=t[n]}),r}(function e(t,r="",n){let a={};for(let[i,o]of Object.entries(t)){if("$schema"===i){a[i]=o;continue}if("object"==typeof o){let t=e(o,i,n);Object.keys(t).length>0&&(a[i]=t)}else for(let e of n){let[t,n]=e.split(":");r===t&&(i===n?a[n]=o:["zero","one","two","few","many","other"].forEach(e=>{i===`${n}_${e}`&&(a[`${n}_${e}`]=o)}))}}return a}(e.translations,"",i)):e.translations,s=({count:e,key:t,translations:r})=>{let n=t.split(":"),a="",i=n.reduce((t,r,i)=>{if("string"==typeof t)return t;"number"==typeof e&&(0===e&&`${r}_zero`in t?a="_zero":1===e&&`${r}_one`in t?a="_one":2===e&&`${r}_two`in t?a="_two":e>5&&`${r}_many`in t?a="_many":e>2&&e<=5&&`${r}_few`in t?a="_few":`${r}_other`in t&&(a="_other"));let o=r;if(i===n.length-1&&a&&(o=`${r}${a}`),t&&o in t)return t[o]},r);return i||console.log("key not found:",t),i||t},l=({translationString:e,vars:t})=>e.split(/(\{\{.*?\}\})/).map(e=>{if(!(e.startsWith("{{")&&e.endsWith("}}")))return e;{let r=t[e.substring(2,e.length-2).trim()];return null!=r?r:e}}).join("");function u({key:e,translations:t,vars:r}){let n=s({count:"number"==typeof r?.count?r.count:void 0,key:e,translations:t});return r&&(n=l({translationString:n,vars:r})),n||(n=e),n}let c=e=>{let{config:t,language:r,translations:n}=e,a=r&&t?.translations?.[r]?function e(t,r){let n={...t};for(let a in r)Object.prototype.hasOwnProperty.call(r,a)&&("object"==typeof r[a]&&!Array.isArray(r[a])&&t[a]?n[a]=e(t[a],r[a]):n[a]=r[a]);return n}(n,t.translations[r]):n;return{t:(e,t)=>u({key:e,translations:a,vars:t}),translations:a}};!function(e,t){let r=new Map}(async({config:e,context:t,language:r=e.fallbackLanguage})=>{if(!r||!e.supportedLanguages?.[r])throw Error(`Language ${r} not supported`);let a=o(e.supportedLanguages?.[r],t),{t:i,translations:s}=c({config:e,language:r||e.fallbackLanguage,translations:a}),l=e.supportedLanguages[r]?.dateFNSKey||"en-US";return{dateFNS:await n(l),dateFNSKey:l,fallbackLanguage:e.fallbackLanguage,language:r||e.fallbackLanguage,t:i,translations:s}},["language","context"])},55150:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;let n=r(11659),a=r(81998);var i=r(11659);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return i._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return i.str}}),Object.defineProperty(t,"strConcat",{enumerable:!0,get:function(){return i.strConcat}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return i.nil}}),Object.defineProperty(t,"getProperty",{enumerable:!0,get:function(){return i.getProperty}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return i.stringify}}),Object.defineProperty(t,"regexpCode",{enumerable:!0,get:function(){return i.regexpCode}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return i.Name}});var o=r(81998);Object.defineProperty(t,"Scope",{enumerable:!0,get:function(){return o.Scope}}),Object.defineProperty(t,"ValueScope",{enumerable:!0,get:function(){return o.ValueScope}}),Object.defineProperty(t,"ValueScopeName",{enumerable:!0,get:function(){return o.ValueScopeName}}),Object.defineProperty(t,"varKinds",{enumerable:!0,get:function(){return o.varKinds}}),t.operators={GT:new n._Code(">"),GTE:new n._Code(">="),LT:new n._Code("<"),LTE:new n._Code("<="),EQ:new n._Code("==="),NEQ:new n._Code("!=="),NOT:new n._Code("!"),OR:new n._Code("||"),AND:new n._Code("&&"),ADD:new n._Code("+")};class s{optimizeNodes(){return this}optimizeNames(e,t){return this}}class l extends s{constructor(e,t,r){super(),this.varKind=e,this.name=t,this.rhs=r}render({es5:e,_n:t}){let r=e?a.varKinds.var:this.varKind,n=void 0===this.rhs?"":` = ${this.rhs}`;return`${r} ${this.name}${n};`+t}optimizeNames(e,t){if(e[this.name.str])return this.rhs&&(this.rhs=O(this.rhs,e,t)),this}get names(){return this.rhs instanceof n._CodeOrName?this.rhs.names:{}}}class u extends s{constructor(e,t,r){super(),this.lhs=e,this.rhs=t,this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(!(this.lhs instanceof n.Name)||e[this.lhs.str]||this.sideEffects)return this.rhs=O(this.rhs,e,t),this}get names(){return T(this.lhs instanceof n.Name?{}:{...this.lhs.names},this.rhs)}}class c extends u{constructor(e,t,r,n){super(e,r,n),this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class d extends s{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`${this.label}:`+e}}class f extends s{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){let t=this.label?` ${this.label}`:"";return`break${t};`+e}}class p extends s{constructor(e){super(),this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class m extends s{constructor(e){super(),this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(e,t){return this.code=O(this.code,e,t),this}get names(){return this.code instanceof n._CodeOrName?this.code.names:{}}}class g extends s{constructor(e=[]){super(),this.nodes=e}render(e){return this.nodes.reduce((t,r)=>t+r.render(e),"")}optimizeNodes(){let{nodes:e}=this,t=e.length;for(;t--;){let r=e[t].optimizeNodes();Array.isArray(r)?e.splice(t,1,...r):r?e[t]=r:e.splice(t,1)}return e.length>0?this:void 0}optimizeNames(e,t){let{nodes:r}=this,n=r.length;for(;n--;){let a=r[n];a.optimizeNames(e,t)||(function(e,t){for(let r in t)e[r]=(e[r]||0)-(t[r]||0)}(e,a.names),r.splice(n,1))}return r.length>0?this:void 0}get names(){return this.nodes.reduce((e,t)=>A(e,t.names),{})}}class y extends g{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class v extends g{}class b extends y{}b.kind="else";class w extends y{constructor(e,t){super(t),this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);return this.else&&(t+="else "+this.else.render(e)),t}optimizeNodes(){super.optimizeNodes();let e=this.condition;if(!0===e)return this.nodes;let t=this.else;if(t){let e=t.optimizeNodes();t=this.else=Array.isArray(e)?new b(e):e}return t?!1===e?t instanceof w?t:t.nodes:this.nodes.length?this:new w(D(e),t instanceof w?[t]:t.nodes):!1!==e&&this.nodes.length?this:void 0}optimizeNames(e,t){var r;if(this.else=null==(r=this.else)?void 0:r.optimizeNames(e,t),super.optimizeNames(e,t)||this.else)return this.condition=O(this.condition,e,t),this}get names(){let e=super.names;return T(e,this.condition),this.else&&A(e,this.else.names),e}}w.kind="if";class x extends y{}x.kind="for";class $ extends x{constructor(e){super(),this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iteration=O(this.iteration,e,t),this}get names(){return A(super.names,this.iteration.names)}}class k extends x{constructor(e,t,r,n){super(),this.varKind=e,this.name=t,this.from=r,this.to=n}render(e){let t=e.es5?a.varKinds.var:this.varKind,{name:r,from:n,to:i}=this;return`for(${t} ${r}=${n}; ${r}<${i}; ${r}++)`+super.render(e)}get names(){let e=T(super.names,this.from);return T(e,this.to)}}class S extends x{constructor(e,t,r,n){super(),this.loop=e,this.varKind=t,this.name=r,this.iterable=n}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iterable=O(this.iterable,e,t),this}get names(){return A(super.names,this.iterable.names)}}class _ extends y{constructor(e,t,r){super(),this.name=e,this.args=t,this.async=r}render(e){let t=this.async?"async ":"";return`${t}function ${this.name}(${this.args})`+super.render(e)}}_.kind="func";class j extends g{render(e){return"return "+super.render(e)}}j.kind="return";class E extends y{render(e){let t="try"+super.render(e);return this.catch&&(t+=this.catch.render(e)),this.finally&&(t+=this.finally.render(e)),t}optimizeNodes(){var e,t;return super.optimizeNodes(),null==(e=this.catch)||e.optimizeNodes(),null==(t=this.finally)||t.optimizeNodes(),this}optimizeNames(e,t){var r,n;return super.optimizeNames(e,t),null==(r=this.catch)||r.optimizeNames(e,t),null==(n=this.finally)||n.optimizeNames(e,t),this}get names(){let e=super.names;return this.catch&&A(e,this.catch.names),this.finally&&A(e,this.finally.names),e}}class C extends y{constructor(e){super(),this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}C.kind="catch";class P extends y{render(e){return"finally"+super.render(e)}}P.kind="finally";class N{constructor(e,t={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...t,_n:t.lines?"\n":""},this._extScope=e,this._scope=new a.Scope({parent:e}),this._nodes=[new v]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){let r=this._extScope.value(e,t);return(this._values[r.prefix]||(this._values[r.prefix]=new Set)).add(r),r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,n){let a=this._scope.toName(t);return void 0!==r&&n&&(this._constants[a.str]=r),this._leafNode(new l(e,a,r)),a}const(e,t,r){return this._def(a.varKinds.const,e,t,r)}let(e,t,r){return this._def(a.varKinds.let,e,t,r)}var(e,t,r){return this._def(a.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new u(e,t,r))}add(e,r){return this._leafNode(new c(e,t.operators.ADD,r))}code(e){return"function"==typeof e?e():e!==n.nil&&this._leafNode(new m(e)),this}object(...e){let t=["{"];for(let[r,a]of e)t.length>1&&t.push(","),t.push(r),(r!==a||this.opts.es5)&&(t.push(":"),(0,n.addCodeArg)(t,a));return t.push("}"),new n._Code(t)}if(e,t,r){if(this._blockNode(new w(e)),t&&r)this.code(t).else().code(r).endIf();else if(t)this.code(t).endIf();else if(r)throw Error('CodeGen: "else" body without "then" body');return this}elseIf(e){return this._elseNode(new w(e))}else(){return this._elseNode(new b)}endIf(){return this._endBlockNode(w,b)}_for(e,t){return this._blockNode(e),t&&this.code(t).endFor(),this}for(e,t){return this._for(new $(e),t)}forRange(e,t,r,n,i=this.opts.es5?a.varKinds.var:a.varKinds.let){let o=this._scope.toName(e);return this._for(new k(i,o,t,r),()=>n(o))}forOf(e,t,r,i=a.varKinds.const){let o=this._scope.toName(e);if(this.opts.es5){let e=t instanceof n.Name?t:this.var("_arr",t);return this.forRange("_i",0,(0,n._)`${e}.length`,t=>{this.var(o,(0,n._)`${e}[${t}]`),r(o)})}return this._for(new S("of",i,o,t),()=>r(o))}forIn(e,t,r,i=this.opts.es5?a.varKinds.var:a.varKinds.const){if(this.opts.ownProperties)return this.forOf(e,(0,n._)`Object.keys(${t})`,r);let o=this._scope.toName(e);return this._for(new S("in",i,o,t),()=>r(o))}endFor(){return this._endBlockNode(x)}label(e){return this._leafNode(new d(e))}break(e){return this._leafNode(new f(e))}return(e){let t=new j;if(this._blockNode(t),this.code(e),1!==t.nodes.length)throw Error('CodeGen: "return" should have one node');return this._endBlockNode(j)}try(e,t,r){if(!t&&!r)throw Error('CodeGen: "try" without "catch" and "finally"');let n=new E;if(this._blockNode(n),this.code(e),t){let e=this.name("e");this._currNode=n.catch=new C(e),t(e)}return r&&(this._currNode=n.finally=new P,this.code(r)),this._endBlockNode(C,P)}throw(e){return this._leafNode(new p(e))}block(e,t){return this._blockStarts.push(this._nodes.length),e&&this.code(e).endBlock(t),this}endBlock(e){let t=this._blockStarts.pop();if(void 0===t)throw Error("CodeGen: not in self-balancing block");let r=this._nodes.length-t;if(r<0||void 0!==e&&r!==e)throw Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`);return this._nodes.length=t,this}func(e,t=n.nil,r,a){return this._blockNode(new _(e,t,r)),a&&this.code(a).endFunc(),this}endFunc(){return this._endBlockNode(_)}optimize(e=1){for(;e-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(e){return this._currNode.nodes.push(e),this}_blockNode(e){this._currNode.nodes.push(e),this._nodes.push(e)}_endBlockNode(e,t){let r=this._currNode;if(r instanceof e||t&&r instanceof t)return this._nodes.pop(),this;throw Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){let t=this._currNode;if(!(t instanceof w))throw Error('CodeGen: "else" without "if"');return this._currNode=t.else=e,this}get _root(){return this._nodes[0]}get _currNode(){let e=this._nodes;return e[e.length-1]}set _currNode(e){let t=this._nodes;t[t.length-1]=e}}function A(e,t){for(let r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function T(e,t){return t instanceof n._CodeOrName?A(e,t.names):e}function O(e,t,r){var a;if(e instanceof n.Name)return i(e);if(!((a=e)instanceof n._Code&&a._items.some(e=>e instanceof n.Name&&1===t[e.str]&&void 0!==r[e.str])))return e;return new n._Code(e._items.reduce((e,t)=>(t instanceof n.Name&&(t=i(t)),t instanceof n._Code?e.push(...t._items):e.push(t),e),[]));function i(e){let n=r[e.str];return void 0===n||1!==t[e.str]?e:(delete t[e.str],n)}}function D(e){return"boolean"==typeof e||"number"==typeof e||null===e?!e:(0,n._)`!${I(e)}`}t.CodeGen=N,t.not=D;let M=L(t.operators.AND);t.and=function(...e){return e.reduce(M)};let R=L(t.operators.OR);function L(e){return(t,r)=>t===n.nil?r:r===n.nil?t:(0,n._)`${I(t)} ${e} ${I(r)}`}function I(e){return e instanceof n.Name?e:(0,n._)`(${e})`}t.or=function(...e){return e.reduce(R)}},56156:(e,t,r)=>{"use strict";r.d(t,{a:()=>G,b:()=>K,c:()=>et,d:()=>er,e:()=>en,f:()=>X,g:()=>Z,h:()=>ee,i:()=>ei});var n=r(89688);r(80665);var a=r(47092),i=r(67252);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){return function t(){for(var r=this,n=arguments.length,a=Array(n),i=0;i<n;i++)a[i]=arguments[i];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];return t.apply(r,[].concat(a,n))}}}function d(e){return({}).toString.call(e).includes("Object")}function f(e){return"function"==typeof e}var p=c(function(e,t){throw Error(e[t]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),m={changes:function(e,t){return d(t)||p("changeType"),Object.keys(t).some(function(t){return!Object.prototype.hasOwnProperty.call(e,t)})&&p("changeField"),t},selector:function(e){f(e)||p("selectorType")},handler:function(e){f(e)||d(e)||p("handlerType"),d(e)&&Object.values(e).some(function(e){return!f(e)})&&p("handlersType")},initial:function(e){e||p("initialIsRequired"),d(e)||p("initialType"),Object.keys(e).length||p("initialContent")}};function g(e,t){return f(t)?t(e.current):t}function y(e,t){return e.current=u(u({},e.current),t),t}function v(e,t,r){return f(t)?t(e.current):Object.keys(r).forEach(function(r){var n;return null==(n=t[r])?void 0:n.call(t,e.current[r])}),r}var b={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},w=(function(e){return function t(){for(var r=this,n=arguments.length,a=Array(n),i=0;i<n;i++)a[i]=arguments[i];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];return t.apply(r,[].concat(a,n))}}})(function(e,t){throw Error(e[t]||e.default)})(b),x=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}},$={type:"cancelation",msg:"operation is manually canceled"},k=function(e){var t=!1,r=new Promise(function(r,n){e.then(function(e){return t?n($):r(e)}),e.catch(n)});return r.cancel=function(){return t=!0},r},S=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,a=!1,i=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),r.length!==t);n=!0);}catch(e){a=!0,i=e}finally{try{n||null==s.return||s.return()}finally{if(a)throw i}}return r}}(e,2)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(e,t)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(({create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};m.initial(e),m.handler(t);var r={current:e},n=c(v)(r,t),a=c(y)(r),i=c(m.changes)(e),o=c(g)(r);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return m.selector(e),e(r.current)},function(e){(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}})(n,a,i,o)(e)}]}}).create({config:{paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}},isInitialized:!1,resolve:null,reject:null,monaco:null}),2),_=S[0],j=S[1];function E(e){return document.body.appendChild(e)}function C(e){var t,r,n=_(function(e){return{config:e.config,reject:e.reject}}),a=(t="".concat(n.config.paths.vs,"/loader.js"),r=document.createElement("script"),t&&(r.src=t),r);return a.onload=function(){return e()},a.onerror=n.reject,a}function P(){var e=_(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(t){N(t),e.resolve(t)},function(t){e.reject(t)})}function N(e){_().monaco||j({monaco:e})}var A=new Promise(function(e,t){return j({resolve:e,reject:t})}),T={init:function(){var e=_(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(j({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),k(A);if(window.monaco&&window.monaco.editor)return N(window.monaco),e.resolve(window.monaco),k(A);x(E,C)(P)}return k(A)}},O={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},D={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},M=function(e){let{children:t}=e;return n.createElement("div",{style:D.container},t)},R=(0,n.memo)(function(e){let{width:t,height:r,isEditorReady:a,loading:i,_ref:o,className:s,wrapperProps:l}=e;return n.createElement("section",{style:{...O.wrapper,width:t,height:r},...l},!a&&n.createElement(M,null,i),n.createElement("div",{ref:o,style:{...O.fullWidth,...!a&&O.hide},className:s}))}),L=function(e){(0,n.useEffect)(e,[])},I=function(e,t){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=(0,n.useRef)(!0);(0,n.useEffect)(a.current||!r?()=>{a.current=!1}:e,t)};function F(){}function B(e,t,r,n){var a,i,o,s,l,u;return a=e,i=n,a.editor.getModel(z(a,i))||(o=e,s=t,l=r,u=n,o.editor.createModel(s,l,u?z(o,u):void 0))}function z(e,t){return e.Uri.parse(t)}var V=function(e){let t=(0,n.useRef)();return(0,n.useEffect)(()=>{t.current=e},[e]),t.current},U=new Map,q=(0,n.memo)(function(e){let{defaultValue:t,defaultLanguage:r,defaultPath:a,value:i,language:o,path:s,theme:l="light",line:u,loading:c="Loading...",options:d={},overrideServices:f={},saveViewState:p=!0,keepCurrentModel:m=!1,width:g="100%",height:y="100%",className:v,wrapperProps:b={},beforeMount:w=F,onMount:x=F,onChange:$,onValidate:k=F}=e,[S,_]=(0,n.useState)(!1),[j,E]=(0,n.useState)(!0),C=(0,n.useRef)(null),P=(0,n.useRef)(null),N=(0,n.useRef)(null),A=(0,n.useRef)(x),O=(0,n.useRef)(w),D=(0,n.useRef)(),M=(0,n.useRef)(i),z=V(s),q=(0,n.useRef)(!1),H=(0,n.useRef)(!1);L(()=>{let e=T.init();return e.then(e=>(C.current=e)&&E(!1)).catch(e=>(null==e?void 0:e.type)!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>{var t,r;return P.current?void(null==(t=D.current)||t.dispose(),m?p&&U.set(s,P.current.saveViewState()):null==(r=P.current.getModel())||r.dispose(),P.current.dispose()):e.cancel()}}),I(()=>{var e,n,l,u;let c=B(C.current,t||i||"",r||o||"",s||a||"");c!==(null==(e=P.current)?void 0:e.getModel())&&(p&&U.set(z,null==(n=P.current)?void 0:n.saveViewState()),null==(l=P.current)||l.setModel(c),p&&(null==(u=P.current)||u.restoreViewState(U.get(s))))},[s],S),I(()=>{var e;null==(e=P.current)||e.updateOptions(d)},[d],S),I(()=>{P.current&&void 0!==i&&(P.current.getOption(C.current.editor.EditorOption.readOnly)?P.current.setValue(i):i!==P.current.getValue()&&(H.current=!0,P.current.executeEdits("",[{range:P.current.getModel().getFullModelRange(),text:i,forceMoveMarkers:!0}]),P.current.pushUndoStop(),H.current=!1))},[i],S),I(()=>{var e,t;let r=null==(e=P.current)?void 0:e.getModel();r&&o&&(null==(t=C.current)||t.editor.setModelLanguage(r,o))},[o],S),I(()=>{var e;void 0!==u&&(null==(e=P.current)||e.revealLine(u))},[u],S),I(()=>{var e;null==(e=C.current)||e.editor.setTheme(l)},[l],S);let W=(0,n.useCallback)(()=>{if(!(!N.current||!C.current)&&!q.current){var e;O.current(C.current);let n=s||a,c=B(C.current,i||t||"",r||o||"",n||"");P.current=null==(e=C.current)?void 0:e.editor.create(N.current,{model:c,automaticLayout:!0,...d},f),p&&P.current.restoreViewState(U.get(n)),C.current.editor.setTheme(l),void 0!==u&&P.current.revealLine(u),_(!0),q.current=!0}},[t,r,a,i,o,s,d,f,p,l,u]);return(0,n.useEffect)(()=>{S&&A.current(P.current,C.current)},[S]),(0,n.useEffect)(()=>{j||S||W()},[j,S,W]),M.current=i,(0,n.useEffect)(()=>{var e,t;S&&$&&(null==(e=D.current)||e.dispose(),D.current=null==(t=P.current)?void 0:t.onDidChangeModelContent(e=>{H.current||$(P.current.getValue(),e)}))},[S,$]),(0,n.useEffect)(()=>{if(S){let e=C.current.editor.onDidChangeMarkers(e=>{var t;let r=null==(t=P.current.getModel())?void 0:t.uri;if(r&&e.find(e=>e.path===r.path)){let e=C.current.editor.getModelMarkers({resource:r});null==k||k(e)}});return()=>{null==e||e.dispose()}}return()=>{}},[S,k]),n.createElement(R,{width:g,height:y,isEditorReady:S,loading:c,_ref:N,className:v,wrapperProps:b})}),H=(0,n.createContext)(void 0);function W(e){var t;if(!(null==e||null==(t=e.blocks)?void 0:t.length)||e.blocksMap)return e.blocksMap={},e;let r={...e};for(let t of(r.blocksMap={},e.blocks))r.blocksMap[t.slug]=t;return r}var G=e=>{let{children:t,config:r}=e,[a,o]=(0,n.useState)(()=>W(r));(0,n.useEffect)(()=>{o(W(r))},[r]);let{collectionsBySlug:s,globalsBySlug:l}=(0,n.useMemo)(()=>{let e={},t={};for(let t of a.collections)e[t.slug]=t;for(let e of a.globals)t[e.slug]=e;return{collectionsBySlug:e,globalsBySlug:t}},[a]),u=(0,n.useCallback)(e=>{var t,r;return"collectionSlug"in e?null!=(t=s[e.collectionSlug])?t:null:"globalSlug"in e&&null!=(r=l[e.globalSlug])?r:null},[s,l]);return(0,i.jsx)(H,{value:{config:a,getEntityConfig:u,setConfig:o},children:t})},K=()=>(0,n.use)(H),J=(0,n.createContext)({autoMode:!0,setTheme:()=>null,theme:"light"});function Y(e,t,r){let n=new Date;n.setTime(n.getTime()+24*r*36e5);let a="expires="+n.toUTCString();document.cookie=e+"="+t+";"+a+";path=/"}var Q=e=>{var t;let r,n=null==(t=window.document.cookie.split("; ").find(t=>t.startsWith("".concat(e,"="))))?void 0:t.split("=")[1];return r="light"===n||"dark"===n?n:window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",document.documentElement.setAttribute("data-theme",r),{theme:r,themeFromCookies:n}},X="light",Z=e=>{let t,r=(0,a.c)(11),{children:o,theme:s}=e,{config:l}=K(),u=l.admin.theme,c="".concat(l.cookiePrefix||"payload","-theme"),[d,f]=(0,n.useState)(s||X),[p,m]=(0,n.useState)(),g,y;r[0]!==c||r[1]!==u?(g=()=>{if("all"!==u)return;let{theme:e,themeFromCookies:t}=Q(c);f(e),m(!t)},y=[u,c],r[0]=c,r[1]=u,r[2]=g,r[3]=y):(g=r[2],y=r[3]),(0,n.useEffect)(g,y),r[4]!==c?(t=e=>{if("light"===e||"dark"===e)f(e),m(!1),Y(c,e,365),document.documentElement.setAttribute("data-theme",e);else if("auto"===e){Y(c,e,-1);let t=window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.setAttribute("data-theme",t),m(!0),f(t)}},r[4]=c,r[5]=t):t=r[5];let v=t,b;return r[6]!==p||r[7]!==o||r[8]!==v||r[9]!==d?(b=(0,i.jsx)(J,{value:{autoMode:p,setTheme:v,theme:d},children:o}),r[6]=p,r[7]=o,r[8]=v,r[9]=d,r[10]=b):b=r[10],b},ee=()=>(0,n.use)(J),et=(e,t)=>{let r,i=(0,a.c)(9),o=void 0!==t&&t,[s,l]=n.useState(!1),u=n.useRef(void 0),c;i[0]!==e?(c=()=>(l(!1),clearTimeout(u.current),u.current=setTimeout(()=>{l(!0)},e),()=>{clearTimeout(u.current)}),i[0]=e,i[1]=c):c=i[1];let d=c,f,p;return i[2]!==d||i[3]!==o?(f=()=>{o&&d()},p=[d,o],i[2]=d,i[3]=o,i[4]=f,i[5]=p):(f=i[4],p=i[5]),n.useEffect(f,p),i[6]!==s||i[7]!==d?(r=[s,d],i[6]=s,i[7]=d,i[8]=r):r=i[8],r},er=e=>{let{animationDelay:t="0ms",height:r="60px",width:n="100%"}=e;return(0,i.jsx)("div",{className:"shimmer-effect",style:{height:"number"==typeof r?"".concat(r,"px"):r,width:"number"==typeof n?"".concat(n,"px"):n},children:(0,i.jsx)("div",{className:"shimmer-effect__shine",style:{animationDelay:t}})})},en=e=>{let t,r=(0,a.c)(7),{className:n,count:o,height:s,renderDelay:l,shimmerDelay:u,shimmerItemClassName:c,width:d}=e,f=void 0===u?25:u,p="number"==typeof f?"".concat(f,"ms"):f,[m]=et(void 0===l?500:l,!0);return m?(r[0]!==n||r[1]!==o||r[2]!==s||r[3]!==p||r[4]!==c||r[5]!==d?(t=(0,i.jsx)("div",{className:n,children:[...Array(o)].map((e,t)=>(0,i.jsx)("div",{className:c,children:(0,i.jsx)(er,{animationDelay:"calc(".concat(t," * ").concat(p,")"),height:s,width:d})},t))}),r[0]=n,r[1]=o,r[2]=s,r[3]=p,r[4]=c,r[5]=d,r[6]=t):t=r[6],t):null},ea=q.default||q,ei=e=>{var t;let r=(0,a.c)(26),o,s,l,u,c,d;r[0]!==e?({className:o,maxHeight:s,minHeight:l,options:u,readOnly:c,...d}=e,r[0]=e,r[1]=o,r[2]=s,r[3]=l,r[4]=u,r[5]=c,r[6]=d):(o=r[1],s=r[2],l=r[3],u=r[4],c=r[5],d=r[6]);let f=null!=l?l:56,p=(null==u?void 0:u.padding)?(u.padding.top||0)+((null==(t=u.padding)?void 0:t.bottom)||0):0,[m,g]=(0,n.useState)(f),{theme:y}=ee(),v=(null==d?void 0:d.defaultLanguage)?"language--".concat(d.defaultLanguage):"",b=c&&"read-only",w;r[7]!==o||r[8]!==v||r[9]!==b?(w=["code-editor",o,v,b].filter(Boolean),r[7]=o,r[8]=v,r[9]=b,r[10]=w):w=r[10];let x=w.join(" "),$;if(r[11]!==f||r[12]!==x||r[13]!==m||r[14]!==s||r[15]!==u||r[16]!==p||r[17]!==c||r[18]!==d||r[19]!==y){let e,t;r[21]!==f||r[22]!==p||r[23]!==d?(e=(e,t)=>{var r;null==(r=d.onChange)||r.call(d,e,t),g(Math.max(f,18*e.split("\n").length+2+p))},t=(e,t)=>{var r;null==(r=d.onMount)||r.call(d,e,t),g(Math.max(f,18*e.getValue().split("\n").length+2+p))},r[21]=f,r[22]=p,r[23]=d,r[24]=e,r[25]=t):(e=r[24],t=r[25]),$=(0,i.jsx)(ea,{className:x,loading:(0,i.jsx)(er,{height:m}),options:{detectIndentation:!0,hideCursorInOverviewRuler:!0,minimap:{enabled:!1},overviewRulerBorder:!1,readOnly:!!c,scrollbar:{alwaysConsumeMouseWheel:!1},scrollBeyondLastLine:!1,tabSize:2,wordWrap:"on",...u},theme:"dark"===y?"vs-dark":"vs",...d,height:s?Math.min(m,s):m,onChange:e,onMount:t}),r[11]=f,r[12]=x,r[13]=m,r[14]=s,r[15]=u,r[16]=p,r[17]=c,r[18]=d,r[19]=y,r[20]=$}else $=r[20];return $}},56824:(e,t,r)=>{"use strict";r.d(t,{P:()=>n});let n=({field:e,operation:t,parentName:r,permissions:n})=>({operation:!0===n||n?.[t]===!0||n?.[r]===!0||"name"in e&&"object"==typeof n&&n?.[e.name]&&(!0===n[e.name]||t in n[e.name]&&n[e.name][t]),permissions:null==n||!0===n||("name"in e?n?.[e.name]:n),read:!0===n||n?.read===!0||n?.[r]===!0||"name"in e&&"object"==typeof n&&n?.[e.name]&&(!0===n[e.name]||"read"in n[e.name]&&n[e.name].read)})},57474:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\s+/g,"-").toLowerCase()},57631:(e,t,r)=>{"use strict";r.d(t,{Relationship:()=>m});var n=r(47092),a=r(67252),i=r(55032),o=r(71464),s=r(56156),l=r(83548),u=r(93653);r(89688);var c=r(11111),d=r(47104);let f="relationship-diff",p=(e,t,r,n,a,i)=>{let o;if(Array.isArray(n))return n.map(n=>p(e,t,r,n,a,i)).filter(Boolean).join(", ");let s="",l="relationTo"in t?t.relationTo:void 0;if(null==n)return String(n);o="object"==typeof n&&"relationTo"in n?n.value:n;let c=l?e.find(e=>e.slug===("object"==typeof n&&"relationTo"in n?n.relationTo:l)):null;if(c){var d;let e=null==c||null==(d=c.admin)?void 0:d.useAsTitle,t=c.fields.find(t=>(0,u.Z7)(t)&&!(0,u.aO)(t)&&t.name===e),n=!1;t&&(0,u.Z7)(t)&&(n=(0,u.Cd)({field:t,parentIsLocalized:i})),"object"==typeof(s=void 0!==(null==o?void 0:o[e])?o[e]:void 0!==(null==o?void 0:o.id)?o.id:o)&&n&&(null==s?void 0:s[r])&&(s=s[r])}else o&&(s=void 0!==(null==o?void 0:o.id)?String(o.id):o);return(s&&"object"==typeof s&&null!==s||"string"!=typeof s)&&(s=JSON.stringify(s)),s},m=e=>{let t,r=(0,n.c)(12),{comparisonValue:u,field:m,locale:g,parentIsLocalized:y,versionValue:v}=e,{i18n:b}=(0,o.d)(),{config:w}=(0,s.b)(),x="[".concat(b.t("general:noValue"),"]"),{config:$}=(0,s.b)(),{collections:k}=$;if(r[0]!==k||r[1]!==u||r[2]!==w||r[3]!==m||r[4]!==b||r[5]!==g||r[6]!==y||r[7]!==x||r[8]!==v){let e,n=x,o=x;v&&(n="hasMany"in m&&m.hasMany&&Array.isArray(v)?v.map(e=>p(k,m,g,e,w,y)).join(", ")||x:p(k,m,g,v,w,y)||x),u&&(o="hasMany"in m&&m.hasMany&&Array.isArray(u)?u.map(e=>p(k,m,g,e,w,y)).join(", ")||x:p(k,m,g,u,w,y)||x);let s="label"in m&&"boolean"!=typeof m.label&&"function"!=typeof m.label?m.label:"";r[10]!==g?(e=g&&(0,a.jsx)("span",{className:"".concat(f,"__locale-label"),children:g}),r[10]=g,r[11]=e):e=r[11],t=(0,a.jsxs)("div",{className:f,children:[(0,a.jsxs)(l.FieldDiffLabel,{children:[e,(0,i.sC)(s,b)]}),(0,a.jsx)(c.Ay,{hideLineNumbers:!0,newValue:n,oldValue:o,showDiffOnly:!1,splitView:!0,styles:d.C})]}),r[0]=k,r[1]=u,r[2]=w,r[3]=m,r[4]=b,r[5]=g,r[6]=y,r[7]=x,r[8]=v,r[9]=t}else t=r[9];return t}},58084:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150);t.default={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>(0,n.str)`must be multiple of ${e}`,params:({schemaCode:e})=>(0,n._)`{multipleOf: ${e}}`},code(e){let{gen:t,data:r,schemaCode:a,it:i}=e,o=i.opts.multipleOfPrecision,s=t.let("res"),l=o?(0,n._)`Math.abs(Math.round(${s}) - ${s}) > 1e-${o}`:(0,n._)`${s} !== parseInt(${s})`;e.fail$data((0,n._)`(${a} === 0 || (${s} = ${r}/${a}, ${l}))`)}}},58139:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DiscrError=void 0,function(e){e.Tag="tag",e.Mapping="mapping"}(r||(t.DiscrError=r={}))},58226:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var n=r(43011);let a=(e,t)=>{let r=t.substring(0,t.lastIndexOf(".")+1),a=t.split(".").pop(),i={};Object.keys(e).forEach(n=>{!e[n].disableFormData&&(0===n.indexOf(`${t}.`)||n===t)&&(i[n.replace(r,"")]=e[n].value,e[n]?.rows&&0===e[n].rows.length&&(i[n.replace(r,"")]=[]))});let o=(0,n.s)(i);return o?.[a]}},58934:(e,t)=>{t.read=function(e,t,r,n,a){var i,o,s=8*a-n-1,l=(1<<s)-1,u=l>>1,c=-7,d=r?a-1:0,f=r?-1:1,p=e[t+d];for(d+=f,i=p&(1<<-c)-1,p>>=-c,c+=s;c>0;i=256*i+e[t+d],d+=f,c-=8);for(o=i&(1<<-c)-1,i>>=-c,c+=n;c>0;o=256*o+e[t+d],d+=f,c-=8);if(0===i)i=1-u;else{if(i===l)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),i-=u}return(p?-1:1)*o*Math.pow(2,i-n)},t.write=function(e,t,r,n,a,i){var o,s,l,u=8*i-a-1,c=(1<<u)-1,d=c>>1,f=5960464477539062e-23*(23===a),p=n?0:i-1,m=n?1:-1,g=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(s=+!!isNaN(t),o=c):(o=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-o))<1&&(o--,l*=2),o+d>=1?t+=f/l:t+=f*Math.pow(2,1-d),t*l>=2&&(o++,l/=2),o+d>=c?(s=0,o=c):o+d>=1?(s=(t*l-1)*Math.pow(2,a),o+=d):(s=t*Math.pow(2,d-1)*Math.pow(2,a),o=0));a>=8;e[r+p]=255&s,p+=m,s/=256,a-=8);for(o=o<<a|s,u+=a;u>0;e[r+p]=255&o,p+=m,o/=256,u-=8);e[r+p-m]|=128*g}},59212:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={keyword:"id",code(){throw Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}}},59228:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;let n=r(26520),a=r(54297),i=r(94731),o=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);t.inlineRef=function(e,t=!0){return"boolean"==typeof e||(!0===t?!function e(t){for(let r in t){if(s.has(r))return!0;let n=t[r];if(Array.isArray(n)&&n.some(e)||"object"==typeof n&&e(n))return!0}return!1}(e):!!t&&function e(t){let r=0;for(let a in t)if("$ref"===a||(r++,!o.has(a)&&("object"==typeof t[a]&&(0,n.eachItem)(t[a],t=>r+=e(t)),r===1/0)))return 1/0;return r}(e)<=t)};let s=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function l(e,t="",r){!1!==r&&(t=d(t));let n=e.parse(t);return u(e,n)}function u(e,t){return e.serialize(t).split("#")[0]+"#"}t.getFullPath=l,t._getFullPath=u;let c=/#\/?$/;function d(e){return e?e.replace(c,""):""}t.normalizeId=d,t.resolveUrl=function(e,t,r){return r=d(r),e.resolve(t,r)};let f=/^[a-z_][-a-z0-9._]*$/i;t.getSchemaRefs=function(e,t){if("boolean"==typeof e)return{};let{schemaId:r,uriResolver:n}=this.opts,o=d(e[r]||t),s={"":o},u=l(n,o,!1),c={},p=new Set;return i(e,{allKeys:!0},(e,t,n,a)=>{if(void 0===a)return;let i=u+t,o=s[a];function l(t){let r=this.opts.uriResolver.resolve;if(t=d(o?r(o,t):t),p.has(t))throw g(t);p.add(t);let n=this.refs[t];return"string"==typeof n&&(n=this.refs[n]),"object"==typeof n?m(e,n.schema,t):t!==d(i)&&("#"===t[0]?(m(e,c[t],t),c[t]=e):this.refs[t]=i),t}function y(e){if("string"==typeof e){if(!f.test(e))throw Error(`invalid anchor "${e}"`);l.call(this,`#${e}`)}}"string"==typeof e[r]&&(o=l.call(this,e[r])),y.call(this,e.$anchor),y.call(this,e.$dynamicAnchor),s[t]=o}),c;function m(e,t,r){if(void 0!==t&&!a(e,t))throw g(r)}function g(e){return Error(`reference "${e}" resolves to more than one schema`)}}},59526:(e,t)=>{"use strict";function r(e){let t,r=e.length,n=0,a=0;for(;a<r;)n++,(t=e.charCodeAt(a++))>=55296&&t<=56319&&a<r&&(64512&(t=e.charCodeAt(a)))==56320&&a++;return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,r.code='require("ajv/dist/runtime/ucs2length").default'},60054:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(26520);t.default={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>void 0===t?(0,n.str)`must contain at least ${e} valid item(s)`:(0,n.str)`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>void 0===t?(0,n._)`{minContains: ${e}}`:(0,n._)`{minContains: ${e}, maxContains: ${t}}`},code(e){let t,r,{gen:i,schema:o,parentSchema:s,data:l,it:u}=e,{minContains:c,maxContains:d}=s;u.opts.next?(t=void 0===c?1:c,r=d):t=1;let f=i.const("len",(0,n._)`${l}.length`);if(e.setParams({min:t,max:r}),void 0===r&&0===t)return void(0,a.checkStrictMode)(u,'"minContains" == 0 without "maxContains": "contains" keyword ignored');if(void 0!==r&&t>r){(0,a.checkStrictMode)(u,'"minContains" > "maxContains" is always invalid'),e.fail();return}if((0,a.alwaysValidSchema)(u,o)){let a=(0,n._)`${f} >= ${t}`;void 0!==r&&(a=(0,n._)`${a} && ${f} <= ${r}`),e.pass(a);return}u.items=!0;let p=i.name("valid");function m(){let e=i.name("_valid"),a=i.let("count",0);g(e,()=>i.if(e,()=>{var e;return e=a,void(i.code((0,n._)`${e}++`),void 0===r?i.if((0,n._)`${e} >= ${t}`,()=>i.assign(p,!0).break()):(i.if((0,n._)`${e} > ${r}`,()=>i.assign(p,!1).break()),1===t?i.assign(p,!0):i.if((0,n._)`${e} >= ${t}`,()=>i.assign(p,!0))))}))}function g(t,r){i.forRange("i",0,f,n=>{e.subschema({keyword:"contains",dataProp:n,dataPropType:a.Type.Num,compositeRule:!0},t),r()})}void 0===r&&1===t?g(p,()=>i.if(p,()=>i.break())):0===t?(i.let(p,!0),void 0!==r&&i.if((0,n._)`${l}.length > 0`,m)):(i.let(p,!1),m()),e.result(p,()=>e.reset())}}},60122:(e,t,r)=>{"use strict";function n({baseVersionField:e,comparisonRow:t,config:r,field:n,row:a,versionRow:i}){let o=[],s=[];if("array"===n.type&&"fields"in n)o=n.fields,s=e.rows?.length?e.rows[a]:e.fields;else if("blocks"===n.type)if(i?.blockType===t?.blockType)o=(r?.blocksMap?.[i?.blockType]??(("blocks"in n||"blockReferences"in n)&&(n.blockReferences??n.blocks)?.find(e=>"string"!=typeof e&&e.slug===i?.blockType)||{fields:[]})).fields,s=e.rows?.length?e.rows[a]:e.fields;else{let l=r?.blocksMap?.[i?.blockType]??(("blocks"in n||"blockReferences"in n)&&(n.blockReferences??n.blocks)?.find(e=>"string"!=typeof e&&e.slug===i?.blockType)||{fields:[]}),u=r?.blocksMap?.[t?.blockType]??(("blocks"in n||"blockReferences"in n)&&(n.blockReferences??n.blocks)?.find(e=>"string"!=typeof e&&e.slug===t?.blockType)||{fields:[]});o=[...new Map([...l.fields,...u.fields].map(e=>[e.name,e])).values()],s=e.rows?.length?e.rows[a]:e.fields}return{fields:o,versionFields:s}}r.d(t,{G:()=>n})},60283:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=[r(84042).default]},61404:(e,t,r)=>{"use strict";r.d(t,{x4:()=>o});let{isPlural:n,singular:a}=r(86324),i=e=>e.charAt(0).toUpperCase()+e.slice(1),o=(e,t=!1)=>{let r=(e||"").trim().split(/[\s-]/),n=[];return r.forEach(e=>{if(""!==e){let t=e.split(/(?=[A-Z])/).join(" ");n.push(i(t))}}),t?n.join("").replace(/\s/g,""):n.join(" ")}},61473:(e,t,r)=>{"use strict";r.d(t,{Tabs:()=>d});var n=r(47092),a=r(67252),i=r(55032),o=r(71464);r(89688);var s=r(20985),l=r(4624),u=r(73063);let c="tabs-diff",d=e=>{let t,r=(0,n.c)(13),{baseVersionField:i,comparisonValue:o,field:l,versionValue:u}=e,{selectedLocales:d}=(0,s.I)();if(r[0]!==i.tabs||r[1]!==o||r[2]!==l||r[3]!==e||r[4]!==d||r[5]!==u){let n;r[7]!==o||r[8]!==l||r[9]!==e||r[10]!==d||r[11]!==u?(n=(t,r)=>{var n,i;if(!(null==t||null==(n=t.fields)?void 0:n.length))return null;let s=null==(i=l.tabs)?void 0:i[r];return(0,a.jsx)("div",{className:"".concat(c,"__tab"),children:(()=>{if("name"in s&&d&&s.localized)return d.map((r,n)=>{var i,l;let d={...e,comparison:null==o||null==(i=o[t.name])?void 0:i[r],version:null==u||null==(l=u[t.name])?void 0:l[r]};return(0,a.jsx)("div",{className:"".concat(c,"__tab-locale"),children:(0,a.jsx)("div",{className:"".concat(c,"__tab-locale-value"),children:(0,a.jsx)(f,{...d,fieldTab:s,locale:r,tab:t},r)})},[r,n].join("-"))});if(!("name"in t)||!t.name)return(0,a.jsx)(f,{fieldTab:s,...e,tab:t},r);{let n={...e,comparison:null==o?void 0:o[t.name],version:null==u?void 0:u[t.name]};return(0,a.jsx)(f,{fieldTab:s,...n,tab:t},r)}})()},r)},r[7]=o,r[8]=l,r[9]=e,r[10]=d,r[11]=u,r[12]=n):n=r[12],t=(0,a.jsx)("div",{className:c,children:i.tabs.map(n)}),r[0]=i.tabs,r[1]=o,r[2]=l,r[3]=e,r[4]=d,r[5]=u,r[6]=t}else t=r[6];return t},f=e=>{var t;let r,d,f=(0,n.c)(12),{comparisonValue:p,fieldTab:m,locale:g,parentIsLocalized:y,tab:v,versionValue:b}=e,{i18n:w}=(0,o.d)(),{selectedLocales:x}=(0,s.I)();if(!(null==(t=v.fields)?void 0:t.length))return null;f[0]!==w||f[1]!==g||f[2]!==v?(r="label"in v&&v.label&&"function"!=typeof v.label&&(0,a.jsxs)("span",{children:[g&&(0,a.jsx)("span",{className:"".concat(c,"__locale-label"),children:g}),(0,i.sC)(v.label,w)]}),f[0]=w,f[1]=g,f[2]=v,f[3]=r):r=f[3];let $=y||m.localized;return f[4]!==p||f[5]!==m.fields||f[6]!==x||f[7]!==r||f[8]!==$||f[9]!==v.fields||f[10]!==b?(d=(0,a.jsx)(l.$,{comparison:p,fields:m.fields,label:r,locales:x,parentIsLocalized:$,version:b,children:(0,a.jsx)(u.RenderVersionFieldsToDiff,{versionFields:v.fields})}),f[4]=p,f[5]=m.fields,f[6]=x,f[7]=r,f[8]=$,f[9]=v.fields,f[10]=b,f[11]=d):d=f[11],d}},62293:(e,t,r)=>{"use strict";function n(e){return null!=e&&("string"!=typeof e||""!==e.trim())&&!Number.isNaN(Number(e))}r.d(t,{E:()=>n})},62597:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;let n=r(77908),a=r(37699),i=r(19516),o=r(37699),s=r(50969),l=r(54982),u=r(48148),c=r(55150),d=r(12208),f=r(59228),p=r(26520),m=r(95439);function g({gen:e,validateName:t,schema:r,schemaEnv:n,opts:a},i){var o;a.code.es5?e.func(t,(0,c._)`${d.default.data}, ${d.default.valCxt}`,n.$async,()=>{var t,n;e.code((0,c._)`"use strict"; ${y(r,a)}`),t=e,n=a,t.if(d.default.valCxt,()=>{t.var(d.default.instancePath,(0,c._)`${d.default.valCxt}.${d.default.instancePath}`),t.var(d.default.parentData,(0,c._)`${d.default.valCxt}.${d.default.parentData}`),t.var(d.default.parentDataProperty,(0,c._)`${d.default.valCxt}.${d.default.parentDataProperty}`),t.var(d.default.rootData,(0,c._)`${d.default.valCxt}.${d.default.rootData}`),n.dynamicRef&&t.var(d.default.dynamicAnchors,(0,c._)`${d.default.valCxt}.${d.default.dynamicAnchors}`)},()=>{t.var(d.default.instancePath,(0,c._)`""`),t.var(d.default.parentData,(0,c._)`undefined`),t.var(d.default.parentDataProperty,(0,c._)`undefined`),t.var(d.default.rootData,d.default.data),n.dynamicRef&&t.var(d.default.dynamicAnchors,(0,c._)`{}`)}),e.code(i)}):e.func(t,(0,c._)`${d.default.data}, ${(o=a,(0,c._)`{${d.default.instancePath}="", ${d.default.parentData}, ${d.default.parentDataProperty}, ${d.default.rootData}=${d.default.data}${o.dynamicRef?(0,c._)`, ${d.default.dynamicAnchors}={}`:c.nil}}={}`)}`,n.$async,()=>e.code(y(r,a)).code(i))}function y(e,t){let r="object"==typeof e&&e[t.schemaId];return r&&(t.code.source||t.code.process)?(0,c._)`/*# sourceURL=${r} */`:c.nil}function v({schema:e,self:t}){if("boolean"==typeof e)return!e;for(let r in e)if(t.RULES.all[r])return!0;return!1}function b(e){return"boolean"!=typeof e.schema}function w(e){(0,p.checkUnknownRules)(e),function(e){let{schema:t,errSchemaPath:r,opts:n,self:a}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,p.schemaHasRulesButRef)(t,a.RULES)&&a.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}(e)}function x(e,t){if(e.opts.jtd)return k(e,[],!1,t);let r=(0,a.getSchemaTypes)(e.schema),n=(0,a.coerceAndCheckDataType)(e,r);k(e,r,!n,t)}function $({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:a}){let i=r.$comment;if(!0===a.$comment)e.code((0,c._)`${d.default.self}.logger.log(${i})`);else if("function"==typeof a.$comment){let r=(0,c.str)`${n}/$comment`,a=e.scopeValue("root",{ref:t.root});e.code((0,c._)`${d.default.self}.opts.$comment(${i}, ${r}, ${a}.schema)`)}}function k(e,t,r,n){var a,s,l,u;let{gen:f,schema:m,data:g,allErrors:y,opts:v,self:b}=e,{RULES:w}=b;if(m.$ref&&(v.ignoreKeywordsWithRef||!(0,p.schemaHasRulesButRef)(m,w)))return void f.block(()=>C(e,"$ref",w.all.$ref.definition));function x(a){(0,i.shouldUseGroup)(m,a)&&(a.type?(f.if((0,o.checkDataType)(a.type,g,v.strictNumbers)),S(e,a),1===t.length&&t[0]===a.type&&r&&(f.else(),(0,o.reportTypeError)(e)),f.endIf()):S(e,a),y||f.if((0,c._)`${d.default.errors} === ${n||0}`))}v.jtd||(a=e,s=t,!a.schemaEnv.meta&&a.opts.strictTypes&&(function(e,t){if(t.length){if(!e.dataTypes.length){e.dataTypes=t;return}t.forEach(t=>{_(e.dataTypes,t)||j(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)}),function(e,t){let r=[];for(let n of e.dataTypes)_(t,n)?r.push(n):t.includes("integer")&&"number"===n&&r.push("integer");e.dataTypes=r}(e,t)}}(a,s),a.opts.allowUnionTypes||(l=a,(u=s).length>1&&!(2===u.length&&u.includes("null"))&&j(l,"use allowUnionTypes to allow union type keyword")),function(e,t){let r=e.self.RULES.all;for(let n in r){let a=r[n];if("object"==typeof a&&(0,i.shouldUseRule)(e.schema,a)){let{type:r}=a.definition;r.length&&!r.some(e=>{var r,n;return r=t,n=e,r.includes(n)||"number"===n&&r.includes("integer")})&&j(e,`missing type "${r.join(",")}" for keyword "${n}"`)}}}(a,a.dataTypes))),f.block(()=>{for(let e of w.rules)x(e);x(w.post)})}function S(e,t){let{gen:r,schema:n,opts:{useDefaults:a}}=e;a&&(0,s.assignDefaults)(e,t.type),r.block(()=>{for(let r of t.rules)(0,i.shouldUseRule)(n,r)&&C(e,r.keyword,r.definition,t.type)})}function _(e,t){return e.includes(t)||"integer"===t&&e.includes("number")}function j(e,t){let r=e.schemaEnv.baseId+e.errSchemaPath;t+=` at "${r}" (strictTypes)`,(0,p.checkStrictMode)(e,t,e.opts.strictTypes)}t.validateFunctionCode=function(e){if(b(e)&&(w(e),v(e)))return void function(e){let{schema:t,opts:r,gen:n}=e;g(e,()=>{r.$comment&&t.$comment&&$(e),function(e){let{schema:t,opts:r}=e;void 0!==t.default&&r.useDefaults&&r.strictSchema&&(0,p.checkStrictMode)(e,"default is ignored in the schema root")}(e),n.let(d.default.vErrors,null),n.let(d.default.errors,0),r.unevaluated&&function(e){let{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",(0,c._)`${r}.evaluated`),t.if((0,c._)`${e.evaluated}.dynamicProps`,()=>t.assign((0,c._)`${e.evaluated}.props`,(0,c._)`undefined`)),t.if((0,c._)`${e.evaluated}.dynamicItems`,()=>t.assign((0,c._)`${e.evaluated}.items`,(0,c._)`undefined`))}(e),x(e),function(e){let{gen:t,schemaEnv:r,validateName:n,ValidationError:a,opts:i}=e;r.$async?t.if((0,c._)`${d.default.errors} === 0`,()=>t.return(d.default.data),()=>t.throw((0,c._)`new ${a}(${d.default.vErrors})`)):(t.assign((0,c._)`${n}.errors`,d.default.vErrors),i.unevaluated&&function({gen:e,evaluated:t,props:r,items:n}){r instanceof c.Name&&e.assign((0,c._)`${t}.props`,r),n instanceof c.Name&&e.assign((0,c._)`${t}.items`,n)}(e),t.return((0,c._)`${d.default.errors} === 0`))}(e)})}(e);g(e,()=>(0,n.topBoolOrEmptySchema)(e))};class E{constructor(e,t,r){if((0,l.validateKeywordUsage)(e,t,r),this.gen=e.gen,this.allErrors=e.allErrors,this.keyword=r,this.data=e.data,this.schema=e.schema[r],this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,p.schemaRefOrVal)(e,this.schema,r,this.$data),this.schemaType=t.schemaType,this.parentSchema=e.schema,this.params={},this.it=e,this.def=t,this.$data)this.schemaCode=e.gen.const("vSchema",A(this.$data,e));else if(this.schemaCode=this.schemaValue,!(0,l.validSchemaType)(this.schema,t.schemaType,t.allowUndefined))throw Error(`${r} value must be ${JSON.stringify(t.schemaType)}`);("code"in t?t.trackErrors:!1!==t.errors)&&(this.errsCount=e.gen.const("_errs",d.default.errors))}result(e,t,r){this.failResult((0,c.not)(e),t,r)}failResult(e,t,r){this.gen.if(e),r?r():this.error(),t?(this.gen.else(),t(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(e,t){this.failResult((0,c.not)(e),void 0,t)}fail(e){if(void 0===e){this.error(),this.allErrors||this.gen.if(!1);return}this.gen.if(e),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);let{schemaCode:t}=this;this.fail((0,c._)`${t} !== undefined && (${(0,c.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t){this.setParams(t),this._error(e,r),this.setParams({});return}this._error(e,r)}_error(e,t){(e?m.reportExtraError:m.reportError)(this,this.def.error,t)}$dataError(){(0,m.reportError)(this,this.def.$dataError||m.keyword$DataError)}reset(){if(void 0===this.errsCount)throw Error('add "trackErrors" to keyword definition');(0,m.resetErrorsCount)(this.gen,this.errsCount)}ok(e){this.allErrors||this.gen.if(e)}setParams(e,t){t?Object.assign(this.params,e):this.params=e}block$data(e,t,r=c.nil){this.gen.block(()=>{this.check$data(e,r),t()})}check$data(e=c.nil,t=c.nil){if(!this.$data)return;let{gen:r,schemaCode:n,schemaType:a,def:i}=this;r.if((0,c.or)((0,c._)`${n} === undefined`,t)),e!==c.nil&&r.assign(e,!0),(a.length||i.validateSchema)&&(r.elseIf(this.invalid$data()),this.$dataError(),e!==c.nil&&r.assign(e,!1)),r.else()}invalid$data(){let{gen:e,schemaCode:t,schemaType:r,def:n,it:a}=this;return(0,c.or)(function(){if(r.length){if(!(t instanceof c.Name))throw Error("ajv implementation error");let e=Array.isArray(r)?r:[r];return(0,c._)`${(0,o.checkDataTypes)(e,t,a.opts.strictNumbers,o.DataType.Wrong)}`}return c.nil}(),function(){if(n.validateSchema){let r=e.scopeValue("validate$data",{ref:n.validateSchema});return(0,c._)`!${r}(${t})`}return c.nil}())}subschema(e,t){let r=(0,u.getSubschema)(this.it,e);(0,u.extendSubschemaData)(r,this.it,e),(0,u.extendSubschemaMode)(r,e);let a={...this.it,...r,items:void 0,props:void 0};return!function(e,t){if(b(e)&&(w(e),v(e))){var r=e,a=t;let{schema:n,gen:i,opts:o}=r;o.$comment&&n.$comment&&$(r),function(e){let t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,f.resolveUrl)(e.opts.uriResolver,e.baseId,t))}(r),function(e){if(e.schema.$async&&!e.schemaEnv.$async)throw Error("async schema in sync schema")}(r);let s=i.const("_errs",d.default.errors);x(r,s),i.var(a,(0,c._)`${s} === ${d.default.errors}`);return}(0,n.boolOrEmptySchema)(e,t)}(a,t),a}mergeEvaluated(e,t){let{it:r,gen:n}=this;r.opts.unevaluated&&(!0!==r.props&&void 0!==e.props&&(r.props=p.mergeEvaluated.props(n,e.props,r.props,t)),!0!==r.items&&void 0!==e.items&&(r.items=p.mergeEvaluated.items(n,e.items,r.items,t)))}mergeValidEvaluated(e,t){let{it:r,gen:n}=this;if(r.opts.unevaluated&&(!0!==r.props||!0!==r.items))return n.if(t,()=>this.mergeEvaluated(e,c.Name)),!0}}function C(e,t,r,n){let a=new E(e,r,t);"code"in r?r.code(a,n):a.$data&&r.validate?(0,l.funcKeywordCode)(a,r):"macro"in r?(0,l.macroKeywordCode)(a,r):(r.compile||r.validate)&&(0,l.funcKeywordCode)(a,r)}t.KeywordCxt=E;let P=/^\/(?:[^~]|~0|~1)*$/,N=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function A(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let a,i;if(""===e)return d.default.rootData;if("/"===e[0]){if(!P.test(e))throw Error(`Invalid JSON-pointer: ${e}`);a=e,i=d.default.rootData}else{let o=N.exec(e);if(!o)throw Error(`Invalid JSON-pointer: ${e}`);let l=+o[1];if("#"===(a=o[2])){if(l>=t)throw Error(s("property/index",l));return n[t-l]}if(l>t)throw Error(s("data",l));if(i=r[t-l],!a)return i}let o=i;for(let e of a.split("/"))e&&(i=(0,c._)`${i}${(0,c.getProperty)((0,p.unescapeJsonPointer)(e))}`,o=(0,c._)`${o} && ${i}`);return o;function s(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=A},62919:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(26520);t.default={keyword:"allOf",schemaType:"array",code(e){let{gen:t,schema:r,it:a}=e;if(!Array.isArray(r))throw Error("ajv implementation error");let i=t.name("valid");r.forEach((t,r)=>{if((0,n.alwaysValidSchema)(a,t))return;let o=e.subschema({keyword:"allOf",schemaProp:r},i);e.ok(i),e.mergeEvaluated(o)})}}},63034:e=>{"use strict";e.exports=JSON.parse('{"$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON AnySchema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},63165:(e,t,r)=>{"use strict";r.d(t,{q:()=>a});let n={};function a(){return n}},63510:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.callRef=t.getValidate=void 0;let n=r(49380),a=r(37742),i=r(55150),o=r(12208),s=r(4424),l=r(26520);function u(e,t){let{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):(0,i._)`${r.scopeValue("wrapper",{ref:t})}.validate`}function c(e,t,r,n){let{gen:s,it:u}=e,{allErrors:c,schemaEnv:d,opts:f}=u,p=f.passContext?o.default.this:i.nil;function m(e){let t=(0,i._)`${e}.errors`;s.assign(o.default.vErrors,(0,i._)`${o.default.vErrors} === null ? ${t} : ${o.default.vErrors}.concat(${t})`),s.assign(o.default.errors,(0,i._)`${o.default.vErrors}.length`)}function g(e){var t;if(!u.opts.unevaluated)return;let n=null==(t=null==r?void 0:r.validate)?void 0:t.evaluated;if(!0!==u.props)if(n&&!n.dynamicProps)void 0!==n.props&&(u.props=l.mergeEvaluated.props(s,n.props,u.props));else{let t=s.var("props",(0,i._)`${e}.evaluated.props`);u.props=l.mergeEvaluated.props(s,t,u.props,i.Name)}if(!0!==u.items)if(n&&!n.dynamicItems)void 0!==n.items&&(u.items=l.mergeEvaluated.items(s,n.items,u.items));else{let t=s.var("items",(0,i._)`${e}.evaluated.items`);u.items=l.mergeEvaluated.items(s,t,u.items,i.Name)}}n?function(){if(!d.$async)throw Error("async schema referenced by sync schema");let r=s.let("valid");s.try(()=>{s.code((0,i._)`await ${(0,a.callValidateCode)(e,t,p)}`),g(t),c||s.assign(r,!0)},e=>{s.if((0,i._)`!(${e} instanceof ${u.ValidationError})`,()=>s.throw(e)),m(e),c||s.assign(r,!1)}),e.ok(r)}():e.result((0,a.callValidateCode)(e,t,p),()=>g(t),()=>m(t))}t.getValidate=u,t.callRef=c,t.default={keyword:"$ref",schemaType:"string",code(e){let{gen:t,schema:r,it:a}=e,{baseId:o,schemaEnv:l,validateName:d,opts:f,self:p}=a,{root:m}=l;if(("#"===r||"#/"===r)&&o===m.baseId){if(l===m)return c(e,d,l,l.$async);let r=t.scopeValue("root",{ref:m});return c(e,(0,i._)`${r}.validate`,m,m.$async)}let g=s.resolveRef.call(p,m,o,r);if(void 0===g)throw new n.default(a.opts.uriResolver,o,r);return g instanceof s.SchemaEnv?function(t){let r=u(e,t);c(e,r,t,t.$async)}(g):function(n){let a=t.scopeValue("schema",!0===f.code.source?{ref:n,code:(0,i.stringify)(n)}:{ref:n}),o=t.name("valid"),s=e.subschema({schema:n,dataTypes:[],schemaPath:i.nil,topSchemaRef:a,errSchemaPath:r},o);e.mergeEvaluated(s),e.ok(o)}(g)}}},64227:(e,t,r)=>{"use strict";let{normalizeIPv6:n,normalizeIPv4:a,removeDotSegments:i,recomposeAuthority:o,normalizeComponentEncoding:s}=r(36006),l=r(82127);function u(e,t,r,n){let a={};return n||(e=p(c(e,r),r),t=p(c(t,r),r)),!(r=r||{}).tolerant&&t.scheme?(a.scheme=t.scheme,a.userinfo=t.userinfo,a.host=t.host,a.port=t.port,a.path=i(t.path||""),a.query=t.query):(void 0!==t.userinfo||void 0!==t.host||void 0!==t.port?(a.userinfo=t.userinfo,a.host=t.host,a.port=t.port,a.path=i(t.path||""),a.query=t.query):(t.path?("/"===t.path.charAt(0)?a.path=i(t.path):(void 0===e.userinfo&&void 0===e.host&&void 0===e.port||e.path?e.path?a.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:a.path=t.path:a.path="/"+t.path,a.path=i(a.path)),a.query=t.query):(a.path=e.path,void 0!==t.query?a.query=t.query:a.query=e.query),a.userinfo=e.userinfo,a.host=e.host,a.port=e.port),a.scheme=e.scheme),a.fragment=t.fragment,a}function c(e,t){let r={host:e.host,scheme:e.scheme,userinfo:e.userinfo,port:e.port,path:e.path,query:e.query,nid:e.nid,nss:e.nss,uuid:e.uuid,fragment:e.fragment,reference:e.reference,resourceName:e.resourceName,secure:e.secure,error:""},n=Object.assign({},t),a=[],s=l[(n.scheme||r.scheme||"").toLowerCase()];s&&s.serialize&&s.serialize(r,n),void 0!==r.path&&(n.skipEscape?r.path=unescape(r.path):(r.path=escape(r.path),void 0!==r.scheme&&(r.path=r.path.split("%3A").join(":")))),"suffix"!==n.reference&&r.scheme&&a.push(r.scheme,":");let u=o(r);if(void 0!==u&&("suffix"!==n.reference&&a.push("//"),a.push(u),r.path&&"/"!==r.path.charAt(0)&&a.push("/")),void 0!==r.path){let e=r.path;n.absolutePath||s&&s.absolutePath||(e=i(e)),void 0===u&&(e=e.replace(/^\/\//u,"/%2F")),a.push(e)}return void 0!==r.query&&a.push("?",r.query),void 0!==r.fragment&&a.push("#",r.fragment),a.join("")}let d=Array.from({length:127},(e,t)=>/[^!"$&'()*+,\-.;=_`a-z{}~]/u.test(String.fromCharCode(t))),f=/^(?:([^#/:?]+):)?(?:\/\/((?:([^#/?@]*)@)?(\[[^#/?\]]+\]|[^#/:?]*)(?::(\d*))?))?([^#?]*)(?:\?([^#]*))?(?:#((?:.|[\n\r])*))?/u;function p(e,t){let r=Object.assign({},t),i={scheme:void 0,userinfo:void 0,host:"",port:void 0,path:"",query:void 0,fragment:void 0},o=-1!==e.indexOf("%"),s=!1;"suffix"===r.reference&&(e=(r.scheme?r.scheme+":":"")+"//"+e);let u=e.match(f);if(u){if(i.scheme=u[1],i.userinfo=u[3],i.host=u[4],i.port=parseInt(u[5],10),i.path=u[6]||"",i.query=u[7],i.fragment=u[8],isNaN(i.port)&&(i.port=u[5]),i.host){let e=a(i.host);if(!1===e.isIPV4){let t=n(e.host);i.host=t.host.toLowerCase(),s=t.isIPV6}else i.host=e.host,s=!0}void 0!==i.scheme||void 0!==i.userinfo||void 0!==i.host||void 0!==i.port||void 0!==i.query||i.path?void 0===i.scheme?i.reference="relative":void 0===i.fragment?i.reference="absolute":i.reference="uri":i.reference="same-document",r.reference&&"suffix"!==r.reference&&r.reference!==i.reference&&(i.error=i.error||"URI is not a "+r.reference+" reference.");let e=l[(r.scheme||i.scheme||"").toLowerCase()];if(!r.unicodeSupport&&(!e||!e.unicodeSupport)&&i.host&&(r.domainHost||e&&e.domainHost)&&!1===s&&function(e){let t=0;for(let r=0,n=e.length;r<n;++r)if((t=e.charCodeAt(r))>126||d[t])return!0;return!1}(i.host))try{i.host=URL.domainToASCII(i.host.toLowerCase())}catch(e){i.error=i.error||"Host's domain name can not be converted to ASCII: "+e}(!e||e&&!e.skipNormalize)&&(o&&void 0!==i.scheme&&(i.scheme=unescape(i.scheme)),o&&void 0!==i.host&&(i.host=unescape(i.host)),i.path&&(i.path=escape(unescape(i.path))),i.fragment&&(i.fragment=encodeURI(decodeURIComponent(i.fragment)))),e&&e.parse&&e.parse(i,r)}else i.error=i.error||"URI can not be parsed.";return i}let m={SCHEMES:l,normalize:function(e,t){return"string"==typeof e?e=c(p(e,t),t):"object"==typeof e&&(e=p(c(e,t),t)),e},resolve:function(e,t,r){let n=Object.assign({scheme:"null"},r);return c(u(p(e,n),p(t,n),n,!0),{...n,skipEscape:!0})},resolveComponents:u,equal:function(e,t,r){return"string"==typeof e?e=c(s(p(e=unescape(e),r),!0),{...r,skipEscape:!0}):"object"==typeof e&&(e=c(s(e,!0),{...r,skipEscape:!0})),"string"==typeof t?t=c(s(p(t=unescape(t),r),!0),{...r,skipEscape:!0}):"object"==typeof t&&(t=c(s(t,!0),{...r,skipEscape:!0})),e.toLowerCase()===t.toLowerCase()},serialize:c,parse:p};e.exports=m,e.exports.default=m,e.exports.fastUri=m},64360:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(26520),i=r(37549);t.default={keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>(0,n._)`{allowedValue: ${e}}`},code(e){let{gen:t,data:r,$data:o,schemaCode:s,schema:l}=e;o||l&&"object"==typeof l?e.fail$data((0,n._)`!${(0,a.useFunc)(t,i.default)}(${r}, ${s})`):e.fail((0,n._)`${l} !== ${r}`)}}},64466:(e,t,r)=>{"use strict";r.d(t,{NotFoundClient:()=>c});var n=r(47092),a=r(67252),i=r(83548),o=r(71464),s=r(56156),l=r(89688);let u="not-found",c=e=>{let t,r,c,d,f=(0,n.c)(10),{marginTop:p}=e,m=void 0===p?"large":p,{setStepNav:g}=(0,i.useStepNav)(),{t:y}=(0,o.d)(),{config:v}=(0,s.b)(),{routes:b}=v,{admin:w}=b;f[0]!==g||f[1]!==y?(t=()=>{g([{label:y("general:notFound")}])},r=[g,y],f[0]=g,f[1]=y,f[2]=t,f[3]=r):(t=f[2],r=f[3]),(0,l.useEffect)(t,r);let x=m&&"".concat(u,"--margin-top-").concat(m);f[4]!==x?(c=[u,x].filter(Boolean),f[4]=x,f[5]=c):c=f[5];let $=c.join(" ");return f[6]!==w||f[7]!==y||f[8]!==$?(d=(0,a.jsx)("div",{className:$,children:(0,a.jsxs)(i.Gutter,{className:"".concat(u,"__wrap"),children:[(0,a.jsxs)("div",{className:"".concat(u,"__content"),children:[(0,a.jsx)("h1",{children:y("general:nothingFound")}),(0,a.jsx)("p",{children:y("general:sorryNotFound")})]}),(0,a.jsx)(i.Button,{className:"".concat(u,"__button"),el:"link",size:"large",to:w,children:y("general:backToDashboard")})]})}),f[6]=w,f[7]=y,f[8]=$,f[9]=d):d=f[9],d}},65410:(e,t,r)=>{"use strict";r.d(t,{Group:()=>d});var n=r(47092),a=r(67252),i=r(55032),o=r(71464);r(89688);var s=r(20985),l=r(4624),u=r(73063);let c="group-diff",d=e=>{let t,r,d=(0,n.c)(12),{baseVersionField:f,comparisonValue:p,field:m,locale:g,parentIsLocalized:y,versionValue:v}=e,{i18n:b}=(0,o.d)(),{selectedLocales:w}=(0,s.I)();d[0]!==m||d[1]!==b||d[2]!==g?(t="label"in m&&m.label&&"function"!=typeof m.label&&(0,a.jsxs)("span",{children:[g&&(0,a.jsx)("span",{className:"".concat(c,"__locale-label"),children:g}),(0,i.sC)(m.label,b)]}),d[0]=m,d[1]=b,d[2]=g,d[3]=t):t=d[3];let x=y||m.localized;return d[4]!==f.fields||d[5]!==p||d[6]!==m.fields||d[7]!==w||d[8]!==t||d[9]!==x||d[10]!==v?(r=(0,a.jsx)("div",{className:c,children:(0,a.jsx)(l.$,{comparison:p,fields:m.fields,label:t,locales:w,parentIsLocalized:x,version:v,children:(0,a.jsx)(u.RenderVersionFieldsToDiff,{versionFields:f.fields})})}),d[4]=f.fields,d[5]=p,d[6]=m.fields,d[7]=w,d[8]=t,d[9]=x,d[10]=v,d[11]=r):r=d[11],r}},65726:(e,t,r)=>{"use strict";r.d(t,{a:()=>a});var n=r(40496);function a(e,t){return(0,n.w)(t||e,e)}},66720:(e,t,r)=>{"use strict";r.d(t,{x:()=>a});var n=r(40496);function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];let i=n.w.bind(null,e||r.find(e=>"object"==typeof e));return r.map(i)}},67261:(e,t,r)=>{"use strict";r.d(t,{Text:()=>f});var n=r(47092),a=r(67252),i=r(55032),o=r(71464),s=r(83548);r(89688);var l=r(47104),u=r(11111);let c=e=>{let{comparisonToRender:t,diffMethod:r,diffStyles:n,placeholder:i,versionToRender:o}=e;return(0,a.jsx)(u.Ay,{compareMethod:u.J7[r],hideLineNumbers:!0,newValue:void 0!==o?String(o):i,oldValue:void 0!==t?String(t):i,showDiffOnly:!1,splitView:!0,styles:n})},d="text-diff",f=e=>{let t,r,u,f,p=(0,n.c)(14),{comparisonValue:m,diffMethod:g,field:y,locale:v,versionValue:b}=e,{i18n:w}=(0,o.d)(),x="";b==m&&(x="[".concat(w.t("general:noValue"),"]")),p[0]!==b?(t="string"==typeof b?b:JSON.stringify(b,null,2),p[0]=b,p[1]=t):t=p[1];let $=t;p[2]!==m?(r="string"==typeof m?m:JSON.stringify(m,null,2),p[2]=m,p[3]=r):r=p[3];let k=r;return p[4]!==v?(u=v&&(0,a.jsx)("span",{className:"".concat(d,"__locale-label"),children:v}),p[4]=v,p[5]=u):u=p[5],p[6]!==k||p[7]!==g||p[8]!==y||p[9]!==w||p[10]!==x||p[11]!==u||p[12]!==$?(f=(0,a.jsxs)("div",{className:d,children:[(0,a.jsxs)(s.FieldDiffLabel,{children:[u,"label"in y&&"function"!=typeof y.label&&(0,i.sC)(y.label||"",w)]}),(0,a.jsx)(c,{comparisonToRender:k,diffMethod:g,diffStyles:l.C,placeholder:x,versionToRender:$})]}),p[6]=k,p[7]=g,p[8]=y,p[9]=w,p[10]=x,p[11]=u,p[12]=$,p[13]=f):f=p[13],f}},67617:(e,t,r)=>{"use strict";let n;r.d(t,{Mp:()=>tn,Hd:()=>tv,vL:()=>s,uN:()=>eO,AN:()=>eI,fp:()=>ea,y$:()=>ei,Sj:()=>ef,Vy:()=>er,sl:()=>ep,TT:()=>es,Qo:()=>eo,fF:()=>ts,E5:()=>q,PM:()=>to,zM:()=>tu,MS:()=>J,FR:()=>Y});var a,i,o,s,l,u,c,d,f,p,m=r(89688),g=r(80665);let y="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function v(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function b(e){return"nodeType"in e}function w(e){var t,r;return e?v(e)?e:b(e)&&null!=(t=null==(r=e.ownerDocument)?void 0:r.defaultView)?t:window:window}function x(e){let{Document:t}=w(e);return e instanceof t}function $(e){return!v(e)&&e instanceof w(e).HTMLElement}function k(e){return e instanceof w(e).SVGElement}function S(e){return e?v(e)?e.document:b(e)?x(e)?e:$(e)||k(e)?e.ownerDocument:document:document:document}let _=y?m.useLayoutEffect:m.useEffect;function j(e){let t=(0,m.useRef)(e);return _(()=>{t.current=e}),(0,m.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}function E(e,t){void 0===t&&(t=[e]);let r=(0,m.useRef)(e);return _(()=>{r.current!==e&&(r.current=e)},t),r}function C(e,t){let r=(0,m.useRef)();return(0,m.useMemo)(()=>{let t=e(r.current);return r.current=t,t},[...t])}function P(e){let t=j(e),r=(0,m.useRef)(null),n=(0,m.useCallback)(e=>{e!==r.current&&(null==t||t(e,r.current)),r.current=e},[]);return[r,n]}function N(e){let t=(0,m.useRef)();return(0,m.useEffect)(()=>{t.current=e},[e]),t.current}let A={};function T(e,t){return(0,m.useMemo)(()=>{if(t)return t;let r=null==A[e]?0:A[e]+1;return A[e]=r,e+"-"+r},[e,t])}function O(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>{for(let[n,a]of Object.entries(r)){let r=t[n];null!=r&&(t[n]=r+e*a)}return t},{...t})}}let D=O(1),M=O(-1);function R(e){if(!e)return!1;let{KeyboardEvent:t}=w(e.target);return t&&e instanceof t}function L(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=w(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:r}=e.touches[0];return{x:t,y:r}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:r}=e.changedTouches[0];return{x:t,y:r}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let I=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:r}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(r?Math.round(r):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:r}=e;return"scaleX("+t+") scaleY("+r+")"}},Transform:{toString(e){if(e)return[I.Translate.toString(e),I.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:r,easing:n}=e;return t+" "+r+"ms "+n}}}),F="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",B={display:"none"};function z(e){let{id:t,value:r}=e;return m.createElement("div",{id:t,style:B},r)}function V(e){let{id:t,announcement:r,ariaLiveType:n="assertive"}=e;return m.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":n,"aria-atomic":!0},r)}let U=(0,m.createContext)(null);function q(e){let t=(0,m.useContext)(U);(0,m.useEffect)(()=>{if(!t)throw Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}let H={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},W={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was moved over droppable area "+r.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was dropped over droppable area "+r.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function G(e){let{announcements:t=W,container:r,hiddenTextDescribedById:n,screenReaderInstructions:a=H}=e,{announce:i,announcement:o}=function(){let[e,t]=(0,m.useState)("");return{announce:(0,m.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),s=T("DndLiveRegion"),[l,u]=(0,m.useState)(!1);if((0,m.useEffect)(()=>{u(!0)},[]),q((0,m.useMemo)(()=>({onDragStart(e){let{active:r}=e;i(t.onDragStart({active:r}))},onDragMove(e){let{active:r,over:n}=e;t.onDragMove&&i(t.onDragMove({active:r,over:n}))},onDragOver(e){let{active:r,over:n}=e;i(t.onDragOver({active:r,over:n}))},onDragEnd(e){let{active:r,over:n}=e;i(t.onDragEnd({active:r,over:n}))},onDragCancel(e){let{active:r,over:n}=e;i(t.onDragCancel({active:r,over:n}))}}),[i,t])),!l)return null;let c=m.createElement(m.Fragment,null,m.createElement(z,{id:n,value:a.draggable}),m.createElement(V,{id:s,announcement:o}));return r?(0,g.createPortal)(c,r):c}function K(){}function J(e,t){return(0,m.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function Y(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,m.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(a||(a={}));let Q=Object.freeze({x:0,y:0});function X(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Z(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return r-n}function ee(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return n-r}function et(e){let{left:t,top:r,height:n,width:a}=e;return[{x:t,y:r},{x:t+a,y:r},{x:t,y:r+n},{x:t+a,y:r+n}]}function er(e,t){if(!e||0===e.length)return null;let[r]=e;return t?r[t]:r}function en(e,t,r){return void 0===t&&(t=e.left),void 0===r&&(r=e.top),{x:t+.5*e.width,y:r+.5*e.height}}let ea=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=en(t,t.left,t.top),i=[];for(let e of n){let{id:t}=e,n=r.get(t);if(n){let r=X(en(n),a);i.push({id:t,data:{droppableContainer:e,value:r}})}}return i.sort(Z)},ei=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=et(t),i=[];for(let e of n){let{id:t}=e,n=r.get(t);if(n){let r=et(n),o=Number((a.reduce((e,t,n)=>e+X(r[n],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:o}})}}return i.sort(Z)},eo=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=[];for(let e of n){let{id:n}=e,i=r.get(n);if(i){let r=function(e,t){let r=Math.max(t.top,e.top),n=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(n<a&&r<i){let o=t.width*t.height,s=e.width*e.height,l=(a-n)*(i-r);return Number((l/(o+s-l)).toFixed(4))}return 0}(i,t);r>0&&a.push({id:n,data:{droppableContainer:e,value:r}})}}return a.sort(ee)},es=e=>{let{droppableContainers:t,droppableRects:r,pointerCoordinates:n}=e;if(!n)return[];let a=[];for(let e of t){let{id:t}=e,i=r.get(t);if(i&&function(e,t){let{top:r,left:n,bottom:a,right:i}=t;return r<=e.y&&e.y<=a&&n<=e.x&&e.x<=i}(n,i)){let r=Number((et(i).reduce((e,t)=>e+X(n,t),0)/4).toFixed(4));a.push({id:t,data:{droppableContainer:e,value:r}})}}return a.sort(Z)};function el(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:Q}let eu=function(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>({...t,top:t.top+e*r.y,bottom:t.bottom+e*r.y,left:t.left+e*r.x,right:t.right+e*r.x}),{...t})}}(1);function ec(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let ed={ignoreTransform:!1};function ef(e,t){void 0===t&&(t=ed);let r=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:n}=w(e).getComputedStyle(e);t&&(r=function(e,t,r){let n=ec(t);if(!n)return e;let{scaleX:a,scaleY:i,x:o,y:s}=n,l=e.left-o-(1-a)*parseFloat(r),u=e.top-s-(1-i)*parseFloat(r.slice(r.indexOf(" ")+1)),c=a?e.width/a:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:u,right:l+c,bottom:u+d,left:l}}(r,t,n))}let{top:n,left:a,width:i,height:o,bottom:s,right:l}=r;return{top:n,left:a,width:i,height:o,bottom:s,right:l}}function eh(e){return ef(e,{ignoreTransform:!0})}function ep(e,t){let r=[];return e?function n(a){var i;if(null!=t&&r.length>=t||!a)return r;if(x(a)&&null!=a.scrollingElement&&!r.includes(a.scrollingElement))return r.push(a.scrollingElement),r;if(!$(a)||k(a)||r.includes(a))return r;let o=w(e).getComputedStyle(a);return(a!==e&&function(e,t){void 0===t&&(t=w(e).getComputedStyle(e));let r=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let n=t[e];return"string"==typeof n&&r.test(n)})}(a,o)&&r.push(a),void 0===(i=o)&&(i=w(a).getComputedStyle(a)),"fixed"===i.position)?r:n(a.parentNode)}(e):r}function em(e){let[t]=ep(e,1);return null!=t?t:null}function eg(e){return y&&e?v(e)?e:b(e)?x(e)||e===S(e).scrollingElement?window:$(e)?e:null:null:null}function ey(e){return v(e)?e.scrollX:e.scrollLeft}function ev(e){return v(e)?e.scrollY:e.scrollTop}function eb(e){return{x:ey(e),y:ev(e)}}function ew(e){return!!y&&!!e&&e===document.scrollingElement}function ex(e){let t={x:0,y:0},r=ew(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},n={x:e.scrollWidth-r.width,y:e.scrollHeight-r.height},a=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:a,isLeft:i,isBottom:e.scrollTop>=n.y,isRight:e.scrollLeft>=n.x,maxScroll:n,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let e$={x:.2,y:.2};function ek(e){return e.reduce((e,t)=>D(e,eb(t)),Q)}function eS(e,t){if(void 0===t&&(t=ef),!e)return;let{top:r,left:n,bottom:a,right:i}=t(e);em(e)&&(a<=0||i<=0||r>=window.innerHeight||n>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let e_=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+ey(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+ev(t),0)}]];class ej{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let r=ep(t),n=ek(r);for(let[t,a,i]of(this.rect={...e},this.width=e.width,this.height=e.height,e_))for(let e of a)Object.defineProperty(this,e,{get:()=>{let a=i(r),o=n[t]-a;return this.rect[e]+o},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class eE{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,r){var n;null==(n=this.target)||n.addEventListener(e,t,r),this.listeners.push([e,t,r])}}function eC(e,t){let r=Math.abs(e.x),n=Math.abs(e.y);return"number"==typeof t?Math.sqrt(r**2+n**2)>t:"x"in t&&"y"in t?r>t.x&&n>t.y:"x"in t?r>t.x:"y"in t&&n>t.y}function eP(e){e.preventDefault()}function eN(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(o||(o={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter"}(s||(s={}));let eA={start:[s.Space,s.Enter],cancel:[s.Esc],end:[s.Space,s.Enter]},eT=(e,t)=>{let{currentCoordinates:r}=t;switch(e.code){case s.Right:return{...r,x:r.x+25};case s.Left:return{...r,x:r.x-25};case s.Down:return{...r,y:r.y+25};case s.Up:return{...r,y:r.y-25}}};class eO{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new eE(S(t)),this.windowListeners=new eE(w(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(o.Resize,this.handleCancel),this.windowListeners.add(o.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(o.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,r=e.node.current;r&&eS(r),t(Q)}handleKeyDown(e){if(R(e)){let{active:t,context:r,options:n}=this.props,{keyboardCodes:a=eA,coordinateGetter:i=eT,scrollBehavior:o="smooth"}=n,{code:l}=e;if(a.end.includes(l))return void this.handleEnd(e);if(a.cancel.includes(l))return void this.handleCancel(e);let{collisionRect:u}=r.current,c=u?{x:u.left,y:u.top}:Q;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:r.current,currentCoordinates:c});if(d){let t=M(d,c),n={x:0,y:0},{scrollableAncestors:a}=r.current;for(let r of a){let a=e.code,{isTop:i,isRight:l,isLeft:u,isBottom:c,maxScroll:f,minScroll:p}=ex(r),m=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:r,right:n,bottom:a}=e.getBoundingClientRect();return{top:t,left:r,right:n,bottom:a,width:e.clientWidth,height:e.clientHeight}}(r),g={x:Math.min(a===s.Right?m.right-m.width/2:m.right,Math.max(a===s.Right?m.left:m.left+m.width/2,d.x)),y:Math.min(a===s.Down?m.bottom-m.height/2:m.bottom,Math.max(a===s.Down?m.top:m.top+m.height/2,d.y))},y=a===s.Right&&!l||a===s.Left&&!u,v=a===s.Down&&!c||a===s.Up&&!i;if(y&&g.x!==d.x){let e=r.scrollLeft+t.x,i=a===s.Right&&e<=f.x||a===s.Left&&e>=p.x;if(i&&!t.y)return void r.scrollTo({left:e,behavior:o});i?n.x=r.scrollLeft-e:n.x=a===s.Right?r.scrollLeft-f.x:r.scrollLeft-p.x,n.x&&r.scrollBy({left:-n.x,behavior:o});break}if(v&&g.y!==d.y){let e=r.scrollTop+t.y,i=a===s.Down&&e<=f.y||a===s.Up&&e>=p.y;if(i&&!t.x)return void r.scrollTo({top:e,behavior:o});i?n.y=r.scrollTop-e:n.y=a===s.Down?r.scrollTop-f.y:r.scrollTop-p.y,n.y&&r.scrollBy({top:-n.y,behavior:o});break}}this.handleMove(e,D(M(d,this.referenceCoordinates),n))}}}handleMove(e,t){let{onMove:r}=this.props;e.preventDefault(),r(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eD(e){return!!(e&&"distance"in e)}function eM(e){return!!(e&&"delay"in e)}eO.activators=[{eventName:"onKeyDown",handler:(e,t,r)=>{let{keyboardCodes:n=eA,onActivation:a}=t,{active:i}=r,{code:o}=e.nativeEvent;if(n.start.includes(o)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==a||a({event:e.nativeEvent}),!0)}return!1}}];class eR{constructor(e,t,r){var n;void 0===r&&(r=function(e){let{EventTarget:t}=w(e);return e instanceof t?e:S(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:a}=e,{target:i}=a;this.props=e,this.events=t,this.document=S(i),this.documentListeners=new eE(this.document),this.listeners=new eE(r),this.windowListeners=new eE(w(i)),this.initialCoordinates=null!=(n=L(a))?n:Q,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(o.Resize,this.handleCancel),this.windowListeners.add(o.DragStart,eP),this.windowListeners.add(o.VisibilityChange,this.handleCancel),this.windowListeners.add(o.ContextMenu,eP),this.documentListeners.add(o.Keydown,this.handleKeydown),t){if(eD(t))return;if(eM(t)){this.timeoutId=setTimeout(this.handleStart,t.delay);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(o.Click,eN,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(o.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:r,initialCoordinates:n,props:a}=this,{onMove:i,options:{activationConstraint:o}}=a;if(!n)return;let s=null!=(t=L(e))?t:Q,l=M(n,s);if(!r&&o){if(eM(o))return eC(l,o.tolerance)?this.handleCancel():void 0;if(eD(o))return null!=o.tolerance&&eC(l,o.tolerance)?this.handleCancel():eC(l,o.distance)?this.handleStart():void 0}e.cancelable&&e.preventDefault(),i(s)}handleEnd(){let{onEnd:e}=this.props;this.detach(),e()}handleCancel(){let{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===s.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let eL={move:{name:"pointermove"},end:{name:"pointerup"}};class eI extends eR{constructor(e){let{event:t}=e;super(e,eL,S(t.target))}}eI.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return!!r.isPrimary&&0===r.button&&(null==n||n({event:r}),!0)}}];let eF={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(l||(l={}));class eB extends eR{constructor(e){super(e,eF,S(e.event.target))}}eB.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return r.button!==l.RightClick&&(null==n||n({event:r}),!0)}}];let ez={move:{name:"touchmove"},end:{name:"touchend"}};class eV extends eR{constructor(e){super(e,ez)}static setup(){return window.addEventListener(ez.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ez.move.name,e)};function e(){}}}eV.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t,{touches:a}=r;return!(a.length>1)&&(null==n||n({event:r}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(u||(u={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let eU={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(f||(f={})).Optimized="optimized";let eq=new Map;function eH(e,t){return C(r=>e?r||("function"==typeof t?t(e):e):null,[t,e])}function eW(e){let{callback:t,disabled:r}=e,n=j(t),a=(0,m.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(n)},[r]);return(0,m.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}function eG(e){return new ej(ef(e),e)}function eK(e,t,r){void 0===t&&(t=eG);let[n,a]=(0,m.useReducer)(function(n){if(!e)return null;if(!1===e.isConnected){var a;return null!=(a=null!=n?n:r)?a:null}let i=t(e);return JSON.stringify(n)===JSON.stringify(i)?n:i},null),i=function(e){let{callback:t,disabled:r}=e,n=j(t),a=(0,m.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(n)},[n,r]);return(0,m.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}({callback(t){if(e)for(let r of t){let{type:t,target:n}=r;if("childList"===t&&n instanceof HTMLElement&&n.contains(e)){a();break}}}}),o=eW({callback:a});return _(()=>{a(),e?(null==o||o.observe(e),null==i||i.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==i||i.disconnect())},[e]),n}let eJ=[];function eY(e,t){void 0===t&&(t=[]);let r=(0,m.useRef)(null);return(0,m.useEffect)(()=>{r.current=null},t),(0,m.useEffect)(()=>{let t=e!==Q;t&&!r.current&&(r.current=e),!t&&r.current&&(r.current=null)},[e]),r.current?M(e,r.current):Q}function eQ(e){return(0,m.useMemo)(()=>e?function(e){let t=e.innerWidth,r=e.innerHeight;return{top:0,left:0,right:t,bottom:r,width:t,height:r}}(e):null,[e])}let eX=[];function eZ(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return $(t)?t:e}let e0=[{sensor:eI,options:{}},{sensor:eO,options:{}}],e1={current:{}},e2={draggable:{measure:eh},droppable:{measure:eh,strategy:d.WhileDragging,frequency:f.Optimized},dragOverlay:{measure:ef}};class e5 extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,r;return null!=(t=null==(r=this.get(e))?void 0:r.node.current)?t:void 0}}let e6={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new e5,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:K},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:e2,measureDroppableContainers:K,windowRect:null,measuringScheduled:!1},e8={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:K,draggableNodes:new Map,over:null,measureDroppableContainers:K},e4=(0,m.createContext)(e8),e3=(0,m.createContext)(e6);function e7(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new e5}}}function e9(e,t){switch(t.type){case a.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case a.DragMove:if(!e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case a.DragEnd:case a.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case a.RegisterDroppable:{let{element:r}=t,{id:n}=r,a=new e5(e.droppable.containers);return a.set(n,r),{...e,droppable:{...e.droppable,containers:a}}}case a.SetDroppableDisabled:{let{id:r,key:n,disabled:a}=t,i=e.droppable.containers.get(r);if(!i||n!==i.key)return e;let o=new e5(e.droppable.containers);return o.set(r,{...i,disabled:a}),{...e,droppable:{...e.droppable,containers:o}}}case a.UnregisterDroppable:{let{id:r,key:n}=t,a=e.droppable.containers.get(r);if(!a||n!==a.key)return e;let i=new e5(e.droppable.containers);return i.delete(r),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function te(e){let{disabled:t}=e,{active:r,activatorEvent:n,draggableNodes:a}=(0,m.useContext)(e4),i=N(n),o=N(null==r?void 0:r.id);return(0,m.useEffect)(()=>{if(!t&&!n&&i&&null!=o){if(!R(i)||document.activeElement===i.target)return;let e=a.get(o);if(!e)return;let{activatorNode:t,node:r}=e;(t.current||r.current)&&requestAnimationFrame(()=>{for(let e of[t.current,r.current]){if(!e)continue;let t=e.matches(F)?e:e.querySelector(F);if(t){t.focus();break}}})}},[n,t,a,o,i]),null}function tt(e,t){let{transform:r,...n}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...n}),r):r}let tr=(0,m.createContext)({...Q,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(p||(p={}));let tn=(0,m.memo)(function(e){var t,r,n,o,s,l;let{id:f,accessibility:v,autoScroll:b=!0,children:x,sensors:k=e0,collisionDetection:S=eo,measuring:j,modifiers:A,...O}=e,[M,R]=(0,m.useReducer)(e9,void 0,e7),[I,F]=function(){let[e]=(0,m.useState)(()=>new Set),t=(0,m.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,m.useCallback)(t=>{let{type:r,event:n}=t;e.forEach(e=>{var t;return null==(t=e[r])?void 0:t.call(e,n)})},[e]),t]}(),[B,z]=(0,m.useState)(p.Uninitialized),V=B===p.Initialized,{draggable:{active:q,nodes:H,translate:W},droppable:{containers:K}}=M,J=q?H.get(q):null,Y=(0,m.useRef)({initial:null,translated:null}),X=(0,m.useMemo)(()=>{var e;return null!=q?{id:q,data:null!=(e=null==J?void 0:J.data)?e:e1,rect:Y}:null},[q,J]),Z=(0,m.useRef)(null),[ee,et]=(0,m.useState)(null),[en,ea]=(0,m.useState)(null),ei=E(O,Object.values(O)),es=T("DndDescribedBy",f),ec=(0,m.useMemo)(()=>K.getEnabled(),[K]),ed=(0,m.useMemo)(()=>({draggable:{...e2.draggable,...null==j?void 0:j.draggable},droppable:{...e2.droppable,...null==j?void 0:j.droppable},dragOverlay:{...e2.dragOverlay,...null==j?void 0:j.dragOverlay}}),[null==j?void 0:j.draggable,null==j?void 0:j.droppable,null==j?void 0:j.dragOverlay]),{droppableRects:eh,measureDroppableContainers:ey,measuringScheduled:ev}=function(e,t){let{dragging:r,dependencies:n,config:a}=t,[i,o]=(0,m.useState)(null),{frequency:s,measure:l,strategy:u}=a,c=(0,m.useRef)(e),f=function(){switch(u){case d.Always:return!1;case d.BeforeDragging:return r;default:return!r}}(),p=E(f),g=(0,m.useCallback)(function(e){void 0===e&&(e=[]),p.current||o(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[p]),y=(0,m.useRef)(null),v=C(t=>{if(f&&!r)return eq;if(!t||t===eq||c.current!==e||null!=i){let t=new Map;for(let r of e){if(!r)continue;if(i&&i.length>0&&!i.includes(r.id)&&r.rect.current){t.set(r.id,r.rect.current);continue}let e=r.node.current,n=e?new ej(l(e),e):null;r.rect.current=n,n&&t.set(r.id,n)}return t}return t},[e,i,r,f,l]);return(0,m.useEffect)(()=>{c.current=e},[e]),(0,m.useEffect)(()=>{f||g()},[r,f]),(0,m.useEffect)(()=>{i&&i.length>0&&o(null)},[JSON.stringify(i)]),(0,m.useEffect)(()=>{f||"number"!=typeof s||null!==y.current||(y.current=setTimeout(()=>{g(),y.current=null},s))},[s,f,g,...n]),{droppableRects:v,measureDroppableContainers:g,measuringScheduled:null!=i}}(ec,{dragging:V,dependencies:[W.x,W.y],config:ed.droppable}),eS=function(e,t){let r=null!==t?e.get(t):void 0,n=r?r.node.current:null;return C(e=>{var r;return null===t?null:null!=(r=null!=n?n:e)?r:null},[n,t])}(H,q),e_=(0,m.useMemo)(()=>en?L(en):null,[en]),eE=function(){let e=(null==ee?void 0:ee.autoScrollEnabled)===!1,t="object"==typeof b?!1===b.enabled:!1===b,r=V&&!e&&!t;return"object"==typeof b?{...b,enabled:r}:{enabled:r}}(),eC=eH(eS,ed.draggable.measure);!function(e){let{activeNode:t,measure:r,initialRect:n,config:a=!0}=e,i=(0,m.useRef)(!1),{x:o,y:s}="boolean"==typeof a?{x:a,y:a}:a;_(()=>{if(!o&&!s||!t){i.current=!1;return}if(i.current||!n)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let a=el(r(e),n);if(o||(a.x=0),s||(a.y=0),i.current=!0,Math.abs(a.x)>0||Math.abs(a.y)>0){let t=em(e);t&&t.scrollBy({top:a.y,left:a.x})}},[t,o,s,n,r])}({activeNode:q?H.get(q):null,config:eE.layoutShiftCompensation,initialRect:eC,measure:ed.draggable.measure});let eP=eK(eS,ed.draggable.measure,eC),eN=eK(eS?eS.parentElement:null),eA=(0,m.useRef)({activatorEvent:null,active:null,activeNode:eS,collisionRect:null,collisions:null,droppableRects:eh,draggableNodes:H,draggingNode:null,draggingNodeRect:null,droppableContainers:K,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eT=K.getNodeFor(null==(t=eA.current.over)?void 0:t.id),eO=function(e){let{measure:t}=e,[r,n]=(0,m.useState)(null),a=eW({callback:(0,m.useCallback)(e=>{for(let{target:r}of e)if($(r)){n(e=>{let n=t(r);return e?{...e,width:n.width,height:n.height}:n});break}},[t])}),[i,o]=P((0,m.useCallback)(e=>{let r=eZ(e);null==a||a.disconnect(),r&&(null==a||a.observe(r)),n(r?t(r):null)},[t,a]));return(0,m.useMemo)(()=>({nodeRef:i,rect:r,setRef:o}),[r,i,o])}({measure:ed.dragOverlay.measure}),eD=null!=(r=eO.nodeRef.current)?r:eS,eM=V?null!=(n=eO.rect)?n:eP:null,eR=!!(eO.nodeRef.current&&eO.rect),eL=function(e){let t=eH(e);return el(e,t)}(eR?null:eP),eI=eQ(eD?w(eD):null),eF=function(e){let t=(0,m.useRef)(e),r=C(r=>e?r&&r!==eJ&&e&&t.current&&e.parentNode===t.current.parentNode?r:ep(e):eJ,[e]);return(0,m.useEffect)(()=>{t.current=e},[e]),r}(V?null!=eT?eT:eS:null),eB=function(e,t){void 0===t&&(t=ef);let[r]=e,n=eQ(r?w(r):null),[a,i]=(0,m.useReducer)(function(){return e.length?e.map(e=>ew(e)?n:new ej(t(e),e)):eX},eX),o=eW({callback:i});return e.length>0&&a===eX&&i(),_(()=>{e.length?e.forEach(e=>null==o?void 0:o.observe(e)):(null==o||o.disconnect(),i())},[e]),a}(eF),ez=tt(A,{transform:{x:W.x-eL.x,y:W.y-eL.y,scaleX:1,scaleY:1},activatorEvent:en,active:X,activeNodeRect:eP,containerNodeRect:eN,draggingNodeRect:eM,over:eA.current.over,overlayNodeRect:eO.rect,scrollableAncestors:eF,scrollableAncestorRects:eB,windowRect:eI}),eV=e_?D(e_,W):null,eG=function(e){let[t,r]=(0,m.useState)(null),n=(0,m.useRef)(e),a=(0,m.useCallback)(e=>{let t=eg(e.target);t&&r(e=>e?(e.set(t,eb(t)),new Map(e)):null)},[]);return(0,m.useEffect)(()=>{let t=n.current;if(e!==t){i(t);let o=e.map(e=>{let t=eg(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,eb(t)]):null}).filter(e=>null!=e);r(o.length?new Map(o):null),n.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=eg(e);null==t||t.removeEventListener("scroll",a)})}},[a,e]),(0,m.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>D(e,t),Q):ek(e):Q,[e,t])}(eF),e5=eY(eG),e6=eY(eG,[eP]),e8=D(ez,e5),tn=eM?eu(eM,ez):null,ta=X&&tn?S({active:X,collisionRect:tn,droppableRects:eh,droppableContainers:ec,pointerCoordinates:eV}):null,ti=er(ta,"id"),[to,ts]=(0,m.useState)(null),tl=(s=eR?ez:D(ez,e6),l=null!=(o=null==to?void 0:to.rect)?o:null,{...s,scaleX:l&&eP?l.width/eP.width:1,scaleY:l&&eP?l.height/eP.height:1}),tu=(0,m.useCallback)((e,t)=>{let{sensor:r,options:n}=t;if(null==Z.current)return;let i=H.get(Z.current);if(!i)return;let o=e.nativeEvent,s=new r({active:Z.current,activeNode:i,event:o,options:n,context:eA,onStart(e){let t=Z.current;if(null==t)return;let r=H.get(t);if(!r)return;let{onDragStart:n}=ei.current,i={active:{id:t,data:r.data,rect:Y}};(0,g.unstable_batchedUpdates)(()=>{null==n||n(i),z(p.Initializing),R({type:a.DragStart,initialCoordinates:e,active:t}),I({type:"onDragStart",event:i})})},onMove(e){R({type:a.DragMove,coordinates:e})},onEnd:l(a.DragEnd),onCancel:l(a.DragCancel)});function l(e){return async function(){let{active:t,collisions:r,over:n,scrollAdjustedTranslate:i}=eA.current,s=null;if(t&&i){let{cancelDrop:l}=ei.current;s={activatorEvent:o,active:t,collisions:r,delta:i,over:n},e===a.DragEnd&&"function"==typeof l&&await Promise.resolve(l(s))&&(e=a.DragCancel)}Z.current=null,(0,g.unstable_batchedUpdates)(()=>{R({type:e}),z(p.Uninitialized),ts(null),et(null),ea(null);let t=e===a.DragEnd?"onDragEnd":"onDragCancel";if(s){let e=ei.current[t];null==e||e(s),I({type:t,event:s})}})}}(0,g.unstable_batchedUpdates)(()=>{et(s),ea(e.nativeEvent)})},[H]),tc=(0,m.useCallback)((e,t)=>(r,n)=>{let a=r.nativeEvent,i=H.get(n);null!==Z.current||!i||a.dndKit||a.defaultPrevented||!0===e(r,t.options,{active:i})&&(a.dndKit={capturedBy:t.sensor},Z.current=n,tu(r,t))},[H,tu]),td=(0,m.useMemo)(()=>k.reduce((e,t)=>{let{sensor:r}=t;return[...e,...r.activators.map(e=>({eventName:e.eventName,handler:tc(e.handler,t)}))]},[]),[k,tc]);(0,m.useEffect)(()=>{if(!y)return;let e=k.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},k.map(e=>{let{sensor:t}=e;return t})),_(()=>{eP&&B===p.Initializing&&z(p.Initialized)},[eP,B]),(0,m.useEffect)(()=>{let{onDragMove:e}=ei.current,{active:t,activatorEvent:r,collisions:n,over:a}=eA.current;if(!t||!r)return;let i={active:t,activatorEvent:r,collisions:n,delta:{x:e8.x,y:e8.y},over:a};(0,g.unstable_batchedUpdates)(()=>{null==e||e(i),I({type:"onDragMove",event:i})})},[e8.x,e8.y]),(0,m.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:r,droppableContainers:n,scrollAdjustedTranslate:a}=eA.current;if(!e||null==Z.current||!t||!a)return;let{onDragOver:i}=ei.current,o=n.get(ti),s=o&&o.rect.current?{id:o.id,rect:o.rect.current,data:o.data,disabled:o.disabled}:null,l={active:e,activatorEvent:t,collisions:r,delta:{x:a.x,y:a.y},over:s};(0,g.unstable_batchedUpdates)(()=>{ts(s),null==i||i(l),I({type:"onDragOver",event:l})})},[ti]),_(()=>{eA.current={activatorEvent:en,active:X,activeNode:eS,collisionRect:tn,collisions:ta,droppableRects:eh,draggableNodes:H,draggingNode:eD,draggingNodeRect:eM,droppableContainers:K,over:to,scrollableAncestors:eF,scrollAdjustedTranslate:e8},Y.current={initial:eM,translated:tn}},[X,eS,ta,tn,H,eD,eM,eh,K,to,eF,e8]),function(e){let{acceleration:t,activator:r=u.Pointer,canScroll:n,draggingRect:a,enabled:o,interval:s=5,order:l=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:f,scrollableAncestorRects:p,delta:g,threshold:y}=e,v=function(e){let{delta:t,disabled:r}=e,n=N(t);return C(e=>{if(r||!n||!e)return eU;let a={x:Math.sign(t.x-n.x),y:Math.sign(t.y-n.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===a.x,[i.Forward]:e.x[i.Forward]||1===a.x},y:{[i.Backward]:e.y[i.Backward]||-1===a.y,[i.Forward]:e.y[i.Forward]||1===a.y}}},[r,t,n])}({delta:g,disabled:!o}),[b,w]=function(){let e=(0,m.useRef)(null);return[(0,m.useCallback)((t,r)=>{e.current=setInterval(t,r)},[]),(0,m.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),x=(0,m.useRef)({x:0,y:0}),$=(0,m.useRef)({x:0,y:0}),k=(0,m.useMemo)(()=>{switch(r){case u.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case u.DraggableRect:return a}},[r,a,d]),S=(0,m.useRef)(null),_=(0,m.useCallback)(()=>{let e=S.current;if(!e)return;let t=x.current.x*$.current.x,r=x.current.y*$.current.y;e.scrollBy(t,r)},[]),j=(0,m.useMemo)(()=>l===c.TreeOrder?[...f].reverse():f,[l,f]);(0,m.useEffect)(()=>{if(!o||!f.length||!k)return void w();for(let e of j){if((null==n?void 0:n(e))===!1)continue;let r=p[f.indexOf(e)];if(!r)continue;let{direction:a,speed:o}=function(e,t,r,n,a){let{top:o,left:s,right:l,bottom:u}=r;void 0===n&&(n=10),void 0===a&&(a=e$);let{isTop:c,isBottom:d,isLeft:f,isRight:p}=ex(e),m={x:0,y:0},g={x:0,y:0},y={height:t.height*a.y,width:t.width*a.x};return!c&&o<=t.top+y.height?(m.y=i.Backward,g.y=n*Math.abs((t.top+y.height-o)/y.height)):!d&&u>=t.bottom-y.height&&(m.y=i.Forward,g.y=n*Math.abs((t.bottom-y.height-u)/y.height)),!p&&l>=t.right-y.width?(m.x=i.Forward,g.x=n*Math.abs((t.right-y.width-l)/y.width)):!f&&s<=t.left+y.width&&(m.x=i.Backward,g.x=n*Math.abs((t.left+y.width-s)/y.width)),{direction:m,speed:g}}(e,r,k,t,y);for(let e of["x","y"])v[e][a[e]]||(o[e]=0,a[e]=0);if(o.x>0||o.y>0){w(),S.current=e,b(_,s),x.current=o,$.current=a;return}}x.current={x:0,y:0},$.current={x:0,y:0},w()},[t,_,n,w,o,s,JSON.stringify(k),JSON.stringify(v),b,f,j,p,JSON.stringify(y)])}({...eE,delta:W,draggingRect:tn,pointerCoordinates:eV,scrollableAncestors:eF,scrollableAncestorRects:eB});let tf=(0,m.useMemo)(()=>({active:X,activeNode:eS,activeNodeRect:eP,activatorEvent:en,collisions:ta,containerNodeRect:eN,dragOverlay:eO,draggableNodes:H,droppableContainers:K,droppableRects:eh,over:to,measureDroppableContainers:ey,scrollableAncestors:eF,scrollableAncestorRects:eB,measuringConfiguration:ed,measuringScheduled:ev,windowRect:eI}),[X,eS,eP,en,ta,eN,eO,H,K,eh,to,ey,eF,eB,ed,ev,eI]),th=(0,m.useMemo)(()=>({activatorEvent:en,activators:td,active:X,activeNodeRect:eP,ariaDescribedById:{draggable:es},dispatch:R,draggableNodes:H,over:to,measureDroppableContainers:ey}),[en,td,X,eP,R,es,H,to,ey]);return m.createElement(U.Provider,{value:F},m.createElement(e4.Provider,{value:th},m.createElement(e3.Provider,{value:tf},m.createElement(tr.Provider,{value:tl},x)),m.createElement(te,{disabled:(null==v?void 0:v.restoreFocus)===!1})),m.createElement(G,{...v,hiddenTextDescribedById:es}))}),ta=(0,m.createContext)(null),ti="button";function to(e){let{id:t,data:r,disabled:n=!1,attributes:a}=e,i=T("Droppable"),{activators:o,activatorEvent:s,active:l,activeNodeRect:u,ariaDescribedById:c,draggableNodes:d,over:f}=(0,m.useContext)(e4),{role:p=ti,roleDescription:g="draggable",tabIndex:y=0}=null!=a?a:{},v=(null==l?void 0:l.id)===t,b=(0,m.useContext)(v?tr:ta),[w,x]=P(),[$,k]=P(),S=(0,m.useMemo)(()=>o.reduce((e,r)=>{let{eventName:n,handler:a}=r;return e[n]=e=>{a(e,t)},e},{}),[o,t]),j=E(r);return _(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:$,data:j}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:l,activatorEvent:s,activeNodeRect:u,attributes:(0,m.useMemo)(()=>({role:p,tabIndex:y,"aria-disabled":n,"aria-pressed":!!v&&p===ti||void 0,"aria-roledescription":g,"aria-describedby":c.draggable}),[n,p,y,v,g,c.draggable]),isDragging:v,listeners:n?void 0:S,node:w,over:f,setNodeRef:x,setActivatorNodeRef:k,transform:b}}function ts(){return(0,m.useContext)(e3)}let tl={timeout:25};function tu(e){let{data:t,disabled:r=!1,id:n,resizeObserverConfig:i}=e,o=T("Droppable"),{active:s,dispatch:l,over:u,measureDroppableContainers:c}=(0,m.useContext)(e4),d=(0,m.useRef)({disabled:r}),f=(0,m.useRef)(!1),p=(0,m.useRef)(null),g=(0,m.useRef)(null),{disabled:y,updateMeasurementsFor:v,timeout:b}={...tl,...i},w=E(null!=v?v:n),x=eW({callback:(0,m.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=g.current&&clearTimeout(g.current),g.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),g.current=null},b)},[b]),disabled:y||!s}),[$,k]=P((0,m.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),f.current=!1),e&&x.observe(e))},[x])),S=E(t);return(0,m.useEffect)(()=>{x&&$.current&&(x.disconnect(),f.current=!1,x.observe($.current))},[$,x]),_(()=>(l({type:a.RegisterDroppable,element:{id:n,key:o,disabled:r,node:$,rect:p,data:S}}),()=>l({type:a.UnregisterDroppable,key:o,id:n})),[n]),(0,m.useEffect)(()=>{r!==d.current.disabled&&(l({type:a.SetDroppableDisabled,id:n,key:o,disabled:r}),d.current.disabled=r)},[n,o,r,l]),{active:s,rect:p,isOver:(null==u?void 0:u.id)===n,node:$,over:u,setNodeRef:k}}function tc(e){let{animation:t,children:r}=e,[n,a]=(0,m.useState)(null),[i,o]=(0,m.useState)(null),s=N(r);return r||n||!s||a(s),_(()=>{if(!i)return;let e=null==n?void 0:n.key,r=null==n?void 0:n.props.id;if(null==e||null==r)return void a(null);Promise.resolve(t(r,i)).then(()=>{a(null)})},[t,n,i]),m.createElement(m.Fragment,null,r,n?(0,m.cloneElement)(n,{ref:o}):null)}let td={x:0,y:0,scaleX:1,scaleY:1};function tf(e){let{children:t}=e;return m.createElement(e4.Provider,{value:e8},m.createElement(tr.Provider,{value:td},t))}let th={position:"fixed",touchAction:"none"},tp=e=>R(e)?"transform 250ms ease":void 0,tm=(0,m.forwardRef)((e,t)=>{let{as:r,activatorEvent:n,adjustScale:a,children:i,className:o,rect:s,style:l,transform:u,transition:c=tp}=e;if(!s)return null;let d=a?u:{...u,scaleX:1,scaleY:1},f={...th,width:s.width,height:s.height,top:s.top,left:s.left,transform:I.Transform.toString(d),transformOrigin:a&&n?function(e,t){let r=L(e);if(!r)return"0 0";let n={x:(r.x-t.left)/t.width*100,y:(r.y-t.top)/t.height*100};return n.x+"% "+n.y+"%"}(n,s):void 0,transition:"function"==typeof c?c(n):c,...l};return m.createElement(r,{className:o,style:f,ref:t},i)}),tg={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:r}}=e;return[{transform:I.Transform.toString(t)},{transform:I.Transform.toString(r)}]},sideEffects:(n={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:r}=e,a={},{styles:i,className:o}=n;if(null!=i&&i.active)for(let[e,r]of Object.entries(i.active))void 0!==r&&(a[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,r));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&r.node.style.setProperty(e,t);return null!=o&&o.active&&t.node.classList.add(o.active),null!=o&&o.dragOverlay&&r.node.classList.add(o.dragOverlay),function(){for(let[e,r]of Object.entries(a))t.node.style.setProperty(e,r);null!=o&&o.active&&t.node.classList.remove(o.active)}})},ty=0,tv=m.memo(e=>{let{adjustScale:t=!1,children:r,dropAnimation:n,style:a,transition:i,modifiers:o,wrapperElement:s="div",className:l,zIndex:u=999}=e,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:p,draggableNodes:g,droppableContainers:y,dragOverlay:v,over:b,measuringConfiguration:x,scrollableAncestors:$,scrollableAncestorRects:k,windowRect:S}=ts(),_=(0,m.useContext)(tr),E=function(e){return(0,m.useMemo)(()=>{if(null!=e)return++ty},[e])}(null==d?void 0:d.id),C=tt(o,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:p,draggingNodeRect:v.rect,over:b,overlayNodeRect:v.rect,scrollableAncestors:$,scrollableAncestorRects:k,transform:_,windowRect:S}),P=eH(f),N=function(e){let{config:t,draggableNodes:r,droppableContainers:n,measuringConfiguration:a}=e;return j((e,i)=>{if(null===t)return;let o=r.get(e);if(!o)return;let s=o.node.current;if(!s)return;let l=eZ(i);if(!l)return;let{transform:u}=w(i).getComputedStyle(i),c=ec(u);if(!c)return;let d="function"==typeof t?t:function(e){let{duration:t,easing:r,sideEffects:n,keyframes:a}={...tg,...e};return e=>{let{active:i,dragOverlay:o,transform:s,...l}=e;if(!t)return;let u={x:o.rect.left-i.rect.left,y:o.rect.top-i.rect.top},c={scaleX:1!==s.scaleX?i.rect.width*s.scaleX/o.rect.width:1,scaleY:1!==s.scaleY?i.rect.height*s.scaleY/o.rect.height:1},d={x:s.x-u.x,y:s.y-u.y,...c},f=a({...l,active:i,dragOverlay:o,transform:{initial:s,final:d}}),[p]=f,m=f[f.length-1];if(JSON.stringify(p)===JSON.stringify(m))return;let g=null==n?void 0:n({active:i,dragOverlay:o,...l}),y=o.node.animate(f,{duration:t,easing:r,fill:"forwards"});return new Promise(e=>{y.onfinish=()=>{null==g||g(),e()}})}}(t);return eS(s,a.draggable.measure),d({active:{id:e,data:o.data,node:s,rect:a.draggable.measure(s)},draggableNodes:r,dragOverlay:{node:i,rect:a.dragOverlay.measure(l)},droppableContainers:n,measuringConfiguration:a,transform:c})})}({config:n,draggableNodes:g,droppableContainers:y,measuringConfiguration:x}),A=P?v.setRef:void 0;return m.createElement(tf,null,m.createElement(tc,{animation:N},d&&E?m.createElement(tm,{key:E,id:d.id,ref:A,as:s,activatorEvent:c,adjustScale:t,className:l,transition:i,rect:P,style:{zIndex:u,...a},transform:C},r):null))})},68297:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(8253);t.default={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,n.validateTuple)(e,"items")}},69822:(e,t,r)=>{"use strict";r.d(t,{d:()=>n});let n=(e,t)=>t.map(e=>e.replace("*","")).some(t=>e.startsWith(t))},70132:(e,t,r)=>{"use strict";let n;r.d(t,{a:()=>g,b:()=>y,c:()=>v,d:()=>b,e:()=>x,f:()=>$});var a,i,o=r(89688),s=r(80665);function l(e){if("react"===e)return a||(a=r.t(o,2));if("react-dom"===e)return i||(i=r.t(s,2));throw Error("Unknown module ".concat(e))}var u=Object.create,c=Object.defineProperty,d=Object.getOwnPropertyDescriptor,f=Object.getOwnPropertyNames,p=Object.getPrototypeOf,m=Object.prototype.hasOwnProperty,g=l,y=(e,t)=>()=>(e&&(t=e(e=0)),t),v=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),b=(e,t)=>{for(var r in t)c(e,r,{get:t[r],enumerable:!0})},w=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of f(t))m.call(e,a)||a===r||c(e,a,{get:()=>t[a],enumerable:!(n=d(t,a))||n.enumerable});return e},x=(e,t,r)=>(r=null!=e?u(p(e)):{},w(!t&&e&&e.__esModule?r:c(r,"default",{value:e,enumerable:!0}),e)),$=e=>w(c({},"__esModule",{value:!0}),e)},70207:(e,t,r)=>{"use strict";r.d(t,{DefaultNavClient:()=>p});var n=r(47092),a=r(67252),i=r(55032),o=r(56156),s=r(71464),l=r(83548),u=r(73272),c=r(98034),d=r(45311),f=r(89688);let p=e=>{let t,r,p=(0,n.c)(15),{groups:g,navPreferences:y}=e,v=(0,c.usePathname)(),{config:b}=(0,o.b)(),{admin:w,collections:x,routes:$}=b,{routes:k}=w,{browseByFolder:S}=k,{admin:_}=$;p[0]!==x?(t=()=>x.reduce(m,[]),p[0]=x,p[1]=t):t=p[1];let[j]=f.useState(t),{i18n:E}=(0,s.d)();if(p[2]!==_||p[3]!==j.length||p[4]!==S||p[5]!==g||p[6]!==E||p[7]!==(null==y?void 0:y.groups)||p[8]!==v){let e,t=(0,d.Q)({adminRoute:_,path:S}),n=v.startsWith(t);p[10]!==_||p[11]!==E||p[12]!==(null==y?void 0:y.groups)||p[13]!==v?(e=(e,t)=>{var r,n;let{entities:o,label:s}=e;return(0,a.jsx)(l.NavGroup,{isOpen:null==y||null==(n=y.groups)||null==(r=n[s])?void 0:r.open,label:s,children:o.map((e,t)=>{let r,n,{slug:o,type:s,label:c}=e;s===u.ck.collection&&(r=(0,d.Q)({adminRoute:_,path:"/collections/".concat(o)}),n="nav-".concat(o)),s===u.ck.global&&(r=(0,d.Q)({adminRoute:_,path:"/globals/".concat(o)}),n="nav-global-".concat(o));let f=v.startsWith(r)&&["/",void 0].includes(v[r.length]),p=(0,a.jsxs)(a.Fragment,{children:[f&&(0,a.jsx)("div",{className:"".concat("nav","__link-indicator")}),(0,a.jsx)("span",{className:"".concat("nav","__link-label"),children:(0,i.sC)(c,E)})]});return v===r?(0,a.jsx)("div",{className:"".concat("nav","__link"),id:n,children:p},t):(0,a.jsx)(l.Link,{className:"".concat("nav","__link"),href:r,id:n,prefetch:!1,children:p},t)})},t)},p[10]=_,p[11]=E,p[12]=null==y?void 0:y.groups,p[13]=v,p[14]=e):e=p[14],r=(0,a.jsxs)(f.Fragment,{children:[j.length>0&&(0,a.jsx)(l.BrowseByFolderButton,{active:n}),g.map(e)]}),p[2]=_,p[3]=j.length,p[4]=S,p[5]=g,p[6]=E,p[7]=null==y?void 0:y.groups,p[8]=v,p[9]=r}else r=p[9];return r};function m(e,t){return t.folders&&e.push(t.slug),e}},70981:(e,t,r)=>{"use strict";r.d(t,{Collapsible:()=>c});var n=r(47092),a=r(67252),i=r(55032),o=r(71464);r(89688);var s=r(20985),l=r(4624),u=r(73063);let c=e=>{var t;let r,c,d=(0,n.c)(11),{baseVersionField:f,comparisonValue:p,field:m,parentIsLocalized:g,versionValue:y}=e,{i18n:v}=(0,o.d)(),{selectedLocales:b}=(0,s.I)();if(!(null==(t=f.fields)?void 0:t.length))return null;d[0]!==m||d[1]!==v?(r="label"in m&&m.label&&"function"!=typeof m.label&&(0,a.jsx)("span",{children:(0,i.sC)(m.label,v)}),d[0]=m,d[1]=v,d[2]=r):r=d[2];let w=g||m.localized;return d[3]!==f.fields||d[4]!==p||d[5]!==m.fields||d[6]!==b||d[7]!==r||d[8]!==w||d[9]!==y?(c=(0,a.jsx)("div",{className:"collapsible-diff",children:(0,a.jsx)(l.$,{comparison:p,fields:m.fields,label:r,locales:b,parentIsLocalized:w,version:y,children:(0,a.jsx)(u.RenderVersionFieldsToDiff,{versionFields:f.fields})})}),d[3]=f.fields,d[4]=p,d[5]=m.fields,d[6]=b,d[7]=r,d[8]=w,d[9]=y,d[10]=c):c=d[10],c}},72431:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(59212),a=r(63510);t.default=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",n.default,a.default]},73063:(e,t,r)=>{"use strict";r.d(t,{RenderVersionFieldsToDiff:()=>l});var n=r(47092),a=r(67252),i=r(56156),o=r(89688);let s="render-field-diffs",l=e=>{let t,r,l,c=(0,n.c)(5),{versionFields:d}=e,[f,p]=o.useState(!1);return c[0]===Symbol.for("react.memo_cache_sentinel")?(t=()=>{p(!0)},r=[],c[0]=t,c[1]=r):(t=c[0],r=c[1]),(0,o.useEffect)(t,r),c[2]!==f||c[3]!==d?(l=(0,a.jsx)("div",{className:s,children:f?null==d?void 0:d.map(u):(0,a.jsx)(o.Fragment,{children:(0,a.jsx)(i.d,{height:"8rem",width:"100%"})})}),c[2]=f,c[3]=d,c[4]=l):l=c[4],l};function u(e,t){if(e.fieldByLocale){let r=[];for(let[n,i]of Object.entries(e.fieldByLocale))r.push((0,a.jsx)("div",{className:"".concat(s,"__locale"),"data-field-path":i.path,"data-locale":n,children:(0,a.jsx)("div",{className:"".concat(s,"__locale-value"),children:i.CustomComponent})},[n,t].join("-")));return(0,a.jsx)("div",{className:"".concat(s,"__field"),children:r},t)}return e.field?(0,a.jsx)("div",{className:"".concat(s,"__field field__").concat(e.field.type),"data-field-path":e.field.path,children:e.field.CustomComponent},t):null}},73272:(e,t,r)=>{"use strict";r.d(t,{ck:()=>Q,eS:()=>I,Yq:()=>Y,wN:()=>F,n_:()=>X,yF:()=>Z,UG:()=>ee,zG:()=>B}),r(89688);var n=r(96900),a=r(40496),i=r(1044),o=r(63165),s=r(65726);function l(e){let t=(0,s.a)(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),e-r}var u=r(66720),c=r(39476);function d(e,t){let r=(0,s.a)(e,null==t?void 0:t.in);return r.setHours(0,0,0,0),r}var f=r(9688);function p(e,t){return(0,f.k)(e,{...t,weekStartsOn:1})}function m(e,t){let r=(0,s.a)(e,null==t?void 0:t.in),n=r.getFullYear(),i=(0,a.w)(r,0);i.setFullYear(n+1,0,4),i.setHours(0,0,0,0);let o=p(i),l=(0,a.w)(r,0);l.setFullYear(n,0,4),l.setHours(0,0,0,0);let u=p(l);return r.getTime()>=o.getTime()?n+1:r.getTime()>=u.getTime()?n:n-1}function g(e,t){var r,n,i,l,u,c,d,p;let m=(0,s.a)(e,null==t?void 0:t.in),g=m.getFullYear(),y=(0,o.q)(),v=null!=(p=null!=(d=null!=(c=null!=(u=null==t?void 0:t.firstWeekContainsDate)?u:null==t||null==(n=t.locale)||null==(r=n.options)?void 0:r.firstWeekContainsDate)?c:y.firstWeekContainsDate)?d:null==(l=y.locale)||null==(i=l.options)?void 0:i.firstWeekContainsDate)?p:1,b=(0,a.w)((null==t?void 0:t.in)||e,0);b.setFullYear(g+1,0,v),b.setHours(0,0,0,0);let w=(0,f.k)(b,t),x=(0,a.w)((null==t?void 0:t.in)||e,0);x.setFullYear(g,0,v),x.setHours(0,0,0,0);let $=(0,f.k)(x,t);return+m>=+w?g+1:+m>=+$?g:g-1}function y(e,t){let r=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+r}let v={y(e,t){let r=e.getFullYear(),n=r>0?r:1-r;return y("yy"===t?n%100:n,t.length)},M(e,t){let r=e.getMonth();return"M"===t?String(r+1):y(r+1,2)},d:(e,t)=>y(e.getDate(),t.length),a(e,t){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>y(e.getHours()%12||12,t.length),H:(e,t)=>y(e.getHours(),t.length),m:(e,t)=>y(e.getMinutes(),t.length),s:(e,t)=>y(e.getSeconds(),t.length),S(e,t){let r=t.length;return y(Math.trunc(e.getMilliseconds()*Math.pow(10,r-3)),t.length)}},b={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},w={G:function(e,t,r){let n=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});default:return r.era(n,{width:"wide"})}},y:function(e,t,r){if("yo"===t){let t=e.getFullYear();return r.ordinalNumber(t>0?t:1-t,{unit:"year"})}return v.y(e,t)},Y:function(e,t,r,n){let a=g(e,n),i=a>0?a:1-a;return"YY"===t?y(i%100,2):"Yo"===t?r.ordinalNumber(i,{unit:"year"}):y(i,t.length)},R:function(e,t){return y(m(e),t.length)},u:function(e,t){return y(e.getFullYear(),t.length)},Q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return y(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return y(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,r){let n=e.getMonth();switch(t){case"M":case"MM":return v.M(e,t);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,r){let n=e.getMonth();switch(t){case"L":return String(n+1);case"LL":return y(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,r,n){let i=function(e,t){let r=(0,s.a)(e,null==t?void 0:t.in);return Math.round(((0,f.k)(r,t)-function(e,t){var r,n,i,s,l,u,c,d;let p=(0,o.q)(),m=null!=(d=null!=(c=null!=(u=null!=(l=null==t?void 0:t.firstWeekContainsDate)?l:null==t||null==(n=t.locale)||null==(r=n.options)?void 0:r.firstWeekContainsDate)?u:p.firstWeekContainsDate)?c:null==(s=p.locale)||null==(i=s.options)?void 0:i.firstWeekContainsDate)?d:1,y=g(e,t),v=(0,a.w)((null==t?void 0:t.in)||e,0);return v.setFullYear(y,0,m),v.setHours(0,0,0,0),(0,f.k)(v,t)}(r,t))/c.my)+1}(e,n);return"wo"===t?r.ordinalNumber(i,{unit:"week"}):y(i,t.length)},I:function(e,t,r){let n=function(e,t){let r=(0,s.a)(e,void 0);return Math.round((p(r)-function(e,t){let r=m(e,void 0),n=(0,a.w)(e,0);return n.setFullYear(r,0,4),n.setHours(0,0,0,0),p(n)}(r))/c.my)+1}(e);return"Io"===t?r.ordinalNumber(n,{unit:"week"}):y(n,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):v.d(e,t)},D:function(e,t,r){let n=function(e,t){let r=(0,s.a)(e,void 0);return function(e,t,r){let[n,a]=(0,u.x)(void 0,e,t),i=d(n),o=d(a);return Math.round((i-l(i)-(o-l(o)))/c.w4)}(r,function(e,t){let r=(0,s.a)(e,void 0);return r.setFullYear(r.getFullYear(),0,1),r.setHours(0,0,0,0),r}(r))+1}(e);return"Do"===t?r.ordinalNumber(n,{unit:"dayOfYear"}):y(n,t.length)},E:function(e,t,r){let n=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,r,n){let a=e.getDay(),i=(a-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return y(i,2);case"eo":return r.ordinalNumber(i,{unit:"day"});case"eee":return r.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(a,{width:"short",context:"formatting"});default:return r.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,r,n){let a=e.getDay(),i=(a-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return y(i,t.length);case"co":return r.ordinalNumber(i,{unit:"day"});case"ccc":return r.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(a,{width:"narrow",context:"standalone"});case"cccccc":return r.day(a,{width:"short",context:"standalone"});default:return r.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,r){let n=e.getDay(),a=0===n?7:n;switch(t){case"i":return String(a);case"ii":return y(a,t.length);case"io":return r.ordinalNumber(a,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,r){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},b:function(e,t,r){let n,a=e.getHours();switch(n=12===a?b.noon:0===a?b.midnight:a/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},B:function(e,t,r){let n,a=e.getHours();switch(n=a>=17?b.evening:a>=12?b.afternoon:a>=4?b.morning:b.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return v.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):v.H(e,t)},K:function(e,t,r){let n=e.getHours()%12;return"Ko"===t?r.ordinalNumber(n,{unit:"hour"}):y(n,t.length)},k:function(e,t,r){let n=e.getHours();return(0===n&&(n=24),"ko"===t)?r.ordinalNumber(n,{unit:"hour"}):y(n,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):v.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):v.s(e,t)},S:function(e,t){return v.S(e,t)},X:function(e,t,r){let n=e.getTimezoneOffset();if(0===n)return"Z";switch(t){case"X":return $(n);case"XXXX":case"XX":return k(n);default:return k(n,":")}},x:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"x":return $(n);case"xxxx":case"xx":return k(n);default:return k(n,":")}},O:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+x(n,":");default:return"GMT"+k(n,":")}},z:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+x(n,":");default:return"GMT"+k(n,":")}},t:function(e,t,r){return y(Math.trunc(e/1e3),t.length)},T:function(e,t,r){return y(+e,t.length)}};function x(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=e>0?"-":"+",n=Math.abs(e),a=Math.trunc(n/60),i=n%60;return 0===i?r+String(a):r+String(a)+t+y(i,2)}function $(e,t){return e%60==0?(e>0?"-":"+")+y(Math.abs(e)/60,2):k(e,t)}function k(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=Math.abs(e);return(e>0?"-":"+")+y(Math.trunc(r/60),2)+t+y(r%60,2)}let S=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},_=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},j={p:_,P:(e,t)=>{let r,n=e.match(/(P+)(p+)?/)||[],a=n[1],i=n[2];if(!i)return S(e,t);switch(a){case"P":r=t.dateTime({width:"short"});break;case"PP":r=t.dateTime({width:"medium"});break;case"PPP":r=t.dateTime({width:"long"});break;default:r=t.dateTime({width:"full"})}return r.replace("{{date}}",S(a,t)).replace("{{time}}",_(i,t))}},E=/^D+$/,C=/^Y+$/,P=["D","DD","YY","YYYY"],N=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,A=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,T=/^'([^]*?)'?$/,O=/''/g,D=/[a-zA-Z]/;function M(e,t,r){var n,a,l,u,c,d,f,p,m,g,y,v,b,x,$,k,S,_;let M=(0,o.q)(),R=null!=(g=null!=(m=null==r?void 0:r.locale)?m:M.locale)?g:i.enUS,L=null!=(x=null!=(b=null!=(v=null!=(y=null==r?void 0:r.firstWeekContainsDate)?y:null==r||null==(a=r.locale)||null==(n=a.options)?void 0:n.firstWeekContainsDate)?v:M.firstWeekContainsDate)?b:null==(u=M.locale)||null==(l=u.options)?void 0:l.firstWeekContainsDate)?x:1,I=null!=(_=null!=(S=null!=(k=null!=($=null==r?void 0:r.weekStartsOn)?$:null==r||null==(d=r.locale)||null==(c=d.options)?void 0:c.weekStartsOn)?k:M.weekStartsOn)?S:null==(p=M.locale)||null==(f=p.options)?void 0:f.weekStartsOn)?_:0,F=(0,s.a)(e,null==r?void 0:r.in);if(!(F instanceof Date||"object"==typeof F&&"[object Date]"===Object.prototype.toString.call(F))&&"number"!=typeof F||isNaN(+(0,s.a)(F)))throw RangeError("Invalid time value");let B=t.match(A).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,j[t])(e,R.formatLong):e}).join("").match(N).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(T);return t?t[1].replace(O,"'"):e}(e)};if(w[t])return{isToken:!0,value:e};if(t.match(D))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});R.localize.preprocessor&&(B=R.localize.preprocessor(F,B));let z={firstWeekContainsDate:L,weekStartsOn:I,locale:R};return B.map(n=>{if(!n.isToken)return n.value;let a=n.value;return(!(null==r?void 0:r.useAdditionalWeekYearTokens)&&C.test(a)||!(null==r?void 0:r.useAdditionalDayOfYearTokens)&&E.test(a))&&function(e,t,r){let n=function(e,t,r){let n="Y"===e[0]?"years":"days of the month";return"Use `".concat(e.toLowerCase(),"` instead of `").concat(e,"` (in `").concat(t,"`) for formatting ").concat(n," to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md")}(e,t,r);if(console.warn(n),P.includes(e))throw RangeError(n)}(a,t,String(e)),(0,w[a[0]])(F,a,R.localize,z)}).join("")}r(55032);var R=r(45311),L=({elements:e,translationString:t})=>{let r=t.split(/(<[^>]+>.*?<\/[^>]+>)/g);return h.createElement("span",null,r.map((t,r)=>{if(e&&t.startsWith("<")&&t.endsWith(">")){let n=t[1],a=e[n];if(a){let e=RegExp(`<${n}>(.*?)</${n}>`,"g"),i=t.replace(e,(e,t)=>t);return h.createElement(a,{key:r},h.createElement(L,{translationString:i}))}}return t}))};function I(e){if(e)try{e.abort()}catch{}}function F(e){let t=new AbortController;if(e.current)try{e.current.abort()}catch{}return e.current=t,t}var B={delete:(e,t={headers:{}})=>{let r=t&&t.headers?{...t.headers}:{};return fetch(e,{...t,credentials:"include",headers:{...r},method:"delete"})},get:(e,t={headers:{}})=>{let r="";return t.params&&(r=n.A(t.params,{addQueryPrefix:!0})),fetch(`${e}${r}`,{credentials:"include",...t})},patch:(e,t={headers:{}})=>{let r=t&&t.headers?{...t.headers}:{};return fetch(e,{...t,credentials:"include",headers:{...r},method:"PATCH"})},post:(e,t={headers:{}})=>{let r=t&&t.headers?{...t.headers}:{};return fetch(`${e}`,{...t,credentials:"include",headers:{...r},method:"post"})},put:(e,t={headers:{}})=>{let r=t&&t.headers?{...t.headers}:{};return fetch(e,{...t,credentials:"include",headers:{...r},method:"put"})}},z={},V={};function U(e,t){try{let r=(z[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";return r in V?V[r]:H(r,r.split(":"))}catch{if(e in V)return V[e];let t=e?.match(q);return t?H(e,t.slice(1)):NaN}}var q=/([+-]\d\d):?(\d\d)?/;function H(e,t){let r=+t[0],n=+(t[1]||0);return V[e]=r>0?60*r+n:60*r-n}var W=class e extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(U(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),J(this,NaN),K(this)):this.setTime(Date.now())}static tz(t,...r){return r.length?new e(...r,t):new e(Date.now(),t)}withTimeZone(t){return new e(+this,t)}getTimezoneOffset(){return-U(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),K(this),+this}[Symbol.for("constructDateFrom")](t){return new e(+new Date(t),this.timeZone)}},G=/^(get|set)(?!UTC)/;function K(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function J(e){let t=U(e.timeZone,e),r=new Date(+e);r.setUTCHours(r.getUTCHours()-1);let n=-new Date(+e).getTimezoneOffset(),a=n- -new Date(+r).getTimezoneOffset(),i=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();a&&i&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+a);let o=n-t;o&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+o);let s=U(e.timeZone,e),l=-new Date(+e).getTimezoneOffset()-s-o;if(s!==t&&l){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);let t=s-U(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!G.test(e))return;let t=e.replace(G,"$1UTC");W.prototype[t]&&(e.startsWith("get")?W.prototype[e]=function(){return this.internal[t]()}:(W.prototype[e]=function(){var e;return Date.prototype[t].apply(this.internal,arguments),e=this,Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),J(e),+this},W.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),K(this),+this}))});var Y=({date:e,i18n:t,pattern:r,timezone:n})=>{let i=new W(new Date(e));if(n){let e=W.tz(n),o=function(e,t){var r,n;let i="function"==typeof(r=t)&&(null==(n=r.prototype)?void 0:n.constructor)===r?new t(0):(0,a.w)(t,0);return i.setFullYear(e.getFullYear(),e.getMonth(),e.getDate()),i.setHours(e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()),i}(i.withTimeZone(n),e);return t.dateFNS?M(o,r,{locale:t.dateFNS}):`${t.t("general:loading")}...`}return t.dateFNS?M(i,r,{locale:t.dateFNS}):`${t.t("general:loading")}...`},Q=(e=>(e.collection="collections",e.global="globals",e))(Q||{}),X=({adminRoute:e,router:t})=>{let r=(0,R.Q)({adminRoute:e,path:"/"});t.push(r)},Z=({adminRoute:e,collectionSlug:t,router:r})=>{let n=(0,R.Q)({adminRoute:e,path:t?`/collections/${t}`:"/"});r.push(n)},ee=(e,t,r,n,a,i,o,s,l,u)=>{if(l)try{i(e,t??r,n),a||(s.current.hasShownLockedModal=!0),s.current={hasShownLockedModal:s.current?.hasShownLockedModal,isLocked:!0,user:n},o(n),a&&u&&u(!1)}catch(e){console.error("Error during document takeover:",e)}}},73984:(e,t,r)=>{"use strict";r.d(t,{Wrapper:()=>o});var n=r(47092),a=r(67252),i=r(83548);r(89688);let o=e=>{let t,r,o=(0,n.c)(9),{baseClass:s,children:l,className:u}=e,{hydrated:c,navOpen:d,shouldAnimate:f}=(0,i.useNav)(),p=d&&"".concat(s,"--nav-open"),m=f&&"".concat(s,"--nav-animate"),g=c&&"".concat(s,"--nav-hydrated");o[0]!==s||o[1]!==u||o[2]!==p||o[3]!==m||o[4]!==g?(t=[s,u,p,m,g].filter(Boolean),o[0]=s,o[1]=u,o[2]=p,o[3]=m,o[4]=g,o[5]=t):t=o[5];let y=t.join(" ");return o[6]!==l||o[7]!==y?(r=(0,a.jsx)("div",{className:y,children:l}),o[6]=l,o[7]=y,o[8]=r):r=o[8],r}},74129:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=e=>{let t=e.split(" ");return RegExp(t.reduce((e,r,n)=>{let a=r.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&");return`${e}(?=.*(?:(?:[^\\p{L}\\p{N}])|^).*${a}.*(?=[^\\p{L}\\p{N}]|$))${n+1===t.length?".+":""}`},""),"i")}},75075:(e,t,r)=>{"use strict";r.d(t,{Iterable:()=>m});var n=r(47092),a=r(67252),i=r(55032),o=r(71464),s=r(56156),l=r(93653);r(89688);var u=r(20985),c=r(4624),d=r(73063),f=r(60122);let p="iterable-diff",m=e=>{let t,r,m=(0,n.c)(15),{baseVersionField:g,comparisonValue:y,field:v,locale:b,parentIsLocalized:w,versionValue:x}=e,{i18n:$}=(0,o.d)(),{selectedLocales:k}=(0,u.I)(),{config:S}=(0,s.b)(),_=Math.max(Array.isArray(x)?x.length:0,Array.isArray(y)?y.length:0);if(!(0,l.eE)(v)&&!(0,l.MT)(v))throw Error("Expected field to be an array or blocks type but got: ".concat(v.type));if(m[0]!==v||m[1]!==$||m[2]!==b?(t="label"in v&&v.label&&"function"!=typeof v.label&&(0,a.jsxs)("span",{children:[b&&(0,a.jsx)("span",{className:"".concat(p,"__locale-label"),children:b}),(0,i.sC)(v.label,$)]}),m[0]=v,m[1]=$,m[2]=b,m[3]=t):t=m[3],m[4]!==g||m[5]!==y||m[6]!==S||m[7]!==v||m[8]!==$||m[9]!==_||m[10]!==w||m[11]!==k||m[12]!==t||m[13]!==x){var j;r=(0,a.jsx)("div",{className:p,children:(0,a.jsxs)(c.$,{comparison:y,field:v,isIterable:!0,label:t,locales:k,parentIsLocalized:w,version:x,children:[_>0&&(0,a.jsx)("div",{className:"".concat(p,"__rows"),children:Array.from(Array(_).keys()).map((e,t)=>{let r=(null==x?void 0:x[t])||{},n=(null==y?void 0:y[t])||{},{fields:i,versionFields:o}=(0,f.G)({baseVersionField:g,comparisonRow:n,config:S,field:v,row:t,versionRow:r}),s=String(t+1).padStart(2,"0"),u=(0,l.eE)(v)?"Item ".concat(s):"Block ".concat(s);return(0,a.jsx)("div",{className:"".concat(p,"__row"),children:(0,a.jsx)(c.$,{comparison:n,fields:i,label:u,locales:k,parentIsLocalized:w||v.localized,version:r,children:(0,a.jsx)(d.RenderVersionFieldsToDiff,{versionFields:o})})},t)})}),0===_&&(0,a.jsx)("div",{className:"".concat(p,"__no-rows"),children:$.t("version:noRowsFound",{label:"labels"in v&&(null==(j=v.labels)?void 0:j.plural)?(0,i.sC)(v.labels.plural,$):$.t("general:rows")})})]})}),m[4]=g,m[5]=y,m[6]=S,m[7]=v,m[8]=$,m[9]=_,m[10]=w,m[11]=k,m[12]=t,m[13]=x,m[14]=r}else r=m[14];return r}},77908:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;let n=r(95439),a=r(55150),i=r(12208),o={message:"boolean schema is false"};function s(e,t){let{gen:r,data:a}=e;(0,n.reportError)({gen:r,keyword:"false schema",data:a,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e},o,void 0,t)}t.topBoolOrEmptySchema=function(e){let{gen:t,schema:r,validateName:n}=e;!1===r?s(e,!1):"object"==typeof r&&!0===r.$async?t.return(i.default.data):(t.assign((0,a._)`${n}.errors`,null),t.return(!0))},t.boolOrEmptySchema=function(e,t){let{gen:r,schema:n}=e;!1===n?(r.var(t,!1),s(e)):r.var(t,!0)}},78063:(e,t,r)=>{"use strict";function n({field:e,index:t,parentIndexPath:r,parentPath:n,parentSchemaPath:a}){if("name"in e)return{indexPath:`${r?r+"-":""}${t}`,path:`${n?n+".":""}${e.name}`,schemaPath:`${a?a+".":""}${e.name}`};let i=`_index-${r?r+"-":""}${t}`;return{indexPath:`${r?r+"-":""}${t}`,path:`${n?n+".":""}${i}`,schemaPath:`${a?a+".":""}${i}`}}r.d(t,{Z:()=>n})},80222:(e,t,r)=>{"use strict";r.r(t),r.d(t,{EditView:()=>i});var n=r(67252),a=r(83548);r(89688);let i=e=>(0,n.jsx)(a.DefaultEditView,{...e})},80718:(e,t,r)=>{"use strict";r.d(t,{VersionsPill:()=>o});var n=r(47092),a=r(67252),i=r(83548);r(89688);let o=()=>{let e,t=(0,n.c)(2),{versionCount:r}=(0,i.useDocumentInfo)();return r?(t[0]!==r?(e=(0,a.jsx)("span",{className:"pill-version-count",children:r}),t[0]=r,t[1]=e):e=t[1],e):null}},80793:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;var n=r(62597);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return n.KeywordCxt}});var a=r(55150);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return a._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return a.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return a.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return a.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return a.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return a.CodeGen}});let i=r(25421),o=r(49380),s=r(29311),l=r(4424),u=r(55150),c=r(59228),d=r(37699),f=r(26520),p=r(63034),m=r(33151),g=(e,t)=>new RegExp(e,t);g.code="new RegExp";let y=["removeAdditional","useDefaults","coerceTypes"],v=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),b={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},w={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};class x{constructor(e={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,e=this.opts={...e,...function(e){var t,r,n,a,i,o,s,l,u,c,d,f,p,y,v,b,w,x,$,k,S,_,j,E,C;let P=e.strict,N=null==(t=e.code)?void 0:t.optimize,A=!0===N||void 0===N?1:N||0,T=null!=(n=null==(r=e.code)?void 0:r.regExp)?n:g,O=null!=(a=e.uriResolver)?a:m.default;return{strictSchema:null==(o=null!=(i=e.strictSchema)?i:P)||o,strictNumbers:null==(l=null!=(s=e.strictNumbers)?s:P)||l,strictTypes:null!=(c=null!=(u=e.strictTypes)?u:P)?c:"log",strictTuples:null!=(f=null!=(d=e.strictTuples)?d:P)?f:"log",strictRequired:null!=(y=null!=(p=e.strictRequired)?p:P)&&y,code:e.code?{...e.code,optimize:A,regExp:T}:{optimize:A,regExp:T},loopRequired:null!=(v=e.loopRequired)?v:200,loopEnum:null!=(b=e.loopEnum)?b:200,meta:null==(w=e.meta)||w,messages:null==(x=e.messages)||x,inlineRefs:null==($=e.inlineRefs)||$,schemaId:null!=(k=e.schemaId)?k:"$id",addUsedSchema:null==(S=e.addUsedSchema)||S,validateSchema:null==(_=e.validateSchema)||_,validateFormats:null==(j=e.validateFormats)||j,unicodeRegExp:null==(E=e.unicodeRegExp)||E,int32range:null==(C=e.int32range)||C,uriResolver:O}}(e)};let{es5:t,lines:r}=this.opts.code;this.scope=new u.ValueScope({scope:{},prefixes:v,es5:t,lines:r}),this.logger=function(e){if(!1===e)return C;if(void 0===e)return console;if(e.log&&e.warn&&e.error)return e;throw Error("logger must implement log, warn and error methods")}(e.logger);let n=e.validateFormats;e.validateFormats=!1,this.RULES=(0,s.getRules)(),$.call(this,b,e,"NOT SUPPORTED"),$.call(this,w,e,"DEPRECATED","warn"),this._metaOpts=E.call(this),e.formats&&_.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),e.keywords&&j.call(this,e.keywords),"object"==typeof e.meta&&this.addMetaSchema(e.meta),S.call(this),e.validateFormats=n}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){let{$data:e,meta:t,schemaId:r}=this.opts,n=p;"id"===r&&((n={...p}).id=n.$id,delete n.$id),t&&e&&this.addMetaSchema(n,n[r],!1)}defaultMeta(){let{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta="object"==typeof e?e[t]||e:void 0}validate(e,t){let r;if("string"==typeof e){if(!(r=this.getSchema(e)))throw Error(`no schema with key or ref "${e}"`)}else r=this.compile(e);let n=r(t);return"$async"in r||(this.errors=r.errors),n}compile(e,t){let r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if("function"!=typeof this.opts.loadSchema)throw Error("options.loadSchema should be a function");let{loadSchema:r}=this.opts;return n.call(this,e,t);async function n(e,t){await a.call(this,e.$schema);let r=this._addSchema(e,t);return r.validate||i.call(this,r)}async function a(e){e&&!this.getSchema(e)&&await n.call(this,{$ref:e},!0)}async function i(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof o.default))throw t;return s.call(this,t),await l.call(this,t.missingSchema),i.call(this,e)}}function s({missingSchema:e,missingRef:t}){if(this.refs[e])throw Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}async function l(e){let r=await u.call(this,e);this.refs[e]||await a.call(this,r.$schema),this.refs[e]||this.addSchema(r,e,t)}async function u(e){let t=this._loading[e];if(t)return t;try{return await (this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,n=this.opts.validateSchema){let a;if(Array.isArray(e)){for(let t of e)this.addSchema(t,void 0,r,n);return this}if("object"==typeof e){let{schemaId:t}=this.opts;if(void 0!==(a=e[t])&&"string"!=typeof a)throw Error(`schema ${t} must be string`)}return t=(0,c.normalizeId)(t||a),this._checkUnique(t),this.schemas[t]=this._addSchema(e,r,t,n,!0),this}addMetaSchema(e,t,r=this.opts.validateSchema){return this.addSchema(e,t,!0,r),this}validateSchema(e,t){let r;if("boolean"==typeof e)return!0;if(void 0!==(r=e.$schema)&&"string"!=typeof r)throw Error("$schema must be a string");if(!(r=r||this.opts.defaultMeta||this.defaultMeta()))return this.logger.warn("meta-schema not available"),this.errors=null,!0;let n=this.validate(r,e);if(!n&&t){let e="schema is invalid: "+this.errorsText();if("log"===this.opts.validateSchema)this.logger.error(e);else throw Error(e)}return n}getSchema(e){let t;for(;"string"==typeof(t=k.call(this,e));)e=t;if(void 0===t){let{schemaId:r}=this.opts,n=new l.SchemaEnv({schema:{},schemaId:r});if(!(t=l.resolveSchema.call(this,n,e)))return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp)return this._removeAllSchemas(this.schemas,e),this._removeAllSchemas(this.refs,e),this;switch(typeof e){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{let t=k.call(this,e);return"object"==typeof t&&this._cache.delete(t.schema),delete this.schemas[e],delete this.refs[e],this}case"object":{this._cache.delete(e);let t=e[this.opts.schemaId];return t&&(t=(0,c.normalizeId)(t),delete this.schemas[t],delete this.refs[t]),this}default:throw Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(let t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if("string"==typeof e)r=e,"object"==typeof t&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),t.keyword=r);else if("object"==typeof e&&void 0===t){if(Array.isArray(r=(t=e).keyword)&&!r.length)throw Error("addKeywords: keyword must be string or non-empty array")}else throw Error("invalid addKeywords parameters");if(N.call(this,r,t),!t)return(0,f.eachItem)(r,e=>A.call(this,e)),this;O.call(this,t);let n={...t,type:(0,d.getJSONTypes)(t.type),schemaType:(0,d.getJSONTypes)(t.schemaType)};return(0,f.eachItem)(r,0===n.type.length?e=>A.call(this,e,n):e=>n.type.forEach(t=>A.call(this,e,n,t))),this}getKeyword(e){let t=this.RULES.all[e];return"object"==typeof t?t.definition:!!t}removeKeyword(e){let{RULES:t}=this;for(let r of(delete t.keywords[e],delete t.all[e],t.rules)){let t=r.rules.findIndex(t=>t.keyword===e);t>=0&&r.rules.splice(t,1)}return this}addFormat(e,t){return"string"==typeof t&&(t=new RegExp(t)),this.formats[e]=t,this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){return e&&0!==e.length?e.map(e=>`${r}${e.instancePath} ${e.message}`).reduce((e,r)=>e+t+r):"No errors"}$dataMetaSchema(e,t){let r=this.RULES.all;for(let n of(e=JSON.parse(JSON.stringify(e)),t)){let t=n.split("/").slice(1),a=e;for(let e of t)a=a[e];for(let e in r){let t=r[e];if("object"!=typeof t)continue;let{$data:n}=t.definition,i=a[e];n&&i&&(a[e]=M(i))}}return e}_removeAllSchemas(e,t){for(let r in e){let n=e[r];(!t||t.test(r))&&("string"==typeof n?delete e[r]:n&&!n.meta&&(this._cache.delete(n.schema),delete e[r]))}}_addSchema(e,t,r,n=this.opts.validateSchema,a=this.opts.addUsedSchema){let i,{schemaId:o}=this.opts;if("object"==typeof e)i=e[o];else if(this.opts.jtd)throw Error("schema must be object");else if("boolean"!=typeof e)throw Error("schema must be object or boolean");let s=this._cache.get(e);if(void 0!==s)return s;r=(0,c.normalizeId)(i||r);let u=c.getSchemaRefs.call(this,e,r);return s=new l.SchemaEnv({schema:e,schemaId:o,meta:t,baseId:r,localRefs:u}),this._cache.set(s.schema,s),a&&!r.startsWith("#")&&(r&&this._checkUnique(r),this.refs[r]=s),n&&this.validateSchema(e,!0),s}_checkUnique(e){if(this.schemas[e]||this.refs[e])throw Error(`schema with key or id "${e}" already exists`)}_compileSchemaEnv(e){if(e.meta?this._compileMetaSchema(e):l.compileSchema.call(this,e),!e.validate)throw Error("ajv implementation error");return e.validate}_compileMetaSchema(e){let t=this.opts;this.opts=this._metaOpts;try{l.compileSchema.call(this,e)}finally{this.opts=t}}}function $(e,t,r,n="error"){for(let a in e)a in t&&this.logger[n](`${r}: option ${a}. ${e[a]}`)}function k(e){return e=(0,c.normalizeId)(e),this.schemas[e]||this.refs[e]}function S(){let e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(let t in e)this.addSchema(e[t],t)}function _(){for(let e in this.opts.formats){let t=this.opts.formats[e];t&&this.addFormat(e,t)}}function j(e){if(Array.isArray(e))return void this.addVocabulary(e);for(let t in this.logger.warn("keywords option as map is deprecated, pass array"),e){let r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}function E(){let e={...this.opts};for(let t of y)delete e[t];return e}x.ValidationError=i.default,x.MissingRefError=o.default,t.default=x;let C={log(){},warn(){},error(){}},P=/^[a-z_$][a-z0-9_$:-]*$/i;function N(e,t){let{RULES:r}=this;if((0,f.eachItem)(e,e=>{if(r.keywords[e])throw Error(`Keyword ${e} is already defined`);if(!P.test(e))throw Error(`Keyword ${e} has invalid name`)}),t&&t.$data&&!("code"in t||"validate"in t))throw Error('$data keyword must have "code" or "validate" function')}function A(e,t,r){var n;let a=null==t?void 0:t.post;if(r&&a)throw Error('keyword with "post" flag cannot have "type"');let{RULES:i}=this,o=a?i.post:i.rules.find(({type:e})=>e===r);if(o||(o={type:r,rules:[]},i.rules.push(o)),i.keywords[e]=!0,!t)return;let s={keyword:e,definition:{...t,type:(0,d.getJSONTypes)(t.type),schemaType:(0,d.getJSONTypes)(t.schemaType)}};t.before?T.call(this,o,s,t.before):o.rules.push(s),i.all[e]=s,null==(n=t.implements)||n.forEach(e=>this.addKeyword(e))}function T(e,t,r){let n=e.rules.findIndex(e=>e.keyword===r);n>=0?e.rules.splice(n,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function O(e){let{metaSchema:t}=e;void 0!==t&&(e.$data&&this.opts.$data&&(t=M(t)),e.validateSchema=this.compile(t,!0))}let D={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function M(e){return{anyOf:[e,D]}}},81224:(e,t,r)=>{"use strict";r.d(t,{D4:()=>c,F7:()=>y,Pe:()=>m,gd:()=>p,h1:()=>u,kg:()=>g,lF:()=>d,oE:()=>f});var n=r(54201);let a=Object.prototype.hasOwnProperty,i=Array.isArray,o=function(){let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){let t=e.pop(),r=t.obj[t.prop];if(i(r)){let e=[];for(let t=0;t<r.length;++t)void 0!==r[t]&&e.push(r[t]);t.obj[t.prop]=e}}},l=function(e,t){let r=t&&t.plainObjects?Object.create(null):{};for(let t=0;t<e.length;++t)void 0!==e[t]&&(r[t]=e[t]);return r},u=function e(t,r,n){if(!r)return t;if("object"!=typeof r){if(i(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(n&&(n.plainObjects||n.allowPrototypes)||!a.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);let o=t;return(i(t)&&!i(r)&&(o=l(t,n)),i(t)&&i(r))?(r.forEach(function(r,i){if(a.call(t,i)){let a=t[i];a&&"object"==typeof a&&r&&"object"==typeof r?t[i]=e(a,r,n):t.push(r)}else t[i]=r}),t):Object.keys(r).reduce(function(t,i){let o=r[i];return a.call(t,i)?t[i]=e(t[i],o,n):t[i]=o,t},o)},c=function(e,t,r){let n=e.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(e){return n}},d=function(e,t,r,a,i){if(0===e.length)return e;let s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let l="";for(let e=0;e<s.length;e+=1024){let t=s.length>=1024?s.slice(e,e+1024):s,r=[];for(let e=0;e<t.length;++e){let a=t.charCodeAt(e);if(45===a||46===a||95===a||126===a||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||i===n.j1&&(40===a||41===a)){r[r.length]=t.charAt(e);continue}if(a<128){r[r.length]=o[a];continue}if(a<2048){r[r.length]=o[192|a>>6]+o[128|63&a];continue}if(a<55296||a>=57344){r[r.length]=o[224|a>>12]+o[128|a>>6&63]+o[128|63&a];continue}e+=1,a=65536+((1023&a)<<10|1023&t.charCodeAt(e)),r[r.length]=o[240|a>>18]+o[128|a>>12&63]+o[128|a>>6&63]+o[128|63&a]}l+=r.join("")}return l},f=function(e){let t=[{obj:{o:e},prop:"o"}],r=[];for(let e=0;e<t.length;++e){let n=t[e],a=n.obj[n.prop],i=Object.keys(a);for(let e=0;e<i.length;++e){let n=i[e],o=a[n];"object"==typeof o&&null!==o&&-1===r.indexOf(o)&&(t.push({obj:a,prop:n}),r.push(o))}}return s(t),e},p=function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},m=function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},g=function(e,t){return[].concat(e,t)},y=function(e,t){if(i(e)){let r=[];for(let n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)}},81469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return a}});let n=r(89688);function a(e,t){let r=(0,n.useRef)(null),a=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(r.current=i(e,n)),t&&(a.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81510:(e,t,r)=>{for(var n=r(46918),a=r(86709).hp,i=Math.floor(0xffffff*Math.random()),o=m.index=parseInt(0xffffff*Math.random(),10),s=(void 0===n||"number"!=typeof n.pid?Math.floor(1e5*Math.random()):n.pid)%65535,l=(()=>{try{return _Buffer}catch(e){try{return a}catch(e){return null}}})(),u=function(e){return!!(null!=e&&e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e))},c=[],d=0;d<256;d++)c[d]=(d<=15?"0":"")+d.toString(16);var f=RegExp("^[0-9a-fA-F]{24}$"),p=[];for(d=0;d<10;)p[48+d]=d++;for(;d<16;)p[55+d]=p[87+d]=d++;function m(e){if(!(this instanceof m))return new m(e);if(e&&(e instanceof m||"ObjectID"===e._bsontype))return e;if(this._bsontype="ObjectID",null==e||"number"==typeof e){this.id=this.generate(e);return}var t=m.isValid(e);if(t||null==e)if(t&&"string"==typeof e&&24===e.length)return m.createFromHexString(e);else if(null!=e&&12===e.length)this.id=e;else if(null!=e&&"function"==typeof e.toHexString)return e;else throw Error("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters");else throw Error("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters")}e.exports=m,m.default=m,m.createFromTime=function(e){var t,r;return new m((t=8,(8===(r=(r=e=parseInt(e,10)%0xffffffff).toString(16)).length?r:"00000000".substring(r.length,t)+r)+"0000000000000000"))},m.createFromHexString=function(e){if(void 0===e||null!=e&&24!==e.length)throw Error("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters");for(var t="",r=0;r<24;)t+=String.fromCharCode(p[e.charCodeAt(r++)]<<4|p[e.charCodeAt(r++)]);return new m(t)},m.isValid=function(e){return null!=e&&("number"==typeof e||("string"==typeof e?12===e.length||24===e.length&&f.test(e):e instanceof m||(u(e)?m.isValid(e.toString("hex")):"function"==typeof e.toHexString&&!!l&&(e.id instanceof l||"string"==typeof e.id)&&(12===e.id.length||24===e.id.length&&f.test(e.id)))))},m.prototype={constructor:m,toHexString:function(){if(!this.id||!this.id.length)throw Error("invalid ObjectId, ObjectId.id must be either a string or a Buffer, but is ["+JSON.stringify(this.id)+"]");if(24===this.id.length)return this.id;if(u(this.id))return this.id.toString("hex");for(var e="",t=0;t<this.id.length;t++)e+=c[this.id.charCodeAt(t)];return e},equals:function(e){if(e instanceof m)return this.toString()===e.toString();if("string"==typeof e&&m.isValid(e)&&12===e.length&&u(this.id))return e===this.id.toString("binary");if("string"==typeof e&&m.isValid(e)&&24===e.length)return e.toLowerCase()===this.toHexString();if("string"==typeof e&&m.isValid(e)&&12===e.length)return e===this.id;if(null!=e&&(e instanceof m||e.toHexString))return e.toHexString()===this.toHexString();else return!1},getTimestamp:function(){var e,t=new Date;return e=u(this.id)?this.id[3]|this.id[2]<<8|this.id[1]<<16|this.id[0]<<24:this.id.charCodeAt(3)|this.id.charCodeAt(2)<<8|this.id.charCodeAt(1)<<16|this.id.charCodeAt(0)<<24,t.setTime(1e3*Math.floor(e)),t},generate:function(e){"number"!=typeof e&&(e=~~(Date.now()/1e3)),e=parseInt(e,10)%0xffffffff;var t=o=(o+1)%0xffffff;return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e,i>>16&255,i>>8&255,255&i,s>>8&255,255&s,t>>16&255,t>>8&255,255&t)}};var g=Symbol&&Symbol.for&&Symbol.for("nodejs.util.inspect.custom")||"inspect";m.prototype[g]=function(){return"ObjectID("+this+")"},m.prototype.toJSON=m.prototype.toHexString,m.prototype.toString=m.prototype.toHexString},81998:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;let a=r(11659);class i extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`),this.value=e.value}}!function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"}(n||(t.UsedValueState=n={})),t.varKinds={const:new a.Name("const"),let:new a.Name("let"),var:new a.Name("var")};class o{constructor({prefixes:e,parent:t}={}){this._names={},this._prefixes=e,this._parent=t}toName(e){return e instanceof a.Name?e:this.name(e)}name(e){return new a.Name(this._newName(e))}_newName(e){let t=this._names[e]||this._nameGroup(e);return`${e}${t.index++}`}_nameGroup(e){var t,r;if((null==(r=null==(t=this._parent)?void 0:t._prefixes)?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e))throw Error(`CodeGen: prefix "${e}" is not allowed in this scope`);return this._names[e]={prefix:e,index:0}}}t.Scope=o;class s extends a.Name{constructor(e,t){super(t),this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e,this.scopePath=(0,a._)`.${new a.Name(t)}[${r}]`}}t.ValueScopeName=s;let l=(0,a._)`\n`;class u extends o{constructor(e){super(e),this._values={},this._scope=e.scope,this.opts={...e,_n:e.lines?l:a.nil}}get(){return this._scope}name(e){return new s(e,this._newName(e))}value(e,t){var r;if(void 0===t.ref)throw Error("CodeGen: ref must be passed in value");let n=this.toName(e),{prefix:a}=n,i=null!=(r=t.key)?r:t.ref,o=this._values[a];if(o){let e=o.get(i);if(e)return e}else o=this._values[a]=new Map;o.set(i,n);let s=this._scope[a]||(this._scope[a]=[]),l=s.length;return s[l]=t.ref,n.setValue(t,{property:a,itemIndex:l}),n}getValue(e,t){let r=this._values[e];if(r)return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,t=>{if(void 0===t.scopePath)throw Error(`CodeGen: name "${t}" has no value`);return(0,a._)`${e}${t.scopePath}`})}scopeCode(e=this._values,t,r){return this._reduceValues(e,e=>{if(void 0===e.value)throw Error(`CodeGen: name "${e}" has no value`);return e.value.code},t,r)}_reduceValues(e,r,o={},s){let l=a.nil;for(let u in e){let c=e[u];if(!c)continue;let d=o[u]=o[u]||new Map;c.forEach(e=>{if(d.has(e))return;d.set(e,n.Started);let o=r(e);if(o){let r=this.opts.es5?t.varKinds.var:t.varKinds.const;l=(0,a._)`${l}${r} ${e} = ${o};${this.opts._n}`}else if(o=null==s?void 0:s(e))l=(0,a._)`${l}${o}${this.opts._n}`;else throw new i(e);d.set(e,n.Completed)})}return l}}t.ValueScope=u},82127:e=>{"use strict";let t=/^[\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12}$/iu,r=/([\da-z][\d\-a-z]{0,31}):((?:[\w!$'()*+,\-.:;=@]|%[\da-f]{2})+)/iu;function n(e){return"boolean"==typeof e.secure?e.secure:"wss"===String(e.scheme).toLowerCase()}function a(e){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e}function i(e){let t="https"===String(e.scheme).toLowerCase();return(e.port===(t?443:80)||""===e.port)&&(e.port=void 0),e.path||(e.path="/"),e}let o={scheme:"http",domainHost:!0,parse:a,serialize:i},s={scheme:"https",domainHost:o.domainHost,parse:a,serialize:i},l={scheme:"ws",domainHost:!0,parse:function(e){return e.secure=n(e),e.resourceName=(e.path||"/")+(e.query?"?"+e.query:""),e.path=void 0,e.query=void 0,e},serialize:function(e){if((e.port===(n(e)?443:80)||""===e.port)&&(e.port=void 0),"boolean"==typeof e.secure&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){let[t,r]=e.resourceName.split("?");e.path=t&&"/"!==t?t:void 0,e.query=r,e.resourceName=void 0}return e.fragment=void 0,e}},u={scheme:"wss",domainHost:l.domainHost,parse:l.parse,serialize:l.serialize},c={http:o,https:s,ws:l,wss:u,urn:{scheme:"urn",parse:function(e,t){if(!e.path)return e.error="URN can not be parsed",e;let n=e.path.match(r);if(n){let r=t.scheme||e.scheme||"urn";e.nid=n[1].toLowerCase(),e.nss=n[2];let a=c[`${r}:${t.nid||e.nid}`];e.path=void 0,a&&(e=a.parse(e,t))}else e.error=e.error||"URN can not be parsed.";return e},serialize:function(e,t){let r=t.scheme||e.scheme||"urn",n=e.nid.toLowerCase(),a=c[`${r}:${t.nid||n}`];a&&(e=a.serialize(e,t));let i=e,o=e.nss;return i.path=`${n||t.nid}:${o}`,t.skipEscape=!0,i},skipNormalize:!0},"urn:uuid":{scheme:"urn:uuid",parse:function(e,r){return e.uuid=e.nss,e.nss=void 0,r.tolerant||e.uuid&&t.test(e.uuid)||(e.error=e.error||"UUID is not valid."),e},serialize:function(e){return e.nss=(e.uuid||"").toLowerCase(),e},skipNormalize:!0}};e.exports=c},84042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150);t.default={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>(0,n.str)`must match format "${e}"`,params:({schemaCode:e})=>(0,n._)`{format: ${e}}`},code(e,t){let{gen:r,data:a,$data:i,schema:o,schemaCode:s,it:l}=e,{opts:u,errSchemaPath:c,schemaEnv:d,self:f}=l;u.validateFormats&&(i?function(){let i=r.scopeValue("formats",{ref:f.formats,code:u.code.formats}),o=r.const("fDef",(0,n._)`${i}[${s}]`),l=r.let("fType"),c=r.let("format");r.if((0,n._)`typeof ${o} == "object" && !(${o} instanceof RegExp)`,()=>r.assign(l,(0,n._)`${o}.type || "string"`).assign(c,(0,n._)`${o}.validate`),()=>r.assign(l,(0,n._)`"string"`).assign(c,o)),e.fail$data((0,n.or)(!1===u.strictSchema?n.nil:(0,n._)`${s} && !${c}`,function(){let e=d.$async?(0,n._)`(${o}.async ? await ${c}(${a}) : ${c}(${a}))`:(0,n._)`${c}(${a})`,r=(0,n._)`(typeof ${c} == "function" ? ${e} : ${c}.test(${a}))`;return(0,n._)`${c} && ${c} !== true && ${l} === ${t} && !${r}`}()))}():function(){let i=f.formats[o];if(!i){if(!1===u.strictSchema)return f.logger.warn(s());throw Error(s());function s(){return`unknown format "${o}" ignored in schema at path "${c}"`}}if(!0===i)return;let[l,p,m]=function(e){let t=e instanceof RegExp?(0,n.regexpCode)(e):u.code.formats?(0,n._)`${u.code.formats}${(0,n.getProperty)(o)}`:void 0,a=r.scopeValue("formats",{key:o,ref:e,code:t});return"object"!=typeof e||e instanceof RegExp?["string",e,a]:[e.type||"string",e.validate,(0,n._)`${a}.validate`]}(i);l===t&&e.pass(function(){if("object"==typeof i&&!(i instanceof RegExp)&&i.async){if(!d.$async)throw Error("async format in sync schema");return(0,n._)`await ${m}(${a})`}return"function"==typeof p?(0,n._)`${m}(${a})`:(0,n._)`${m}.test(${a})`}())}())}}},85704:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,a]of Object.entries(e))if(Array.isArray(a))for(let e of a)t.append(r,n(e));else t.set(r,n(a));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return a}})},85864:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return s},urlObjectKeys:function(){return o}});let n=r(58272)._(r(85704)),a=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",o=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||a.test(i))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+i+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return i(e)}},86324:function(e){e.exports=function(){var e=[],t=[],r={},n={},a={};function i(e){return"string"==typeof e?RegExp("^"+e+"$","i"):e}function o(e,t){return e===t?t:e===e.toLowerCase()?t.toLowerCase():e===e.toUpperCase()?t.toUpperCase():e[0]===e[0].toUpperCase()?t.charAt(0).toUpperCase()+t.substr(1).toLowerCase():t.toLowerCase()}function s(e,t,n){if(!e.length||r.hasOwnProperty(e))return t;for(var a=n.length;a--;){var i=n[a];if(i[0].test(t))return function(e,t){return e.replace(t[0],function(r,n){var a,i,s=(a=t[1],i=arguments,a.replace(/\$(\d{1,2})/g,function(e,t){return i[t]||""}));return""===r?o(e[n-1],s):o(r,s)})}(t,i)}return t}function l(e,t,r){return function(n){var a=n.toLowerCase();return t.hasOwnProperty(a)?o(n,a):e.hasOwnProperty(a)?o(n,e[a]):s(a,n,r)}}function u(e,t,r,n){return function(n){var a=n.toLowerCase();return!!t.hasOwnProperty(a)||!e.hasOwnProperty(a)&&s(a,a,r)===a}}function c(e,t,r){var n=1===t?c.singular(e):c.plural(e);return(r?t+" ":"")+n}return c.plural=l(a,n,e),c.isPlural=u(a,n,e),c.singular=l(n,a,t),c.isSingular=u(n,a,t),c.addPluralRule=function(t,r){e.push([i(t),r])},c.addSingularRule=function(e,r){t.push([i(e),r])},c.addUncountableRule=function(e){if("string"==typeof e){r[e.toLowerCase()]=!0;return}c.addPluralRule(e,"$0"),c.addSingularRule(e,"$0")},c.addIrregularRule=function(e,t){t=t.toLowerCase(),a[e=e.toLowerCase()]=t,n[t]=e},[["I","we"],["me","us"],["he","they"],["she","they"],["them","them"],["myself","ourselves"],["yourself","yourselves"],["itself","themselves"],["herself","themselves"],["himself","themselves"],["themself","themselves"],["is","are"],["was","were"],["has","have"],["this","these"],["that","those"],["echo","echoes"],["dingo","dingoes"],["volcano","volcanoes"],["tornado","tornadoes"],["torpedo","torpedoes"],["genus","genera"],["viscus","viscera"],["stigma","stigmata"],["stoma","stomata"],["dogma","dogmata"],["lemma","lemmata"],["schema","schemata"],["anathema","anathemata"],["ox","oxen"],["axe","axes"],["die","dice"],["yes","yeses"],["foot","feet"],["eave","eaves"],["goose","geese"],["tooth","teeth"],["quiz","quizzes"],["human","humans"],["proof","proofs"],["carve","carves"],["valve","valves"],["looey","looies"],["thief","thieves"],["groove","grooves"],["pickaxe","pickaxes"],["passerby","passersby"]].forEach(function(e){return c.addIrregularRule(e[0],e[1])}),[[/s?$/i,"s"],[/[^\u0000-\u007F]$/i,"$0"],[/([^aeiou]ese)$/i,"$1"],[/(ax|test)is$/i,"$1es"],[/(alias|[^aou]us|t[lm]as|gas|ris)$/i,"$1es"],[/(e[mn]u)s?$/i,"$1s"],[/([^l]ias|[aeiou]las|[ejzr]as|[iu]am)$/i,"$1"],[/(alumn|syllab|vir|radi|nucle|fung|cact|stimul|termin|bacill|foc|uter|loc|strat)(?:us|i)$/i,"$1i"],[/(alumn|alg|vertebr)(?:a|ae)$/i,"$1ae"],[/(seraph|cherub)(?:im)?$/i,"$1im"],[/(her|at|gr)o$/i,"$1oes"],[/(agend|addend|millenni|dat|extrem|bacteri|desiderat|strat|candelabr|errat|ov|symposi|curricul|automat|quor)(?:a|um)$/i,"$1a"],[/(apheli|hyperbat|periheli|asyndet|noumen|phenomen|criteri|organ|prolegomen|hedr|automat)(?:a|on)$/i,"$1a"],[/sis$/i,"ses"],[/(?:(kni|wi|li)fe|(ar|l|ea|eo|oa|hoo)f)$/i,"$1$2ves"],[/([^aeiouy]|qu)y$/i,"$1ies"],[/([^ch][ieo][ln])ey$/i,"$1ies"],[/(x|ch|ss|sh|zz)$/i,"$1es"],[/(matr|cod|mur|sil|vert|ind|append)(?:ix|ex)$/i,"$1ices"],[/\b((?:tit)?m|l)(?:ice|ouse)$/i,"$1ice"],[/(pe)(?:rson|ople)$/i,"$1ople"],[/(child)(?:ren)?$/i,"$1ren"],[/eaux$/i,"$0"],[/m[ae]n$/i,"men"],["thou","you"]].forEach(function(e){return c.addPluralRule(e[0],e[1])}),[[/s$/i,""],[/(ss)$/i,"$1"],[/(wi|kni|(?:after|half|high|low|mid|non|night|[^\w]|^)li)ves$/i,"$1fe"],[/(ar|(?:wo|[ae])l|[eo][ao])ves$/i,"$1f"],[/ies$/i,"y"],[/\b([pl]|zomb|(?:neck|cross)?t|coll|faer|food|gen|goon|group|lass|talk|goal|cut)ies$/i,"$1ie"],[/\b(mon|smil)ies$/i,"$1ey"],[/\b((?:tit)?m|l)ice$/i,"$1ouse"],[/(seraph|cherub)im$/i,"$1"],[/(x|ch|ss|sh|zz|tto|go|cho|alias|[^aou]us|t[lm]as|gas|(?:her|at|gr)o|[aeiou]ris)(?:es)?$/i,"$1"],[/(analy|diagno|parenthe|progno|synop|the|empha|cri|ne)(?:sis|ses)$/i,"$1sis"],[/(movie|twelve|abuse|e[mn]u)s$/i,"$1"],[/(test)(?:is|es)$/i,"$1is"],[/(alumn|syllab|vir|radi|nucle|fung|cact|stimul|termin|bacill|foc|uter|loc|strat)(?:us|i)$/i,"$1us"],[/(agend|addend|millenni|dat|extrem|bacteri|desiderat|strat|candelabr|errat|ov|symposi|curricul|quor)a$/i,"$1um"],[/(apheli|hyperbat|periheli|asyndet|noumen|phenomen|criteri|organ|prolegomen|hedr|automat)a$/i,"$1on"],[/(alumn|alg|vertebr)ae$/i,"$1a"],[/(cod|mur|sil|vert|ind)ices$/i,"$1ex"],[/(matr|append)ices$/i,"$1ix"],[/(pe)(rson|ople)$/i,"$1rson"],[/(child)ren$/i,"$1"],[/(eau)x?$/i,"$1"],[/men$/i,"man"]].forEach(function(e){return c.addSingularRule(e[0],e[1])}),["adulthood","advice","agenda","aid","aircraft","alcohol","ammo","analytics","anime","athletics","audio","bison","blood","bream","buffalo","butter","carp","cash","chassis","chess","clothing","cod","commerce","cooperation","corps","debris","diabetes","digestion","elk","energy","equipment","excretion","expertise","firmware","flounder","fun","gallows","garbage","graffiti","hardware","headquarters","health","herpes","highjinks","homework","housework","information","jeans","justice","kudos","labour","literature","machinery","mackerel","mail","media","mews","moose","music","mud","manga","news","only","personnel","pike","plankton","pliers","police","pollution","premises","rain","research","rice","salmon","scissors","series","sewage","shambles","shrimp","software","species","staff","swine","tennis","traffic","transportation","trout","tuna","wealth","welfare","whiting","wildebeest","wildlife","you",/pok[eé]mon$/i,/[^aeiou]ese$/i,/deer$/i,/fish$/i,/measles$/i,/o[iu]s$/i,/pox$/i,/sheep$/i].forEach(c.addUncountableRule),c}()},86709:(e,t,r)=>{"use strict";var n=r(27571),a=r(58934),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,s.prototype),t}function s(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e){var n=e,a=t;if(("string"!=typeof a||""===a)&&(a="utf8"),!s.isEncoding(a))throw TypeError("Unknown encoding: "+a);var i=0|p(n,a),l=o(i),u=l.write(n,a);return u!==i&&(l=l.slice(0,u)),l}if(ArrayBuffer.isView(e))return d(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(N(e,ArrayBuffer)||e&&N(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(N(e,SharedArrayBuffer)||e&&N(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),s.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var c=e.valueOf&&e.valueOf();if(null!=c&&c!==e)return s.from(c,t,r);var m=function(e){if(s.isBuffer(e)){var t=0|f(e.length),r=o(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?o(0):d(e):"Buffer"===e.type&&Array.isArray(e.data)?d(e.data):void 0}(e);if(m)return m;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return s.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return u(e),o(e<0?0:0|f(e))}function d(e){for(var t=e.length<0?0:0|f(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.hp=s,t.IS=50,s.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(e,t,r){return(u(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},s.allocUnsafe=function(e){return c(e)},s.allocUnsafeSlow=function(e){return c(e)};function f(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function p(e,t){if(s.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||N(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var a=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return j(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return C(e).length;default:if(a)return n?-1:j(e).length;t=(""+t).toLowerCase(),a=!0}}function m(e,t,r){var a,i,o,s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var a="",i=t;i<r;++i)a+=A[e[i]];return a}(this,t,r);case"utf8":case"utf-8":return b(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var a=t;a<r;++a)n+=String.fromCharCode(127&e[a]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var a=t;a<r;++a)n+=String.fromCharCode(e[a]);return n}(this,t,r);case"base64":return a=this,i=t,o=r,0===i&&o===a.length?n.fromByteArray(a):n.fromByteArray(a.slice(i,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),a="",i=0;i<n.length;i+=2)a+=String.fromCharCode(n[i]+256*n[i+1]);return a}(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,a){var i;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(i=r*=1)!=i&&(r=a?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(a)return -1;else r=e.length-1;else if(r<0)if(!a)return -1;else r=0;if("string"==typeof t&&(t=s.from(t,n)),s.isBuffer(t))return 0===t.length?-1:v(e,t,r,n,a);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(a)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return v(e,[t],r,n,a)}throw TypeError("val must be string, number or Buffer")}function v(e,t,r,n,a){var i,o=1,s=e.length,l=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,s/=2,l/=2,r/=2}function u(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(a){var c=-1;for(i=r;i<s;i++)if(u(e,i)===u(t,-1===c?0:i-c)){if(-1===c&&(c=i),i-c+1===l)return c*o}else -1!==c&&(i-=i-c),c=-1}else for(r+l>s&&(r=s-l),i=r;i>=0;i--){for(var d=!0,f=0;f<l;f++)if(u(e,i+f)!==u(t,f)){d=!1;break}if(d)return i}return -1}s.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==s.prototype},s.compare=function(e,t){if(N(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),N(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(e)||!s.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,a=0,i=Math.min(r,n);a<i;++a)if(e[a]!==t[a]){r=e[a],n=t[a];break}return r<n?-1:+(n<r)},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=s.allocUnsafe(t),a=0;for(r=0;r<e.length;++r){var i=e[r];if(N(i,Uint8Array)&&(i=s.from(i)),!s.isBuffer(i))throw TypeError('"list" argument must be an Array of Buffers');i.copy(n,a),a+=i.length}return n},s.byteLength=p,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},s.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?b(this,0,e):m.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(e){if(!s.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",r=t.IS;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},i&&(s.prototype[i]=s.prototype.inspect),s.prototype.compare=function(e,t,r,n,a){if(N(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===a&&(a=this.length),t<0||r>e.length||n<0||a>this.length)throw RangeError("out of range index");if(n>=a&&t>=r)return 0;if(n>=a)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,a>>>=0,this===e)return 0;for(var i=a-n,o=r-t,l=Math.min(i,o),u=this.slice(n,a),c=e.slice(t,r),d=0;d<l;++d)if(u[d]!==c[d]){i=u[d],o=c[d];break}return i<o?-1:+(o<i)},s.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},s.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},s.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)};function b(e,t,r){r=Math.min(e.length,r);for(var n=[],a=t;a<r;){var i,o,s,l,u=e[a],c=null,d=u>239?4:u>223?3:u>191?2:1;if(a+d<=r)switch(d){case 1:u<128&&(c=u);break;case 2:(192&(i=e[a+1]))==128&&(l=(31&u)<<6|63&i)>127&&(c=l);break;case 3:i=e[a+1],o=e[a+2],(192&i)==128&&(192&o)==128&&(l=(15&u)<<12|(63&i)<<6|63&o)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:i=e[a+1],o=e[a+2],s=e[a+3],(192&i)==128&&(192&o)==128&&(192&s)==128&&(l=(15&u)<<18|(63&i)<<12|(63&o)<<6|63&s)>65535&&l<1114112&&(c=l)}null===c?(c=65533,d=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),a+=d}var f=n,p=f.length;if(p<=4096)return String.fromCharCode.apply(String,f);for(var m="",g=0;g<p;)m+=String.fromCharCode.apply(String,f.slice(g,g+=4096));return m}function w(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function x(e,t,r,n,a,i){if(!s.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>a||t<i)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function $(e,t,r,n,a,i){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function k(e,t,r,n,i){return t*=1,r>>>=0,i||$(e,t,r,4,34028234663852886e22,-34028234663852886e22),a.write(e,t,r,n,23,4),r+4}function S(e,t,r,n,i){return t*=1,r>>>=0,i||$(e,t,r,8,17976931348623157e292,-17976931348623157e292),a.write(e,t,r,n,52,8),r+8}s.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var a,i,o,s,l,u,c,d,f=this.length-t;if((void 0===r||r>f)&&(r=f),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var a=e.length-r;n?(n=Number(n))>a&&(n=a):n=a;var i=t.length;n>i/2&&(n=i/2);for(var o=0;o<n;++o){var s,l=parseInt(t.substr(2*o,2),16);if((s=l)!=s)break;e[r+o]=l}return o}(this,e,t,r);case"utf8":case"utf-8":return a=t,i=r,P(j(e,this.length-a),this,a,i);case"ascii":return o=t,s=r,P(E(e),this,o,s);case"latin1":case"binary":return function(e,t,r,n){return P(E(t),e,r,n)}(this,e,t,r);case"base64":return l=t,u=r,P(C(e),this,l,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=t,d=r,P(function(e,t){for(var r,n,a=[],i=0;i<e.length&&!((t-=2)<0);++i)n=(r=e.charCodeAt(i))>>8,a.push(r%256),a.push(n);return a}(e,this.length-c),this,c,d);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},s.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,s.prototype),n},s.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],a=1,i=0;++i<t&&(a*=256);)n+=this[e+i]*a;return n},s.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e+--t],a=1;t>0&&(a*=256);)n+=this[e+--t]*a;return n},s.prototype.readUInt8=function(e,t){return e>>>=0,t||w(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},s.prototype.readUInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],a=1,i=0;++i<t&&(a*=256);)n+=this[e+i]*a;return n>=(a*=128)&&(n-=Math.pow(2,8*t)),n},s.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=t,a=1,i=this[e+--n];n>0&&(a*=256);)i+=this[e+--n]*a;return i>=(a*=128)&&(i-=Math.pow(2,8*t)),i},s.prototype.readInt8=function(e,t){return(e>>>=0,t||w(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},s.prototype.readInt16LE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt16BE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return e>>>=0,t||w(e,4,this.length),a.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return e>>>=0,t||w(e,4,this.length),a.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return e>>>=0,t||w(e,8,this.length),a.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return e>>>=0,t||w(e,8,this.length),a.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var a=Math.pow(2,8*r)-1;x(this,e,t,r,a,0)}var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},s.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var a=Math.pow(2,8*r)-1;x(this,e,t,r,a,0)}var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},s.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,1,255,0),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},s.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var a=Math.pow(2,8*r-1);x(this,e,t,r,a-1,-a)}var i=0,o=1,s=0;for(this[t]=255&e;++i<r&&(o*=256);)e<0&&0===s&&0!==this[t+i-1]&&(s=1),this[t+i]=(e/o|0)-s&255;return t+r},s.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var a=Math.pow(2,8*r-1);x(this,e,t,r,a-1,-a)}var i=r-1,o=1,s=0;for(this[t+i]=255&e;--i>=0&&(o*=256);)e<0&&0===s&&0!==this[t+i+1]&&(s=1),this[t+i]=(e/o|0)-s&255;return t+r},s.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},s.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeFloatLE=function(e,t,r){return k(this,e,t,!0,r)},s.prototype.writeFloatBE=function(e,t,r){return k(this,e,t,!1,r)},s.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},s.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},s.prototype.copy=function(e,t,r,n){if(!s.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var a=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var i=a-1;i>=0;--i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return a},s.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var a,i=e.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(e=i)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(a=t;a<r;++a)this[a]=e;else{var o=s.isBuffer(e)?e:s.from(e,n),l=o.length;if(0===l)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(a=0;a<r-t;++a)this[a+t]=o[a%l]}return this};var _=/[^+/0-9A-Za-z-_]/g;function j(e,t){t=t||1/0;for(var r,n=e.length,a=null,i=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!a){if(r>56319||o+1===n){(t-=3)>-1&&i.push(239,191,189);continue}a=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),a=r;continue}r=(a-55296<<10|r-56320)+65536}else a&&(t-=3)>-1&&i.push(239,191,189);if(a=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function E(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function C(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(_,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function P(e,t,r,n){for(var a=0;a<n&&!(a+r>=t.length)&&!(a>=e.length);++a)t[a+r]=e[a];return a}function N(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var A=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,a=0;a<16;++a)t[n+a]=e[r]+e[a];return t}()},87710:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:r(37742).validateUnion,error:{message:"must match a schema in anyOf"}}},88414:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0;let n=r(55150),a=r(26520),i=r(37742);t.error={message:({params:{property:e,depsCount:t,deps:r}})=>(0,n.str)`must have ${1===t?"property":"properties"} ${r} when property ${e} is present`,params:({params:{property:e,depsCount:t,deps:r,missingProperty:a}})=>(0,n._)`{property: ${e},
    missingProperty: ${a},
    depsCount: ${t},
    deps: ${r}}`};let o={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){let[t,r]=function({schema:e}){let t={},r={};for(let n in e)"__proto__"!==n&&((Array.isArray(e[n])?t:r)[n]=e[n]);return[t,r]}(e);s(e,t),l(e,r)}};function s(e,t=e.schema){let{gen:r,data:a,it:o}=e;if(0===Object.keys(t).length)return;let l=r.let("missing");for(let s in t){let u=t[s];if(0===u.length)continue;let c=(0,i.propertyInData)(r,a,s,o.opts.ownProperties);e.setParams({property:s,depsCount:u.length,deps:u.join(", ")}),o.allErrors?r.if(c,()=>{for(let t of u)(0,i.checkReportMissingProp)(e,t)}):(r.if((0,n._)`${c} && (${(0,i.checkMissingProp)(e,u,l)})`),(0,i.reportMissingProp)(e,l),r.else())}}function l(e,t=e.schema){let{gen:r,data:n,keyword:o,it:s}=e,u=r.name("valid");for(let l in t)(0,a.alwaysValidSchema)(s,t[l])||(r.if((0,i.propertyInData)(r,n,l,s.opts.ownProperties),()=>{let t=e.subschema({keyword:o,schemaProp:l},u);e.mergeValidEvaluated(t,u)},()=>r.var(u,!0)),e.ok(u))}t.validatePropertyDeps=s,t.validateSchemaDeps=l,t.default=o},88543:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n={src:"/_next/static/media/static-og-image.477255a8.png",height:480,width:640,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAD1BMVEX39/f///+urq7Hx8e6urr/rfEaAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAHUlEQVR4nGNghAIGEAYTjIwMTCwgEsRgRpOCKAYABUMAMfeXEyAAAAAASUVORK5CYII=",blurWidth:8,blurHeight:6}},89964:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(26520),i=r(37549);t.default={keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>(0,n._)`{allowedValues: ${e}}`},code(e){let t,r,{gen:o,data:s,$data:l,schema:u,schemaCode:c,it:d}=e;if(!l&&0===u.length)throw Error("enum must have non-empty array");let f=u.length>=d.opts.loopEnum,p=()=>null!=t?t:t=(0,a.useFunc)(o,i.default);if(f||l)r=o.let("valid"),e.block$data(r,function(){o.assign(r,!1),o.forOf("v",c,e=>o.if((0,n._)`${p()}(${s}, ${e})`,()=>o.assign(r,!0).break()))});else{if(!Array.isArray(u))throw Error("ajv implementation error");let e=o.const("vSchema",c);r=(0,n.or)(...u.map((t,r)=>(function(e,t){let r=u[t];return"object"==typeof r&&null!==r?(0,n._)`${p()}(${s}, ${e}[${t}])`:(0,n._)`${s} === ${r}`})(e,r)))}e.pass(r)}}},90360:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')},90393:(e,t,r)=>{"use strict";r.d(t,{DocumentTabLink:()=>l});var n=r(47092),a=r(67252),i=r(83548),o=r(98034),s=r(45311);r(89688);let l=e=>{let t,r,l,u,c=(0,n.c)(19),{adminRoute:d,ariaLabel:f,baseClass:p,children:m,href:g,isActive:y,newTab:v}=e,b=(0,o.usePathname)(),w=(0,o.useParams)(),x=(0,o.useSearchParams)().get("locale"),[$,k,S]=w.segments||[],_="collections"===$,j="/".concat(_?"collections":"globals","/").concat(k);c[0]!==d||c[1]!==j?(t=(0,s.Q)({adminRoute:d,path:j}),c[0]=d,c[1]=j,c[2]=t):t=c[2];let E=t;_&&S&&(E+="/".concat(S));let C="".concat(E).concat(g),P="".concat(C).concat(x?"?locale=".concat(x):"");c[3]!==E||c[4]!==C||c[5]!==y||c[6]!==b?(r=C===E&&b===E||C!==E&&b.startsWith(C)||y,c[3]=E,c[4]=C,c[5]=y,c[6]=b,c[7]=r):r=c[7];let N=r,A=N&&"".concat(p,"--active");c[8]!==p||c[9]!==A?(l=[p,A].filter(Boolean),c[8]=p,c[9]=A,c[10]=l):l=c[10];let T=l.join(" "),O=N&&C===b?"div":"link",D=N&&C===b?void 0:P;return c[11]!==f||c[12]!==m||c[13]!==N||c[14]!==v||c[15]!==T||c[16]!==O||c[17]!==D?(u=(0,a.jsx)(i.Button,{"aria-label":f,buttonStyle:"tab",className:T,disabled:N,el:O,margin:!1,newTab:v,size:"medium",to:D,children:m}),c[11]=f,c[12]=m,c[13]=N,c[14]=v,c[15]=T,c[16]=O,c[17]=D,c[18]=u):u=c[18],u}},90446:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(37742),a=r(55150),i=r(26520),o=r(26520);t.default={keyword:"patternProperties",type:"object",schemaType:"object",code(e){let{gen:t,schema:r,data:s,parentSchema:l,it:u}=e,{opts:c}=u,d=(0,n.allSchemaProperties)(r),f=d.filter(e=>(0,i.alwaysValidSchema)(u,r[e]));if(0===d.length||f.length===d.length&&(!u.opts.unevaluated||!0===u.props))return;let p=c.strictSchema&&!c.allowMatchingProperties&&l.properties,m=t.name("valid");!0===u.props||u.props instanceof a.Name||(u.props=(0,o.evaluatedPropsToName)(t,u.props));let{props:g}=u;for(let e of d)p&&function(e){for(let t in p)new RegExp(e).test(t)&&(0,i.checkStrictMode)(u,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}(e),u.allErrors?y(e):(t.var(m,!0),y(e),t.if(m));function y(r){t.forIn("key",s,i=>{t.if((0,a._)`${(0,n.usePattern)(e,r)}.test(${i})`,()=>{let n=f.includes(r);n||e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:i,dataPropType:o.Type.Str},m),u.opts.unevaluated&&!0!==g?t.assign((0,a._)`${g}[${i}]`,!0):n||u.allErrors||t.if((0,a.not)(m),()=>t.break())})})}}}},91744:(e,t,r)=>{"use strict";r.d(t,{F:()=>a,l:()=>n});let n=e=>{let t=e;if("string"==typeof e)try{t=JSON.parse(e)}catch(t){console.error("Error parsing columns",e,t)}if(t&&Array.isArray(t))return t.map(e=>{if("string"==typeof e){let t="-"!==e[0];return{accessor:t?e:e.slice(1),active:t}}return{accessor:e.accessor,active:e.active}})},a=e=>e.map(e=>e.active?e.accessor:`-${e.accessor}`)},92061:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(26520),i=r(37742),o=r(29694);t.default={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>(0,n.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,n._)`{limit: ${e}}`},code(e){let{schema:t,parentSchema:r,it:n}=e,{prefixItems:s}=r;n.items=!0,(0,a.alwaysValidSchema)(n,t)||(s?(0,o.validateAdditionalItems)(e,s):e.ok((0,i.validateArray)(e)))}}},92180:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a});let n=new Set(["equals","contains","not_equals","in","all","not_in","exists","greater_than","greater_than_equal","less_than","less_than_equal","like","not_like","within","intersects","near"]),a=e=>!!(e?.or&&e?.or?.length>0&&e?.or?.[0]?.and&&e?.or?.[0]?.and?.length>0)&&e.or.every(e=>!!(e.and&&Array.isArray(e.and))&&e.and.every(e=>{if("object"!=typeof e)return!1;let t=Object.keys(e);if(0===t.length)return!1;for(let r of t){let t=Object.keys(e[r])[0];if(!t||!n.has(t))return!1}return!0}))},92573:(e,t,r)=>{"use strict";r.d(t,{IDCell:()=>i});var n=r(67252),a=r(89688);function i(e){let{id:t}=e;return(0,n.jsx)(a.Fragment,{children:t})}},93653:(e,t,r)=>{"use strict";r.d(t,{Cd:()=>y,I2:()=>f,MT:()=>o,Vh:()=>l,Z7:()=>p,aO:()=>u,eE:()=>i,jY:()=>g,pz:()=>m,sd:()=>a,uT:()=>d,vs:()=>s,zj:()=>c});var n=r(46918);function a(e){return"group"===e.type||"array"===e.type||"row"===e.type||"collapsible"===e.type}function i(e){return"array"===e.type}function o(e){return"blocks"===e.type}function s(e){return"object"==typeof e}function l(e){return Array.isArray(e)&&"object"==typeof e?.[0]}function u(e){return"ui"===e.type}function c(e){return"admin"in e&&"position"in e.admin&&"sidebar"===e.admin.position}function d(e){return"name"in e&&"id"===e.name}function f(e){return"hidden"in e&&e.hidden||"admin"in e&&"disabled"in e.admin&&e.admin.disabled}function p(e){return"name"in e&&!u(e)}function m(e){return"name"in e}function g(e){return"name"in e}function y({field:e,parentIsLocalized:t}){return"localized"in e&&e.localized&&(!t||"true"===n.env.NEXT_PUBLIC_PAYLOAD_COMPATIBILITY_allowLocalizedWithinLocalized)}},93731:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});let n={autosaveInterval:2e3}},93894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return v},NormalizeError:function(){return g},PageNotFoundError:function(){return y},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return i},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return w}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,a=Array(n),i=0;i<n;i++)a[i]=arguments[i];return r||(r=!0,t=e(...a)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>a.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function w(e){return JSON.stringify({message:e.message,stack:e.stack})}},94273:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=t.Ajv=void 0;let n=r(80793),a=r(36279),i=r(98514),o=r(90360),s=["/properties"],l="http://json-schema.org/draft-07/schema";class u extends n.default{_addVocabularies(){super._addVocabularies(),a.default.forEach(e=>this.addVocabulary(e)),this.opts.discriminator&&this.addKeyword(i.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;let e=this.opts.$data?this.$dataMetaSchema(o,s):o;this.addMetaSchema(e,l,!1),this.refs["http://json-schema.org/schema"]=l}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(l)?l:void 0)}}t.Ajv=u,e.exports=t=u,e.exports.Ajv=u,Object.defineProperty(t,"__esModule",{value:!0}),t.default=u;var c=r(62597);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return c.KeywordCxt}});var d=r(55150);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return d._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return d.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return d.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return d.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return d.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return d.CodeGen}});var f=r(25421);Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return f.default}});var p=r(49380);Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return p.default}})},94731:e=>{"use strict";var t=e.exports=function(e,r,n){"function"==typeof r&&(n=r,r={});var a="function"==typeof(n=r.cb||n)?n:n.pre||function(){};!function e(r,n,a,i,o,s,l,u,c,d){if(i&&"object"==typeof i&&!Array.isArray(i)){for(var f in n(i,o,s,l,u,c,d),i){var p=i[f];if(Array.isArray(p)){if(f in t.arrayKeywords)for(var m=0;m<p.length;m++)e(r,n,a,p[m],o+"/"+f+"/"+m,s,o,f,i,m)}else if(f in t.propsKeywords){if(p&&"object"==typeof p)for(var g in p)e(r,n,a,p[g],o+"/"+f+"/"+g.replace(/~/g,"~0").replace(/\//g,"~1"),s,o,f,i,g)}else(f in t.keywords||r.allKeys&&!(f in t.skipKeywords))&&e(r,n,a,p,o+"/"+f,s,o,f,i)}a(i,o,s,l,u,c,d)}}(r,a,n.post||function(){},e,"",e)};t.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0},t.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},t.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},t.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}},94808:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(43011);let a=(e,t,r)=>{let a={};return e&&(Object.keys(e).forEach(t=>{!0!==r&&e[t]?.disableFormData||(a[t]=e[t]?.value)}),t&&(a=(0,n.s)(a))),a}},95439:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0;let n=r(55150),a=r(26520),i=r(12208);function o(e,t){let r=e.const("err",t);e.if((0,n._)`${i.default.vErrors} === null`,()=>e.assign(i.default.vErrors,(0,n._)`[${r}]`),(0,n._)`${i.default.vErrors}.push(${r})`),e.code((0,n._)`${i.default.errors}++`)}function s(e,t){let{gen:r,validateName:a,schemaEnv:i}=e;i.$async?r.throw((0,n._)`new ${e.ValidationError}(${t})`):(r.assign((0,n._)`${a}.errors`,t),r.return(!1))}t.keywordError={message:({keyword:e})=>(0,n.str)`must pass "${e}" keyword validation`},t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?(0,n.str)`"${e}" keyword must be ${t} ($data)`:(0,n.str)`"${e}" keyword is invalid ($data)`},t.reportError=function(e,r=t.keywordError,a,i){let{it:l}=e,{gen:c,compositeRule:d,allErrors:f}=l,p=u(e,r,a);(null!=i?i:d||f)?o(c,p):s(l,(0,n._)`[${p}]`)},t.reportExtraError=function(e,r=t.keywordError,n){let{it:a}=e,{gen:l,compositeRule:c,allErrors:d}=a;o(l,u(e,r,n)),c||d||s(a,i.default.vErrors)},t.resetErrorsCount=function(e,t){e.assign(i.default.errors,t),e.if((0,n._)`${i.default.vErrors} !== null`,()=>e.if(t,()=>e.assign((0,n._)`${i.default.vErrors}.length`,t),()=>e.assign(i.default.vErrors,null)))},t.extendErrors=function({gen:e,keyword:t,schemaValue:r,data:a,errsCount:o,it:s}){if(void 0===o)throw Error("ajv implementation error");let l=e.name("err");e.forRange("i",o,i.default.errors,o=>{e.const(l,(0,n._)`${i.default.vErrors}[${o}]`),e.if((0,n._)`${l}.instancePath === undefined`,()=>e.assign((0,n._)`${l}.instancePath`,(0,n.strConcat)(i.default.instancePath,s.errorPath))),e.assign((0,n._)`${l}.schemaPath`,(0,n.str)`${s.errSchemaPath}/${t}`),s.opts.verbose&&(e.assign((0,n._)`${l}.schema`,r),e.assign((0,n._)`${l}.data`,a))})};let l={keyword:new n.Name("keyword"),schemaPath:new n.Name("schemaPath"),params:new n.Name("params"),propertyName:new n.Name("propertyName"),message:new n.Name("message"),schema:new n.Name("schema"),parentSchema:new n.Name("parentSchema")};function u(e,t,r){let{createErrors:o}=e.it;return!1===o?(0,n._)`{}`:function(e,t,r={}){let{gen:o,it:s}=e,u=[function({errorPath:e},{instancePath:t}){let r=t?(0,n.str)`${e}${(0,a.getErrorPath)(t,a.Type.Str)}`:e;return[i.default.instancePath,(0,n.strConcat)(i.default.instancePath,r)]}(s,r),function({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:i}){let o=i?t:(0,n.str)`${t}/${e}`;return r&&(o=(0,n.str)`${o}${(0,a.getErrorPath)(r,a.Type.Str)}`),[l.schemaPath,o]}(e,r)];return function(e,{params:t,message:r},a){let{keyword:o,data:s,schemaValue:u,it:c}=e,{opts:d,propertyName:f,topSchemaRef:p,schemaPath:m}=c;a.push([l.keyword,o],[l.params,"function"==typeof t?t(e):t||(0,n._)`{}`]),d.messages&&a.push([l.message,"function"==typeof r?r(e):r]),d.verbose&&a.push([l.schema,u],[l.parentSchema,(0,n._)`${p}${m}`],[i.default.data,s]),f&&a.push([l.propertyName,f])}(e,t,u),o.object(...u)}(e,t,r)}},95665:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(37742),a=r(55150);t.default={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>(0,a.str)`must match pattern "${e}"`,params:({schemaCode:e})=>(0,a._)`{pattern: ${e}}`},code(e){let{data:t,$data:r,schema:i,schemaCode:o,it:s}=e,l=s.opts.unicodeRegExp?"u":"",u=r?(0,a._)`(new RegExp(${o}, ${l}))`:(0,n.usePattern)(e,i);e.fail$data((0,a._)`!${u}.test(${t})`)}}},95775:(e,t,r)=>{"use strict";r.d(t,{CreatedAtCell:()=>c});var n=r(47092),a=r(67252),i=r(56156),o=r(71464),s=r(83548),l=r(73272),u=r(45311);r(89688);let c=e=>{let t,r,c=(0,n.c)(11),{collectionSlug:d,docID:f,globalSlug:p,rowData:m}=e;c[0]!==m?(t=void 0===m?{}:m,c[0]=m,c[1]=t):t=c[1];let{id:g,updatedAt:y}=t,{config:v}=(0,i.b)(),{admin:b,routes:w}=v,{dateFormat:x}=b,{admin:$}=w,{i18n:k}=(0,o.d)();if(c[2]!==$||c[3]!==d||c[4]!==x||c[5]!==f||c[6]!==p||c[7]!==k||c[8]!==g||c[9]!==y){let e;d&&(e=(0,u.Q)({adminRoute:$,path:"/collections/".concat(d,"/").concat(f,"/versions/").concat(g)})),p&&(e=(0,u.Q)({adminRoute:$,path:"/globals/".concat(p,"/versions/").concat(g)})),r=(0,a.jsx)(s.Link,{href:e,prefetch:!1,children:(0,l.Yq)({date:y,i18n:k,pattern:x})}),c[2]=$,c[3]=d,c[4]=x,c[5]=f,c[6]=p,c[7]=k,c[8]=g,c[9]=y,c[10]=r}else r=c[10];return r}},95954:(e,t,r)=>{"use strict";function n(e,t=0){if(0===e)return"0 bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return`${parseFloat((e/1024**r).toFixed(t<0?0:t))}${[" bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][r]}`}r.d(t,{B:()=>n})},96900:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var n=r(81224),a=r(54201);let i=Object.prototype.hasOwnProperty,o={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},s=Array.isArray,l=Array.prototype.push,u=function(e,t){l.apply(e,s(t)?t:[t])},c=Date.prototype.toISOString,d=a.Ay,f={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.lF,encodeValuesOnly:!1,format:d,formatter:a._J[d],indices:!1,serializeDate:function(e){return c.call(e)},skipNulls:!1,strictNullHandling:!1},p={},m=function(e,t,r,a,i,o,l,c,d,g,y,v,b,w,x,$,k,S){var _;let j,E=e,C=S,P=0,N=!1;for(;void 0!==(C=C.get(p))&&!N;){let t=C.get(e);if(P+=1,void 0!==t)if(t===P)throw RangeError("Cyclic object value");else N=!0;void 0===C.get(p)&&(P=0)}if("function"==typeof g?E=g(t,E):E instanceof Date?E=b(E):"comma"===r&&s(E)&&(E=n.F7(E,function(e){return e instanceof Date?b(e):e})),null===E){if(o)return d&&!$?d(t,f.encoder,k,"key",w):t;E=""}if("string"==typeof(_=E)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||n.Pe(E))return d?[x($?t:d(t,f.encoder,k,"key",w))+"="+x(d(E,f.encoder,k,"value",w))]:[x(t)+"="+x(String(E))];let A=[];if(void 0===E)return A;if("comma"===r&&s(E))$&&d&&(E=n.F7(E,d)),j=[{value:E.length>0?E.join(",")||null:void 0}];else if(s(g))j=g;else{let e=Object.keys(E);j=y?e.sort(y):e}let T=c?t.replace(/\./g,"%2E"):t,O=a&&s(E)&&1===E.length?T+"[]":T;if(i&&s(E)&&0===E.length)return O+"[]";for(let t=0;t<j.length;++t){let n=j[t],f="object"==typeof n&&void 0!==n.value?n.value:E[n];if(l&&null===f)continue;let _=v&&c?n.replace(/\./g,"%2E"):n,C=s(E)?"function"==typeof r?r(O,_):O:O+(v?"."+_:"["+_+"]");S.set(e,P);let N=new WeakMap;N.set(p,S),u(A,m(f,C,r,a,i,o,l,c,"comma"===r&&$&&s(E)?null:d,g,y,v,b,w,x,$,k,N))}return A},g=function(e){let t;if(!e)return f;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let r=e.charset||f.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=a.Ay;if(void 0!==e.format){if(!i.call(a._J,e.format))throw TypeError("Unknown format option provided.");n=e.format}let l=a._J[n],u=f.filter;if(("function"==typeof e.filter||s(e.filter))&&(u=e.filter),t=e.arrayFormat in o?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":f.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let c=void 0===e.allowDots?!0===e.encodeDotInKeys||f.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:f.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:f.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:f.charsetSentinel,commaRoundTrip:e.commaRoundTrip,delimiter:void 0===e.delimiter?f.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:f.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:f.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:f.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:f.encodeValuesOnly,filter:u,format:n,formatter:l,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:f.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:f.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:f.strictNullHandling}};function y(e,t){let r,n,a=e,i=g(t);"function"==typeof i.filter?a=(0,i.filter)("",a):s(i.filter)&&(r=i.filter);let l=[];if("object"!=typeof a||null===a)return"";let c=o[i.arrayFormat],d="comma"===c&&i.commaRoundTrip;r||(r=Object.keys(a)),i.sort&&r.sort(i.sort);let f=new WeakMap;for(let e=0;e<r.length;++e){let t=r[e];i.skipNulls&&null===a[t]||u(l,m(a[t],t,c,d,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,f))}let p=l.join(i.delimiter),y=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?y+="utf8=%26%2310003%3B&":y+="utf8=%E2%9C%93&"),p.length>0?y+p:""}},97129:(e,t,r)=>{"use strict";r.d(t,{k:()=>n});let n=({sizes:e,targetSizeMax:t=180,targetSizeMin:r=40,thumbnailURL:n,url:a,width:i})=>n||e&&Object.values(e).reduce((e,n)=>!n.width||n.width<r?e:n.width>=r&&n.width<=t?!e.width||n.width<e.width||e.width<r||e.width>t?n:e:!e.width||!e.original&&e.width<r&&n.width>e.width||e.width>t&&n.width<e.width?n:e,{original:!0,url:a,width:i}).url||a},98034:(e,t,r)=>{"use strict";var n=r(22694);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},98232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(37742),a=r(55150),i=r(26520);t.default={keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>(0,a.str)`must have required property '${e}'`,params:({params:{missingProperty:e}})=>(0,a._)`{missingProperty: ${e}}`},code(e){let{gen:t,schema:r,schemaCode:o,data:s,$data:l,it:u}=e,{opts:c}=u;if(!l&&0===r.length)return;let d=r.length>=c.loopRequired;if(u.allErrors?function(){if(d||l)e.block$data(a.nil,f);else for(let t of r)(0,n.checkReportMissingProp)(e,t)}():function(){let i=t.let("missing");if(d||l){let r=t.let("valid",!0);e.block$data(r,()=>{var l,u;return l=i,u=r,void(e.setParams({missingProperty:l}),t.forOf(l,o,()=>{t.assign(u,(0,n.propertyInData)(t,s,l,c.ownProperties)),t.if((0,a.not)(u),()=>{e.error(),t.break()})},a.nil))}),e.ok(r)}else t.if((0,n.checkMissingProp)(e,r,i)),(0,n.reportMissingProp)(e,i),t.else()}(),c.strictRequired){let t=e.parentSchema.properties,{definedProperties:n}=e.it;for(let e of r)if((null==t?void 0:t[e])===void 0&&!n.has(e)){let t=u.schemaEnv.baseId+u.errSchemaPath,r=`required property "${e}" is not defined at "${t}" (strictRequired)`;(0,i.checkStrictMode)(u,r,u.opts.strictRequired)}}function f(){t.forOf("prop",o,r=>{e.setParams({missingProperty:r}),t.if((0,n.noPropertyInData)(t,s,r,c.ownProperties),()=>e.error())})}}}},98514:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(55150),a=r(58139),i=r(4424),o=r(49380),s=r(26520);t.default={keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===a.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>(0,n._)`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){let{gen:t,data:r,schema:l,parentSchema:u,it:c}=e,{oneOf:d}=u;if(!c.opts.discriminator)throw Error("discriminator: requires discriminator option");let f=l.propertyName;if("string"!=typeof f)throw Error("discriminator: requires propertyName");if(l.mapping)throw Error("discriminator: mapping is not supported");if(!d)throw Error("discriminator: requires oneOf keyword");let p=t.let("valid",!1),m=t.const("tag",(0,n._)`${r}${(0,n.getProperty)(f)}`);t.if((0,n._)`typeof ${m} == "string"`,()=>(function(){let r=function(){var e;let t={},r=a(u),n=!0;for(let t=0;t<d.length;t++){let u=d[t];if((null==u?void 0:u.$ref)&&!(0,s.schemaHasRulesButRef)(u,c.self.RULES)){let e=u.$ref;if((u=i.resolveRef.call(c.self,c.schemaEnv.root,c.baseId,e))instanceof i.SchemaEnv&&(u=u.schema),void 0===u)throw new o.default(c.opts.uriResolver,c.baseId,e)}let p=null==(e=null==u?void 0:u.properties)?void 0:e[f];if("object"!=typeof p)throw Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${f}"`);n=n&&(r||a(u)),function(e,t){if(e.const)l(e.const,t);else if(e.enum)for(let r of e.enum)l(r,t);else throw Error(`discriminator: "properties/${f}" must have "const" or "enum"`)}(p,t)}if(!n)throw Error(`discriminator: "${f}" must be required`);return t;function a({required:e}){return Array.isArray(e)&&e.includes(f)}function l(e,r){if("string"!=typeof e||e in t)throw Error(`discriminator: "${f}" values must be unique strings`);t[e]=r}}();for(let a in t.if(!1),r)t.elseIf((0,n._)`${m} === ${a}`),t.assign(p,function(r){let a=t.name("valid"),i=e.subschema({keyword:"oneOf",schemaProp:r},a);return e.mergeEvaluated(i,n.Name),a}(r[a]));t.else(),e.error(!1,{discrError:a.DiscrError.Mapping,tag:m,tagName:f}),t.endIf()})(),()=>e.error(!1,{discrError:a.DiscrError.Tag,tag:m,tagName:f})),e.ok(p)}}},99145:(e,t,r)=>{"use strict";r.d(t,{a:()=>n});let n=e=>e?e.or&&!e.and?{or:e.or.map(e=>e.and?e:{and:[e]})}:e.and&&!e.or?{or:[{and:e.and}]}:e.or||e.and?e:{or:[{and:[e]}]}:{}},99544:(e,t,r)=>{"use strict";function n(e,t="and"){return 0===e.length?{}:{[t]:e.filter(e=>!!e&&"object"==typeof e&&!!(Object.keys(e).length>0))}}r.d(t,{h:()=>n})}}]);