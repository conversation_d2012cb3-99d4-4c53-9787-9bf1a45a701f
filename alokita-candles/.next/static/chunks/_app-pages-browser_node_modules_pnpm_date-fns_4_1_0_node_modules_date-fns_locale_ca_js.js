"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_ca_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ca: () => (/* binding */ ca),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ca_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ca/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatDistance.js\");\n/* harmony import */ var _ca_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ca/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatLong.js\");\n/* harmony import */ var _ca_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ca/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatRelative.js\");\n/* harmony import */ var _ca_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ca/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/localize.js\");\n/* harmony import */ var _ca_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ca/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Catalan locale.\n * @language Catalan\n * @iso-639-2 cat\n * <AUTHOR> Grau [@guigrpa](https://github.com/guigrpa)\n * <AUTHOR> Vizcaino [@avizcaino](https://github.com/avizcaino)\n */ const ca = {\n    code: \"ca\",\n    formatDistance: _ca_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _ca_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _ca_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _ca_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _ca_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ca);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL2NhLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNkQ7QUFDUjtBQUNRO0FBQ1o7QUFDTjtBQUUzQzs7Ozs7OztDQU9DLEdBQ00sTUFBTUssS0FBSztJQUNoQkMsTUFBTTtJQUNOTixnQkFBZ0JBLHFFQUFjQTtJQUM5QkMsWUFBWUEsNkRBQVVBO0lBQ3RCQyxnQkFBZ0JBLHFFQUFjQTtJQUM5QkMsVUFBVUEseURBQVFBO0lBQ2xCQyxPQUFPQSxtREFBS0E7SUFDWkcsU0FBUztRQUNQQyxjQUFjLEVBQUUsVUFBVTtRQUMxQkMsdUJBQXVCO0lBQ3pCO0FBQ0YsRUFBRTtBQUVGLG9DQUFvQztBQUNwQyxpRUFBZUosRUFBRUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9jYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmb3JtYXREaXN0YW5jZSB9IGZyb20gXCIuL2NhL19saWIvZm9ybWF0RGlzdGFuY2UuanNcIjtcbmltcG9ydCB7IGZvcm1hdExvbmcgfSBmcm9tIFwiLi9jYS9fbGliL2Zvcm1hdExvbmcuanNcIjtcbmltcG9ydCB7IGZvcm1hdFJlbGF0aXZlIH0gZnJvbSBcIi4vY2EvX2xpYi9mb3JtYXRSZWxhdGl2ZS5qc1wiO1xuaW1wb3J0IHsgbG9jYWxpemUgfSBmcm9tIFwiLi9jYS9fbGliL2xvY2FsaXplLmpzXCI7XG5pbXBvcnQgeyBtYXRjaCB9IGZyb20gXCIuL2NhL19saWIvbWF0Y2guanNcIjtcblxuLyoqXG4gKiBAY2F0ZWdvcnkgTG9jYWxlc1xuICogQHN1bW1hcnkgQ2F0YWxhbiBsb2NhbGUuXG4gKiBAbGFuZ3VhZ2UgQ2F0YWxhblxuICogQGlzby02MzktMiBjYXRcbiAqIEBhdXRob3IgR3VpbGxlcm1vIEdyYXUgW0BndWlncnBhXShodHRwczovL2dpdGh1Yi5jb20vZ3VpZ3JwYSlcbiAqIEBhdXRob3IgQWxleCBWaXpjYWlubyBbQGF2aXpjYWlub10oaHR0cHM6Ly9naXRodWIuY29tL2F2aXpjYWlubylcbiAqL1xuZXhwb3J0IGNvbnN0IGNhID0ge1xuICBjb2RlOiBcImNhXCIsXG4gIGZvcm1hdERpc3RhbmNlOiBmb3JtYXREaXN0YW5jZSxcbiAgZm9ybWF0TG9uZzogZm9ybWF0TG9uZyxcbiAgZm9ybWF0UmVsYXRpdmU6IGZvcm1hdFJlbGF0aXZlLFxuICBsb2NhbGl6ZTogbG9jYWxpemUsXG4gIG1hdGNoOiBtYXRjaCxcbiAgb3B0aW9uczoge1xuICAgIHdlZWtTdGFydHNPbjogMSAvKiBNb25kYXkgKi8sXG4gICAgZmlyc3RXZWVrQ29udGFpbnNEYXRlOiA0LFxuICB9LFxufTtcblxuLy8gRmFsbGJhY2sgZm9yIG1vZHVsYXJpemVkIGltcG9ydHM6XG5leHBvcnQgZGVmYXVsdCBjYTtcbiJdLCJuYW1lcyI6WyJmb3JtYXREaXN0YW5jZSIsImZvcm1hdExvbmciLCJmb3JtYXRSZWxhdGl2ZSIsImxvY2FsaXplIiwibWF0Y2giLCJjYSIsImNvZGUiLCJvcHRpb25zIiwid2Vla1N0YXJ0c09uIiwiZmlyc3RXZWVrQ29udGFpbnNEYXRlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\n/**\n * Davant de les xifres que es diuen amb vocal inicial, 1 i 11, s'apostrofen els articles el i la i la preposició de igual que si estiguessin escrits amb lletres.\n *    l'1 de juliol ('l'u')\n *    l'11 de novembre ('l'onze')\n *    l'11a clàusula del contracte ('l'onzena')\n *    la contractació d'11 jugadors ('d'onze')\n *    l'aval d'11.000 socis ('d'onze mil')\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=apostrofaci%25F3+davant+xifres&action=Principal&method=detall_completa&numPagina=1&idHit=11236&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=11236&titol=apostrofaci%F3%20davant%20de%20xifres%20%2F%20apostrofaci%F3%20davant%20de%201%20i%2011&numeroResultat=1&clickLink=detall&tipusCerca=cerca.normes\n */ const formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"menys d'un segon\",\n        eleven: \"menys d'onze segons\",\n        other: \"menys de {{count}} segons\"\n    },\n    xSeconds: {\n        one: \"1 segon\",\n        other: \"{{count}} segons\"\n    },\n    halfAMinute: \"mig minut\",\n    lessThanXMinutes: {\n        one: \"menys d'un minut\",\n        eleven: \"menys d'onze minuts\",\n        other: \"menys de {{count}} minuts\"\n    },\n    xMinutes: {\n        one: \"1 minut\",\n        other: \"{{count}} minuts\"\n    },\n    aboutXHours: {\n        one: \"aproximadament una hora\",\n        other: \"aproximadament {{count}} hores\"\n    },\n    xHours: {\n        one: \"1 hora\",\n        other: \"{{count}} hores\"\n    },\n    xDays: {\n        one: \"1 dia\",\n        other: \"{{count}} dies\"\n    },\n    aboutXWeeks: {\n        one: \"aproximadament una setmana\",\n        other: \"aproximadament {{count}} setmanes\"\n    },\n    xWeeks: {\n        one: \"1 setmana\",\n        other: \"{{count}} setmanes\"\n    },\n    aboutXMonths: {\n        one: \"aproximadament un mes\",\n        other: \"aproximadament {{count}} mesos\"\n    },\n    xMonths: {\n        one: \"1 mes\",\n        other: \"{{count}} mesos\"\n    },\n    aboutXYears: {\n        one: \"aproximadament un any\",\n        other: \"aproximadament {{count}} anys\"\n    },\n    xYears: {\n        one: \"1 any\",\n        other: \"{{count}} anys\"\n    },\n    overXYears: {\n        one: \"més d'un any\",\n        eleven: \"més d'onze anys\",\n        other: \"més de {{count}} anys\"\n    },\n    almostXYears: {\n        one: \"gairebé un any\",\n        other: \"gairebé {{count}} anys\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else if (count === 11 && tokenValue.eleven) {\n        result = tokenValue.eleven;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"en \" + result;\n        } else {\n            return \"fa \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, d 'de' MMMM y\",\n    long: \"d 'de' MMMM y\",\n    medium: \"d MMM y\",\n    short: \"dd/MM/y\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'a les' {{time}}\",\n    long: \"{{date}} 'a les' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'el' eeee 'passat a la' LT\",\n    yesterday: \"'ahir a la' p\",\n    today: \"'avui a la' p\",\n    tomorrow: \"'demà a la' p\",\n    nextWeek: \"eeee 'a la' p\",\n    other: \"P\"\n};\nconst formatRelativeLocalePlural = {\n    lastWeek: \"'el' eeee 'passat a les' p\",\n    yesterday: \"'ahir a les' p\",\n    today: \"'avui a les' p\",\n    tomorrow: \"'demà a les' p\",\n    nextWeek: \"eeee 'a les' p\",\n    other: \"P\"\n};\nconst formatRelative = (token, date, _baseDate, _options)=>{\n    if (date.getHours() !== 1) {\n        return formatRelativeLocalePlural[token];\n    }\n    return formatRelativeLocale[token];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\n/**\n * General information\n * Reference: https://aplicacions.llengua.gencat.cat\n * Reference: https://www.uoc.edu/portal/ca/servei-linguistic/convencions/abreviacions/simbols/simbols-habituals.html\n */ /**\n * Abans de Crist: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abans+de+crist&action=Principal&method=detall_completa&numPagina=1&idHit=6876&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=6876&titol=abans%20de%20Crist%20(abreviatura)%20/%20abans%20de%20Crist%20(sigla)&numeroResultat=1&clickLink=detall&tipusCerca=cerca.fitxes\n * Desprest de Crist: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=despr%E9s+de+crist&action=Principal&method=detall_completa&numPagina=1&idHit=6879&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=6879&titol=despr%E9s%20de%20Crist%20(sigla)%20/%20despr%E9s%20de%20Crist%20(abreviatura)&numeroResultat=1&clickLink=detall&tipusCerca=cerca.fitxes\n */ const eraValues = {\n    narrow: [\n        \"aC\",\n        \"dC\"\n    ],\n    abbreviated: [\n        \"a. de C.\",\n        \"d. de C.\"\n    ],\n    wide: [\n        \"abans de Crist\",\n        \"després de Crist\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"T1\",\n        \"T2\",\n        \"T3\",\n        \"T4\"\n    ],\n    wide: [\n        \"1r trimestre\",\n        \"2n trimestre\",\n        \"3r trimestre\",\n        \"4t trimestre\"\n    ]\n};\n/**\n * Dins d'un text convé fer servir la forma sencera dels mesos, ja que sempre és més clar el mot sencer que l'abreviatura, encara que aquesta sigui força coneguda.\n * Cal reservar, doncs, les abreviatures per a les llistes o classificacions, els gràfics, les taules o quadres estadístics, els textos publicitaris, etc.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abreviacions+mesos&action=Principal&method=detall_completa&numPagina=1&idHit=8402&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=8402&titol=abreviatures%20dels%20mesos%20de%20l%27any&numeroResultat=5&clickLink=detall&tipusCerca=cerca.fitxes\n */ const monthValues = {\n    narrow: [\n        \"GN\",\n        \"FB\",\n        \"MÇ\",\n        \"AB\",\n        \"MG\",\n        \"JN\",\n        \"JL\",\n        \"AG\",\n        \"ST\",\n        \"OC\",\n        \"NV\",\n        \"DS\"\n    ],\n    /**\n   * Les abreviatures dels mesos de l'any es formen seguint una de les normes generals de formació d'abreviatures.\n   * S'escriu la primera síl·laba i les consonants de la síl·laba següent anteriors a la primera vocal.\n   * Els mesos de març, maig i juny no s'abreugen perquè són paraules d'una sola síl·laba.\n   */ abbreviated: [\n        \"gen.\",\n        \"febr.\",\n        \"març\",\n        \"abr.\",\n        \"maig\",\n        \"juny\",\n        \"jul.\",\n        \"ag.\",\n        \"set.\",\n        \"oct.\",\n        \"nov.\",\n        \"des.\"\n    ],\n    wide: [\n        \"gener\",\n        \"febrer\",\n        \"març\",\n        \"abril\",\n        \"maig\",\n        \"juny\",\n        \"juliol\",\n        \"agost\",\n        \"setembre\",\n        \"octubre\",\n        \"novembre\",\n        \"desembre\"\n    ]\n};\n/**\n * Les abreviatures dels dies de la setmana comencen totes amb la lletra d.\n * Tot seguit porten la consonant següent a la i, excepte en el cas de dimarts, dimecres i diumenge, en què aquesta consonant és la m i, per tant, hi podria haver confusió.\n * Per evitar-ho, s'ha substituït la m per una t (en el cas de dimarts), una c (en el cas de dimecres) i una g (en el cas de diumenge), respectivament.\n *\n * Seguint la norma general d'ús de les abreviatures, les dels dies de la setmana sempre porten punt final.\n * Igualment, van amb la primera lletra en majúscula quan la paraula sencera també hi aniria.\n * En canvi, van amb la primera lletra en minúscula quan la inicial de la paraula sencera també hi aniria.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abreviatures+dies&action=Principal&method=detall_completa&numPagina=1&idHit=8387&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=8387&titol=abreviatures%20dels%20dies%20de%20la%20setmana&numeroResultat=1&clickLink=detall&tipusCerca=cerca.tot\n */ const dayValues = {\n    narrow: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    short: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    abbreviated: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    wide: [\n        \"diumenge\",\n        \"dilluns\",\n        \"dimarts\",\n        \"dimecres\",\n        \"dijous\",\n        \"divendres\",\n        \"dissabte\"\n    ]\n};\n/**\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?action=Principal&method=detall&input_cercar=parts+del+dia&numPagina=1&database=FITXES_PUB&idFont=12801&idHit=12801&tipusFont=Fitxes+de+l%27Optimot&numeroResultat=1&databases_avansada=&categories_avansada=&clickLink=detall&titol=Nom+de+les+parts+del+dia&tematica=&tipusCerca=cerca.fitxes\n */ const dayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    },\n    abbreviated: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    },\n    wide: {\n        am: \"ante meridiem\",\n        pm: \"post meridiem\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    },\n    wide: {\n        am: \"ante meridiem\",\n        pm: \"post meridiem\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    }\n};\n/**\n * Quan van en singular, els nombres ordinals es representen, en forma d’abreviatura, amb la xifra seguida de l’última lletra del mot desplegat.\n * És optatiu posar punt després de la lletra.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/pdf/abrevia.pdf#page=18\n */ const ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    const rem100 = number % 100;\n    if (rem100 > 20 || rem100 < 10) {\n        switch(rem100 % 10){\n            case 1:\n                return number + \"r\";\n            case 2:\n                return number + \"n\";\n            case 3:\n                return number + \"r\";\n            case 4:\n                return number + \"t\";\n        }\n    }\n    return number + \"è\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(è|r|n|r|t)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(aC|dC)/i,\n    abbreviated: /^(a. de C.|d. de C.)/i,\n    wide: /^(abans de Crist|despr[eé]s de Crist)/i\n};\nconst parseEraPatterns = {\n    narrow: [\n        /^aC/i,\n        /^dC/i\n    ],\n    abbreviated: [\n        /^(a. de C.)/i,\n        /^(d. de C.)/i\n    ],\n    wide: [\n        /^(abans de Crist)/i,\n        /^(despr[eé]s de Crist)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^T[1234]/i,\n    wide: /^[1234](è|r|n|r|t)? trimestre/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^(GN|FB|MÇ|AB|MG|JN|JL|AG|ST|OC|NV|DS)/i,\n    abbreviated: /^(gen.|febr.|març|abr.|maig|juny|jul.|ag.|set.|oct.|nov.|des.)/i,\n    wide: /^(gener|febrer|març|abril|maig|juny|juliol|agost|setembre|octubre|novembre|desembre)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^GN/i,\n        /^FB/i,\n        /^MÇ/i,\n        /^AB/i,\n        /^MG/i,\n        /^JN/i,\n        /^JL/i,\n        /^AG/i,\n        /^ST/i,\n        /^OC/i,\n        /^NV/i,\n        /^DS/i\n    ],\n    abbreviated: [\n        /^gen./i,\n        /^febr./i,\n        /^març/i,\n        /^abr./i,\n        /^maig/i,\n        /^juny/i,\n        /^jul./i,\n        /^ag./i,\n        /^set./i,\n        /^oct./i,\n        /^nov./i,\n        /^des./i\n    ],\n    wide: [\n        /^gener/i,\n        /^febrer/i,\n        /^març/i,\n        /^abril/i,\n        /^maig/i,\n        /^juny/i,\n        /^juliol/i,\n        /^agost/i,\n        /^setembre/i,\n        /^octubre/i,\n        /^novembre/i,\n        /^desembre/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    short: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    abbreviated: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    wide: /^(diumenge|dilluns|dimarts|dimecres|dijous|divendres|dissabte)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^dg./i,\n        /^dl./i,\n        /^dt./i,\n        /^dm./i,\n        /^dj./i,\n        /^dv./i,\n        /^ds./i\n    ],\n    abbreviated: [\n        /^dg./i,\n        /^dl./i,\n        /^dt./i,\n        /^dm./i,\n        /^dj./i,\n        /^dv./i,\n        /^ds./i\n    ],\n    wide: [\n        /^diumenge/i,\n        /^dilluns/i,\n        /^dimarts/i,\n        /^dimecres/i,\n        /^dijous/i,\n        /^divendres/i,\n        /^disssabte/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(a|p|mn|md|(del|de la) (matí|tarda|vespre|nit))/i,\n    abbreviated: /^([ap]\\.?\\s?m\\.?|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i,\n    wide: /^(ante meridiem|post meridiem|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^a/i,\n        pm: /^p/i,\n        midnight: /^mitjanit/i,\n        noon: /^migdia/i,\n        morning: /matí/i,\n        afternoon: /tarda/i,\n        evening: /vespre/i,\n        night: /nit/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/match.js\n"));

/***/ })

}]);