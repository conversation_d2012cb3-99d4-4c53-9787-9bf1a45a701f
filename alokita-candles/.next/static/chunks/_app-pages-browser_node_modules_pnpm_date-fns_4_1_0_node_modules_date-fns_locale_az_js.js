"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_az_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   az: () => (/* binding */ az),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _az_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./az/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatDistance.js\");\n/* harmony import */ var _az_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./az/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatLong.js\");\n/* harmony import */ var _az_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./az/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatRelative.js\");\n/* harmony import */ var _az_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./az/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/localize.js\");\n/* harmony import */ var _az_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./az/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Azerbaijani locale.\n * @language Azerbaijani\n * @iso-639-2 aze\n */ const az = {\n    code: \"az\",\n    formatDistance: _az_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _az_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _az_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _az_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _az_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (az);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL2F6LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNkQ7QUFDUjtBQUNRO0FBQ1o7QUFDTjtBQUUzQzs7Ozs7Q0FLQyxHQUVNLE1BQU1LLEtBQUs7SUFDaEJDLE1BQU07SUFDTk4sZ0JBQWdCQSxxRUFBY0E7SUFDOUJDLFlBQVlBLDZEQUFVQTtJQUN0QkMsZ0JBQWdCQSxxRUFBY0E7SUFDOUJDLFVBQVVBLHlEQUFRQTtJQUNsQkMsT0FBT0EsbURBQUtBO0lBQ1pHLFNBQVM7UUFDUEMsY0FBYztRQUNkQyx1QkFBdUI7SUFDekI7QUFDRixFQUFFO0FBRUYsb0NBQW9DO0FBQ3BDLGlFQUFlSixFQUFFQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvaGFyc2hsdWhhci9sYWxpdCBkb24vYWxva2l0YS1jYW5kbGVzL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL2F6LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZvcm1hdERpc3RhbmNlIH0gZnJvbSBcIi4vYXovX2xpYi9mb3JtYXREaXN0YW5jZS5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0TG9uZyB9IGZyb20gXCIuL2F6L19saWIvZm9ybWF0TG9uZy5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0UmVsYXRpdmUgfSBmcm9tIFwiLi9hei9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzXCI7XG5pbXBvcnQgeyBsb2NhbGl6ZSB9IGZyb20gXCIuL2F6L19saWIvbG9jYWxpemUuanNcIjtcbmltcG9ydCB7IG1hdGNoIH0gZnJvbSBcIi4vYXovX2xpYi9tYXRjaC5qc1wiO1xuXG4vKipcbiAqIEBjYXRlZ29yeSBMb2NhbGVzXG4gKiBAc3VtbWFyeSBBemVyYmFpamFuaSBsb2NhbGUuXG4gKiBAbGFuZ3VhZ2UgQXplcmJhaWphbmlcbiAqIEBpc28tNjM5LTIgYXplXG4gKi9cblxuZXhwb3J0IGNvbnN0IGF6ID0ge1xuICBjb2RlOiBcImF6XCIsXG4gIGZvcm1hdERpc3RhbmNlOiBmb3JtYXREaXN0YW5jZSxcbiAgZm9ybWF0TG9uZzogZm9ybWF0TG9uZyxcbiAgZm9ybWF0UmVsYXRpdmU6IGZvcm1hdFJlbGF0aXZlLFxuICBsb2NhbGl6ZTogbG9jYWxpemUsXG4gIG1hdGNoOiBtYXRjaCxcbiAgb3B0aW9uczoge1xuICAgIHdlZWtTdGFydHNPbjogMSxcbiAgICBmaXJzdFdlZWtDb250YWluc0RhdGU6IDEsXG4gIH0sXG59O1xuXG4vLyBGYWxsYmFjayBmb3IgbW9kdWxhcml6ZWQgaW1wb3J0czpcbmV4cG9ydCBkZWZhdWx0IGF6O1xuIl0sIm5hbWVzIjpbImZvcm1hdERpc3RhbmNlIiwiZm9ybWF0TG9uZyIsImZvcm1hdFJlbGF0aXZlIiwibG9jYWxpemUiLCJtYXRjaCIsImF6IiwiY29kZSIsIm9wdGlvbnMiLCJ3ZWVrU3RhcnRzT24iLCJmaXJzdFdlZWtDb250YWluc0RhdGUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"bir saniyədən az\",\n        other: \"{{count}} bir saniyədən az\"\n    },\n    xSeconds: {\n        one: \"1 saniyə\",\n        other: \"{{count}} saniyə\"\n    },\n    halfAMinute: \"yarım dəqiqə\",\n    lessThanXMinutes: {\n        one: \"bir dəqiqədən az\",\n        other: \"{{count}} bir dəqiqədən az\"\n    },\n    xMinutes: {\n        one: \"bir dəqiqə\",\n        other: \"{{count}} dəqiqə\"\n    },\n    aboutXHours: {\n        one: \"təxminən 1 saat\",\n        other: \"təxminən {{count}} saat\"\n    },\n    xHours: {\n        one: \"1 saat\",\n        other: \"{{count}} saat\"\n    },\n    xDays: {\n        one: \"1 gün\",\n        other: \"{{count}} gün\"\n    },\n    aboutXWeeks: {\n        one: \"təxminən 1 həftə\",\n        other: \"təxminən {{count}} həftə\"\n    },\n    xWeeks: {\n        one: \"1 həftə\",\n        other: \"{{count}} həftə\"\n    },\n    aboutXMonths: {\n        one: \"təxminən 1 ay\",\n        other: \"təxminən {{count}} ay\"\n    },\n    xMonths: {\n        one: \"1 ay\",\n        other: \"{{count}} ay\"\n    },\n    aboutXYears: {\n        one: \"təxminən 1 il\",\n        other: \"təxminən {{count}} il\"\n    },\n    xYears: {\n        one: \"1 il\",\n        other: \"{{count}} il\"\n    },\n    overXYears: {\n        one: \"1 ildən çox\",\n        other: \"{{count}} ildən çox\"\n    },\n    almostXYears: {\n        one: \"demək olar ki 1 il\",\n        other: \"demək olar ki {{count}} il\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return result + \" sonra\";\n        } else {\n            return result + \" əvvəl\";\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, do MMMM y 'il'\",\n    long: \"do MMMM y 'il'\",\n    medium: \"d MMM y 'il'\",\n    short: \"dd.MM.yyyy\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} {{time}} - 'də'\",\n    long: \"{{date}} {{time}} - 'də'\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'sonuncu' eeee p -'də'\",\n    yesterday: \"'dünən' p -'də'\",\n    today: \"'bugün' p -'də'\",\n    tomorrow: \"'sabah' p -'də'\",\n    nextWeek: \"eeee p -'də'\",\n    other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL2F6L19saWIvZm9ybWF0UmVsYXRpdmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE1BQU1BLHVCQUF1QjtJQUMzQkMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLE9BQU87SUFDUEMsVUFBVTtJQUNWQyxVQUFVO0lBQ1ZDLE9BQU87QUFDVDtBQUVPLE1BQU1DLGlCQUFpQixDQUFDQyxPQUFPQyxPQUFPQyxXQUFXQyxXQUN0RFgsb0JBQW9CLENBQUNRLE1BQU0sQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9hei9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGZvcm1hdFJlbGF0aXZlTG9jYWxlID0ge1xuICBsYXN0V2VlazogXCInc29udW5jdScgZWVlZSBwIC0nZMmZJ1wiLFxuICB5ZXN0ZXJkYXk6IFwiJ2TDvG7JmW4nIHAgLSdkyZknXCIsXG4gIHRvZGF5OiBcIididWfDvG4nIHAgLSdkyZknXCIsXG4gIHRvbW9ycm93OiBcIidzYWJhaCcgcCAtJ2TJmSdcIixcbiAgbmV4dFdlZWs6IFwiZWVlZSBwIC0nZMmZJ1wiLFxuICBvdGhlcjogXCJQXCIsXG59O1xuXG5leHBvcnQgY29uc3QgZm9ybWF0UmVsYXRpdmUgPSAodG9rZW4sIF9kYXRlLCBfYmFzZURhdGUsIF9vcHRpb25zKSA9PlxuICBmb3JtYXRSZWxhdGl2ZUxvY2FsZVt0b2tlbl07XG4iXSwibmFtZXMiOlsiZm9ybWF0UmVsYXRpdmVMb2NhbGUiLCJsYXN0V2VlayIsInllc3RlcmRheSIsInRvZGF5IiwidG9tb3Jyb3ciLCJuZXh0V2VlayIsIm90aGVyIiwiZm9ybWF0UmVsYXRpdmUiLCJ0b2tlbiIsIl9kYXRlIiwiX2Jhc2VEYXRlIiwiX29wdGlvbnMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"e.ə\",\n        \"b.e\"\n    ],\n    abbreviated: [\n        \"e.ə\",\n        \"b.e\"\n    ],\n    wide: [\n        \"eramızdan əvvəl\",\n        \"bizim era\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"K1\",\n        \"K2\",\n        \"K3\",\n        \"K4\"\n    ],\n    wide: [\n        \"1ci kvartal\",\n        \"2ci kvartal\",\n        \"3cü kvartal\",\n        \"4cü kvartal\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"Y\",\n        \"F\",\n        \"M\",\n        \"A\",\n        \"M\",\n        \"İ\",\n        \"İ\",\n        \"A\",\n        \"S\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"Yan\",\n        \"Fev\",\n        \"Mar\",\n        \"Apr\",\n        \"May\",\n        \"İyun\",\n        \"İyul\",\n        \"Avq\",\n        \"Sen\",\n        \"Okt\",\n        \"Noy\",\n        \"Dek\"\n    ],\n    wide: [\n        \"Yanvar\",\n        \"Fevral\",\n        \"Mart\",\n        \"Aprel\",\n        \"May\",\n        \"İyun\",\n        \"İyul\",\n        \"Avqust\",\n        \"Sentyabr\",\n        \"Oktyabr\",\n        \"Noyabr\",\n        \"Dekabr\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"B.\",\n        \"B.e\",\n        \"Ç.a\",\n        \"Ç.\",\n        \"C.a\",\n        \"C.\",\n        \"Ş.\"\n    ],\n    short: [\n        \"B.\",\n        \"B.e\",\n        \"Ç.a\",\n        \"Ç.\",\n        \"C.a\",\n        \"C.\",\n        \"Ş.\"\n    ],\n    abbreviated: [\n        \"Baz\",\n        \"Baz.e\",\n        \"Çər.a\",\n        \"Çər\",\n        \"Cüm.a\",\n        \"Cüm\",\n        \"Şə\"\n    ],\n    wide: [\n        \"Bazar\",\n        \"Bazar ertəsi\",\n        \"Çərşənbə axşamı\",\n        \"Çərşənbə\",\n        \"Cümə axşamı\",\n        \"Cümə\",\n        \"Şənbə\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    wide: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"a\",\n        pm: \"p\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    wide: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    }\n};\nconst suffixes = {\n    1: \"-inci\",\n    5: \"-inci\",\n    8: \"-inci\",\n    70: \"-inci\",\n    80: \"-inci\",\n    2: \"-nci\",\n    7: \"-nci\",\n    20: \"-nci\",\n    50: \"-nci\",\n    3: \"-üncü\",\n    4: \"-üncü\",\n    100: \"-üncü\",\n    6: \"-ncı\",\n    9: \"-uncu\",\n    10: \"-uncu\",\n    30: \"-uncu\",\n    60: \"-ıncı\",\n    90: \"-ıncı\"\n};\nconst getSuffix = (number)=>{\n    if (number === 0) {\n        // special case for zero\n        return number + \"-ıncı\";\n    }\n    const a = number % 10;\n    const b = number % 100 - a;\n    const c = number >= 100 ? 100 : null;\n    if (suffixes[a]) {\n        return suffixes[a];\n    } else if (suffixes[b]) {\n        return suffixes[b];\n    } else if (c !== null) {\n        return suffixes[c];\n    }\n    return \"\";\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    const suffix = getSuffix(number);\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(ci|inci|nci|uncu|üncü|ncı))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(b|a)$/i,\n    abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)$/i,\n    wide: /^(bizim eradan əvvəl|bizim era)$/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^b$/i,\n        /^(a|c)$/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]$/i,\n    abbreviated: /^K[1234]$/i,\n    wide: /^[1234](ci)? kvartal$/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[(?-i)yfmaisond]$/i,\n    abbreviated: /^(Yan|Fev|Mar|Apr|May|İyun|İyul|Avq|Sen|Okt|Noy|Dek)$/i,\n    wide: /^(Yanvar|Fevral|Mart|Aprel|May|İyun|İyul|Avgust|Sentyabr|Oktyabr|Noyabr|Dekabr)$/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^[(?-i)y]$/i,\n        /^[(?-i)f]$/i,\n        /^[(?-i)m]$/i,\n        /^[(?-i)a]$/i,\n        /^[(?-i)m]$/i,\n        /^[(?-i)i]$/i,\n        /^[(?-i)i]$/i,\n        /^[(?-i)a]$/i,\n        /^[(?-i)s]$/i,\n        /^[(?-i)o]$/i,\n        /^[(?-i)n]$/i,\n        /^[(?-i)d]$/i\n    ],\n    abbreviated: [\n        /^Yan$/i,\n        /^Fev$/i,\n        /^Mar$/i,\n        /^Apr$/i,\n        /^May$/i,\n        /^İyun$/i,\n        /^İyul$/i,\n        /^Avg$/i,\n        /^Sen$/i,\n        /^Okt$/i,\n        /^Noy$/i,\n        /^Dek$/i\n    ],\n    wide: [\n        /^Yanvar$/i,\n        /^Fevral$/i,\n        /^Mart$/i,\n        /^Aprel$/i,\n        /^May$/i,\n        /^İyun$/i,\n        /^İyul$/i,\n        /^Avgust$/i,\n        /^Sentyabr$/i,\n        /^Oktyabr$/i,\n        /^Noyabr$/i,\n        /^Dekabr$/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^(B\\.|B\\.e|Ç\\.a|Ç\\.|C\\.a|C\\.|Ş\\.)$/i,\n    short: /^(B\\.|B\\.e|Ç\\.a|Ç\\.|C\\.a|C\\.|Ş\\.)$/i,\n    abbreviated: /^(Baz\\.e|Çər|Çər\\.a|Cüm|Cüm\\.a|Şə)$/i,\n    wide: /^(Bazar|Bazar ertəsi|Çərşənbə axşamı|Çərşənbə|Cümə axşamı|Cümə|Şənbə)$/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^B\\.$/i,\n        /^B\\.e$/i,\n        /^Ç\\.a$/i,\n        /^Ç\\.$/i,\n        /^C\\.a$/i,\n        /^C\\.$/i,\n        /^Ş\\.$/i\n    ],\n    abbreviated: [\n        /^Baz$/i,\n        /^Baz\\.e$/i,\n        /^Çər\\.a$/i,\n        /^Çər$/i,\n        /^Cüm\\.a$/i,\n        /^Cüm$/i,\n        /^Şə$/i\n    ],\n    wide: [\n        /^Bazar$/i,\n        /^Bazar ertəsi$/i,\n        /^Çərşənbə axşamı$/i,\n        /^Çərşənbə$/i,\n        /^Cümə axşamı$/i,\n        /^Cümə$/i,\n        /^Şənbə$/i\n    ],\n    any: [\n        /^B\\.$/i,\n        /^B\\.e$/i,\n        /^Ç\\.a$/i,\n        /^Ç\\.$/i,\n        /^C\\.a$/i,\n        /^C\\.$/i,\n        /^Ş\\.$/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(a|p|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i,\n    any: /^(am|pm|a\\.m\\.|p\\.m\\.|AM|PM|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^a$/i,\n        pm: /^p$/i,\n        midnight: /^gecəyarı$/i,\n        noon: /^gün$/i,\n        morning: /səhər$/i,\n        afternoon: /gündüz$/i,\n        evening: /axşam$/i,\n        night: /gecə$/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"narrow\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/match.js\n"));

/***/ })

}]);