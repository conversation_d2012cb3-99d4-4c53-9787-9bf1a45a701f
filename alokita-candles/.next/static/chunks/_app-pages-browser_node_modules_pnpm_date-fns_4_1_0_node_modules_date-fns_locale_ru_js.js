"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_ru_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ru: () => (/* binding */ ru)\n/* harmony export */ });\n/* harmony import */ var _ru_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ru/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatDistance.js\");\n/* harmony import */ var _ru_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ru/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatLong.js\");\n/* harmony import */ var _ru_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ru/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatRelative.js\");\n/* harmony import */ var _ru_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ru/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/localize.js\");\n/* harmony import */ var _ru_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ru/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Russian locale.\n * @language Russian\n * @iso-639-2 rus\n * <AUTHOR> Koss [@kossnocorp](https://github.com/kossnocorp)\n * <AUTHOR> Koss [@leshakoss](https://github.com/leshakoss)\n */ const ru = {\n    code: \"ru\",\n    formatDistance: _ru_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _ru_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _ru_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _ru_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _ru_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ru);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declension(scheme, count) {\n    // scheme for count=1 exists\n    if (scheme.one !== undefined && count === 1) {\n        return scheme.one;\n    }\n    const rem10 = count % 10;\n    const rem100 = count % 100;\n    // 1, 21, 31, ...\n    if (rem10 === 1 && rem100 !== 11) {\n        return scheme.singularNominative.replace(\"{{count}}\", String(count));\n    // 2, 3, 4, 22, 23, 24, 32 ...\n    } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n        return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n    // 5, 6, 7, 8, 9, 10, 11, ...\n    } else {\n        return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n    }\n}\nfunction buildLocalizeTokenFn(scheme) {\n    return (count, options)=>{\n        if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                if (scheme.future) {\n                    return declension(scheme.future, count);\n                } else {\n                    return \"через \" + declension(scheme.regular, count);\n                }\n            } else {\n                if (scheme.past) {\n                    return declension(scheme.past, count);\n                } else {\n                    return declension(scheme.regular, count) + \" назад\";\n                }\n            }\n        } else {\n            return declension(scheme.regular, count);\n        }\n    };\n}\nconst formatDistanceLocale = {\n    lessThanXSeconds: buildLocalizeTokenFn({\n        regular: {\n            one: \"меньше секунды\",\n            singularNominative: \"меньше {{count}} секунды\",\n            singularGenitive: \"меньше {{count}} секунд\",\n            pluralGenitive: \"меньше {{count}} секунд\"\n        },\n        future: {\n            one: \"меньше, чем через секунду\",\n            singularNominative: \"меньше, чем через {{count}} секунду\",\n            singularGenitive: \"меньше, чем через {{count}} секунды\",\n            pluralGenitive: \"меньше, чем через {{count}} секунд\"\n        }\n    }),\n    xSeconds: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} секунда\",\n            singularGenitive: \"{{count}} секунды\",\n            pluralGenitive: \"{{count}} секунд\"\n        },\n        past: {\n            singularNominative: \"{{count}} секунду назад\",\n            singularGenitive: \"{{count}} секунды назад\",\n            pluralGenitive: \"{{count}} секунд назад\"\n        },\n        future: {\n            singularNominative: \"через {{count}} секунду\",\n            singularGenitive: \"через {{count}} секунды\",\n            pluralGenitive: \"через {{count}} секунд\"\n        }\n    }),\n    halfAMinute: (_count, options)=>{\n        if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                return \"через полминуты\";\n            } else {\n                return \"полминуты назад\";\n            }\n        }\n        return \"полминуты\";\n    },\n    lessThanXMinutes: buildLocalizeTokenFn({\n        regular: {\n            one: \"меньше минуты\",\n            singularNominative: \"меньше {{count}} минуты\",\n            singularGenitive: \"меньше {{count}} минут\",\n            pluralGenitive: \"меньше {{count}} минут\"\n        },\n        future: {\n            one: \"меньше, чем через минуту\",\n            singularNominative: \"меньше, чем через {{count}} минуту\",\n            singularGenitive: \"меньше, чем через {{count}} минуты\",\n            pluralGenitive: \"меньше, чем через {{count}} минут\"\n        }\n    }),\n    xMinutes: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} минута\",\n            singularGenitive: \"{{count}} минуты\",\n            pluralGenitive: \"{{count}} минут\"\n        },\n        past: {\n            singularNominative: \"{{count}} минуту назад\",\n            singularGenitive: \"{{count}} минуты назад\",\n            pluralGenitive: \"{{count}} минут назад\"\n        },\n        future: {\n            singularNominative: \"через {{count}} минуту\",\n            singularGenitive: \"через {{count}} минуты\",\n            pluralGenitive: \"через {{count}} минут\"\n        }\n    }),\n    aboutXHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} часа\",\n            singularGenitive: \"около {{count}} часов\",\n            pluralGenitive: \"около {{count}} часов\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} час\",\n            singularGenitive: \"приблизительно через {{count}} часа\",\n            pluralGenitive: \"приблизительно через {{count}} часов\"\n        }\n    }),\n    xHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} час\",\n            singularGenitive: \"{{count}} часа\",\n            pluralGenitive: \"{{count}} часов\"\n        }\n    }),\n    xDays: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} день\",\n            singularGenitive: \"{{count}} дня\",\n            pluralGenitive: \"{{count}} дней\"\n        }\n    }),\n    aboutXWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} недели\",\n            singularGenitive: \"около {{count}} недель\",\n            pluralGenitive: \"около {{count}} недель\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} неделю\",\n            singularGenitive: \"приблизительно через {{count}} недели\",\n            pluralGenitive: \"приблизительно через {{count}} недель\"\n        }\n    }),\n    xWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} неделя\",\n            singularGenitive: \"{{count}} недели\",\n            pluralGenitive: \"{{count}} недель\"\n        }\n    }),\n    aboutXMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} месяца\",\n            singularGenitive: \"около {{count}} месяцев\",\n            pluralGenitive: \"около {{count}} месяцев\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} месяц\",\n            singularGenitive: \"приблизительно через {{count}} месяца\",\n            pluralGenitive: \"приблизительно через {{count}} месяцев\"\n        }\n    }),\n    xMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} месяц\",\n            singularGenitive: \"{{count}} месяца\",\n            pluralGenitive: \"{{count}} месяцев\"\n        }\n    }),\n    aboutXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} года\",\n            singularGenitive: \"около {{count}} лет\",\n            pluralGenitive: \"около {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} год\",\n            singularGenitive: \"приблизительно через {{count}} года\",\n            pluralGenitive: \"приблизительно через {{count}} лет\"\n        }\n    }),\n    xYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} год\",\n            singularGenitive: \"{{count}} года\",\n            pluralGenitive: \"{{count}} лет\"\n        }\n    }),\n    overXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"больше {{count}} года\",\n            singularGenitive: \"больше {{count}} лет\",\n            pluralGenitive: \"больше {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"больше, чем через {{count}} год\",\n            singularGenitive: \"больше, чем через {{count}} года\",\n            pluralGenitive: \"больше, чем через {{count}} лет\"\n        }\n    }),\n    almostXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"почти {{count}} год\",\n            singularGenitive: \"почти {{count}} года\",\n            pluralGenitive: \"почти {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"почти через {{count}} год\",\n            singularGenitive: \"почти через {{count}} года\",\n            pluralGenitive: \"почти через {{count}} лет\"\n        }\n    })\n};\nconst formatDistance = (token, count, options)=>{\n    return formatDistanceLocale[token](count, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, d MMMM y 'г.'\",\n    long: \"d MMMM y 'г.'\",\n    medium: \"d MMM y 'г.'\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    any: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n\nconst accusativeWeekdays = [\n    \"воскресенье\",\n    \"понедельник\",\n    \"вторник\",\n    \"среду\",\n    \"четверг\",\n    \"пятницу\",\n    \"субботу\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n            return \"'в прошлое \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'в прошлый \" + weekday + \" в' p\";\n        case 3:\n        case 5:\n        case 6:\n            return \"'в прошлую \" + weekday + \" в' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    if (day === 2 /* Tue */ ) {\n        return \"'во \" + weekday + \" в' p\";\n    } else {\n        return \"'в \" + weekday + \" в' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n            return \"'в следующее \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'в следующий \" + weekday + \" в' p\";\n        case 3:\n        case 5:\n        case 6:\n            return \"'в следующую \" + weekday + \" в' p\";\n    }\n}\nconst formatRelativeLocale = {\n    lastWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return lastWeek(day);\n        }\n    },\n    yesterday: \"'вчера в' p\",\n    today: \"'сегодня в' p\",\n    tomorrow: \"'завтра в' p\",\n    nextWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return nextWeek(day);\n        }\n    },\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"до н.э.\",\n        \"н.э.\"\n    ],\n    abbreviated: [\n        \"до н. э.\",\n        \"н. э.\"\n    ],\n    wide: [\n        \"до нашей эры\",\n        \"нашей эры\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-й кв.\",\n        \"2-й кв.\",\n        \"3-й кв.\",\n        \"4-й кв.\"\n    ],\n    wide: [\n        \"1-й квартал\",\n        \"2-й квартал\",\n        \"3-й квартал\",\n        \"4-й квартал\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"Я\",\n        \"Ф\",\n        \"М\",\n        \"А\",\n        \"М\",\n        \"И\",\n        \"И\",\n        \"А\",\n        \"С\",\n        \"О\",\n        \"Н\",\n        \"Д\"\n    ],\n    abbreviated: [\n        \"янв.\",\n        \"фев.\",\n        \"март\",\n        \"апр.\",\n        \"май\",\n        \"июнь\",\n        \"июль\",\n        \"авг.\",\n        \"сент.\",\n        \"окт.\",\n        \"нояб.\",\n        \"дек.\"\n    ],\n    wide: [\n        \"январь\",\n        \"февраль\",\n        \"март\",\n        \"апрель\",\n        \"май\",\n        \"июнь\",\n        \"июль\",\n        \"август\",\n        \"сентябрь\",\n        \"октябрь\",\n        \"ноябрь\",\n        \"декабрь\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"Я\",\n        \"Ф\",\n        \"М\",\n        \"А\",\n        \"М\",\n        \"И\",\n        \"И\",\n        \"А\",\n        \"С\",\n        \"О\",\n        \"Н\",\n        \"Д\"\n    ],\n    abbreviated: [\n        \"янв.\",\n        \"фев.\",\n        \"мар.\",\n        \"апр.\",\n        \"мая\",\n        \"июн.\",\n        \"июл.\",\n        \"авг.\",\n        \"сент.\",\n        \"окт.\",\n        \"нояб.\",\n        \"дек.\"\n    ],\n    wide: [\n        \"января\",\n        \"февраля\",\n        \"марта\",\n        \"апреля\",\n        \"мая\",\n        \"июня\",\n        \"июля\",\n        \"августа\",\n        \"сентября\",\n        \"октября\",\n        \"ноября\",\n        \"декабря\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"В\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"вс\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"вск\",\n        \"пнд\",\n        \"втр\",\n        \"срд\",\n        \"чтв\",\n        \"птн\",\n        \"суб\"\n    ],\n    wide: [\n        \"воскресенье\",\n        \"понедельник\",\n        \"вторник\",\n        \"среда\",\n        \"четверг\",\n        \"пятница\",\n        \"суббота\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ночь\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ночь\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полночь\",\n        noon: \"полдень\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"вечер\",\n        night: \"ночь\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночи\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночи\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полночь\",\n        noon: \"полдень\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"вечера\",\n        night: \"ночи\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const number = Number(dirtyNumber);\n    const unit = options === null || options === void 0 ? void 0 : options.unit;\n    let suffix;\n    if (unit === \"date\") {\n        suffix = \"-е\";\n    } else if (unit === \"week\" || unit === \"minute\" || unit === \"second\") {\n        suffix = \"-я\";\n    } else {\n        suffix = \"-й\";\n    }\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"any\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|я|й|ое|ье|ая|ья|ый|ой|ий|ый))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((до )?н\\.?\\s?э\\.?)/i,\n    abbreviated: /^((до )?н\\.?\\s?э\\.?)/i,\n    wide: /^(до нашей эры|нашей эры|наша эра)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^д/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[ыои]?й?)? кв.?/i,\n    wide: /^[1234](-?[ыои]?й?)? квартал/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[яфмаисонд]/i,\n    abbreviated: /^(янв|фев|март?|апр|ма[йя]|июн[ья]?|июл[ья]?|авг|сент?|окт|нояб?|дек)\\.?/i,\n    wide: /^(январ[ья]|феврал[ья]|марта?|апрел[ья]|ма[йя]|июн[ья]|июл[ья]|августа?|сентябр[ья]|октябр[ья]|октябр[ья]|ноябр[ья]|декабр[ья])/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^я/i,\n        /^ф/i,\n        /^м/i,\n        /^а/i,\n        /^м/i,\n        /^и/i,\n        /^и/i,\n        /^а/i,\n        /^с/i,\n        /^о/i,\n        /^н/i,\n        /^я/i\n    ],\n    any: [\n        /^я/i,\n        /^ф/i,\n        /^мар/i,\n        /^ап/i,\n        /^ма[йя]/i,\n        /^июн/i,\n        /^июл/i,\n        /^ав/i,\n        /^с/i,\n        /^о/i,\n        /^н/i,\n        /^д/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[впсч]/i,\n    short: /^(вс|во|пн|по|вт|ср|чт|че|пт|пя|сб|су)\\.?/i,\n    abbreviated: /^(вск|вос|пнд|пон|втр|вто|срд|сре|чтв|чет|птн|пят|суб).?/i,\n    wide: /^(воскресень[ея]|понедельника?|вторника?|сред[аы]|четверга?|пятниц[аы]|суббот[аы])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^в/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^в[ос]/i,\n        /^п[он]/i,\n        /^в/i,\n        /^ср/i,\n        /^ч/i,\n        /^п[ят]/i,\n        /^с[уб]/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n    abbreviated: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n    wide: /^([дп]п|полночь|полдень|утр[оа]|день|дня|вечера?|ноч[ьи])/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^дп/i,\n        pm: /^пп/i,\n        midnight: /^полн/i,\n        noon: /^полд/i,\n        morning: /^у/i,\n        afternoon: /^д[ен]/i,\n        evening: /^в/i,\n        night: /^н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/match.js\n"));

/***/ })

}]);