"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_nb_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   nb: () => (/* binding */ nb)\n/* harmony export */ });\n/* harmony import */ var _nb_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./nb/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatDistance.js\");\n/* harmony import */ var _nb_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nb/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatLong.js\");\n/* harmony import */ var _nb_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./nb/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatRelative.js\");\n/* harmony import */ var _nb_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nb/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/localize.js\");\n/* harmony import */ var _nb_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./nb/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Norwegian Bokmål locale.\n * @language Norwegian Bokmål\n * @iso-639-2 nob\n * <AUTHOR> Koren [@Hanse](https://github.com/Hanse)\n * <AUTHOR> Grzyb [@mikolajgrzyb](https://github.com/mikolajgrzyb)\n * <AUTHOR> Stuan [@dagstuan](https://github.com/dagstuan)\n */ const nb = {\n    code: \"nb\",\n    formatDistance: _nb_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _nb_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _nb_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _nb_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _nb_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (nb);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"mindre enn ett sekund\",\n        other: \"mindre enn {{count}} sekunder\"\n    },\n    xSeconds: {\n        one: \"ett sekund\",\n        other: \"{{count}} sekunder\"\n    },\n    halfAMinute: \"et halvt minutt\",\n    lessThanXMinutes: {\n        one: \"mindre enn ett minutt\",\n        other: \"mindre enn {{count}} minutter\"\n    },\n    xMinutes: {\n        one: \"ett minutt\",\n        other: \"{{count}} minutter\"\n    },\n    aboutXHours: {\n        one: \"omtrent en time\",\n        other: \"omtrent {{count}} timer\"\n    },\n    xHours: {\n        one: \"en time\",\n        other: \"{{count}} timer\"\n    },\n    xDays: {\n        one: \"en dag\",\n        other: \"{{count}} dager\"\n    },\n    aboutXWeeks: {\n        one: \"omtrent en uke\",\n        other: \"omtrent {{count}} uker\"\n    },\n    xWeeks: {\n        one: \"en uke\",\n        other: \"{{count}} uker\"\n    },\n    aboutXMonths: {\n        one: \"omtrent en måned\",\n        other: \"omtrent {{count}} måneder\"\n    },\n    xMonths: {\n        one: \"en måned\",\n        other: \"{{count}} måneder\"\n    },\n    aboutXYears: {\n        one: \"omtrent ett år\",\n        other: \"omtrent {{count}} år\"\n    },\n    xYears: {\n        one: \"ett år\",\n        other: \"{{count}} år\"\n    },\n    overXYears: {\n        one: \"over ett år\",\n        other: \"over {{count}} år\"\n    },\n    almostXYears: {\n        one: \"nesten ett år\",\n        other: \"nesten {{count}} år\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"om \" + result;\n        } else {\n            return result + \" siden\";\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE d. MMMM y\",\n    long: \"d. MMMM y\",\n    medium: \"d. MMM y\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"'kl'. HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'kl.' {{time}}\",\n    long: \"{{date}} 'kl.' {{time}}\",\n    medium: \"{{date}} {{time}}\",\n    short: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'forrige' eeee 'kl.' p\",\n    yesterday: \"'i går kl.' p\",\n    today: \"'i dag kl.' p\",\n    tomorrow: \"'i morgen kl.' p\",\n    nextWeek: \"EEEE 'kl.' p\",\n    other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9kYXRlLWZuc0A0LjEuMC9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvbG9jYWxlL25iL19saWIvZm9ybWF0UmVsYXRpdmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE1BQU1BLHVCQUF1QjtJQUMzQkMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLE9BQU87SUFDUEMsVUFBVTtJQUNWQyxVQUFVO0lBQ1ZDLE9BQU87QUFDVDtBQUVPLE1BQU1DLGlCQUFpQixDQUFDQyxPQUFPQyxPQUFPQyxXQUFXQyxXQUN0RFgsb0JBQW9CLENBQUNRLE1BQU0sQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9uYi9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGZvcm1hdFJlbGF0aXZlTG9jYWxlID0ge1xuICBsYXN0V2VlazogXCInZm9ycmlnZScgZWVlZSAna2wuJyBwXCIsXG4gIHllc3RlcmRheTogXCInaSBnw6VyIGtsLicgcFwiLFxuICB0b2RheTogXCInaSBkYWcga2wuJyBwXCIsXG4gIHRvbW9ycm93OiBcIidpIG1vcmdlbiBrbC4nIHBcIixcbiAgbmV4dFdlZWs6IFwiRUVFRSAna2wuJyBwXCIsXG4gIG90aGVyOiBcIlBcIixcbn07XG5cbmV4cG9ydCBjb25zdCBmb3JtYXRSZWxhdGl2ZSA9ICh0b2tlbiwgX2RhdGUsIF9iYXNlRGF0ZSwgX29wdGlvbnMpID0+XG4gIGZvcm1hdFJlbGF0aXZlTG9jYWxlW3Rva2VuXTtcbiJdLCJuYW1lcyI6WyJmb3JtYXRSZWxhdGl2ZUxvY2FsZSIsImxhc3RXZWVrIiwieWVzdGVyZGF5IiwidG9kYXkiLCJ0b21vcnJvdyIsIm5leHRXZWVrIiwib3RoZXIiLCJmb3JtYXRSZWxhdGl2ZSIsInRva2VuIiwiX2RhdGUiLCJfYmFzZURhdGUiLCJfb3B0aW9ucyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"f.Kr.\",\n        \"e.Kr.\"\n    ],\n    abbreviated: [\n        \"f.Kr.\",\n        \"e.Kr.\"\n    ],\n    wide: [\n        \"før Kristus\",\n        \"etter Kristus\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"Q1\",\n        \"Q2\",\n        \"Q3\",\n        \"Q4\"\n    ],\n    wide: [\n        \"1. kvartal\",\n        \"2. kvartal\",\n        \"3. kvartal\",\n        \"4. kvartal\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"J\",\n        \"F\",\n        \"M\",\n        \"A\",\n        \"M\",\n        \"J\",\n        \"J\",\n        \"A\",\n        \"S\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"jan.\",\n        \"feb.\",\n        \"mars\",\n        \"apr.\",\n        \"mai\",\n        \"juni\",\n        \"juli\",\n        \"aug.\",\n        \"sep.\",\n        \"okt.\",\n        \"nov.\",\n        \"des.\"\n    ],\n    wide: [\n        \"januar\",\n        \"februar\",\n        \"mars\",\n        \"april\",\n        \"mai\",\n        \"juni\",\n        \"juli\",\n        \"august\",\n        \"september\",\n        \"oktober\",\n        \"november\",\n        \"desember\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"S\",\n        \"M\",\n        \"T\",\n        \"O\",\n        \"T\",\n        \"F\",\n        \"L\"\n    ],\n    short: [\n        \"sø\",\n        \"ma\",\n        \"ti\",\n        \"on\",\n        \"to\",\n        \"fr\",\n        \"lø\"\n    ],\n    abbreviated: [\n        \"søn\",\n        \"man\",\n        \"tir\",\n        \"ons\",\n        \"tor\",\n        \"fre\",\n        \"lør\"\n    ],\n    wide: [\n        \"søndag\",\n        \"mandag\",\n        \"tirsdag\",\n        \"onsdag\",\n        \"torsdag\",\n        \"fredag\",\n        \"lørdag\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"a\",\n        pm: \"p\",\n        midnight: \"midnatt\",\n        noon: \"middag\",\n        morning: \"på morg.\",\n        afternoon: \"på etterm.\",\n        evening: \"på kvelden\",\n        night: \"på natten\"\n    },\n    abbreviated: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"midnatt\",\n        noon: \"middag\",\n        morning: \"på morg.\",\n        afternoon: \"på etterm.\",\n        evening: \"på kvelden\",\n        night: \"på natten\"\n    },\n    wide: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"midnatt\",\n        noon: \"middag\",\n        morning: \"på morgenen\",\n        afternoon: \"på ettermiddagen\",\n        evening: \"på kvelden\",\n        night: \"på natten\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)\\.?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(f\\.? ?Kr\\.?|fvt\\.?|e\\.? ?Kr\\.?|evt\\.?)/i,\n    abbreviated: /^(f\\.? ?Kr\\.?|fvt\\.?|e\\.? ?Kr\\.?|evt\\.?)/i,\n    wide: /^(før Kristus|før vår tid|etter Kristus|vår tid)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^f/i,\n        /^e/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^q[1234]/i,\n    wide: /^[1234](\\.)? kvartal/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmasond]/i,\n    abbreviated: /^(jan|feb|mars?|apr|mai|juni?|juli?|aug|sep|okt|nov|des)\\.?/i,\n    wide: /^(januar|februar|mars|april|mai|juni|juli|august|september|oktober|november|desember)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^ja/i,\n        /^f/i,\n        /^mar/i,\n        /^ap/i,\n        /^mai/i,\n        /^jun/i,\n        /^jul/i,\n        /^aug/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[smtofl]/i,\n    short: /^(sø|ma|ti|on|to|fr|lø)/i,\n    abbreviated: /^(søn|man|tir|ons|tor|fre|lør)/i,\n    wide: /^(søndag|mandag|tirsdag|onsdag|torsdag|fredag|lørdag)/i\n};\nconst parseDayPatterns = {\n    any: [\n        /^s/i,\n        /^m/i,\n        /^ti/i,\n        /^o/i,\n        /^to/i,\n        /^f/i,\n        /^l/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(midnatt|middag|(på) (morgenen|ettermiddagen|kvelden|natten)|[ap])/i,\n    any: /^([ap]\\.?\\s?m\\.?|midnatt|middag|(på) (morgenen|ettermiddagen|kvelden|natten))/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^a(\\.?\\s?m\\.?)?$/i,\n        pm: /^p(\\.?\\s?m\\.?)?$/i,\n        midnight: /^midn/i,\n        noon: /^midd/i,\n        morning: /morgen/i,\n        afternoon: /ettermiddag/i,\n        evening: /kveld/i,\n        night: /natt/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/nb/_lib/match.js\n"));

/***/ })

}]);