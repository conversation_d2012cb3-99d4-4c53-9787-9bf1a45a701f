"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_hu_js"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hu: () => (/* binding */ hu)\n/* harmony export */ });\n/* harmony import */ var _hu_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hu/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatDistance.js\");\n/* harmony import */ var _hu_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hu/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatLong.js\");\n/* harmony import */ var _hu_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hu/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatRelative.js\");\n/* harmony import */ var _hu_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hu/_lib/localize.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/localize.js\");\n/* harmony import */ var _hu_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hu/_lib/match.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Hungarian locale.\n * @language Hungarian\n * @iso-639-2 hun\n * <AUTHOR> Shpak [@pshpak](https://github.com/pshpak)\n * <AUTHOR> Pardo [@eduardopsll](https://github.com/eduardopsll)\n * <AUTHOR> Szepesi [@twodcube](https://github.com/twodcube)\n */ const hu = {\n    code: \"hu\",\n    formatDistance: _hu_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _hu_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _hu_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _hu_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _hu_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (hu);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatDistance.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatDistance.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst translations = {\n    about: \"körülbelül\",\n    over: \"több mint\",\n    almost: \"majdnem\",\n    lessthan: \"kevesebb mint\"\n};\nconst withoutSuffixes = {\n    xseconds: \" másodperc\",\n    halfaminute: \"fél perc\",\n    xminutes: \" perc\",\n    xhours: \" óra\",\n    xdays: \" nap\",\n    xweeks: \" hét\",\n    xmonths: \" hónap\",\n    xyears: \" év\"\n};\nconst withSuffixes = {\n    xseconds: {\n        \"-1\": \" másodperccel ezelőtt\",\n        1: \" másodperc múlva\",\n        0: \" másodperce\"\n    },\n    halfaminute: {\n        \"-1\": \"fél perccel ezelőtt\",\n        1: \"fél perc múlva\",\n        0: \"fél perce\"\n    },\n    xminutes: {\n        \"-1\": \" perccel ezelőtt\",\n        1: \" perc múlva\",\n        0: \" perce\"\n    },\n    xhours: {\n        \"-1\": \" órával ezelőtt\",\n        1: \" óra múlva\",\n        0: \" órája\"\n    },\n    xdays: {\n        \"-1\": \" nappal ezelőtt\",\n        1: \" nap múlva\",\n        0: \" napja\"\n    },\n    xweeks: {\n        \"-1\": \" héttel ezelőtt\",\n        1: \" hét múlva\",\n        0: \" hete\"\n    },\n    xmonths: {\n        \"-1\": \" hónappal ezelőtt\",\n        1: \" hónap múlva\",\n        0: \" hónapja\"\n    },\n    xyears: {\n        \"-1\": \" évvel ezelőtt\",\n        1: \" év múlva\",\n        0: \" éve\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    const adverb = token.match(/about|over|almost|lessthan/i);\n    const unit = adverb ? token.replace(adverb[0], \"\") : token;\n    const addSuffix = (options === null || options === void 0 ? void 0 : options.addSuffix) === true;\n    const key = unit.toLowerCase();\n    const comparison = (options === null || options === void 0 ? void 0 : options.comparison) || 0;\n    const translated = addSuffix ? withSuffixes[key][comparison] : withoutSuffixes[key];\n    let result = key === \"halfaminute\" ? translated : count + translated;\n    if (adverb) {\n        const adv = adverb[0].toLowerCase();\n        result = translations[adv] + \" \" + result;\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatLong.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatLong.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"y. MMMM d., EEEE\",\n    long: \"y. MMMM d.\",\n    medium: \"y. MMM d.\",\n    short: \"y. MM. dd.\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} {{time}}\",\n    long: \"{{date}} {{time}}\",\n    medium: \"{{date}} {{time}}\",\n    short: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatRelative.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatRelative.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst accusativeWeekdays = [\n    \"vasárnap\",\n    \"hétfőn\",\n    \"kedden\",\n    \"szerdán\",\n    \"csütörtökön\",\n    \"pénteken\",\n    \"szombaton\"\n];\nfunction week(isFuture) {\n    return (date)=>{\n        const weekday = accusativeWeekdays[date.getDay()];\n        const prefix = isFuture ? \"\" : \"'múlt' \";\n        return \"\".concat(prefix, \"'\").concat(weekday, \"' p'-kor'\");\n    };\n}\nconst formatRelativeLocale = {\n    lastWeek: week(false),\n    yesterday: \"'tegnap' p'-kor'\",\n    today: \"'ma' p'-kor'\",\n    tomorrow: \"'holnap' p'-kor'\",\n    nextWeek: week(true),\n    other: \"P\"\n};\nconst formatRelative = (token, date)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/localize.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/localize.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"ie.\",\n        \"isz.\"\n    ],\n    abbreviated: [\n        \"i. e.\",\n        \"i. sz.\"\n    ],\n    wide: [\n        \"Krisztus előtt\",\n        \"időszámításunk szerint\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1.\",\n        \"2.\",\n        \"3.\",\n        \"4.\"\n    ],\n    abbreviated: [\n        \"1. n.év\",\n        \"2. n.év\",\n        \"3. n.év\",\n        \"4. n.év\"\n    ],\n    wide: [\n        \"1. negyedév\",\n        \"2. negyedév\",\n        \"3. negyedév\",\n        \"4. negyedév\"\n    ]\n};\nconst formattingQuarterValues = {\n    narrow: [\n        \"I.\",\n        \"II.\",\n        \"III.\",\n        \"IV.\"\n    ],\n    abbreviated: [\n        \"I. n.év\",\n        \"II. n.év\",\n        \"III. n.év\",\n        \"IV. n.év\"\n    ],\n    wide: [\n        \"I. negyedév\",\n        \"II. negyedév\",\n        \"III. negyedév\",\n        \"IV. negyedév\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"J\",\n        \"F\",\n        \"M\",\n        \"Á\",\n        \"M\",\n        \"J\",\n        \"J\",\n        \"A\",\n        \"Sz\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"jan.\",\n        \"febr.\",\n        \"márc.\",\n        \"ápr.\",\n        \"máj.\",\n        \"jún.\",\n        \"júl.\",\n        \"aug.\",\n        \"szept.\",\n        \"okt.\",\n        \"nov.\",\n        \"dec.\"\n    ],\n    wide: [\n        \"január\",\n        \"február\",\n        \"március\",\n        \"április\",\n        \"május\",\n        \"június\",\n        \"július\",\n        \"augusztus\",\n        \"szeptember\",\n        \"október\",\n        \"november\",\n        \"december\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"V\",\n        \"H\",\n        \"K\",\n        \"Sz\",\n        \"Cs\",\n        \"P\",\n        \"Sz\"\n    ],\n    short: [\n        \"V\",\n        \"H\",\n        \"K\",\n        \"Sze\",\n        \"Cs\",\n        \"P\",\n        \"Szo\"\n    ],\n    abbreviated: [\n        \"V\",\n        \"H\",\n        \"K\",\n        \"Sze\",\n        \"Cs\",\n        \"P\",\n        \"Szo\"\n    ],\n    wide: [\n        \"vasárnap\",\n        \"hétfő\",\n        \"kedd\",\n        \"szerda\",\n        \"csütörtök\",\n        \"péntek\",\n        \"szombat\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"de.\",\n        pm: \"du.\",\n        midnight: \"éjfél\",\n        noon: \"dél\",\n        morning: \"reggel\",\n        afternoon: \"du.\",\n        evening: \"este\",\n        night: \"éjjel\"\n    },\n    abbreviated: {\n        am: \"de.\",\n        pm: \"du.\",\n        midnight: \"éjfél\",\n        noon: \"dél\",\n        morning: \"reggel\",\n        afternoon: \"du.\",\n        evening: \"este\",\n        night: \"éjjel\"\n    },\n    wide: {\n        am: \"de.\",\n        pm: \"du.\",\n        midnight: \"éjfél\",\n        noon: \"dél\",\n        morning: \"reggel\",\n        afternoon: \"délután\",\n        evening: \"este\",\n        night: \"éjjel\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1,\n        formattingValues: formattingQuarterValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/match.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/match.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)\\.?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(ie\\.|isz\\.)/i,\n    abbreviated: /^(i\\.\\s?e\\.?|b?\\s?c\\s?e|i\\.\\s?sz\\.?)/i,\n    wide: /^(Krisztus előtt|időszámításunk előtt|időszámításunk szerint|i\\. sz\\.)/i\n};\nconst parseEraPatterns = {\n    narrow: [\n        /ie/i,\n        /isz/i\n    ],\n    abbreviated: [\n        /^(i\\.?\\s?e\\.?|b\\s?ce)/i,\n        /^(i\\.?\\s?sz\\.?|c\\s?e)/i\n    ],\n    any: [\n        /előtt/i,\n        /(szerint|i. sz.)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]\\.?/i,\n    abbreviated: /^[1234]?\\.?\\s?n\\.év/i,\n    wide: /^([1234]|I|II|III|IV)?\\.?\\s?negyedév/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1|I$/i,\n        /2|II$/i,\n        /3|III/i,\n        /4|IV/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmaásond]|sz/i,\n    abbreviated: /^(jan\\.?|febr\\.?|márc\\.?|ápr\\.?|máj\\.?|jún\\.?|júl\\.?|aug\\.?|szept\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n    wide: /^(január|február|március|április|május|június|július|augusztus|szeptember|október|november|december)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a|á/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s|sz/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^ja/i,\n        /^f/i,\n        /^már/i,\n        /^áp/i,\n        /^máj/i,\n        /^jún/i,\n        /^júl/i,\n        /^au/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^([vhkpc]|sz|cs|sz)/i,\n    short: /^([vhkp]|sze|cs|szo)/i,\n    abbreviated: /^([vhkp]|sze|cs|szo)/i,\n    wide: /^(vasárnap|hétfő|kedd|szerda|csütörtök|péntek|szombat)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^v/i,\n        /^h/i,\n        /^k/i,\n        /^sz/i,\n        /^c/i,\n        /^p/i,\n        /^sz/i\n    ],\n    any: [\n        /^v/i,\n        /^h/i,\n        /^k/i,\n        /^sze/i,\n        /^c/i,\n        /^p/i,\n        /^szo/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    any: /^((de|du)\\.?|éjfél|délután|dél|reggel|este|éjjel)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^de\\.?/i,\n        pm: /^du\\.?/i,\n        midnight: /^éjf/i,\n        noon: /^dé/i,\n        morning: /reg/i,\n        afternoon: /^délu\\.?/i,\n        evening: /es/i,\n        night: /éjj/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/match.js\n"));

/***/ })

}]);