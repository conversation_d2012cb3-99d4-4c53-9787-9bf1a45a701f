/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/(frontend)/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.8 | MIT License | https://tailwindcss.com */
@layer properties;
.\@container\/card-header {
  container-type: inline-size;
  container-name: card-header;
}
.pointer-events-none {
  pointer-events: none;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.static {
  position: static;
}
.sticky {
  position: sticky;
}
.top-\[1px\] {
  top: 1px;
}
.top-\[60\%\] {
  top: 60%;
}
.top-full {
  top: 100%;
}
.left-1\/2 {
  left: calc(1/2 * 100%);
}
.isolate {
  isolation: isolate;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-50 {
  z-index: 50;
}
.z-\[1\] {
  z-index: 1;
}
.col-start-2 {
  grid-column-start: 2;
}
.row-span-2 {
  grid-row: span 2 / span 2;
}
.row-start-1 {
  grid-row-start: 1;
}
.container {
  width: 100%;
}
.mx-auto {
  margin-inline: auto;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block {
  display: block;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.inline-flex {
  display: inline-flex;
}
.field-sizing-content {
  field-sizing: content;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.size-full {
  width: 100%;
  height: 100%;
}
.h-\[var\(--radix-navigation-menu-viewport-height\)\] {
  height: var(--radix-navigation-menu-viewport-height);
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.max-h-\(--radix-select-content-available-height\) {
  max-height: var(--radix-select-content-available-height);
}
.min-h-screen {
  min-height: 100vh;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-max {
  width: -moz-max-content;
  width: max-content;
}
.max-w-max {
  max-width: -moz-max-content;
  max-width: max-content;
}
.max-w-none {
  max-width: none;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.flex-1 {
  flex: 1;
}
.shrink-0 {
  flex-shrink: 0;
}
.origin-\(--radix-select-content-transform-origin\) {
  transform-origin: var(--radix-select-content-transform-origin);
}
.-translate-x-1\/2 {
  --tw-translate-x: calc(calc(1/2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.rotate-45 {
  rotate: 45deg;
}
.transform {
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
}
.cursor-default {
  cursor: default;
}
.cursor-pointer {
  cursor: pointer;
}
.list-none {
  list-style-type: none;
}
.auto-rows-min {
  grid-auto-rows: min-content;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-rows-\[auto_auto\] {
  grid-template-rows: auto auto;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-start {
  align-items: flex-start;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.self-start {
  align-self: flex-start;
}
.justify-self-end {
  justify-self: flex-end;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.rounded-full {
  border-radius: calc(infinity * 1px);
}
.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-2 {
  border-style: var(--tw-border-style);
  border-width: 2px;
}
.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}
.border-transparent {
  border-color: transparent;
}
.bg-transparent {
  background-color: transparent;
}
.bg-gradient-to-br {
  --tw-gradient-position: to bottom right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.fill-current {
  fill: currentcolor;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.text-center {
  text-align: center;
}
.leading-none {
  --tw-leading: 1;
  line-height: 1;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.opacity-0 {
  opacity: 0%;
}
.opacity-50 {
  opacity: 50%;
}
.ring {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.outline-hidden {
  --tw-outline-style: none;
  outline-style: none;
  @media (forced-colors: active) {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }
}
.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.filter {
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.transition {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-\[color\,box-shadow\] {
  transition-property: color,box-shadow;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.duration-300 {
  --tw-duration: 300ms;
  transition-duration: 300ms;
}
.outline-none {
  --tw-outline-style: none;
  outline-style: none;
}
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
       user-select: none;
}
.group-hover\:scale-105 {
  &:is(:where(.group):hover *) {
    @media (hover: hover) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
}
.group-hover\:opacity-100 {
  &:is(:where(.group):hover *) {
    @media (hover: hover) {
      opacity: 100%;
    }
  }
}
.group-data-\[state\=open\]\:rotate-180 {
  &:is(:where(.group)[data-state="open"] *) {
    rotate: 180deg;
  }
}
.group-data-\[viewport\=false\]\/navigation-menu\:top-full {
  &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    top: 100%;
  }
}
.group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden {
  &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    overflow: hidden;
  }
}
.group-data-\[viewport\=false\]\/navigation-menu\:border {
  &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
}
.group-data-\[viewport\=false\]\/navigation-menu\:duration-200 {
  &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
}
.file\:inline-flex {
  &::file-selector-button {
    display: inline-flex;
  }
}
.file\:border-0 {
  &::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
}
.file\:bg-transparent {
  &::file-selector-button {
    background-color: transparent;
  }
}
.hover\:underline {
  &:hover {
    @media (hover: hover) {
      text-decoration-line: underline;
    }
  }
}
.hover\:opacity-75 {
  &:hover {
    @media (hover: hover) {
      opacity: 75%;
    }
  }
}
.focus-visible\:ring-\[3px\] {
  &:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
.focus-visible\:outline-1 {
  &:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
}
.disabled\:pointer-events-none {
  &:disabled {
    pointer-events: none;
  }
}
.disabled\:cursor-not-allowed {
  &:disabled {
    cursor: not-allowed;
  }
}
.disabled\:opacity-50 {
  &:disabled {
    opacity: 50%;
  }
}
.has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\] {
  &:has(*[data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }
}
.data-\[disabled\]\:pointer-events-none {
  &[data-disabled] {
    pointer-events: none;
  }
}
.data-\[disabled\]\:opacity-50 {
  &[data-disabled] {
    opacity: 50%;
  }
}
.data-\[orientation\=horizontal\]\:h-px {
  &[data-orientation="horizontal"] {
    height: 1px;
  }
}
.data-\[orientation\=horizontal\]\:w-full {
  &[data-orientation="horizontal"] {
    width: 100%;
  }
}
.data-\[orientation\=vertical\]\:h-full {
  &[data-orientation="vertical"] {
    height: 100%;
  }
}
.data-\[orientation\=vertical\]\:w-px {
  &[data-orientation="vertical"] {
    width: 1px;
  }
}
.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 {
  :is(& *) {
    &[data-slot="navigation-menu-link"] {
      &:focus {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
}
.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none {
  :is(& *) {
    &[data-slot="navigation-menu-link"] {
      &:focus {
        --tw-outline-style: none;
        outline-style: none;
      }
    }
  }
}
.\*\:data-\[slot\=select-value\]\:line-clamp-1 {
  :is(& > *) {
    &[data-slot="select-value"] {
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }
}
.\*\:data-\[slot\=select-value\]\:flex {
  :is(& > *) {
    &[data-slot="select-value"] {
      display: flex;
    }
  }
}
.\*\:data-\[slot\=select-value\]\:items-center {
  :is(& > *) {
    &[data-slot="select-value"] {
      align-items: center;
    }
  }
}
.\[\&_svg\]\:pointer-events-none {
  & svg {
    pointer-events: none;
  }
}
.\[\&_svg\]\:shrink-0 {
  & svg {
    flex-shrink: 0;
  }
}
.\*\:\[span\]\:last\:flex {
  :is(& > *) {
    &:is(span) {
      &:last-child {
        display: flex;
      }
    }
  }
}
.\*\:\[span\]\:last\:items-center {
  :is(& > *) {
    &:is(span) {
      &:last-child {
        align-items: center;
      }
    }
  }
}
.\[\&\>svg\]\:pointer-events-none {
  &>svg {
    pointer-events: none;
  }
}
* {
  box-sizing: border-box;
}
html, body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  margin: 0;
  padding: 0;
  background-color: white;
  color: #1f2937;
}
.test-tailwind {
  background-color: #3b82f6 !important;
  color: white !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  font-size: 1.25rem !important;
  font-weight: bold !important;
}
.text-gradient-gold {
  background: linear-gradient(135deg, #ca8a04 0%, #facc15 50%, #eab308 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

