[{"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx": "1", "/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx": "2", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/[[...segments]]/not-found.tsx": "3", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/[[...segments]]/page.tsx": "4", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/importMap.js": "5", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/[...slug]/route.ts": "6", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/graphql/route.ts": "7", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/graphql-playground/route.ts": "8", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/layout.tsx": "9", "/Users/<USER>/lalit don/alokita-candles/src/app/my-route/route.ts": "10", "/Users/<USER>/lalit don/alokita-candles/src/collections/Categories.ts": "11", "/Users/<USER>/lalit don/alokita-candles/src/collections/Media.ts": "12", "/Users/<USER>/lalit don/alokita-candles/src/collections/Orders.ts": "13", "/Users/<USER>/lalit don/alokita-candles/src/collections/Pages.ts": "14", "/Users/<USER>/lalit don/alokita-candles/src/collections/Products.ts": "15", "/Users/<USER>/lalit don/alokita-candles/src/collections/Users.ts": "16", "/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx": "17", "/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx": "18", "/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx": "19", "/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx": "20", "/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx": "21", "/Users/<USER>/lalit don/alokita-candles/src/components/ProductDetail.tsx": "22", "/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx": "23", "/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx": "24", "/Users/<USER>/lalit don/alokita-candles/src/components/ui/badge.tsx": "25", "/Users/<USER>/lalit don/alokita-candles/src/components/ui/button.tsx": "26", "/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx": "27", "/Users/<USER>/lalit don/alokita-candles/src/lib/utils.ts": "28", "/Users/<USER>/lalit don/alokita-candles/src/payload-types.ts": "29", "/Users/<USER>/lalit don/alokita-candles/src/payload.config.ts": "30", "/Users/<USER>/lalit don/alokita-candles/src/seed/index.ts": "31"}, {"size": 634, "mtime": 1749408848895, "results": "32", "hashOfConfig": "33"}, {"size": 1849, "mtime": 1749407981457, "results": "34", "hashOfConfig": "33"}, {"size": 731, "mtime": 1749243282000, "results": "35", "hashOfConfig": "33"}, {"size": 715, "mtime": 1749243282000, "results": "36", "hashOfConfig": "33"}, {"size": 28, "mtime": 1749243282000, "results": "37", "hashOfConfig": "38"}, {"size": 550, "mtime": 1749243282000, "results": "39", "hashOfConfig": "33"}, {"size": 315, "mtime": 1749243282000, "results": "40", "hashOfConfig": "33"}, {"size": 305, "mtime": 1749243282000, "results": "41", "hashOfConfig": "33"}, {"size": 810, "mtime": 1749243282000, "results": "42", "hashOfConfig": "33"}, {"size": 287, "mtime": 1749243282000, "results": "43", "hashOfConfig": "33"}, {"size": 1270, "mtime": 1749407802990, "results": "44", "hashOfConfig": "33"}, {"size": 1317, "mtime": 1749407848908, "results": "45", "hashOfConfig": "33"}, {"size": 3858, "mtime": 1749407817692, "results": "46", "hashOfConfig": "33"}, {"size": 5457, "mtime": 1749407835218, "results": "47", "hashOfConfig": "33"}, {"size": 3571, "mtime": 1749407794107, "results": "48", "hashOfConfig": "33"}, {"size": 244, "mtime": 1749243282000, "results": "49", "hashOfConfig": "33"}, {"size": 4707, "mtime": 1749407963064, "results": "50", "hashOfConfig": "33"}, {"size": 6993, "mtime": 1749408839586, "results": "51", "hashOfConfig": "33"}, {"size": 2718, "mtime": 1749407909757, "results": "52", "hashOfConfig": "33"}, {"size": 4700, "mtime": 1749408809121, "results": "53", "hashOfConfig": "33"}, {"size": 2887, "mtime": 1749407923286, "results": "54", "hashOfConfig": "33"}, {"size": 7148, "mtime": 1749408901129, "results": "55", "hashOfConfig": "33"}, {"size": 2590, "mtime": 1749411801622, "results": "56", "hashOfConfig": "33"}, {"size": 3247, "mtime": 1749407945357, "results": "57", "hashOfConfig": "33"}, {"size": 1289, "mtime": 1749407897516, "results": "58", "hashOfConfig": "33"}, {"size": 1945, "mtime": 1749407882298, "results": "59", "hashOfConfig": "33"}, {"size": 1877, "mtime": 1749407890510, "results": "60", "hashOfConfig": "33"}, {"size": 166, "mtime": 1749407560121, "results": "61", "hashOfConfig": "33"}, {"size": 16209, "mtime": 1749408873353, "results": "62", "hashOfConfig": "33"}, {"size": 1320, "mtime": 1749407867419, "results": "63", "hashOfConfig": "33"}, {"size": 8669, "mtime": 1749412330180, "results": "64", "hashOfConfig": "33"}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8wjptm", {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10oo1j2", {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/[[...segments]]/not-found.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/[[...segments]]/page.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/importMap.js", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/[...slug]/route.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/graphql/route.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/graphql-playground/route.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/layout.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/my-route/route.ts", ["158", "159"], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Categories.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Media.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Orders.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Pages.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Products.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Users.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx", ["160", "161"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx", ["162"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx", ["163"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx", ["164", "165", "166", "167", "168"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx", ["169", "170", "171"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ProductDetail.tsx", ["172", "173", "174", "175"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx", ["176"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx", ["177", "178", "179"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ui/badge.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ui/button.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/lib/utils.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/payload-types.ts", [], ["180", "181", "182", "183", "184"], "/Users/<USER>/lalit don/alokita-candles/src/payload.config.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/seed/index.ts", ["185", "186", "187"], [], {"ruleId": "188", "severity": 1, "message": "189", "line": 4, "column": 27, "nodeType": null, "messageId": "190", "endLine": 4, "endColumn": 34}, {"ruleId": "188", "severity": 1, "message": "191", "line": 5, "column": 9, "nodeType": null, "messageId": "190", "endLine": 5, "endColumn": 16}, {"ruleId": "192", "severity": 1, "message": "193", "line": 39, "column": 83, "nodeType": "194", "messageId": "195", "suggestions": "196"}, {"ruleId": "197", "severity": 1, "message": "198", "line": 57, "column": 15, "nodeType": "199", "endLine": 61, "endColumn": 17}, {"ruleId": "200", "severity": 1, "message": "201", "line": 46, "column": 17, "nodeType": "199", "endLine": 46, "endColumn": 93}, {"ruleId": "197", "severity": 1, "message": "198", "line": 24, "column": 11, "nodeType": "199", "endLine": 28, "endColumn": 13}, {"ruleId": "200", "severity": 1, "message": "201", "line": 15, "column": 13, "nodeType": "199", "endLine": 15, "endColumn": 75}, {"ruleId": "200", "severity": 1, "message": "201", "line": 23, "column": 15, "nodeType": "199", "endLine": 26, "endColumn": 16}, {"ruleId": "200", "severity": 1, "message": "202", "line": 62, "column": 15, "nodeType": "199", "endLine": 62, "endColumn": 32}, {"ruleId": "200", "severity": 1, "message": "201", "line": 87, "column": 15, "nodeType": "199", "endLine": 90, "endColumn": 16}, {"ruleId": "200", "severity": 1, "message": "202", "line": 122, "column": 19, "nodeType": "199", "endLine": 122, "endColumn": 36}, {"ruleId": "188", "severity": 1, "message": "203", "line": 2, "column": 29, "nodeType": null, "messageId": "190", "endLine": 2, "endColumn": 39}, {"ruleId": "188", "severity": 1, "message": "204", "line": 21, "column": 3, "nodeType": null, "messageId": "190", "endLine": 21, "endColumn": 5}, {"ruleId": "197", "severity": 1, "message": "198", "line": 34, "column": 11, "nodeType": "199", "endLine": 38, "endColumn": 13}, {"ruleId": "188", "severity": 1, "message": "205", "line": 4, "column": 16, "nodeType": null, "messageId": "190", "endLine": 4, "endColumn": 27}, {"ruleId": "206", "severity": 1, "message": "207", "line": 10, "column": 18, "nodeType": "208", "messageId": "209", "endLine": 10, "endColumn": 21, "suggestions": "210"}, {"ruleId": "197", "severity": 1, "message": "198", "line": 51, "column": 15, "nodeType": "199", "endLine": 55, "endColumn": 17}, {"ruleId": "197", "severity": 1, "message": "198", "line": 68, "column": 19, "nodeType": "199", "endLine": 72, "endColumn": 21}, {"ruleId": "188", "severity": 1, "message": "211", "line": 5, "column": 11, "nodeType": null, "messageId": "190", "endLine": 5, "endColumn": 18}, {"ruleId": "197", "severity": 1, "message": "198", "line": 61, "column": 21, "nodeType": "199", "endLine": 65, "endColumn": 23}, {"ruleId": "192", "severity": 1, "message": "212", "line": 85, "column": 19, "nodeType": "194", "messageId": "195", "suggestions": "213"}, {"ruleId": "192", "severity": 1, "message": "212", "line": 85, "column": 40, "nodeType": "194", "messageId": "195", "suggestions": "214"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 68, "column": 11, "nodeType": "217", "messageId": "218", "endLine": 68, "endColumn": 13, "suggestions": "219", "suppressions": "220"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 80, "column": 21, "nodeType": "217", "messageId": "218", "endLine": 80, "endColumn": 23, "suggestions": "221", "suppressions": "222"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 95, "column": 12, "nodeType": "217", "messageId": "218", "endLine": 95, "endColumn": 14, "suggestions": "223", "suppressions": "224"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 96, "column": 18, "nodeType": "217", "messageId": "218", "endLine": 96, "endColumn": 20, "suggestions": "225", "suppressions": "226"}, {"ruleId": "215", "severity": 1, "message": "227", "line": 715, "column": 20, "nodeType": "228", "messageId": "229", "endLine": 715, "endColumn": 34, "suggestions": "230", "suppressions": "231"}, {"ruleId": "188", "severity": 1, "message": "232", "line": 205, "column": 13, "nodeType": null, "messageId": "190", "endLine": 205, "endColumn": 20}, {"ruleId": "188", "severity": 1, "message": "233", "line": 213, "column": 11, "nodeType": null, "messageId": "190", "endLine": 213, "endColumn": 19}, {"ruleId": "188", "severity": 1, "message": "234", "line": 233, "column": 11, "nodeType": null, "messageId": "190", "endLine": 233, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'request' is defined but never used. Allowed unused args must match /^_/u.", "unusedVar", "'payload' is assigned a value but never used. Allowed unused vars must match /^_/u.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["235", "236", "237", "238"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/admin/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'CardFooter' is defined but never used. Allowed unused vars must match /^_/u.", "'id' is defined but never used. Allowed unused args must match /^_/u.", "'CardContent' is defined but never used. Allowed unused vars must match /^_/u.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["239", "240"], "'Product' is defined but never used. Allowed unused vars must match /^_/u.", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["241", "242", "243", "244"], ["245", "246", "247", "248"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["249", "250"], ["251"], ["252", "253"], ["254"], ["255", "256"], ["257"], ["258", "259"], ["260"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["261"], ["262"], "'created' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'homepage' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'aboutPage' is assigned a value but never used. Allowed unused vars must match /^_/u.", {"messageId": "263", "data": "264", "fix": "265", "desc": "266"}, {"messageId": "263", "data": "267", "fix": "268", "desc": "269"}, {"messageId": "263", "data": "270", "fix": "271", "desc": "272"}, {"messageId": "263", "data": "273", "fix": "274", "desc": "275"}, {"messageId": "276", "fix": "277", "desc": "278"}, {"messageId": "279", "fix": "280", "desc": "281"}, {"messageId": "263", "data": "282", "fix": "283", "desc": "284"}, {"messageId": "263", "data": "285", "fix": "286", "desc": "287"}, {"messageId": "263", "data": "288", "fix": "289", "desc": "290"}, {"messageId": "263", "data": "291", "fix": "292", "desc": "293"}, {"messageId": "263", "data": "294", "fix": "295", "desc": "284"}, {"messageId": "263", "data": "296", "fix": "297", "desc": "287"}, {"messageId": "263", "data": "298", "fix": "299", "desc": "290"}, {"messageId": "263", "data": "300", "fix": "301", "desc": "293"}, {"messageId": "302", "data": "303", "fix": "304", "desc": "305"}, {"messageId": "302", "data": "306", "fix": "307", "desc": "308"}, {"kind": "309", "justification": "310"}, {"messageId": "302", "data": "311", "fix": "312", "desc": "305"}, {"messageId": "302", "data": "313", "fix": "314", "desc": "308"}, {"kind": "309", "justification": "310"}, {"messageId": "302", "data": "315", "fix": "316", "desc": "305"}, {"messageId": "302", "data": "317", "fix": "318", "desc": "308"}, {"kind": "309", "justification": "310"}, {"messageId": "302", "data": "319", "fix": "320", "desc": "305"}, {"messageId": "302", "data": "321", "fix": "322", "desc": "308"}, {"kind": "309", "justification": "310"}, {"messageId": "323", "fix": "324", "desc": "325"}, {"kind": "309", "justification": "310"}, "replaceWithAlt", {"alt": "326"}, {"range": "327", "text": "328"}, "Replace with `&apos;`.", {"alt": "329"}, {"range": "330", "text": "331"}, "Replace with `&lsquo;`.", {"alt": "332"}, {"range": "333", "text": "334"}, "Replace with `&#39;`.", {"alt": "335"}, {"range": "336", "text": "337"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "338", "text": "339"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "340", "text": "341"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "342"}, {"range": "343", "text": "344"}, "Replace with `&quot;`.", {"alt": "345"}, {"range": "346", "text": "347"}, "Replace with `&ldquo;`.", {"alt": "348"}, {"range": "349", "text": "350"}, "Replace with `&#34;`.", {"alt": "351"}, {"range": "352", "text": "353"}, "Replace with `&rdquo;`.", {"alt": "342"}, {"range": "354", "text": "355"}, {"alt": "345"}, {"range": "356", "text": "357"}, {"alt": "348"}, {"range": "358", "text": "359"}, {"alt": "351"}, {"range": "360", "text": "361"}, "replaceEmptyObjectType", {"replacement": "362"}, {"range": "363", "text": "362"}, "Replace `{}` with `object`.", {"replacement": "339"}, {"range": "364", "text": "339"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "362"}, {"range": "365", "text": "362"}, {"replacement": "339"}, {"range": "366", "text": "339"}, {"replacement": "362"}, {"range": "367", "text": "362"}, {"replacement": "339"}, {"range": "368", "text": "339"}, {"replacement": "362"}, {"range": "369", "text": "362"}, {"replacement": "339"}, {"range": "370", "text": "339"}, "replaceEmptyInterfaceWithSuper", {"range": "371", "text": "372"}, "Replace empty interface with a type alias.", "&apos;", [1481, 1702], "\n                Based in the heart of Ahmedabad, we draw inspiration from the city&apos;s rich textile heritage \n                and vibrant colors, translating these elements into fragrances that tell a story.\n              ", "&lsquo;", [1481, 1702], "\n                Based in the heart of Ahmedabad, we draw inspiration from the city&lsquo;s rich textile heritage \n                and vibrant colors, translating these elements into fragrances that tell a story.\n              ", "&#39;", [1481, 1702], "\n                Based in the heart of Ahmedabad, we draw inspiration from the city&#39;s rich textile heritage \n                and vibrant colors, translating these elements into fragrances that tell a story.\n              ", "&rsquo;", [1481, 1702], "\n                Based in the heart of Ahmedabad, we draw inspiration from the city&rsquo;s rich textile heritage \n                and vibrant colors, translating these elements into fragrances that tell a story.\n              ", [271, 274], "unknown", [271, 274], "never", "&quot;", [3063, 3083], "\n                  &quot;", "&ldquo;", [3063, 3083], "\n                  &ldquo;", "&#34;", [3063, 3083], "\n                  &#34;", "&rdquo;", [3063, 3083], "\n                  &rdquo;", [3103, 3121], "&quot;\n                ", [3103, 3121], "&ldquo;\n                ", [3103, 3121], "&#34;\n                ", [3103, 3121], "&rdquo;\n                ", "object", [1506, 1508], [1506, 1508], [1817, 1819], [1817, 1819], [2522, 2524], [2522, 2524], [2543, 2545], [2543, 2545], [16165, 16207], "type GeneratedTypes = Config"]