[{"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx": "1", "/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx": "2", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/[[...segments]]/not-found.tsx": "3", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/[[...segments]]/page.tsx": "4", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/importMap.js": "5", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/[...slug]/route.ts": "6", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/graphql/route.ts": "7", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/graphql-playground/route.ts": "8", "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/layout.tsx": "9", "/Users/<USER>/lalit don/alokita-candles/src/app/my-route/route.ts": "10", "/Users/<USER>/lalit don/alokita-candles/src/collections/Categories.ts": "11", "/Users/<USER>/lalit don/alokita-candles/src/collections/Media.ts": "12", "/Users/<USER>/lalit don/alokita-candles/src/collections/Orders.ts": "13", "/Users/<USER>/lalit don/alokita-candles/src/collections/Pages.ts": "14", "/Users/<USER>/lalit don/alokita-candles/src/collections/Products.ts": "15", "/Users/<USER>/lalit don/alokita-candles/src/collections/Users.ts": "16", "/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx": "17", "/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx": "18", "/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx": "19", "/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx": "20", "/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx": "21", "/Users/<USER>/lalit don/alokita-candles/src/components/ProductDetail.tsx": "22", "/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx": "23", "/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx": "24", "/Users/<USER>/lalit don/alokita-candles/src/components/ui/badge.tsx": "25", "/Users/<USER>/lalit don/alokita-candles/src/components/ui/button.tsx": "26", "/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx": "27", "/Users/<USER>/lalit don/alokita-candles/src/lib/utils.ts": "28", "/Users/<USER>/lalit don/alokita-candles/src/payload-types.ts": "29", "/Users/<USER>/lalit don/alokita-candles/src/payload.config.ts": "30", "/Users/<USER>/lalit don/alokita-candles/src/seed/index.ts": "31"}, {"size": 634, "mtime": 1749408848895, "results": "32", "hashOfConfig": "33"}, {"size": 1849, "mtime": 1749407981457, "results": "34", "hashOfConfig": "33"}, {"size": 731, "mtime": 1749243282000, "results": "35", "hashOfConfig": "33"}, {"size": 715, "mtime": 1749243282000, "results": "36", "hashOfConfig": "33"}, {"size": 28, "mtime": 1749243282000, "results": "37", "hashOfConfig": "38"}, {"size": 550, "mtime": 1749243282000, "results": "39", "hashOfConfig": "33"}, {"size": 315, "mtime": 1749243282000, "results": "40", "hashOfConfig": "33"}, {"size": 305, "mtime": 1749243282000, "results": "41", "hashOfConfig": "33"}, {"size": 810, "mtime": 1749243282000, "results": "42", "hashOfConfig": "33"}, {"size": 287, "mtime": 1749243282000, "results": "43", "hashOfConfig": "33"}, {"size": 1270, "mtime": 1749407802990, "results": "44", "hashOfConfig": "33"}, {"size": 1317, "mtime": 1749407848908, "results": "45", "hashOfConfig": "33"}, {"size": 3858, "mtime": 1749407817692, "results": "46", "hashOfConfig": "33"}, {"size": 5457, "mtime": 1749407835218, "results": "47", "hashOfConfig": "33"}, {"size": 3571, "mtime": 1749407794107, "results": "48", "hashOfConfig": "33"}, {"size": 244, "mtime": 1749243282000, "results": "49", "hashOfConfig": "33"}, {"size": 4707, "mtime": 1749407963064, "results": "50", "hashOfConfig": "33"}, {"size": 6993, "mtime": 1749408839586, "results": "51", "hashOfConfig": "33"}, {"size": 2718, "mtime": 1749407909757, "results": "52", "hashOfConfig": "33"}, {"size": 4700, "mtime": 1749408809121, "results": "53", "hashOfConfig": "33"}, {"size": 2887, "mtime": 1749407923286, "results": "54", "hashOfConfig": "33"}, {"size": 7148, "mtime": 1749408901129, "results": "55", "hashOfConfig": "33"}, {"size": 1823, "mtime": 1749407931805, "results": "56", "hashOfConfig": "33"}, {"size": 3247, "mtime": 1749407945357, "results": "57", "hashOfConfig": "33"}, {"size": 1289, "mtime": 1749407897516, "results": "58", "hashOfConfig": "33"}, {"size": 1945, "mtime": 1749407882298, "results": "59", "hashOfConfig": "33"}, {"size": 1877, "mtime": 1749407890510, "results": "60", "hashOfConfig": "33"}, {"size": 166, "mtime": 1749407560121, "results": "61", "hashOfConfig": "33"}, {"size": 16209, "mtime": 1749408873353, "results": "62", "hashOfConfig": "33"}, {"size": 1320, "mtime": 1749407867419, "results": "63", "hashOfConfig": "33"}, {"size": 7850, "mtime": 1749408012382, "results": "64", "hashOfConfig": "33"}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8wjptm", {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10oo1j2", {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/[[...segments]]/not-found.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/[[...segments]]/page.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/admin/importMap.js", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/[...slug]/route.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/graphql/route.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/api/graphql-playground/route.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/(payload)/layout.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/app/my-route/route.ts", ["158", "159"], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Categories.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Media.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Orders.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Pages.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Products.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/collections/Users.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx", ["160", "161"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx", ["162"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx", ["163"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx", ["164", "165", "166", "167", "168"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx", ["169", "170", "171"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ProductDetail.tsx", ["172", "173", "174", "175"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx", ["176", "177", "178"], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ui/badge.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ui/button.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx", [], [], "/Users/<USER>/lalit don/alokita-candles/src/lib/utils.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/payload-types.ts", [], ["179", "180", "181", "182", "183"], "/Users/<USER>/lalit don/alokita-candles/src/payload.config.ts", [], [], "/Users/<USER>/lalit don/alokita-candles/src/seed/index.ts", ["184", "185", "186"], [], {"ruleId": "187", "severity": 1, "message": "188", "line": 4, "column": 27, "nodeType": null, "messageId": "189", "endLine": 4, "endColumn": 34}, {"ruleId": "187", "severity": 1, "message": "190", "line": 5, "column": 9, "nodeType": null, "messageId": "189", "endLine": 5, "endColumn": 16}, {"ruleId": "191", "severity": 1, "message": "192", "line": 39, "column": 83, "nodeType": "193", "messageId": "194", "suggestions": "195"}, {"ruleId": "196", "severity": 1, "message": "197", "line": 57, "column": 15, "nodeType": "198", "endLine": 61, "endColumn": 17}, {"ruleId": "199", "severity": 1, "message": "200", "line": 46, "column": 17, "nodeType": "198", "endLine": 46, "endColumn": 93}, {"ruleId": "196", "severity": 1, "message": "197", "line": 24, "column": 11, "nodeType": "198", "endLine": 28, "endColumn": 13}, {"ruleId": "199", "severity": 1, "message": "200", "line": 15, "column": 13, "nodeType": "198", "endLine": 15, "endColumn": 75}, {"ruleId": "199", "severity": 1, "message": "200", "line": 23, "column": 15, "nodeType": "198", "endLine": 26, "endColumn": 16}, {"ruleId": "199", "severity": 1, "message": "201", "line": 62, "column": 15, "nodeType": "198", "endLine": 62, "endColumn": 32}, {"ruleId": "199", "severity": 1, "message": "200", "line": 87, "column": 15, "nodeType": "198", "endLine": 90, "endColumn": 16}, {"ruleId": "199", "severity": 1, "message": "201", "line": 122, "column": 19, "nodeType": "198", "endLine": 122, "endColumn": 36}, {"ruleId": "187", "severity": 1, "message": "202", "line": 2, "column": 29, "nodeType": null, "messageId": "189", "endLine": 2, "endColumn": 39}, {"ruleId": "187", "severity": 1, "message": "203", "line": 21, "column": 3, "nodeType": null, "messageId": "189", "endLine": 21, "endColumn": 5}, {"ruleId": "196", "severity": 1, "message": "197", "line": 34, "column": 11, "nodeType": "198", "endLine": 38, "endColumn": 13}, {"ruleId": "187", "severity": 1, "message": "204", "line": 4, "column": 16, "nodeType": null, "messageId": "189", "endLine": 4, "endColumn": 27}, {"ruleId": "205", "severity": 1, "message": "206", "line": 10, "column": 18, "nodeType": "207", "messageId": "208", "endLine": 10, "endColumn": 21, "suggestions": "209"}, {"ruleId": "196", "severity": 1, "message": "197", "line": 51, "column": 15, "nodeType": "198", "endLine": 55, "endColumn": 17}, {"ruleId": "196", "severity": 1, "message": "197", "line": 68, "column": 19, "nodeType": "198", "endLine": 72, "endColumn": 21}, {"ruleId": "196", "severity": 1, "message": "197", "line": 61, "column": 21, "nodeType": "198", "endLine": 65, "endColumn": 23}, {"ruleId": "191", "severity": 1, "message": "210", "line": 85, "column": 19, "nodeType": "193", "messageId": "194", "suggestions": "211"}, {"ruleId": "191", "severity": 1, "message": "210", "line": 85, "column": 40, "nodeType": "193", "messageId": "194", "suggestions": "212"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 68, "column": 11, "nodeType": "215", "messageId": "216", "endLine": 68, "endColumn": 13, "suggestions": "217", "suppressions": "218"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 80, "column": 21, "nodeType": "215", "messageId": "216", "endLine": 80, "endColumn": 23, "suggestions": "219", "suppressions": "220"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 95, "column": 12, "nodeType": "215", "messageId": "216", "endLine": 95, "endColumn": 14, "suggestions": "221", "suppressions": "222"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 96, "column": 18, "nodeType": "215", "messageId": "216", "endLine": 96, "endColumn": 20, "suggestions": "223", "suppressions": "224"}, {"ruleId": "213", "severity": 1, "message": "225", "line": 715, "column": 20, "nodeType": "226", "messageId": "227", "endLine": 715, "endColumn": 34, "suggestions": "228", "suppressions": "229"}, {"ruleId": "187", "severity": 1, "message": "230", "line": 177, "column": 13, "nodeType": null, "messageId": "189", "endLine": 177, "endColumn": 20}, {"ruleId": "187", "severity": 1, "message": "231", "line": 185, "column": 11, "nodeType": null, "messageId": "189", "endLine": 185, "endColumn": 19}, {"ruleId": "187", "severity": 1, "message": "232", "line": 205, "column": 11, "nodeType": null, "messageId": "189", "endLine": 205, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'request' is defined but never used. Allowed unused args must match /^_/u.", "unusedVar", "'payload' is assigned a value but never used. Allowed unused vars must match /^_/u.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["233", "234", "235", "236"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/admin/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'CardFooter' is defined but never used. Allowed unused vars must match /^_/u.", "'id' is defined but never used. Allowed unused args must match /^_/u.", "'CardContent' is defined but never used. Allowed unused vars must match /^_/u.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["237", "238"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["239", "240", "241", "242"], ["243", "244", "245", "246"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["247", "248"], ["249"], ["250", "251"], ["252"], ["253", "254"], ["255"], ["256", "257"], ["258"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["259"], ["260"], "'created' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'homepage' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'aboutPage' is assigned a value but never used. Allowed unused vars must match /^_/u.", {"messageId": "261", "data": "262", "fix": "263", "desc": "264"}, {"messageId": "261", "data": "265", "fix": "266", "desc": "267"}, {"messageId": "261", "data": "268", "fix": "269", "desc": "270"}, {"messageId": "261", "data": "271", "fix": "272", "desc": "273"}, {"messageId": "274", "fix": "275", "desc": "276"}, {"messageId": "277", "fix": "278", "desc": "279"}, {"messageId": "261", "data": "280", "fix": "281", "desc": "282"}, {"messageId": "261", "data": "283", "fix": "284", "desc": "285"}, {"messageId": "261", "data": "286", "fix": "287", "desc": "288"}, {"messageId": "261", "data": "289", "fix": "290", "desc": "291"}, {"messageId": "261", "data": "292", "fix": "293", "desc": "282"}, {"messageId": "261", "data": "294", "fix": "295", "desc": "285"}, {"messageId": "261", "data": "296", "fix": "297", "desc": "288"}, {"messageId": "261", "data": "298", "fix": "299", "desc": "291"}, {"messageId": "300", "data": "301", "fix": "302", "desc": "303"}, {"messageId": "300", "data": "304", "fix": "305", "desc": "306"}, {"kind": "307", "justification": "308"}, {"messageId": "300", "data": "309", "fix": "310", "desc": "303"}, {"messageId": "300", "data": "311", "fix": "312", "desc": "306"}, {"kind": "307", "justification": "308"}, {"messageId": "300", "data": "313", "fix": "314", "desc": "303"}, {"messageId": "300", "data": "315", "fix": "316", "desc": "306"}, {"kind": "307", "justification": "308"}, {"messageId": "300", "data": "317", "fix": "318", "desc": "303"}, {"messageId": "300", "data": "319", "fix": "320", "desc": "306"}, {"kind": "307", "justification": "308"}, {"messageId": "321", "fix": "322", "desc": "323"}, {"kind": "307", "justification": "308"}, "replaceWithAlt", {"alt": "324"}, {"range": "325", "text": "326"}, "Replace with `&apos;`.", {"alt": "327"}, {"range": "328", "text": "329"}, "Replace with `&lsquo;`.", {"alt": "330"}, {"range": "331", "text": "332"}, "Replace with `&#39;`.", {"alt": "333"}, {"range": "334", "text": "335"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "336", "text": "337"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "338", "text": "339"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "340"}, {"range": "341", "text": "342"}, "Replace with `&quot;`.", {"alt": "343"}, {"range": "344", "text": "345"}, "Replace with `&ldquo;`.", {"alt": "346"}, {"range": "347", "text": "348"}, "Replace with `&#34;`.", {"alt": "349"}, {"range": "350", "text": "351"}, "Replace with `&rdquo;`.", {"alt": "340"}, {"range": "352", "text": "353"}, {"alt": "343"}, {"range": "354", "text": "355"}, {"alt": "346"}, {"range": "356", "text": "357"}, {"alt": "349"}, {"range": "358", "text": "359"}, "replaceEmptyObjectType", {"replacement": "360"}, {"range": "361", "text": "360"}, "Replace `{}` with `object`.", {"replacement": "337"}, {"range": "362", "text": "337"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "360"}, {"range": "363", "text": "360"}, {"replacement": "337"}, {"range": "364", "text": "337"}, {"replacement": "360"}, {"range": "365", "text": "360"}, {"replacement": "337"}, {"range": "366", "text": "337"}, {"replacement": "360"}, {"range": "367", "text": "360"}, {"replacement": "337"}, {"range": "368", "text": "337"}, "replaceEmptyInterfaceWithSuper", {"range": "369", "text": "370"}, "Replace empty interface with a type alias.", "&apos;", [1481, 1702], "\n                Based in the heart of Ahmedabad, we draw inspiration from the city&apos;s rich textile heritage \n                and vibrant colors, translating these elements into fragrances that tell a story.\n              ", "&lsquo;", [1481, 1702], "\n                Based in the heart of Ahmedabad, we draw inspiration from the city&lsquo;s rich textile heritage \n                and vibrant colors, translating these elements into fragrances that tell a story.\n              ", "&#39;", [1481, 1702], "\n                Based in the heart of Ahmedabad, we draw inspiration from the city&#39;s rich textile heritage \n                and vibrant colors, translating these elements into fragrances that tell a story.\n              ", "&rsquo;", [1481, 1702], "\n                Based in the heart of Ahmedabad, we draw inspiration from the city&rsquo;s rich textile heritage \n                and vibrant colors, translating these elements into fragrances that tell a story.\n              ", [271, 274], "unknown", [271, 274], "never", "&quot;", [3063, 3083], "\n                  &quot;", "&ldquo;", [3063, 3083], "\n                  &ldquo;", "&#34;", [3063, 3083], "\n                  &#34;", "&rdquo;", [3063, 3083], "\n                  &rdquo;", [3103, 3121], "&quot;\n                ", [3103, 3121], "&ldquo;\n                ", [3103, 3121], "&#34;\n                ", [3103, 3121], "&rdquo;\n                ", "object", [1506, 1508], [1506, 1508], [1817, 1819], [1817, 1819], [2522, 2524], [2522, 2524], [2543, 2545], [2543, 2545], [16165, 16207], "type GeneratedTypes = Config"]