(()=>{var e={};e.id=6676,e.ids=[6676],e.modules={39:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,90367,23)),Promise.resolve().then(t.t.bind(t,26051,23)),Promise.resolve().then(t.t.bind(t,82647,23)),Promise.resolve().then(t.t.bind(t,40774,23)),Promise.resolve().then(t.t.bind(t,1474,23)),Promise.resolve().then(t.t.bind(t,77350,23)),Promise.resolve().then(t.t.bind(t,87348,23)),Promise.resolve().then(t.t.bind(t,75654,23))},208:(e,r,t)=>{"use strict";t.d(r,{Navigation:()=>o});let o=(0,t(4362).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx","Navigation")},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},16911:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,33885,23)),Promise.resolve().then(t.t.bind(t,33645,23)),Promise.resolve().then(t.t.bind(t,58277,23)),Promise.resolve().then(t.t.bind(t,36100,23)),Promise.resolve().then(t.t.bind(t,50556,23)),Promise.resolve().then(t.t.bind(t,27616,23)),Promise.resolve().then(t.t.bind(t,89902,23)),Promise.resolve().then(t.t.bind(t,60632,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20175:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>eD});var o=t(66394),s=t(75791),a=t(835),n=t(1023);function l(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var i=function(e){let r=function(e){let r=a.forwardRef((e,r)=>{let{children:t,...o}=e;if(a.isValidElement(t)){var s;let e,n,i=(s=t,(n=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(n=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),d=function(e,r){let t={...r};for(let o in r){let s=e[o],a=r[o];/^on[A-Z]/.test(o)?s&&a?t[o]=(...e)=>{let r=a(...e);return s(...e),r}:s&&(t[o]=s):"style"===o?t[o]={...s,...a}:"className"===o&&(t[o]=[s,a].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==a.Fragment&&(d.ref=r?function(...e){return r=>{let t=!1,o=e.map(e=>{let o=l(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():l(e[r],null)}}}}(r,i):i),a.cloneElement(t,d)}return a.Children.count(t)>1?a.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=a.forwardRef((e,t)=>{let{children:s,...n}=e,l=a.Children.toArray(s),i=l.find(c);if(i){let e=i.props.children,s=l.map(r=>r!==i?r:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,o.jsx)(r,{...n,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,o.jsx)(r,{...n,ref:t,children:s})});return t.displayName=`${e}.Slot`,t}("Slot"),d=Symbol("radix.slottable");function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}function m(){for(var e,r,t=0,o="",s=arguments.length;t<s;t++)(e=arguments[t])&&(r=function e(r){var t,o,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(s&&(s+=" "),s+=o)}else for(o in r)r[o]&&(s&&(s+=" "),s+=o);return s}(e))&&(o&&(o+=" "),o+=r);return o}let u=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,p=(e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return m(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:a}=r,n=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],o=null==a?void 0:a[e];if(null===r)return null;let n=u(r)||u(o);return s[e][n]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return m(e,n,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...a,...l}[r]):({...a,...l})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)},f=e=>{let r=b(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),g(t,r)||x(e)},getConflictingClassGroupIds:(e,r)=>{let s=t[e]||[];return r&&o[e]?[...s,...o[e]]:s}}},g=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),s=o?g(e.slice(1),o):void 0;if(s)return s;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},h=/^\[(.+)\]$/,x=e=>{if(h.test(e)){let r=h.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},b=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)v(t[e],o,e,r);return o},v=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:y(r,e)).classGroupId=t;return}if("function"==typeof e)return w(e)?void v(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,s])=>{v(s,y(r,e),t,o)})})},y=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},w=e=>e.isThemeGetter,k=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,s=(s,a)=>{t.set(s,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(s(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):s(e,r)}}},j=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,s=0,a=0;for(let n=0;n<e.length;n++){let l=e[n];if(0===o&&0===s){if(":"===l){t.push(e.slice(a,n)),a=n+1;continue}if("/"===l){r=n;continue}}"["===l?o++:"]"===l?o--:"("===l?s++:")"===l&&s--}let n=0===t.length?e:e.substring(a),l=N(n);return{modifiers:t,hasImportantModifier:l!==n,baseClassName:l,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},N=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,z=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},C=e=>({cache:k(e.cacheSize),parseClassName:j(e),sortModifiers:z(e),...f(e)}),P=/\s+/,q=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:s,sortModifiers:a}=r,n=[],l=e.trim().split(P),i="";for(let e=l.length-1;e>=0;e-=1){let r=l[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:u,maybePostfixModifierPosition:p}=t(r);if(d){i=r+(i.length>0?" "+i:i);continue}let f=!!p,g=o(f?u.substring(0,p):u);if(!g){if(!f||!(g=o(u))){i=r+(i.length>0?" "+i:i);continue}f=!1}let h=a(c).join(":"),x=m?h+"!":h,b=x+g;if(n.includes(b))continue;n.push(b);let v=s(g,f);for(let e=0;e<v.length;++e){let r=v[e];n.push(x+r)}i=r+(i.length>0?" "+i:i)}return i};function A(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=M(e))&&(o&&(o+=" "),o+=r);return o}let M=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=M(e[o]))&&(t&&(t+=" "),t+=r);return t},S=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},E=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,_=/^\((?:(\w[\w-]*):)?(.+)\)$/i,D=/^\d+\/\d+$/,$=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,G=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,I=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,O=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,R=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,W=e=>D.test(e),F=e=>!!e&&!Number.isNaN(Number(e)),B=e=>!!e&&Number.isInteger(Number(e)),V=e=>e.endsWith("%")&&F(e.slice(0,-1)),T=e=>$.test(e),H=()=>!0,L=e=>G.test(e)&&!I.test(e),U=()=>!1,K=e=>O.test(e),Q=e=>R.test(e),Z=e=>!Y(e)&&!es(e),X=e=>em(e,eg,U),Y=e=>E.test(e),J=e=>em(e,eh,L),ee=e=>em(e,ex,F),er=e=>em(e,ep,U),et=e=>em(e,ef,Q),eo=e=>em(e,ev,K),es=e=>_.test(e),ea=e=>eu(e,eh),en=e=>eu(e,eb),el=e=>eu(e,ep),ei=e=>eu(e,eg),ed=e=>eu(e,ef),ec=e=>eu(e,ev,!0),em=(e,r,t)=>{let o=E.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},eu=(e,r,t=!1)=>{let o=_.exec(e);return!!o&&(o[1]?r(o[1]):t)},ep=e=>"position"===e||"percentage"===e,ef=e=>"image"===e||"url"===e,eg=e=>"length"===e||"size"===e||"bg-size"===e,eh=e=>"length"===e,ex=e=>"number"===e,eb=e=>"family-name"===e,ev=e=>"shadow"===e;Symbol.toStringTag;let ey=function(e,...r){let t,o,s,a=function(l){return o=(t=C(r.reduce((e,r)=>r(e),e()))).cache.get,s=t.cache.set,a=n,n(l)};function n(e){let r=o(e);if(r)return r;let a=q(e,t);return s(e,a),a}return function(){return a(A.apply(null,arguments))}}(()=>{let e=S("color"),r=S("font"),t=S("text"),o=S("font-weight"),s=S("tracking"),a=S("leading"),n=S("breakpoint"),l=S("container"),i=S("spacing"),d=S("radius"),c=S("shadow"),m=S("inset-shadow"),u=S("text-shadow"),p=S("drop-shadow"),f=S("blur"),g=S("perspective"),h=S("aspect"),x=S("ease"),b=S("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...y(),es,Y],k=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],N=()=>[es,Y,i],z=()=>[W,"full","auto",...N()],C=()=>[B,"none","subgrid",es,Y],P=()=>["auto",{span:["full",B,es,Y]},B,es,Y],q=()=>[B,"auto",es,Y],A=()=>["auto","min","max","fr",es,Y],M=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],E=()=>["start","end","center","stretch","center-safe","end-safe"],_=()=>["auto",...N()],D=()=>[W,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],$=()=>[e,es,Y],G=()=>[...y(),el,er,{position:[es,Y]}],I=()=>["no-repeat",{repeat:["","x","y","space","round"]}],O=()=>["auto","cover","contain",ei,X,{size:[es,Y]}],R=()=>[V,ea,J],L=()=>["","none","full",d,es,Y],U=()=>["",F,ea,J],K=()=>["solid","dashed","dotted","double"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[F,V,el,er],eu=()=>["","none",f,es,Y],ep=()=>["none",F,es,Y],ef=()=>["none",F,es,Y],eg=()=>[F,es,Y],eh=()=>[W,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[T],breakpoint:[T],color:[H],container:[T],"drop-shadow":[T],ease:["in","out","in-out"],font:[Z],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[T],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[T],shadow:[T],spacing:["px",F],text:[T],"text-shadow":[T],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",W,Y,es,h]}],container:["container"],columns:[{columns:[F,Y,es,l]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[B,"auto",es,Y]}],basis:[{basis:[W,"full","auto",l,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[F,W,"auto","initial","none",Y]}],grow:[{grow:["",F,es,Y]}],shrink:[{shrink:["",F,es,Y]}],order:[{order:[B,"first","last","none",es,Y]}],"grid-cols":[{"grid-cols":C()}],"col-start-end":[{col:P()}],"col-start":[{"col-start":q()}],"col-end":[{"col-end":q()}],"grid-rows":[{"grid-rows":C()}],"row-start-end":[{row:P()}],"row-start":[{"row-start":q()}],"row-end":[{"row-end":q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":A()}],"auto-rows":[{"auto-rows":A()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...M(),"normal"]}],"justify-items":[{"justify-items":[...E(),"normal"]}],"justify-self":[{"justify-self":["auto",...E()]}],"align-content":[{content:["normal",...M()]}],"align-items":[{items:[...E(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...E(),{baseline:["","last"]}]}],"place-content":[{"place-content":M()}],"place-items":[{"place-items":[...E(),"baseline"]}],"place-self":[{"place-self":["auto",...E()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:_()}],mx:[{mx:_()}],my:[{my:_()}],ms:[{ms:_()}],me:[{me:_()}],mt:[{mt:_()}],mr:[{mr:_()}],mb:[{mb:_()}],ml:[{ml:_()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[l,"screen",...D()]}],"min-w":[{"min-w":[l,"screen","none",...D()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[n]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",t,ea,J]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,es,ee]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",V,Y]}],"font-family":[{font:[en,Y,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,es,Y]}],"line-clamp":[{"line-clamp":[F,"none",es,ee]}],leading:[{leading:[a,...N()]}],"list-image":[{"list-image":["none",es,Y]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",es,Y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:$()}],"text-color":[{text:$()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...K(),"wavy"]}],"text-decoration-thickness":[{decoration:[F,"from-font","auto",es,J]}],"text-decoration-color":[{decoration:$()}],"underline-offset":[{"underline-offset":[F,"auto",es,Y]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",es,Y]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",es,Y]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:G()}],"bg-repeat":[{bg:I()}],"bg-size":[{bg:O()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},B,es,Y],radial:["",es,Y],conic:[B,es,Y]},ed,et]}],"bg-color":[{bg:$()}],"gradient-from-pos":[{from:R()}],"gradient-via-pos":[{via:R()}],"gradient-to-pos":[{to:R()}],"gradient-from":[{from:$()}],"gradient-via":[{via:$()}],"gradient-to":[{to:$()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:U()}],"border-w-x":[{"border-x":U()}],"border-w-y":[{"border-y":U()}],"border-w-s":[{"border-s":U()}],"border-w-e":[{"border-e":U()}],"border-w-t":[{"border-t":U()}],"border-w-r":[{"border-r":U()}],"border-w-b":[{"border-b":U()}],"border-w-l":[{"border-l":U()}],"divide-x":[{"divide-x":U()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":U()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...K(),"hidden","none"]}],"divide-style":[{divide:[...K(),"hidden","none"]}],"border-color":[{border:$()}],"border-color-x":[{"border-x":$()}],"border-color-y":[{"border-y":$()}],"border-color-s":[{"border-s":$()}],"border-color-e":[{"border-e":$()}],"border-color-t":[{"border-t":$()}],"border-color-r":[{"border-r":$()}],"border-color-b":[{"border-b":$()}],"border-color-l":[{"border-l":$()}],"divide-color":[{divide:$()}],"outline-style":[{outline:[...K(),"none","hidden"]}],"outline-offset":[{"outline-offset":[F,es,Y]}],"outline-w":[{outline:["",F,ea,J]}],"outline-color":[{outline:$()}],shadow:[{shadow:["","none",c,ec,eo]}],"shadow-color":[{shadow:$()}],"inset-shadow":[{"inset-shadow":["none",m,ec,eo]}],"inset-shadow-color":[{"inset-shadow":$()}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:$()}],"ring-offset-w":[{"ring-offset":[F,J]}],"ring-offset-color":[{"ring-offset":$()}],"inset-ring-w":[{"inset-ring":U()}],"inset-ring-color":[{"inset-ring":$()}],"text-shadow":[{"text-shadow":["none",u,ec,eo]}],"text-shadow-color":[{"text-shadow":$()}],opacity:[{opacity:[F,es,Y]}],"mix-blend":[{"mix-blend":[...Q(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Q()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[F]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":$()}],"mask-image-linear-to-color":[{"mask-linear-to":$()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":$()}],"mask-image-t-to-color":[{"mask-t-to":$()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":$()}],"mask-image-r-to-color":[{"mask-r-to":$()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":$()}],"mask-image-b-to-color":[{"mask-b-to":$()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":$()}],"mask-image-l-to-color":[{"mask-l-to":$()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":$()}],"mask-image-x-to-color":[{"mask-x-to":$()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":$()}],"mask-image-y-to-color":[{"mask-y-to":$()}],"mask-image-radial":[{"mask-radial":[es,Y]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":$()}],"mask-image-radial-to-color":[{"mask-radial-to":$()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[F]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":$()}],"mask-image-conic-to-color":[{"mask-conic-to":$()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:G()}],"mask-repeat":[{mask:I()}],"mask-size":[{mask:O()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",es,Y]}],filter:[{filter:["","none",es,Y]}],blur:[{blur:eu()}],brightness:[{brightness:[F,es,Y]}],contrast:[{contrast:[F,es,Y]}],"drop-shadow":[{"drop-shadow":["","none",p,ec,eo]}],"drop-shadow-color":[{"drop-shadow":$()}],grayscale:[{grayscale:["",F,es,Y]}],"hue-rotate":[{"hue-rotate":[F,es,Y]}],invert:[{invert:["",F,es,Y]}],saturate:[{saturate:[F,es,Y]}],sepia:[{sepia:["",F,es,Y]}],"backdrop-filter":[{"backdrop-filter":["","none",es,Y]}],"backdrop-blur":[{"backdrop-blur":eu()}],"backdrop-brightness":[{"backdrop-brightness":[F,es,Y]}],"backdrop-contrast":[{"backdrop-contrast":[F,es,Y]}],"backdrop-grayscale":[{"backdrop-grayscale":["",F,es,Y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[F,es,Y]}],"backdrop-invert":[{"backdrop-invert":["",F,es,Y]}],"backdrop-opacity":[{"backdrop-opacity":[F,es,Y]}],"backdrop-saturate":[{"backdrop-saturate":[F,es,Y]}],"backdrop-sepia":[{"backdrop-sepia":["",F,es,Y]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",es,Y]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[F,"initial",es,Y]}],ease:[{ease:["linear","initial",x,es,Y]}],delay:[{delay:[F,es,Y]}],animate:[{animate:["none",b,es,Y]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,es,Y]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[es,Y,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:$()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:$()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",es,Y]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",es,Y]}],fill:[{fill:["none",...$()]}],"stroke-w":[{stroke:[F,ea,J,ee]}],stroke:[{stroke:["none",...$()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ew(...e){return ey(m(e))}let ek=p("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gold:"bg-gradient-to-r from-gold-400 to-gold-600 text-white hover:from-gold-500 hover:to-gold-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ej=a.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...a},n)=>(0,o.jsx)(s?i:"button",{className:ew(ek({variant:r,size:t,className:e})),ref:n,...a}));function eN({headline:e="Illuminate Your Space with Alokita Candles",subheadline:r="Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.",backgroundImage:t,ctaText:s="Shop Now",ctaLink:a="/products"}){return(0,o.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,o.jsxs)("div",{className:"absolute inset-0 z-0",children:[t?(0,o.jsx)("img",{src:t,alt:"Alokita Candles Hero",className:"w-full h-full object-cover"}):(0,o.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-rose-50 via-white to-gold-50"}),(0,o.jsx)("div",{className:"absolute inset-0 bg-black/20"})]}),(0,o.jsxs)("div",{className:"relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto",children:[(0,o.jsx)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight",children:(0,o.jsx)("span",{className:"block",children:e})}),(0,o.jsx)("p",{className:"text-lg sm:text-xl lg:text-2xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed",children:r}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,o.jsx)(ej,{variant:"gold",size:"lg",className:"text-lg px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300",asChild:!0,children:(0,o.jsx)("a",{href:a,children:s})}),(0,o.jsx)(ej,{variant:"outline",size:"lg",className:"text-lg px-8 py-3 bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm",asChild:!0,children:(0,o.jsx)("a",{href:"/about",children:"Our Story"})})]}),(0,o.jsx)("div",{className:"absolute -top-10 -left-10 w-20 h-20 bg-gold-300/20 rounded-full blur-xl"}),(0,o.jsx)("div",{className:"absolute -bottom-10 -right-10 w-32 h-32 bg-rose-300/20 rounded-full blur-xl"})]}),(0,o.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10",children:(0,o.jsx)("div",{className:"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center",children:(0,o.jsx)("div",{className:"w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce"})})})]})}ej.displayName="Button";let ez=a.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:ew("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));ez.displayName="Card",a.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:ew("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",a.forwardRef(({className:e,...r},t)=>(0,o.jsx)("h3",{ref:t,className:ew("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",a.forwardRef(({className:e,...r},t)=>(0,o.jsx)("p",{ref:t,className:ew("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let eC=a.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:ew("p-6 pt-0",e),...r}));eC.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:ew("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let eP=p("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",gold:"border-transparent bg-gold-100 text-gold-800 hover:bg-gold-200",rose:"border-transparent bg-rose-100 text-rose-800 hover:bg-rose-200"}},defaultVariants:{variant:"default"}});function eq({className:e,variant:r,...t}){return(0,o.jsx)("div",{className:ew(eP({variant:r}),e),...t})}function eA({id:e,name:r,price:t,scent:s,image:a,isFeatured:n=!1,inStock:l=!0,slug:i}){return(0,o.jsxs)(ez,{className:"group overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:[(0,o.jsxs)("div",{className:"relative aspect-square overflow-hidden",children:[a?(0,o.jsx)("img",{src:a.url,alt:a.alt,className:"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"}):(0,o.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gold-100 to-rose-100 flex items-center justify-center",children:(0,o.jsx)("div",{className:"text-gold-600 text-4xl",children:"\uD83D\uDD6F️"})}),(0,o.jsxs)("div",{className:"absolute top-3 left-3 flex flex-col gap-2",children:[n&&(0,o.jsx)(eq,{variant:"gold",className:"shadow-sm",children:"Featured"}),!l&&(0,o.jsx)(eq,{variant:"destructive",className:"shadow-sm",children:"Out of Stock"})]}),(0,o.jsx)("div",{className:"absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center",children:(0,o.jsx)(ej,{variant:"outline",className:"bg-white/90 hover:bg-white text-black border-white/50",asChild:!0,children:(0,o.jsx)("a",{href:`/products/${i}`,children:"Quick View"})})})]}),(0,o.jsxs)(eC,{className:"p-4",children:[(0,o.jsx)("h3",{className:"font-semibold text-lg mb-2 line-clamp-2 group-hover:text-gold-600 transition-colors",children:(0,o.jsx)("a",{href:`/products/${i}`,className:"hover:underline",children:r})}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground mb-3 line-clamp-1",children:s}),(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("span",{className:"text-xl font-bold text-gold-600",children:["₹",t.toLocaleString("en-IN")]}),(0,o.jsx)(ej,{size:"sm",variant:l?"default":"outline",disabled:!l,className:"ml-2",children:l?"Add to Cart":"Notify Me"})]})]})]})}function eM({products:e,title:r,className:t=""}){return e&&0!==e.length?(0,o.jsx)("section",{className:`py-16 ${t}`,children:(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[r&&(0,o.jsx)("h2",{className:"text-3xl font-bold text-center mb-12 text-gradient-gold",children:r}),(0,o.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>{let r,t=e.images?.[0];if(t){if("string"==typeof t.image)r={url:t.image,alt:t.alt};else if(t.image&&"object"==typeof t.image){let e=t.image;e.url&&(r={url:e.url,alt:e.alt||t.alt})}}return(0,o.jsx)(eA,{id:e.id,name:e.name,price:e.price,scent:e.scent,image:r,isFeatured:e.isFeatured||!1,inStock:e.inStock||!1,slug:e.slug},e.id)})})]})}):(0,o.jsx)("section",{className:`py-16 ${t}`,children:(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[r&&(0,o.jsx)("h2",{className:"text-3xl font-bold text-center mb-12 text-gradient-gold",children:r}),(0,o.jsx)("div",{className:"text-center py-12",children:(0,o.jsx)("p",{className:"text-muted-foreground text-lg",children:"No products available at the moment."})})]})})}function eS({title:e="Our Story",content:r="Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.",image:t,className:s=""}){return(0,o.jsx)("section",{className:`py-16 ${s}`,children:(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsx)("h2",{className:"text-3xl lg:text-4xl font-bold text-gradient-gold",children:e}),(0,o.jsxs)("div",{className:"prose prose-lg max-w-none",children:[(0,o.jsx)("p",{className:"text-gray-700 leading-relaxed",children:r}),(0,o.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"Each candle is carefully crafted with premium soy wax and housed in beautiful glass containers that can be repurposed long after the candle has burned. From floral to woody scents, our collection captures the essence of different moods and seasons."}),(0,o.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"Based in the heart of Ahmedabad, we draw inspiration from the city's rich textile heritage and vibrant colors, translating these elements into fragrances that tell a story."})]}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,o.jsx)(ej,{variant:"gold",size:"lg",asChild:!0,children:(0,o.jsx)("a",{href:"/products",children:"Explore Our Collection"})}),(0,o.jsx)(ej,{variant:"outline",size:"lg",asChild:!0,children:(0,o.jsx)("a",{href:"/contact",children:"Get in Touch"})})]})]}),(0,o.jsxs)("div",{className:"relative",children:[t?(0,o.jsx)("img",{src:t,alt:"About Alokita Candles",className:"w-full h-96 lg:h-[500px] object-cover rounded-lg shadow-lg"}):(0,o.jsx)("div",{className:"w-full h-96 lg:h-[500px] bg-gradient-to-br from-gold-100 via-rose-100 to-gold-200 rounded-lg shadow-lg flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD6F️"}),(0,o.jsx)("p",{className:"text-gold-600 font-medium",children:"Handcrafted with Love"})]})}),(0,o.jsx)("div",{className:"absolute -top-4 -right-4 w-24 h-24 bg-gold-200/30 rounded-full blur-xl"}),(0,o.jsx)("div",{className:"absolute -bottom-4 -left-4 w-32 h-32 bg-rose-200/30 rounded-full blur-xl"})]})]}),(0,o.jsxs)("div",{className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,o.jsx)("span",{className:"text-white text-2xl",children:"\uD83C\uDF3F"})}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Natural Ingredients"}),(0,o.jsx)("p",{className:"text-muted-foreground",children:"Made with premium soy wax and natural fragrances for a clean burn."})]}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-rose-400 to-rose-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,o.jsx)("span",{className:"text-white text-2xl",children:"\uD83C\uDFFA"})}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Elegant Glass"}),(0,o.jsx)("p",{className:"text-muted-foreground",children:"Beautiful glass containers that can be repurposed after use."})]}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-gold-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,o.jsx)("span",{className:"text-white text-2xl",children:"✨"})}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Handcrafted"}),(0,o.jsx)("p",{className:"text-muted-foreground",children:"Each candle is carefully made by hand with attention to detail."})]})]})]})})}function eE({rating:e}){return(0,o.jsx)("div",{className:"flex items-center gap-1",children:[1,2,3,4,5].map(r=>(0,o.jsx)("svg",{className:`w-5 h-5 ${r<=e?"text-gold-400 fill-current":"text-gray-300"}`,viewBox:"0 0 20 20",children:(0,o.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},r))})}function e_({title:e="What Our Customers Say",testimonials:r,className:t=""}){return r&&0!==r.length?(0,o.jsx)("section",{className:`py-16 bg-gradient-to-br from-rose-50 to-gold-50 ${t}`,children:(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[(0,o.jsx)("h2",{className:"text-3xl font-bold text-center mb-12 text-gradient-gold",children:e}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map((e,r)=>(0,o.jsx)(ez,{className:"bg-white/80 backdrop-blur-sm border-gold-200/50 hover:shadow-lg transition-all duration-300",children:(0,o.jsxs)(eC,{className:"p-6",children:[(0,o.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[e.image?(0,o.jsx)("img",{src:e.image.url,alt:e.image.alt,className:"w-12 h-12 rounded-full object-cover"}):(0,o.jsx)("div",{className:"w-12 h-12 rounded-full bg-gradient-to-br from-gold-200 to-rose-200 flex items-center justify-center",children:(0,o.jsx)("span",{className:"text-gold-600 font-semibold text-lg",children:e.name.charAt(0)})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"font-semibold text-gray-900",children:e.name}),e.location&&(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:e.location})]})]}),(0,o.jsx)(eE,{rating:e.rating}),(0,o.jsxs)("blockquote",{className:"mt-4 text-gray-700 italic",children:['"',e.review,'"']})]})},r))})]})}):null}async function eD(){let e=await n.A,r=await (0,s.nm0)({config:e}),{docs:t}=await r.find({collection:"products",where:{isFeatured:{equals:!0},inStock:{equals:!0}},limit:6});return(0,o.jsxs)("main",{className:"min-h-screen",children:[(0,o.jsx)(eN,{}),(0,o.jsx)(eM,{products:t,title:"Featured Candles",className:"bg-white"}),(0,o.jsx)(eS,{className:"bg-gradient-to-br from-rose-50 to-gold-50"}),(0,o.jsx)(e_,{testimonials:[{name:"Priya Sharma",location:"Mumbai",review:"The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!",rating:5},{name:"Arjun Patel",location:"Ahmedabad",review:"Amazing quality and the scents are so authentic. Love supporting local artisans!",rating:5},{name:"Kavya Reddy",location:"Bangalore",review:"Perfect for my meditation space. The woody scent collection is my favorite.",rating:4}],className:"bg-white"})]})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28468:()=>{},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30444:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>n});var o=t(66394);t(835),t(67470);var s=t(208);function a(){return(0,o.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,o.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,o.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,o.jsx)("h3",{className:"text-2xl font-bold text-gradient-gold mb-4",children:"Alokita Candles"}),(0,o.jsx)("p",{className:"text-gray-300 mb-4 max-w-md",children:"Handcrafted scented candles from Ahmedabad, cased in elegant glass. Each candle is made with premium ingredients and designed to elevate your space."}),(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsx)("a",{href:"#",className:"text-gray-400 hover:text-gold-400 transition-colors",children:(0,o.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{d:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"})})}),(0,o.jsx)("a",{href:"#",className:"text-gray-400 hover:text-gold-400 transition-colors",children:(0,o.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{d:"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"})})}),(0,o.jsx)("a",{href:"#",className:"text-gray-400 hover:text-gold-400 transition-colors",children:(0,o.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"})})}),(0,o.jsx)("a",{href:"#",className:"text-gray-400 hover:text-gold-400 transition-colors",children:(0,o.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),(0,o.jsxs)("ul",{className:"space-y-2",children:[(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"/",className:"text-gray-300 hover:text-gold-400 transition-colors",children:"Home"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"/products",className:"text-gray-300 hover:text-gold-400 transition-colors",children:"All Products"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"/categories/floral",className:"text-gray-300 hover:text-gold-400 transition-colors",children:"Floral Candles"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"/categories/woody",className:"text-gray-300 hover:text-gold-400 transition-colors",children:"Woody Candles"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"/about",className:"text-gray-300 hover:text-gold-400 transition-colors",children:"About Us"})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"Contact"}),(0,o.jsxs)("div",{className:"space-y-2 text-gray-300",children:[(0,o.jsx)("p",{children:"\uD83D\uDCCD Ahmedabad, Gujarat, India"}),(0,o.jsx)("p",{children:"\uD83D\uDCDE +91 98765 43210"}),(0,o.jsx)("p",{children:"✉️ <EMAIL>"}),(0,o.jsx)("p",{children:"\uD83D\uDD52 Mon-Sat: 9AM-6PM"})]})]})]}),(0,o.jsxs)("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,o.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 Alokita Candles. All rights reserved."}),(0,o.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,o.jsx)("a",{href:"/privacy",className:"text-gray-400 hover:text-gold-400 text-sm transition-colors",children:"Privacy Policy"}),(0,o.jsx)("a",{href:"/terms",className:"text-gray-400 hover:text-gold-400 text-sm transition-colors",children:"Terms of Service"}),(0,o.jsx)("a",{href:"/shipping",className:"text-gray-400 hover:text-gold-400 text-sm transition-colors",children:"Shipping Info"})]})]})]})})}let n={description:"Alokita Candles - Handcrafted scented candles from Ahmedabad",title:"Alokita Candles | Handcrafted Scented Candles"};async function l(e){let{children:r}=e;return(0,o.jsx)("html",{lang:"en",children:(0,o.jsxs)("body",{className:"min-h-screen flex flex-col",children:[(0,o.jsx)(s.Navigation,{}),(0,o.jsx)("main",{className:"flex-1",children:r}),(0,o.jsx)(a,{})]})})}},30676:()=>{},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41204:e=>{"use strict";e.exports=require("string_decoder")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57175:(e,r,t)=>{Promise.resolve().then(t.bind(t,208))},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},67470:()=>{},70311:e=>{"use strict";e.exports=require("timers/promises")},70913:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var o=t(87306),s=t(34349),a=t(58277),n=t.n(a),l=t(59194),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d={children:["",{children:["(frontend)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20175)),"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,30444)),"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,49115,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,38550,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,24239,23)),"next/dist/client/components/unauthorized-error"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,49115,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,38550,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,24239,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(frontend)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84559:(e,r,t)=>{"use strict";t.d(r,{Navigation:()=>ey});var o=t(88728),s=t(85737);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var n=function(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...o}=e;if(s.isValidElement(t)){var n;let e,l,i=(n=t,(l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(l=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),d=function(e,r){let t={...r};for(let o in r){let s=e[o],a=r[o];/^on[A-Z]/.test(o)?s&&a?t[o]=(...e)=>{let r=a(...e);return s(...e),r}:s&&(t[o]=s):"style"===o?t[o]={...s,...a}:"className"===o&&(t[o]=[s,a].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==s.Fragment&&(d.ref=r?function(...e){return r=>{let t=!1,o=e.map(e=>{let o=a(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():a(e[r],null)}}}}(r,i):i),s.cloneElement(t,d)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:a,...n}=e,l=s.Children.toArray(a),d=l.find(i);if(d){let e=d.props.children,a=l.map(r=>r!==d?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,o.jsx)(r,{...n,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,o.jsx)(r,{...n,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}("Slot"),l=Symbol("radix.slottable");function i(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}function d(){for(var e,r,t=0,o="",s=arguments.length;t<s;t++)(e=arguments[t])&&(r=function e(r){var t,o,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(s&&(s+=" "),s+=o)}else for(o in r)r[o]&&(s&&(s+=" "),s+=o);return s}(e))&&(o&&(o+=" "),o+=r);return o}let c=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,m=e=>{let r=g(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),u(t,r)||f(e)},getConflictingClassGroupIds:(e,r)=>{let s=t[e]||[];return r&&o[e]?[...s,...o[e]]:s}}},u=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),s=o?u(e.slice(1),o):void 0;if(s)return s;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},p=/^\[(.+)\]$/,f=e=>{if(p.test(e)){let r=p.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},g=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)h(t[e],o,e,r);return o},h=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:x(r,e)).classGroupId=t;return}if("function"==typeof e)return b(e)?void h(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,s])=>{h(s,x(r,e),t,o)})})},x=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},b=e=>e.isThemeGetter,v=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,s=(s,a)=>{t.set(s,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(s(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):s(e,r)}}},y=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,s=0,a=0;for(let n=0;n<e.length;n++){let l=e[n];if(0===o&&0===s){if(":"===l){t.push(e.slice(a,n)),a=n+1;continue}if("/"===l){r=n;continue}}"["===l?o++:"]"===l?o--:"("===l?s++:")"===l&&s--}let n=0===t.length?e:e.substring(a),l=w(n);return{modifiers:t,hasImportantModifier:l!==n,baseClassName:l,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},w=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,k=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},j=e=>({cache:v(e.cacheSize),parseClassName:y(e),sortModifiers:k(e),...m(e)}),N=/\s+/,z=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:s,sortModifiers:a}=r,n=[],l=e.trim().split(N),i="";for(let e=l.length-1;e>=0;e-=1){let r=l[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:u,maybePostfixModifierPosition:p}=t(r);if(d){i=r+(i.length>0?" "+i:i);continue}let f=!!p,g=o(f?u.substring(0,p):u);if(!g){if(!f||!(g=o(u))){i=r+(i.length>0?" "+i:i);continue}f=!1}let h=a(c).join(":"),x=m?h+"!":h,b=x+g;if(n.includes(b))continue;n.push(b);let v=s(g,f);for(let e=0;e<v.length;++e){let r=v[e];n.push(x+r)}i=r+(i.length>0?" "+i:i)}return i};function C(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=P(e))&&(o&&(o+=" "),o+=r);return o}let P=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=P(e[o]))&&(t&&(t+=" "),t+=r);return t},q=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},A=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,M=/^\((?:(\w[\w-]*):)?(.+)\)$/i,S=/^\d+\/\d+$/,E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,_=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,D=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,$=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,G=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,I=e=>S.test(e),O=e=>!!e&&!Number.isNaN(Number(e)),R=e=>!!e&&Number.isInteger(Number(e)),W=e=>e.endsWith("%")&&O(e.slice(0,-1)),F=e=>E.test(e),B=()=>!0,V=e=>_.test(e)&&!D.test(e),T=()=>!1,H=e=>$.test(e),L=e=>G.test(e),U=e=>!Q(e)&&!er(e),K=e=>ei(e,eu,T),Q=e=>A.test(e),Z=e=>ei(e,ep,V),X=e=>ei(e,ef,O),Y=e=>ei(e,ec,T),J=e=>ei(e,em,L),ee=e=>ei(e,eh,H),er=e=>M.test(e),et=e=>ed(e,ep),eo=e=>ed(e,eg),es=e=>ed(e,ec),ea=e=>ed(e,eu),en=e=>ed(e,em),el=e=>ed(e,eh,!0),ei=(e,r,t)=>{let o=A.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},ed=(e,r,t=!1)=>{let o=M.exec(e);return!!o&&(o[1]?r(o[1]):t)},ec=e=>"position"===e||"percentage"===e,em=e=>"image"===e||"url"===e,eu=e=>"length"===e||"size"===e||"bg-size"===e,ep=e=>"length"===e,ef=e=>"number"===e,eg=e=>"family-name"===e,eh=e=>"shadow"===e;Symbol.toStringTag;let ex=function(e,...r){let t,o,s,a=function(l){return o=(t=j(r.reduce((e,r)=>r(e),e()))).cache.get,s=t.cache.set,a=n,n(l)};function n(e){let r=o(e);if(r)return r;let a=z(e,t);return s(e,a),a}return function(){return a(C.apply(null,arguments))}}(()=>{let e=q("color"),r=q("font"),t=q("text"),o=q("font-weight"),s=q("tracking"),a=q("leading"),n=q("breakpoint"),l=q("container"),i=q("spacing"),d=q("radius"),c=q("shadow"),m=q("inset-shadow"),u=q("text-shadow"),p=q("drop-shadow"),f=q("blur"),g=q("perspective"),h=q("aspect"),x=q("ease"),b=q("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...y(),er,Q],k=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],N=()=>[er,Q,i],z=()=>[I,"full","auto",...N()],C=()=>[R,"none","subgrid",er,Q],P=()=>["auto",{span:["full",R,er,Q]},R,er,Q],A=()=>[R,"auto",er,Q],M=()=>["auto","min","max","fr",er,Q],S=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],E=()=>["start","end","center","stretch","center-safe","end-safe"],_=()=>["auto",...N()],D=()=>[I,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],$=()=>[e,er,Q],G=()=>[...y(),es,Y,{position:[er,Q]}],V=()=>["no-repeat",{repeat:["","x","y","space","round"]}],T=()=>["auto","cover","contain",ea,K,{size:[er,Q]}],H=()=>[W,et,Z],L=()=>["","none","full",d,er,Q],ei=()=>["",O,et,Z],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[O,W,es,Y],eu=()=>["","none",f,er,Q],ep=()=>["none",O,er,Q],ef=()=>["none",O,er,Q],eg=()=>[O,er,Q],eh=()=>[I,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[F],breakpoint:[F],color:[B],container:[F],"drop-shadow":[F],ease:["in","out","in-out"],font:[U],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[F],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[F],shadow:[F],spacing:["px",O],text:[F],"text-shadow":[F],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",I,Q,er,h]}],container:["container"],columns:[{columns:[O,Q,er,l]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[R,"auto",er,Q]}],basis:[{basis:[I,"full","auto",l,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[O,I,"auto","initial","none",Q]}],grow:[{grow:["",O,er,Q]}],shrink:[{shrink:["",O,er,Q]}],order:[{order:[R,"first","last","none",er,Q]}],"grid-cols":[{"grid-cols":C()}],"col-start-end":[{col:P()}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":C()}],"row-start-end":[{row:P()}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":M()}],"auto-rows":[{"auto-rows":M()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...S(),"normal"]}],"justify-items":[{"justify-items":[...E(),"normal"]}],"justify-self":[{"justify-self":["auto",...E()]}],"align-content":[{content:["normal",...S()]}],"align-items":[{items:[...E(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...E(),{baseline:["","last"]}]}],"place-content":[{"place-content":S()}],"place-items":[{"place-items":[...E(),"baseline"]}],"place-self":[{"place-self":["auto",...E()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:_()}],mx:[{mx:_()}],my:[{my:_()}],ms:[{ms:_()}],me:[{me:_()}],mt:[{mt:_()}],mr:[{mr:_()}],mb:[{mb:_()}],ml:[{ml:_()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[l,"screen",...D()]}],"min-w":[{"min-w":[l,"screen","none",...D()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[n]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",t,et,Z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,er,X]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",W,Q]}],"font-family":[{font:[eo,Q,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,er,Q]}],"line-clamp":[{"line-clamp":[O,"none",er,X]}],leading:[{leading:[a,...N()]}],"list-image":[{"list-image":["none",er,Q]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",er,Q]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:$()}],"text-color":[{text:$()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[O,"from-font","auto",er,Z]}],"text-decoration-color":[{decoration:$()}],"underline-offset":[{"underline-offset":[O,"auto",er,Q]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",er,Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",er,Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:G()}],"bg-repeat":[{bg:V()}],"bg-size":[{bg:T()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},R,er,Q],radial:["",er,Q],conic:[R,er,Q]},en,J]}],"bg-color":[{bg:$()}],"gradient-from-pos":[{from:H()}],"gradient-via-pos":[{via:H()}],"gradient-to-pos":[{to:H()}],"gradient-from":[{from:$()}],"gradient-via":[{via:$()}],"gradient-to":[{to:$()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:$()}],"border-color-x":[{"border-x":$()}],"border-color-y":[{"border-y":$()}],"border-color-s":[{"border-s":$()}],"border-color-e":[{"border-e":$()}],"border-color-t":[{"border-t":$()}],"border-color-r":[{"border-r":$()}],"border-color-b":[{"border-b":$()}],"border-color-l":[{"border-l":$()}],"divide-color":[{divide:$()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[O,er,Q]}],"outline-w":[{outline:["",O,et,Z]}],"outline-color":[{outline:$()}],shadow:[{shadow:["","none",c,el,ee]}],"shadow-color":[{shadow:$()}],"inset-shadow":[{"inset-shadow":["none",m,el,ee]}],"inset-shadow-color":[{"inset-shadow":$()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:$()}],"ring-offset-w":[{"ring-offset":[O,Z]}],"ring-offset-color":[{"ring-offset":$()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":$()}],"text-shadow":[{"text-shadow":["none",u,el,ee]}],"text-shadow-color":[{"text-shadow":$()}],opacity:[{opacity:[O,er,Q]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[O]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":$()}],"mask-image-linear-to-color":[{"mask-linear-to":$()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":$()}],"mask-image-t-to-color":[{"mask-t-to":$()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":$()}],"mask-image-r-to-color":[{"mask-r-to":$()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":$()}],"mask-image-b-to-color":[{"mask-b-to":$()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":$()}],"mask-image-l-to-color":[{"mask-l-to":$()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":$()}],"mask-image-x-to-color":[{"mask-x-to":$()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":$()}],"mask-image-y-to-color":[{"mask-y-to":$()}],"mask-image-radial":[{"mask-radial":[er,Q]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":$()}],"mask-image-radial-to-color":[{"mask-radial-to":$()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[O]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":$()}],"mask-image-conic-to-color":[{"mask-conic-to":$()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:G()}],"mask-repeat":[{mask:V()}],"mask-size":[{mask:T()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",er,Q]}],filter:[{filter:["","none",er,Q]}],blur:[{blur:eu()}],brightness:[{brightness:[O,er,Q]}],contrast:[{contrast:[O,er,Q]}],"drop-shadow":[{"drop-shadow":["","none",p,el,ee]}],"drop-shadow-color":[{"drop-shadow":$()}],grayscale:[{grayscale:["",O,er,Q]}],"hue-rotate":[{"hue-rotate":[O,er,Q]}],invert:[{invert:["",O,er,Q]}],saturate:[{saturate:[O,er,Q]}],sepia:[{sepia:["",O,er,Q]}],"backdrop-filter":[{"backdrop-filter":["","none",er,Q]}],"backdrop-blur":[{"backdrop-blur":eu()}],"backdrop-brightness":[{"backdrop-brightness":[O,er,Q]}],"backdrop-contrast":[{"backdrop-contrast":[O,er,Q]}],"backdrop-grayscale":[{"backdrop-grayscale":["",O,er,Q]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[O,er,Q]}],"backdrop-invert":[{"backdrop-invert":["",O,er,Q]}],"backdrop-opacity":[{"backdrop-opacity":[O,er,Q]}],"backdrop-saturate":[{"backdrop-saturate":[O,er,Q]}],"backdrop-sepia":[{"backdrop-sepia":["",O,er,Q]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",er,Q]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[O,"initial",er,Q]}],ease:[{ease:["linear","initial",x,er,Q]}],delay:[{delay:[O,er,Q]}],animate:[{animate:["none",b,er,Q]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,er,Q]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[er,Q,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:$()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:$()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",er,Q]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",er,Q]}],fill:[{fill:["none",...$()]}],"stroke-w":[{stroke:[O,et,Z,X]}],stroke:[{stroke:["none",...$()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}}),eb=((e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return d(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:a}=r,n=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],o=null==a?void 0:a[e];if(null===r)return null;let n=c(r)||c(o);return s[e][n]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return d(e,n,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...a,...l}[r]):({...a,...l})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gold:"bg-gradient-to-r from-gold-400 to-gold-600 text-white hover:from-gold-500 hover:to-gold-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ev=s.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...a},l)=>(0,o.jsx)(s?n:"button",{className:function(...e){return ex(d(e))}(eb({variant:r,size:t,className:e})),ref:l,...a}));function ey(){let[e,r]=(0,s.useState)(!1);return(0,o.jsx)("nav",{className:"bg-white/95 backdrop-blur-sm border-b border-gold-200/50 sticky top-0 z-50",children:(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,o.jsx)("div",{className:"flex items-center",children:(0,o.jsx)("a",{href:"/",className:"text-2xl font-bold text-gradient-gold",children:"Alokita Candles"})}),(0,o.jsx)("div",{className:"hidden md:block",children:(0,o.jsxs)("div",{className:"ml-10 flex items-baseline space-x-8",children:[(0,o.jsx)("a",{href:"/",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"Home"}),(0,o.jsx)("a",{href:"/products",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"Products"}),(0,o.jsx)("a",{href:"/categories",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"Categories"}),(0,o.jsx)("a",{href:"/about",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"About"}),(0,o.jsx)("a",{href:"/contact",className:"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors",children:"Contact"})]})}),(0,o.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,o.jsx)(ev,{variant:"outline",size:"sm",children:"Cart (0)"}),(0,o.jsx)(ev,{variant:"gold",size:"sm",asChild:!0,children:(0,o.jsx)("a",{href:"/admin",children:"Admin"})})]}),(0,o.jsx)("div",{className:"md:hidden",children:(0,o.jsx)("button",{onClick:()=>r(!e),className:"text-gray-700 hover:text-gold-600 focus:outline-none focus:text-gold-600",children:(0,o.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,o.jsx)("div",{className:"md:hidden",children:(0,o.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gold-200/50",children:[(0,o.jsx)("a",{href:"/",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"Home"}),(0,o.jsx)("a",{href:"/products",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"Products"}),(0,o.jsx)("a",{href:"/categories",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"Categories"}),(0,o.jsx)("a",{href:"/about",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"About"}),(0,o.jsx)("a",{href:"/contact",className:"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors",children:"Contact"}),(0,o.jsxs)("div",{className:"flex space-x-2 px-3 py-2",children:[(0,o.jsx)(ev,{variant:"outline",size:"sm",className:"flex-1",children:"Cart (0)"}),(0,o.jsx)(ev,{variant:"gold",size:"sm",className:"flex-1",asChild:!0,children:(0,o.jsx)("a",{href:"/admin",children:"Admin"})})]})]})})]})})}ev.displayName="Button"},85199:(e,r,t)=>{Promise.resolve().then(t.bind(t,84559))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98995:e=>{"use strict";e.exports=require("node:module")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1385,4379,7137,8628],()=>t(70913));module.exports=o})();