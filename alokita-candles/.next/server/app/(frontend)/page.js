/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(frontend)/page";
exports.ids = ["app/(frontend)/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(frontend)%2Fpage&page=%2F(frontend)%2Fpage&appPaths=%2F(frontend)%2Fpage&pagePath=private-next-app-dir%2F(frontend)%2Fpage.tsx&appDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(frontend)%2Fpage&page=%2F(frontend)%2Fpage&appPaths=%2F(frontend)%2Fpage&pagePath=private-next-app-dir%2F(frontend)%2Fpage.tsx&appDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?98b0\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(frontend)/layout.tsx */ \"(rsc)/./src/app/(frontend)/layout.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(frontend)/page.tsx */ \"(rsc)/./src/app/(frontend)/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(frontend)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module3, \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\"],\n'not-found': [module4, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(frontend)/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(frontend)%2Fpage&page=%2F(frontend)%2Fpage&appPaths=%2F(frontend)%2Fpage&pagePath=private-next-app-dir%2F(frontend)%2Fpage.tsx&appDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(rsc)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(rsc)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMF9zYXNzQDEuNzcuNC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGYXBwJTJGKGZyb250ZW5kKSUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGY29tcG9uZW50cyUyRkZvb3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJGb290ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZoYXJzaGx1aGFyJTJGbGFsaXQlMjBkb24lMkZhbG9raXRhLWNhbmRsZXMlMkZzcmMlMkZjb21wb25lbnRzJTJGTmF2aWdhdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJOYXZpZ2F0aW9uJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBc0k7QUFDdEk7QUFDQSwwS0FBOEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkZvb3RlclwiXSAqLyBcIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2NvbXBvbmVudHMvRm9vdGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTmF2aWdhdGlvblwiXSAqLyBcIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2NvbXBvbmVudHMvTmF2aWdhdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(frontend)/page.tsx */ \"(rsc)/./src/app/(frontend)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMF9zYXNzQDEuNzcuNC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGYXBwJTJGKGZyb250ZW5kKSUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBNEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2FwcC8oZnJvbnRlbmQpL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/(frontend)/globals.css":
/*!****************************************!*\
  !*** ./src/app/(frontend)/globals.css ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ecca505437ef\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhmcm9udGVuZCkvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2FwcC8oZnJvbnRlbmQpL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZWNjYTUwNTQzN2VmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(frontend)/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(frontend)/layout.tsx":
/*!***************************************!*\
  !*** ./src/app/(frontend)/layout.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/(frontend)/globals.css\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    description: 'Alokita Candles - Handcrafted scented candles from Ahmedabad',\n    title: 'Alokita Candles | Handcrafted Scented Candles'\n};\nasync function RootLayout(props) {\n    const { children } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_3__.Navigation, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhmcm9udGVuZCkvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXlCO0FBQ0g7QUFDOEI7QUFDUjtBQUVyQyxNQUFNRyxXQUFXO0lBQ3RCQyxhQUFhO0lBQ2JDLE9BQU87QUFDVCxFQUFDO0FBRWMsZUFBZUMsV0FBV0MsS0FBb0M7SUFDM0UsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR0Q7SUFFckIscUJBQ0UsOERBQUNFO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVU7OzhCQUNkLDhEQUFDWCw4REFBVUE7Ozs7OzhCQUNYLDhEQUFDWTtvQkFBS0QsV0FBVTs4QkFBVUo7Ozs7Ozs4QkFDMUIsOERBQUNOLHNEQUFNQTs7Ozs7Ozs7Ozs7Ozs7OztBQUlmIiwic291cmNlcyI6WyIvVXNlcnMvaGFyc2hsdWhhci9sYWxpdCBkb24vYWxva2l0YS1jYW5kbGVzL3NyYy9hcHAvKGZyb250ZW5kKS9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IE5hdmlnYXRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvTmF2aWdhdGlvbidcbmltcG9ydCB7IEZvb3RlciB9IGZyb20gJ0AvY29tcG9uZW50cy9Gb290ZXInXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgZGVzY3JpcHRpb246ICdBbG9raXRhIENhbmRsZXMgLSBIYW5kY3JhZnRlZCBzY2VudGVkIGNhbmRsZXMgZnJvbSBBaG1lZGFiYWQnLFxuICB0aXRsZTogJ0Fsb2tpdGEgQ2FuZGxlcyB8IEhhbmRjcmFmdGVkIFNjZW50ZWQgQ2FuZGxlcycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIFJvb3RMYXlvdXQocHJvcHM6IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IHsgY2hpbGRyZW4gfSA9IHByb3BzXG5cbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIDxOYXZpZ2F0aW9uIC8+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMVwiPntjaGlsZHJlbn08L21haW4+XG4gICAgICAgIDxGb290ZXIgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIk5hdmlnYXRpb24iLCJGb290ZXIiLCJtZXRhZGF0YSIsImRlc2NyaXB0aW9uIiwidGl0bGUiLCJSb290TGF5b3V0IiwicHJvcHMiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(frontend)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/(frontend)/page.tsx":
/*!*************************************!*\
  !*** ./src/app/(frontend)/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navigation: () => (/* binding */ Navigation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Navigation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx",
"Navigation",
);

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(ssr)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMF9zYXNzQDEuNzcuNC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGYXBwJTJGKGZyb250ZW5kKSUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGY29tcG9uZW50cyUyRkZvb3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJGb290ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZoYXJzaGx1aGFyJTJGbGFsaXQlMjBkb24lMkZhbG9raXRhLWNhbmRsZXMlMkZzcmMlMkZjb21wb25lbnRzJTJGTmF2aWdhdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJOYXZpZ2F0aW9uJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBc0k7QUFDdEk7QUFDQSwwS0FBOEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkZvb3RlclwiXSAqLyBcIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2NvbXBvbmVudHMvRm9vdGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTmF2aWdhdGlvblwiXSAqLyBcIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2NvbXBvbmVudHMvTmF2aWdhdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(frontend)/page.tsx */ \"(ssr)/./src/app/(frontend)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMF9zYXNzQDEuNzcuNC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGYXBwJTJGKGZyb250ZW5kKSUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBNEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2FwcC8oZnJvbnRlbmQpL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(frontend)/page.tsx":
/*!*************************************!*\
  !*** ./src/app/(frontend)/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Mock data for development when database is not available\nconst mockProducts = [\n    {\n        id: '1',\n        name: 'Rose Petal Bliss',\n        price: 899,\n        scent: 'Rose, Jasmine, Peony',\n        isFeatured: true,\n        inStock: true,\n        slug: 'rose-petal-bliss'\n    },\n    {\n        id: '2',\n        name: 'Sandalwood Serenity',\n        price: 1099,\n        scent: 'Sandalwood, Cedar, Vanilla',\n        isFeatured: true,\n        inStock: true,\n        slug: 'sandalwood-serenity'\n    },\n    {\n        id: '3',\n        name: 'Festive Spice',\n        price: 1299,\n        scent: 'Cinnamon, Orange, Clove',\n        isFeatured: true,\n        inStock: true,\n        slug: 'festive-spice'\n    }\n];\nasync function HomePage() {\n    // Use mock data for development (when database is not available)\n    const featuredProducts = mockProducts;\n    // Sample testimonials data (you can move this to CMS later)\n    const testimonials = [\n        {\n            name: \"Priya Sharma\",\n            location: \"Mumbai\",\n            review: \"The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!\",\n            rating: 5\n        },\n        {\n            name: \"Arjun Patel\",\n            location: \"Ahmedabad\",\n            review: \"Amazing quality and the scents are so authentic. Love supporting local artisans!\",\n            rating: 5\n        },\n        {\n            name: \"Kavya Reddy\",\n            location: \"Bangalore\",\n            review: \"Perfect for my meditation space. The woody scent collection is my favorite.\",\n            rating: 4\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"test-tailwind\",\n                children: \"TAILWIND TEST - If this is blue with white text, Tailwind is working!\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                style: {\n                    minHeight: '100vh',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    background: 'linear-gradient(135deg, #fdf2f8 0%, #ffffff 50%, #fef7cd 100%)',\n                    padding: '2rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        maxWidth: '1200px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-gradient-gold\",\n                            style: {\n                                fontSize: 'clamp(2rem, 5vw, 4rem)',\n                                fontWeight: 'bold',\n                                marginBottom: '1.5rem',\n                                lineHeight: '1.2'\n                            },\n                            children: \"Illuminate Your Space with Alokita Candles\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                fontSize: 'clamp(1rem, 2.5vw, 1.5rem)',\n                                color: '#6b7280',\n                                marginBottom: '2rem',\n                                lineHeight: '1.6'\n                            },\n                            children: \"Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '1rem',\n                                justifyContent: 'center',\n                                flexWrap: 'wrap'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        backgroundColor: '#eab308',\n                                        color: 'white',\n                                        padding: '0.75rem 2rem',\n                                        borderRadius: '0.5rem',\n                                        border: 'none',\n                                        fontSize: '1.125rem',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s'\n                                    },\n                                    children: \"Shop Now\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        backgroundColor: 'transparent',\n                                        color: '#374151',\n                                        padding: '0.75rem 2rem',\n                                        borderRadius: '0.5rem',\n                                        border: '2px solid #d1d5db',\n                                        fontSize: '1.125rem',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s'\n                                    },\n                                    children: \"Our Story\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                style: {\n                    padding: '4rem 2rem',\n                    backgroundColor: '#ffffff'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-gradient-gold\",\n                            style: {\n                                fontSize: '2.5rem',\n                                fontWeight: 'bold',\n                                textAlign: 'center',\n                                marginBottom: '3rem'\n                            },\n                            children: \"Featured Candles\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                                gap: '2rem'\n                            },\n                            children: featuredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '0.5rem',\n                                        border: '1px solid #e5e7eb',\n                                        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n                                        overflow: 'hidden',\n                                        transition: 'transform 0.2s, box-shadow 0.2s'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                aspectRatio: '1',\n                                                background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '4rem'\n                                                },\n                                                children: \"\\uD83D\\uDD6F️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '1.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        fontSize: '1.25rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.5rem',\n                                                        color: '#111827'\n                                                    },\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#6b7280',\n                                                        marginBottom: '1rem',\n                                                        fontSize: '0.875rem'\n                                                    },\n                                                    children: product.scent\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'space-between'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                fontSize: '1.5rem',\n                                                                fontWeight: 'bold',\n                                                                color: '#eab308'\n                                                            },\n                                                            children: [\n                                                                \"₹\",\n                                                                product.price.toLocaleString('en-IN')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            style: {\n                                                                backgroundColor: '#eab308',\n                                                                color: 'white',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '0.375rem',\n                                                                border: 'none',\n                                                                fontWeight: '500',\n                                                                cursor: 'pointer',\n                                                                transition: 'background-color 0.2s'\n                                                            },\n                                                            children: \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                style: {\n                    padding: '5rem 2rem',\n                    background: 'linear-gradient(135deg, #fdf2f8 0%, #ffffff 50%, #fef7cd 100%)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: '1fr',\n                            gap: '3rem',\n                            alignItems: 'center'\n                        },\n                        className: \"lg:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-gradient-gold\",\n                                        style: {\n                                            fontSize: '2.5rem',\n                                            fontWeight: 'bold',\n                                            marginBottom: '1.5rem',\n                                            lineHeight: '1.2'\n                                        },\n                                        children: \"Our Story\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: '1.125rem',\n                                            color: '#6b7280',\n                                            marginBottom: '1.5rem',\n                                            lineHeight: '1.7'\n                                        },\n                                        children: \"Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#6b7280',\n                                            marginBottom: '2rem',\n                                            lineHeight: '1.6'\n                                        },\n                                        children: \"Each candle is carefully crafted with premium soy wax and housed in beautiful glass containers that can be repurposed long after the candle has burned.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                            gap: '1rem',\n                                            marginBottom: '2rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                                                    padding: '1.5rem',\n                                                    borderRadius: '0.75rem',\n                                                    border: '1px solid rgba(234, 179, 8, 0.2)',\n                                                    textAlign: 'center',\n                                                    backdropFilter: 'blur(8px)'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '2rem',\n                                                            marginBottom: '0.5rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDF3F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        style: {\n                                                            fontWeight: '600',\n                                                            marginBottom: '0.5rem',\n                                                            color: '#111827'\n                                                        },\n                                                        children: \"Natural Ingredients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            color: '#6b7280'\n                                                        },\n                                                        children: \"Premium soy wax & natural fragrances\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                                                    padding: '1.5rem',\n                                                    borderRadius: '0.75rem',\n                                                    border: '1px solid rgba(234, 179, 8, 0.2)',\n                                                    textAlign: 'center',\n                                                    backdropFilter: 'blur(8px)'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '2rem',\n                                                            marginBottom: '0.5rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDFFA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        style: {\n                                                            fontWeight: '600',\n                                                            marginBottom: '0.5rem',\n                                                            color: '#111827'\n                                                        },\n                                                        children: \"Elegant Glass\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            color: '#6b7280'\n                                                        },\n                                                        children: \"Reusable glass containers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                                                    padding: '1.5rem',\n                                                    borderRadius: '0.75rem',\n                                                    border: '1px solid rgba(234, 179, 8, 0.2)',\n                                                    textAlign: 'center',\n                                                    backdropFilter: 'blur(8px)'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '2rem',\n                                                            marginBottom: '0.5rem'\n                                                        },\n                                                        children: \"✨\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        style: {\n                                                            fontWeight: '600',\n                                                            marginBottom: '0.5rem',\n                                                            color: '#111827'\n                                                        },\n                                                        children: \"Handcrafted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            color: '#6b7280'\n                                                        },\n                                                        children: \"Made with love in Ahmedabad\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            backgroundColor: '#eab308',\n                                            color: 'white',\n                                            padding: '0.75rem 1.5rem',\n                                            borderRadius: '0.5rem',\n                                            border: 'none',\n                                            fontSize: '1rem',\n                                            fontWeight: '500',\n                                            cursor: 'pointer',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                                        },\n                                        children: \"Learn More About Us\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    aspectRatio: '1',\n                                    background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',\n                                    borderRadius: '1rem',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    position: 'relative',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: 'absolute',\n                                            top: '10%',\n                                            left: '10%',\n                                            width: '20px',\n                                            height: '20px',\n                                            backgroundColor: 'rgba(234, 179, 8, 0.3)',\n                                            borderRadius: '50%',\n                                            animation: 'float 3s ease-in-out infinite'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: 'absolute',\n                                            bottom: '20%',\n                                            right: '15%',\n                                            width: '15px',\n                                            height: '15px',\n                                            backgroundColor: 'rgba(236, 72, 153, 0.3)',\n                                            borderRadius: '50%',\n                                            animation: 'float 4s ease-in-out infinite reverse'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '6rem',\n                                            filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))'\n                                        },\n                                        children: \"\\uD83D\\uDD6F️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                style: {\n                    padding: '5rem 2rem',\n                    backgroundColor: '#ffffff'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center',\n                                marginBottom: '3rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gradient-gold\",\n                                    style: {\n                                        fontSize: '2.5rem',\n                                        fontWeight: 'bold',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"What Our Customers Say\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontSize: '1.125rem',\n                                        color: '#6b7280',\n                                        maxWidth: '600px',\n                                        margin: '0 auto'\n                                    },\n                                    children: \"Don't just take our word for it. Here's what our customers have to say about their Alokita Candles experience.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',\n                                gap: '2rem'\n                            },\n                            children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '1rem',\n                                        border: '1px solid #e5e7eb',\n                                        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n                                        padding: '2rem',\n                                        transition: 'all 0.3s ease',\n                                        position: 'relative',\n                                        overflow: 'hidden'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.transform = 'translateY(-4px)';\n                                        e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.15)';\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.transform = 'translateY(0)';\n                                        e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '1rem',\n                                                right: '1rem',\n                                                fontSize: '3rem',\n                                                color: 'rgba(234, 179, 8, 0.1)',\n                                                fontFamily: 'serif'\n                                            },\n                                            children: '\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                marginBottom: '1.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '3.5rem',\n                                                        height: '3.5rem',\n                                                        background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',\n                                                        borderRadius: '50%',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        marginRight: '1rem',\n                                                        border: '2px solid rgba(234, 179, 8, 0.2)'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontWeight: '600',\n                                                            color: '#eab308',\n                                                            fontSize: '1.25rem'\n                                                        },\n                                                        children: testimonial.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            style: {\n                                                                fontWeight: '600',\n                                                                color: '#111827',\n                                                                marginBottom: '0.25rem',\n                                                                fontSize: '1.125rem'\n                                                            },\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: '0.875rem',\n                                                                color: '#6b7280',\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.25rem'\n                                                            },\n                                                            children: [\n                                                                \"\\uD83D\\uDCCD \",\n                                                                testimonial.location\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                marginBottom: '1.5rem',\n                                                gap: '0.25rem'\n                                            },\n                                            children: [\n                                                [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: i < testimonial.rating ? '#fbbf24' : '#e5e7eb',\n                                                            fontSize: '1.25rem'\n                                                        },\n                                                        children: \"★\"\n                                                    }, i, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        marginLeft: '0.5rem',\n                                                        fontSize: '0.875rem',\n                                                        color: '#6b7280',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: [\n                                                        testimonial.rating,\n                                                        \"/5\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            style: {\n                                                color: '#374151',\n                                                fontStyle: 'italic',\n                                                fontSize: '1rem',\n                                                lineHeight: '1.6',\n                                                position: 'relative',\n                                                paddingLeft: '1rem',\n                                                borderLeft: '3px solid #eab308'\n                                            },\n                                            children: [\n                                                '\"',\n                                                testimonial.review,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginTop: '1.5rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.5rem',\n                                                fontSize: '0.875rem',\n                                                color: '#059669',\n                                                fontWeight: '500'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Verified Purchase\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center',\n                                marginTop: '4rem',\n                                padding: '2rem',\n                                background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',\n                                borderRadius: '1rem',\n                                border: '1px solid rgba(234, 179, 8, 0.2)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: '600',\n                                        marginBottom: '1rem',\n                                        color: '#111827'\n                                    },\n                                    children: \"Join thousands of happy customers\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#6b7280',\n                                        marginBottom: '1.5rem',\n                                        fontSize: '1rem'\n                                    },\n                                    children: \"Experience the magic of Alokita Candles in your own home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        backgroundColor: '#eab308',\n                                        color: 'white',\n                                        padding: '0.75rem 2rem',\n                                        borderRadius: '0.5rem',\n                                        border: 'none',\n                                        fontSize: '1rem',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s',\n                                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                                    },\n                                    children: \"Shop Our Collection\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(frontend)/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        style: {\n            background: 'linear-gradient(135deg, #111827 0%, #1f2937 100%)',\n            color: 'white'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: '1200px',\n                margin: '0 auto',\n                padding: '3rem 2rem'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                        gap: '2rem'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                gridColumn: 'span 2'\n                            },\n                            className: \"md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.5rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '2rem'\n                                            },\n                                            children: \"\\uD83D\\uDD6F️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-gradient-gold\",\n                                            style: {\n                                                fontSize: '1.75rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: \"Alokita Candles\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#d1d5db',\n                                        marginBottom: '1.5rem',\n                                        maxWidth: '400px',\n                                        lineHeight: '1.6'\n                                    },\n                                    children: \"Handcrafted scented candles from Ahmedabad, cased in elegant glass. Each candle is made with premium ingredients and designed to elevate your space.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '1rem'\n                                    },\n                                    children: [\n                                        {\n                                            name: 'Facebook',\n                                            icon: '📘'\n                                        },\n                                        {\n                                            name: 'Instagram',\n                                            icon: '📷'\n                                        },\n                                        {\n                                            name: 'Twitter',\n                                            icon: '🐦'\n                                        },\n                                        {\n                                            name: 'WhatsApp',\n                                            icon: '💬'\n                                        }\n                                    ].map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                width: '2.5rem',\n                                                height: '2.5rem',\n                                                backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                                                borderRadius: '50%',\n                                                color: '#d1d5db',\n                                                textDecoration: 'none',\n                                                transition: 'all 0.2s',\n                                                border: '1px solid rgba(255, 255, 255, 0.1)'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.backgroundColor = '#eab308';\n                                                e.target.style.transform = 'translateY(-2px)';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';\n                                                e.target.style.transform = 'translateY(0)';\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '1.25rem'\n                                                },\n                                                children: social.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, social.name, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/products\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"All Products\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/categories/floral\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"Floral Candles\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/categories/woody\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"Woody Candles\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/about\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDCCD Ahmedabad, Gujarat, India\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDCDE +91 98765 43210\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"✉️ <EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDD52 Mon-Sat: 9AM-6PM\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"\\xa9 2024 Alokita Candles. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-6 mt-4 md:mt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/privacy\",\n                                    className: \"text-gray-400 hover:text-gold-400 text-sm transition-colors\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/terms\",\n                                    className: \"text-gray-400 hover:text-gold-400 text-sm transition-colors\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/shipping\",\n                                    className: \"text-gray-400 hover:text-gold-400 text-sm transition-colors\",\n                                    children: \"Shipping Info\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\nfunction Navigation() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        style: {\n            backgroundColor: 'rgba(255, 255, 255, 0.95)',\n            backdropFilter: 'blur(8px)',\n            borderBottom: '1px solid rgba(234, 179, 8, 0.2)',\n            position: 'sticky',\n            top: 0,\n            zIndex: 50,\n            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: '1200px',\n                margin: '0 auto',\n                padding: '0 1rem'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        height: '4rem'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.5rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDD6F️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gradient-gold\",\n                                            style: {\n                                                fontSize: '1.5rem',\n                                                fontWeight: 'bold',\n                                                letterSpacing: '-0.025em'\n                                            },\n                                            children: \"Alokita Candles\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'none'\n                            },\n                            className: \"md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '2rem',\n                                    marginLeft: '2.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        style: {\n                                            color: '#374151',\n                                            textDecoration: 'none',\n                                            padding: '0.5rem 0.75rem',\n                                            fontSize: '0.875rem',\n                                            fontWeight: '500',\n                                            transition: 'color 0.2s',\n                                            borderRadius: '0.375rem'\n                                        },\n                                        onMouseEnter: (e)=>e.target.style.color = '#eab308',\n                                        onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/products\",\n                                        style: {\n                                            color: '#374151',\n                                            textDecoration: 'none',\n                                            padding: '0.5rem 0.75rem',\n                                            fontSize: '0.875rem',\n                                            fontWeight: '500',\n                                            transition: 'color 0.2s',\n                                            borderRadius: '0.375rem'\n                                        },\n                                        onMouseEnter: (e)=>e.target.style.color = '#eab308',\n                                        onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                        children: \"Products\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/categories\",\n                                        style: {\n                                            color: '#374151',\n                                            textDecoration: 'none',\n                                            padding: '0.5rem 0.75rem',\n                                            fontSize: '0.875rem',\n                                            fontWeight: '500',\n                                            transition: 'color 0.2s',\n                                            borderRadius: '0.375rem'\n                                        },\n                                        onMouseEnter: (e)=>e.target.style.color = '#eab308',\n                                        onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                        children: \"Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/about\",\n                                        style: {\n                                            color: '#374151',\n                                            textDecoration: 'none',\n                                            padding: '0.5rem 0.75rem',\n                                            fontSize: '0.875rem',\n                                            fontWeight: '500',\n                                            transition: 'color 0.2s',\n                                            borderRadius: '0.375rem'\n                                        },\n                                        onMouseEnter: (e)=>e.target.style.color = '#eab308',\n                                        onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/contact\",\n                                        style: {\n                                            color: '#374151',\n                                            textDecoration: 'none',\n                                            padding: '0.5rem 0.75rem',\n                                            fontSize: '0.875rem',\n                                            fontWeight: '500',\n                                            transition: 'color 0.2s',\n                                            borderRadius: '0.375rem'\n                                        },\n                                        onMouseEnter: (e)=>e.target.style.color = '#eab308',\n                                        onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'none',\n                                alignItems: 'center',\n                                gap: '1rem'\n                            },\n                            className: \"md:flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.5rem',\n                                        padding: '0.5rem 1rem',\n                                        border: '1px solid #d1d5db',\n                                        borderRadius: '0.375rem',\n                                        backgroundColor: 'transparent',\n                                        color: '#374151',\n                                        fontSize: '0.875rem',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s'\n                                    },\n                                    children: [\n                                        \"\\uD83D\\uDED2 Cart\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                            style: {\n                                                backgroundColor: '#eab308',\n                                                color: 'white',\n                                                fontSize: '0.75rem',\n                                                padding: '0.125rem 0.375rem'\n                                            },\n                                            children: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        backgroundColor: '#eab308',\n                                        color: 'white',\n                                        padding: '0.5rem 1rem',\n                                        borderRadius: '0.375rem',\n                                        border: 'none',\n                                        fontSize: '0.875rem',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'background-color 0.2s'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/admin\",\n                                        style: {\n                                            color: 'inherit',\n                                            textDecoration: 'none'\n                                        },\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'block'\n                            },\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                style: {\n                                    color: '#374151',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    padding: '0.5rem',\n                                    borderRadius: '0.375rem',\n                                    transition: 'color 0.2s'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    style: {\n                                        width: '1.5rem',\n                                        height: '1.5rem'\n                                    },\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'block'\n                    },\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '1rem',\n                            backgroundColor: 'white',\n                            borderTop: '1px solid rgba(234, 179, 8, 0.2)',\n                            borderRadius: '0 0 0.5rem 0.5rem',\n                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                        },\n                        children: [\n                            [\n                                'Home',\n                                'Products',\n                                'Categories',\n                                'About',\n                                'Contact'\n                            ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item === 'Home' ? '/' : `/${item.toLowerCase()}`,\n                                    style: {\n                                        display: 'block',\n                                        color: '#374151',\n                                        textDecoration: 'none',\n                                        padding: '0.75rem',\n                                        fontSize: '1rem',\n                                        fontWeight: '500',\n                                        borderRadius: '0.375rem',\n                                        transition: 'all 0.2s',\n                                        marginBottom: '0.25rem'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.target.style.backgroundColor = '#fef7cd';\n                                        e.target.style.color = '#eab308';\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.target.style.backgroundColor = 'transparent';\n                                        e.target.style.color = '#374151';\n                                    },\n                                    children: item\n                                }, item, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '0.5rem',\n                                    marginTop: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            flex: 1,\n                                            padding: '0.5rem',\n                                            border: '1px solid #d1d5db',\n                                            borderRadius: '0.375rem',\n                                            backgroundColor: 'transparent',\n                                            color: '#374151',\n                                            fontSize: '0.875rem',\n                                            fontWeight: '500',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"\\uD83D\\uDED2 Cart (0)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            flex: 1,\n                                            backgroundColor: '#eab308',\n                                            color: 'white',\n                                            padding: '0.5rem',\n                                            borderRadius: '0.375rem',\n                                            border: 'none',\n                                            fontSize: '0.875rem',\n                                            fontWeight: '500',\n                                            cursor: 'pointer'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/admin\",\n                                            style: {\n                                                color: 'inherit',\n                                                textDecoration: 'none'\n                                            },\n                                            children: \"Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9OYXZpZ2F0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXVDO0FBRU07QUFFdEMsU0FBU0c7SUFDZCxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0osK0NBQVFBLENBQUM7SUFFN0MscUJBQ0UsOERBQUNLO1FBQUlDLE9BQU87WUFDVkMsaUJBQWlCO1lBQ2pCQyxnQkFBZ0I7WUFDaEJDLGNBQWM7WUFDZEMsVUFBVTtZQUNWQyxLQUFLO1lBQ0xDLFFBQVE7WUFDUkMsV0FBVztRQUNiO2tCQUNFLDRFQUFDQztZQUFJUixPQUFPO2dCQUFFUyxVQUFVO2dCQUFVQyxRQUFRO2dCQUFVQyxTQUFTO1lBQVM7OzhCQUNwRSw4REFBQ0g7b0JBQUlSLE9BQU87d0JBQUVZLFNBQVM7d0JBQVFDLFlBQVk7d0JBQVVDLGdCQUFnQjt3QkFBaUJDLFFBQVE7b0JBQU87O3NDQUVuRyw4REFBQ1A7NEJBQUlSLE9BQU87Z0NBQUVZLFNBQVM7Z0NBQVFDLFlBQVk7NEJBQVM7c0NBQ2xELDRFQUFDRztnQ0FBRUMsTUFBSztnQ0FBSWpCLE9BQU87b0NBQUVrQixnQkFBZ0I7Z0NBQU87MENBQzFDLDRFQUFDVjtvQ0FBSVIsT0FBTzt3Q0FBRVksU0FBUzt3Q0FBUUMsWUFBWTt3Q0FBVU0sS0FBSztvQ0FBUzs7c0RBQ2pFLDhEQUFDQzs0Q0FBS3BCLE9BQU87Z0RBQUVxQixVQUFVOzRDQUFTO3NEQUFHOzs7Ozs7c0RBQ3JDLDhEQUFDRDs0Q0FBS0UsV0FBVTs0Q0FBcUJ0QixPQUFPO2dEQUMxQ3FCLFVBQVU7Z0RBQ1ZFLFlBQVk7Z0RBQ1pDLGVBQWU7NENBQ2pCO3NEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVFULDhEQUFDaEI7NEJBQUlSLE9BQU87Z0NBQUVZLFNBQVM7NEJBQU87NEJBQUdVLFdBQVU7c0NBQ3pDLDRFQUFDZDtnQ0FBSVIsT0FBTztvQ0FBRVksU0FBUztvQ0FBUUMsWUFBWTtvQ0FBVU0sS0FBSztvQ0FBUU0sWUFBWTtnQ0FBUzs7a0RBQ3JGLDhEQUFDVDt3Q0FDQ0MsTUFBSzt3Q0FDTGpCLE9BQU87NENBQ0wwQixPQUFPOzRDQUNQUixnQkFBZ0I7NENBQ2hCUCxTQUFTOzRDQUNUVSxVQUFVOzRDQUNWRSxZQUFZOzRDQUNaSSxZQUFZOzRDQUNaQyxjQUFjO3dDQUNoQjt3Q0FDQUMsY0FBYyxDQUFDQyxJQUFNQSxFQUFFQyxNQUFNLENBQUMvQixLQUFLLENBQUMwQixLQUFLLEdBQUc7d0NBQzVDTSxjQUFjLENBQUNGLElBQU1BLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUssQ0FBQzBCLEtBQUssR0FBRztrREFDN0M7Ozs7OztrREFHRCw4REFBQ1Y7d0NBQ0NDLE1BQUs7d0NBQ0xqQixPQUFPOzRDQUNMMEIsT0FBTzs0Q0FDUFIsZ0JBQWdCOzRDQUNoQlAsU0FBUzs0Q0FDVFUsVUFBVTs0Q0FDVkUsWUFBWTs0Q0FDWkksWUFBWTs0Q0FDWkMsY0FBYzt3Q0FDaEI7d0NBQ0FDLGNBQWMsQ0FBQ0MsSUFBTUEsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSyxDQUFDMEIsS0FBSyxHQUFHO3dDQUM1Q00sY0FBYyxDQUFDRixJQUFNQSxFQUFFQyxNQUFNLENBQUMvQixLQUFLLENBQUMwQixLQUFLLEdBQUc7a0RBQzdDOzs7Ozs7a0RBR0QsOERBQUNWO3dDQUNDQyxNQUFLO3dDQUNMakIsT0FBTzs0Q0FDTDBCLE9BQU87NENBQ1BSLGdCQUFnQjs0Q0FDaEJQLFNBQVM7NENBQ1RVLFVBQVU7NENBQ1ZFLFlBQVk7NENBQ1pJLFlBQVk7NENBQ1pDLGNBQWM7d0NBQ2hCO3dDQUNBQyxjQUFjLENBQUNDLElBQU1BLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUssQ0FBQzBCLEtBQUssR0FBRzt3Q0FDNUNNLGNBQWMsQ0FBQ0YsSUFBTUEsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSyxDQUFDMEIsS0FBSyxHQUFHO2tEQUM3Qzs7Ozs7O2tEQUdELDhEQUFDVjt3Q0FDQ0MsTUFBSzt3Q0FDTGpCLE9BQU87NENBQ0wwQixPQUFPOzRDQUNQUixnQkFBZ0I7NENBQ2hCUCxTQUFTOzRDQUNUVSxVQUFVOzRDQUNWRSxZQUFZOzRDQUNaSSxZQUFZOzRDQUNaQyxjQUFjO3dDQUNoQjt3Q0FDQUMsY0FBYyxDQUFDQyxJQUFNQSxFQUFFQyxNQUFNLENBQUMvQixLQUFLLENBQUMwQixLQUFLLEdBQUc7d0NBQzVDTSxjQUFjLENBQUNGLElBQU1BLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUssQ0FBQzBCLEtBQUssR0FBRztrREFDN0M7Ozs7OztrREFHRCw4REFBQ1Y7d0NBQ0NDLE1BQUs7d0NBQ0xqQixPQUFPOzRDQUNMMEIsT0FBTzs0Q0FDUFIsZ0JBQWdCOzRDQUNoQlAsU0FBUzs0Q0FDVFUsVUFBVTs0Q0FDVkUsWUFBWTs0Q0FDWkksWUFBWTs0Q0FDWkMsY0FBYzt3Q0FDaEI7d0NBQ0FDLGNBQWMsQ0FBQ0MsSUFBTUEsRUFBRUMsTUFBTSxDQUFDL0IsS0FBSyxDQUFDMEIsS0FBSyxHQUFHO3dDQUM1Q00sY0FBYyxDQUFDRixJQUFNQSxFQUFFQyxNQUFNLENBQUMvQixLQUFLLENBQUMwQixLQUFLLEdBQUc7a0RBQzdDOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPTCw4REFBQ2xCOzRCQUFJUixPQUFPO2dDQUFFWSxTQUFTO2dDQUFRQyxZQUFZO2dDQUFVTSxLQUFLOzRCQUFPOzRCQUFHRyxXQUFVOzs4Q0FDNUUsOERBQUNXO29DQUFPakMsT0FBTzt3Q0FDYlksU0FBUzt3Q0FDVEMsWUFBWTt3Q0FDWk0sS0FBSzt3Q0FDTFIsU0FBUzt3Q0FDVHVCLFFBQVE7d0NBQ1JOLGNBQWM7d0NBQ2QzQixpQkFBaUI7d0NBQ2pCeUIsT0FBTzt3Q0FDUEwsVUFBVTt3Q0FDVkUsWUFBWTt3Q0FDWlksUUFBUTt3Q0FDUlIsWUFBWTtvQ0FDZDs7d0NBQUc7c0RBRUQsOERBQUNoQyx1REFBS0E7NENBQUNLLE9BQU87Z0RBQ1pDLGlCQUFpQjtnREFDakJ5QixPQUFPO2dEQUNQTCxVQUFVO2dEQUNWVixTQUFTOzRDQUNYO3NEQUFHOzs7Ozs7Ozs7Ozs7OENBSUwsOERBQUNzQjtvQ0FBT2pDLE9BQU87d0NBQ2JDLGlCQUFpQjt3Q0FDakJ5QixPQUFPO3dDQUNQZixTQUFTO3dDQUNUaUIsY0FBYzt3Q0FDZE0sUUFBUTt3Q0FDUmIsVUFBVTt3Q0FDVkUsWUFBWTt3Q0FDWlksUUFBUTt3Q0FDUlIsWUFBWTtvQ0FDZDs4Q0FDRSw0RUFBQ1g7d0NBQUVDLE1BQUs7d0NBQVNqQixPQUFPOzRDQUFFMEIsT0FBTzs0Q0FBV1IsZ0JBQWdCO3dDQUFPO2tEQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPMUUsOERBQUNWOzRCQUFJUixPQUFPO2dDQUFFWSxTQUFTOzRCQUFROzRCQUFHVSxXQUFVO3NDQUMxQyw0RUFBQ1c7Z0NBQ0NHLFNBQVMsSUFBTXRDLGNBQWMsQ0FBQ0Q7Z0NBQzlCRyxPQUFPO29DQUNMMEIsT0FBTztvQ0FDUFcsWUFBWTtvQ0FDWkgsUUFBUTtvQ0FDUkMsUUFBUTtvQ0FDUnhCLFNBQVM7b0NBQ1RpQixjQUFjO29DQUNkRCxZQUFZO2dDQUNkOzBDQUVBLDRFQUFDVztvQ0FBSXRDLE9BQU87d0NBQUV1QyxPQUFPO3dDQUFVeEIsUUFBUTtvQ0FBUztvQ0FBR3lCLE1BQUs7b0NBQU9DLFNBQVE7b0NBQVlDLFFBQU87OENBQ3ZGN0MsMkJBQ0MsOERBQUM4Qzt3Q0FBS0MsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFDLGFBQWE7d0NBQUdDLEdBQUU7Ozs7OzZEQUVyRSw4REFBQ0o7d0NBQUtDLGVBQWM7d0NBQVFDLGdCQUFlO3dDQUFRQyxhQUFhO3dDQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBUTlFbEQsNEJBQ0MsOERBQUNXO29CQUFJUixPQUFPO3dCQUFFWSxTQUFTO29CQUFRO29CQUFHVSxXQUFVOzhCQUMxQyw0RUFBQ2Q7d0JBQUlSLE9BQU87NEJBQ1ZXLFNBQVM7NEJBQ1RWLGlCQUFpQjs0QkFDakIrQyxXQUFXOzRCQUNYcEIsY0FBYzs0QkFDZHJCLFdBQVc7d0JBQ2I7OzRCQUNHO2dDQUFDO2dDQUFRO2dDQUFZO2dDQUFjO2dDQUFTOzZCQUFVLENBQUMwQyxHQUFHLENBQUMsQ0FBQ0MscUJBQzNELDhEQUFDbEM7b0NBRUNDLE1BQU1pQyxTQUFTLFNBQVMsTUFBTSxDQUFDLENBQUMsRUFBRUEsS0FBS0MsV0FBVyxJQUFJO29DQUN0RG5ELE9BQU87d0NBQ0xZLFNBQVM7d0NBQ1RjLE9BQU87d0NBQ1BSLGdCQUFnQjt3Q0FDaEJQLFNBQVM7d0NBQ1RVLFVBQVU7d0NBQ1ZFLFlBQVk7d0NBQ1pLLGNBQWM7d0NBQ2RELFlBQVk7d0NBQ1p5QixjQUFjO29DQUNoQjtvQ0FDQXZCLGNBQWMsQ0FBQ0M7d0NBQ2JBLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUssQ0FBQ0MsZUFBZSxHQUFHO3dDQUNqQzZCLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUssQ0FBQzBCLEtBQUssR0FBRztvQ0FDekI7b0NBQ0FNLGNBQWMsQ0FBQ0Y7d0NBQ2JBLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUssQ0FBQ0MsZUFBZSxHQUFHO3dDQUNqQzZCLEVBQUVDLE1BQU0sQ0FBQy9CLEtBQUssQ0FBQzBCLEtBQUssR0FBRztvQ0FDekI7OENBRUN3QjttQ0F0QklBOzs7OzswQ0F5QlQsOERBQUMxQztnQ0FBSVIsT0FBTztvQ0FBRVksU0FBUztvQ0FBUU8sS0FBSztvQ0FBVWtDLFdBQVc7Z0NBQU87O2tEQUM5RCw4REFBQ3BCO3dDQUFPakMsT0FBTzs0Q0FDYnNELE1BQU07NENBQ04zQyxTQUFTOzRDQUNUdUIsUUFBUTs0Q0FDUk4sY0FBYzs0Q0FDZDNCLGlCQUFpQjs0Q0FDakJ5QixPQUFPOzRDQUNQTCxVQUFVOzRDQUNWRSxZQUFZOzRDQUNaWSxRQUFRO3dDQUNWO2tEQUFHOzs7Ozs7a0RBR0gsOERBQUNGO3dDQUFPakMsT0FBTzs0Q0FDYnNELE1BQU07NENBQ05yRCxpQkFBaUI7NENBQ2pCeUIsT0FBTzs0Q0FDUGYsU0FBUzs0Q0FDVGlCLGNBQWM7NENBQ2RNLFFBQVE7NENBQ1JiLFVBQVU7NENBQ1ZFLFlBQVk7NENBQ1pZLFFBQVE7d0NBQ1Y7a0RBQ0UsNEVBQUNuQjs0Q0FBRUMsTUFBSzs0Q0FBU2pCLE9BQU87Z0RBQUUwQixPQUFPO2dEQUFXUixnQkFBZ0I7NENBQU87c0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVd4RiIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9zcmMvY29tcG9uZW50cy9OYXZpZ2F0aW9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5cbmV4cG9ydCBmdW5jdGlvbiBOYXZpZ2F0aW9uKCkge1xuICBjb25zdCBbaXNNZW51T3Blbiwgc2V0SXNNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICByZXR1cm4gKFxuICAgIDxuYXYgc3R5bGU9e3tcbiAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC45NSknLFxuICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDhweCknLFxuICAgICAgYm9yZGVyQm90dG9tOiAnMXB4IHNvbGlkIHJnYmEoMjM0LCAxNzksIDgsIDAuMiknLFxuICAgICAgcG9zaXRpb246ICdzdGlja3knLFxuICAgICAgdG9wOiAwLFxuICAgICAgekluZGV4OiA1MCxcbiAgICAgIGJveFNoYWRvdzogJzAgMXB4IDNweCAwIHJnYmEoMCwgMCwgMCwgMC4xKSdcbiAgICB9fT5cbiAgICAgIDxkaXYgc3R5bGU9e3sgbWF4V2lkdGg6ICcxMjAwcHgnLCBtYXJnaW46ICcwIGF1dG8nLCBwYWRkaW5nOiAnMCAxcmVtJyB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBoZWlnaHQ6ICc0cmVtJyB9fT5cbiAgICAgICAgICB7LyogTG9nbyAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICA8YSBocmVmPVwiL1wiIHN0eWxlPXt7IHRleHREZWNvcmF0aW9uOiAnbm9uZScgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMC41cmVtJyB9fT5cbiAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250U2l6ZTogJzEuNXJlbScgfX0+8J+Vr++4jzwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYWRpZW50LWdvbGRcIiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogJy0wLjAyNWVtJ1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgQWxva2l0YSBDYW5kbGVzXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBEZXNrdG9wIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnbm9uZScgfX0gY2xhc3NOYW1lPVwibWQ6YmxvY2tcIj5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMnJlbScsIG1hcmdpbkxlZnQ6ICcyLjVyZW0nIH19PlxuICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgIGhyZWY9XCIvXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzc0MTUxJyxcbiAgICAgICAgICAgICAgICAgIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtIDAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdjb2xvciAwLjJzJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuMzc1cmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnI2VhYjMwOCd9XG4gICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnIzM3NDE1MSd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBIb21lXG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICBocmVmPVwiL3Byb2R1Y3RzXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzc0MTUxJyxcbiAgICAgICAgICAgICAgICAgIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtIDAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdjb2xvciAwLjJzJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuMzc1cmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnI2VhYjMwOCd9XG4gICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnIzM3NDE1MSd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBQcm9kdWN0c1xuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9jYXRlZ29yaWVzXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzc0MTUxJyxcbiAgICAgICAgICAgICAgICAgIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtIDAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdjb2xvciAwLjJzJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuMzc1cmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnI2VhYjMwOCd9XG4gICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnIzM3NDE1MSd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYXRlZ29yaWVzXG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICBocmVmPVwiL2Fib3V0XCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzc0MTUxJyxcbiAgICAgICAgICAgICAgICAgIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtIDAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdjb2xvciAwLjJzJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuMzc1cmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnI2VhYjMwOCd9XG4gICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnIzM3NDE1MSd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBBYm91dFxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9jb250YWN0XCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzc0MTUxJyxcbiAgICAgICAgICAgICAgICAgIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtIDAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdjb2xvciAwLjJzJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuMzc1cmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnI2VhYjMwOCd9XG4gICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnIzM3NDE1MSd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDb250YWN0XG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIENhcnQgYW5kIEFkbWluICovfVxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ25vbmUnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMXJlbScgfX0gY2xhc3NOYW1lPVwibWQ6ZmxleFwiPlxuICAgICAgICAgICAgPGJ1dHRvbiBzdHlsZT17e1xuICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICBnYXA6ICcwLjVyZW0nLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtIDFyZW0nLFxuICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2QxZDVkYicsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuMzc1cmVtJyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICBjb2xvcjogJyMzNzQxNTEnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzAuODc1cmVtJyxcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuMnMnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAg8J+bkiBDYXJ0XG4gICAgICAgICAgICAgIDxCYWRnZSBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNlYWIzMDgnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC43NXJlbScsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzAuMTI1cmVtIDAuMzc1cmVtJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAwXG4gICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b24gc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2VhYjMwOCcsXG4gICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtIDFyZW0nLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjM3NXJlbScsXG4gICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzAuODc1cmVtJyxcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYmFja2dyb3VuZC1jb2xvciAwLjJzJ1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIvYWRtaW5cIiBzdHlsZT17eyBjb2xvcjogJ2luaGVyaXQnLCB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnIH19PlxuICAgICAgICAgICAgICAgIEFkbWluXG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE1vYmlsZSBtZW51IGJ1dHRvbiAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdibG9jaycgfX0gY2xhc3NOYW1lPVwibWQ6aGlkZGVuXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oIWlzTWVudU9wZW4pfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGNvbG9yOiAnIzM3NDE1MScsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ25vbmUnLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjVyZW0nLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuMzc1cmVtJyxcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnY29sb3IgMC4ycydcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBzdHlsZT17eyB3aWR0aDogJzEuNXJlbScsIGhlaWdodDogJzEuNXJlbScgfX0gZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAge2lzTWVudU9wZW4gPyAoXG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNiAxOEwxOCA2TTYgNmwxMiAxMlwiIC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk00IDZoMTZNNCAxMmgxNk00IDE4aDE2XCIgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTW9iaWxlIE5hdmlnYXRpb24gKi99XG4gICAgICAgIHtpc01lbnVPcGVuICYmIChcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdibG9jaycgfX0gY2xhc3NOYW1lPVwibWQ6aGlkZGVuXCI+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcxcmVtJyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBib3JkZXJUb3A6ICcxcHggc29saWQgcmdiYSgyMzQsIDE3OSwgOCwgMC4yKScsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAgMCAwLjVyZW0gMC41cmVtJyxcbiAgICAgICAgICAgICAgYm94U2hhZG93OiAnMCA0cHggNnB4IC0xcHggcmdiYSgwLCAwLCAwLCAwLjEpJ1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIHtbJ0hvbWUnLCAnUHJvZHVjdHMnLCAnQ2F0ZWdvcmllcycsICdBYm91dCcsICdDb250YWN0J10ubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgIGtleT17aXRlbX1cbiAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0gPT09ICdIb21lJyA/ICcvJyA6IGAvJHtpdGVtLnRvTG93ZXJDYXNlKCl9YH1cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdibG9jaycsXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzM3NDE1MScsXG4gICAgICAgICAgICAgICAgICAgIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuMzc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzJyxcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMC4yNXJlbSdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmRDb2xvciA9ICcjZmVmN2NkJ1xuICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5jb2xvciA9ICcjZWFiMzA4J1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gJ3RyYW5zcGFyZW50J1xuICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5jb2xvciA9ICcjMzc0MTUxJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXRlbX1cbiAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMC41cmVtJywgbWFyZ2luVG9wOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgZmxleDogMSxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICNkMWQ1ZGInLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMC4zNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzc0MTUxJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJ1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAg8J+bkiBDYXJ0ICgwKVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b24gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGZsZXg6IDEsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZWFiMzA4JyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNXJlbScsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjM3NXJlbScsXG4gICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJ1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIi9hZG1pblwiIHN0eWxlPXt7IGNvbG9yOiAnaW5oZXJpdCcsIHRleHREZWNvcmF0aW9uOiAnbm9uZScgfX0+XG4gICAgICAgICAgICAgICAgICAgIEFkbWluXG4gICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L25hdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJCYWRnZSIsIk5hdmlnYXRpb24iLCJpc01lbnVPcGVuIiwic2V0SXNNZW51T3BlbiIsIm5hdiIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwiYmFja2Ryb3BGaWx0ZXIiLCJib3JkZXJCb3R0b20iLCJwb3NpdGlvbiIsInRvcCIsInpJbmRleCIsImJveFNoYWRvdyIsImRpdiIsIm1heFdpZHRoIiwibWFyZ2luIiwicGFkZGluZyIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJoZWlnaHQiLCJhIiwiaHJlZiIsInRleHREZWNvcmF0aW9uIiwiZ2FwIiwic3BhbiIsImZvbnRTaXplIiwiY2xhc3NOYW1lIiwiZm9udFdlaWdodCIsImxldHRlclNwYWNpbmciLCJtYXJnaW5MZWZ0IiwiY29sb3IiLCJ0cmFuc2l0aW9uIiwiYm9yZGVyUmFkaXVzIiwib25Nb3VzZUVudGVyIiwiZSIsInRhcmdldCIsIm9uTW91c2VMZWF2ZSIsImJ1dHRvbiIsImJvcmRlciIsImN1cnNvciIsIm9uQ2xpY2siLCJiYWNrZ3JvdW5kIiwic3ZnIiwid2lkdGgiLCJmaWxsIiwidmlld0JveCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJib3JkZXJUb3AiLCJtYXAiLCJpdGVtIiwidG9Mb3dlckNhc2UiLCJtYXJnaW5Cb3R0b20iLCJtYXJnaW5Ub3AiLCJmbGV4Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.0_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@3.3.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9zcmMvbGliL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/tailwind-merge@3.3.0","vendor-chunks/clsx@2.1.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@19.1.0_react@19.1.0","vendor-chunks/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.0_react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(frontend)%2Fpage&page=%2F(frontend)%2Fpage&appPaths=%2F(frontend)%2Fpage&pagePath=private-next-app-dir%2F(frontend)%2Fpage.tsx&appDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();