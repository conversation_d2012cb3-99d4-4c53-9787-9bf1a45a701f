/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(frontend)/page";
exports.ids = ["app/(frontend)/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(frontend)%2Fpage&page=%2F(frontend)%2Fpage&appPaths=%2F(frontend)%2Fpage&pagePath=private-next-app-dir%2F(frontend)%2Fpage.tsx&appDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(frontend)%2Fpage&page=%2F(frontend)%2Fpage&appPaths=%2F(frontend)%2Fpage&pagePath=private-next-app-dir%2F(frontend)%2Fpage.tsx&appDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?98b0\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(frontend)/layout.tsx */ \"(rsc)/./src/app/(frontend)/layout.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(frontend)/page.tsx */ \"(rsc)/./src/app/(frontend)/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(frontend)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module3, \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\"],\n'not-found': [module4, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(frontend)/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(frontend)%2Fpage&page=%2F(frontend)%2Fpage&appPaths=%2F(frontend)%2Fpage&pagePath=private-next-app-dir%2F(frontend)%2Fpage.tsx&appDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(rsc)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMF9zYXNzQDEuNzcuNC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGYXBwJTJGKGZyb250ZW5kKSUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGY29tcG9uZW50cyUyRk5hdmlnYXRpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTmF2aWdhdGlvbiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQThJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJOYXZpZ2F0aW9uXCJdICovIFwiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9zcmMvY29tcG9uZW50cy9OYXZpZ2F0aW9uLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/(frontend)/globals.css":
/*!****************************************!*\
  !*** ./src/app/(frontend)/globals.css ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7e1de3efb8ee\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhmcm9udGVuZCkvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvc3JjL2FwcC8oZnJvbnRlbmQpL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiN2UxZGUzZWZiOGVlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(frontend)/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(frontend)/layout.tsx":
/*!***************************************!*\
  !*** ./src/app/(frontend)/layout.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/(frontend)/globals.css\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    description: 'Alokita Candles - Handcrafted scented candles from Ahmedabad',\n    title: 'Alokita Candles | Handcrafted Scented Candles'\n};\nasync function RootLayout(props) {\n    const { children } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_3__.Navigation, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhmcm9udGVuZCkvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXlCO0FBQ0g7QUFDOEI7QUFDUjtBQUVyQyxNQUFNRyxXQUFXO0lBQ3RCQyxhQUFhO0lBQ2JDLE9BQU87QUFDVCxFQUFDO0FBRWMsZUFBZUMsV0FBV0MsS0FBb0M7SUFDM0UsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR0Q7SUFFckIscUJBQ0UsOERBQUNFO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVU7OzhCQUNkLDhEQUFDWCw4REFBVUE7Ozs7OzhCQUNYLDhEQUFDWTtvQkFBS0QsV0FBVTs4QkFBVUo7Ozs7Ozs4QkFDMUIsOERBQUNOLHNEQUFNQTs7Ozs7Ozs7Ozs7Ozs7OztBQUlmIiwic291cmNlcyI6WyIvVXNlcnMvaGFyc2hsdWhhci9sYWxpdCBkb24vYWxva2l0YS1jYW5kbGVzL3NyYy9hcHAvKGZyb250ZW5kKS9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IE5hdmlnYXRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvTmF2aWdhdGlvbidcbmltcG9ydCB7IEZvb3RlciB9IGZyb20gJ0AvY29tcG9uZW50cy9Gb290ZXInXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgZGVzY3JpcHRpb246ICdBbG9raXRhIENhbmRsZXMgLSBIYW5kY3JhZnRlZCBzY2VudGVkIGNhbmRsZXMgZnJvbSBBaG1lZGFiYWQnLFxuICB0aXRsZTogJ0Fsb2tpdGEgQ2FuZGxlcyB8IEhhbmRjcmFmdGVkIFNjZW50ZWQgQ2FuZGxlcycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIFJvb3RMYXlvdXQocHJvcHM6IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IHsgY2hpbGRyZW4gfSA9IHByb3BzXG5cbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIDxOYXZpZ2F0aW9uIC8+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMVwiPntjaGlsZHJlbn08L21haW4+XG4gICAgICAgIDxGb290ZXIgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIk5hdmlnYXRpb24iLCJGb290ZXIiLCJtZXRhZGF0YSIsImRlc2NyaXB0aW9uIiwidGl0bGUiLCJSb290TGF5b3V0IiwicHJvcHMiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(frontend)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/(frontend)/page.tsx":
/*!*************************************!*\
  !*** ./src/app/(frontend)/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _payload_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/payload.config */ \"(rsc)/./src/payload.config.ts\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Hero */ \"(rsc)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_ProductGrid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ProductGrid */ \"(rsc)/./src/components/ProductGrid.tsx\");\n/* harmony import */ var _components_About__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/About */ \"(rsc)/./src/components/About.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Testimonials */ \"(rsc)/./src/components/Testimonials.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_1__, _payload_config__WEBPACK_IMPORTED_MODULE_3__]);\n([payload__WEBPACK_IMPORTED_MODULE_1__, _payload_config__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nasync function HomePage() {\n    let featuredProducts = [];\n    try {\n        const payloadConfig = await _payload_config__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        const payload = await (0,payload__WEBPACK_IMPORTED_MODULE_1__.getPayload)({\n            config: payloadConfig\n        });\n        // Fetch featured products\n        const { docs } = await payload.find({\n            collection: 'products',\n            where: {\n                isFeatured: {\n                    equals: true\n                },\n                inStock: {\n                    equals: true\n                }\n            },\n            limit: 6\n        });\n        featuredProducts = docs;\n    } catch (error) {\n        console.log('Database not available, using mock data');\n        // Mock data for when database is not available\n        featuredProducts = [];\n    }\n    // Sample testimonials data (you can move this to CMS later)\n    const testimonials = [\n        {\n            name: \"Priya Sharma\",\n            location: \"Mumbai\",\n            review: \"The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!\",\n            rating: 5\n        },\n        {\n            name: \"Arjun Patel\",\n            location: \"Ahmedabad\",\n            review: \"Amazing quality and the scents are so authentic. Love supporting local artisans!\",\n            rating: 5\n        },\n        {\n            name: \"Kavya Reddy\",\n            location: \"Bangalore\",\n            review: \"Perfect for my meditation space. The woody scent collection is my favorite.\",\n            rating: 4\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_4__.Hero, {}, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductGrid__WEBPACK_IMPORTED_MODULE_5__.ProductGrid, {\n                products: featuredProducts,\n                title: \"Featured Candles\",\n                className: \"bg-white\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_About__WEBPACK_IMPORTED_MODULE_6__.About, {\n                className: \"bg-gradient-to-br from-rose-50 to-gold-50\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_7__.Testimonials, {\n                testimonials: testimonials,\n                className: \"bg-white\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/app/(frontend)/page.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(frontend)/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/collections/Categories.ts":
/*!***************************************!*\
  !*** ./src/collections/Categories.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Categories: () => (/* binding */ Categories)\n/* harmony export */ });\nconst Categories = {\n    slug: 'categories',\n    admin: {\n        useAsTitle: 'name',\n        defaultColumns: [\n            'name',\n            'description',\n            'isActive'\n        ]\n    },\n    fields: [\n        {\n            name: 'name',\n            type: 'text',\n            required: true,\n            label: 'Category Name'\n        },\n        {\n            name: 'description',\n            type: 'textarea',\n            label: 'Description'\n        },\n        {\n            name: 'image',\n            type: 'upload',\n            relationTo: 'media',\n            label: 'Category Image'\n        },\n        {\n            name: 'isActive',\n            type: 'checkbox',\n            label: 'Active',\n            defaultValue: true\n        },\n        {\n            name: 'slug',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: 'URL Slug',\n            admin: {\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'sortOrder',\n            type: 'number',\n            label: 'Sort Order',\n            defaultValue: 0,\n            admin: {\n                position: 'sidebar'\n            }\n        }\n    ],\n    hooks: {\n        beforeChange: [\n            ({ data })=>{\n                if (data.name && !data.slug) {\n                    data.slug = data.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');\n                }\n                return data;\n            }\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Categories.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Media.ts":
/*!**********************************!*\
  !*** ./src/collections/Media.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: () => (/* binding */ Media)\n/* harmony export */ });\nconst Media = {\n    slug: 'media',\n    access: {\n        read: ()=>true\n    },\n    admin: {\n        useAsTitle: 'filename'\n    },\n    fields: [\n        {\n            name: 'alt',\n            type: 'text',\n            required: true,\n            label: 'Alt Text'\n        },\n        {\n            name: 'caption',\n            type: 'text',\n            label: 'Caption'\n        },\n        {\n            name: 'type',\n            type: 'select',\n            label: 'Media Type',\n            options: [\n                {\n                    label: 'Product Image',\n                    value: 'product'\n                },\n                {\n                    label: 'Hero Image',\n                    value: 'hero'\n                },\n                {\n                    label: 'Category Image',\n                    value: 'category'\n                },\n                {\n                    label: 'General',\n                    value: 'general'\n                }\n            ],\n            defaultValue: 'general'\n        }\n    ],\n    upload: {\n        staticDir: 'media',\n        imageSizes: [\n            {\n                name: 'thumbnail',\n                width: 400,\n                height: 300,\n                position: 'centre'\n            },\n            {\n                name: 'card',\n                width: 768,\n                height: 1024,\n                position: 'centre'\n            },\n            {\n                name: 'tablet',\n                width: 1024,\n                height: undefined,\n                position: 'centre'\n            },\n            {\n                name: 'desktop',\n                width: 1920,\n                height: undefined,\n                position: 'centre'\n            }\n        ],\n        adminThumbnail: 'thumbnail',\n        mimeTypes: [\n            'image/*'\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Media.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Orders.ts":
/*!***********************************!*\
  !*** ./src/collections/Orders.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Orders: () => (/* binding */ Orders)\n/* harmony export */ });\nconst Orders = {\n    slug: 'orders',\n    admin: {\n        useAsTitle: 'orderNumber',\n        defaultColumns: [\n            'orderNumber',\n            'customerEmail',\n            'status',\n            'total',\n            'createdAt'\n        ]\n    },\n    fields: [\n        {\n            name: 'orderNumber',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: 'Order Number',\n            admin: {\n                readOnly: true\n            }\n        },\n        {\n            name: 'customerEmail',\n            type: 'email',\n            required: true,\n            label: 'Customer Email'\n        },\n        {\n            name: 'customerName',\n            type: 'text',\n            required: true,\n            label: 'Customer Name'\n        },\n        {\n            name: 'customerPhone',\n            type: 'text',\n            label: 'Customer Phone'\n        },\n        {\n            name: 'shippingAddress',\n            type: 'group',\n            label: 'Shipping Address',\n            fields: [\n                {\n                    name: 'street',\n                    type: 'text',\n                    required: true,\n                    label: 'Street Address'\n                },\n                {\n                    name: 'city',\n                    type: 'text',\n                    required: true,\n                    label: 'City'\n                },\n                {\n                    name: 'state',\n                    type: 'text',\n                    required: true,\n                    label: 'State'\n                },\n                {\n                    name: 'pincode',\n                    type: 'text',\n                    required: true,\n                    label: 'Pincode'\n                },\n                {\n                    name: 'country',\n                    type: 'text',\n                    required: true,\n                    label: 'Country',\n                    defaultValue: 'India'\n                }\n            ]\n        },\n        {\n            name: 'items',\n            type: 'array',\n            required: true,\n            minRows: 1,\n            label: 'Order Items',\n            fields: [\n                {\n                    name: 'product',\n                    type: 'relationship',\n                    relationTo: 'products',\n                    required: true\n                },\n                {\n                    name: 'quantity',\n                    type: 'number',\n                    required: true,\n                    min: 1\n                },\n                {\n                    name: 'price',\n                    type: 'number',\n                    required: true,\n                    label: 'Unit Price (₹)'\n                },\n                {\n                    name: 'total',\n                    type: 'number',\n                    required: true,\n                    label: 'Item Total (₹)'\n                }\n            ]\n        },\n        {\n            name: 'subtotal',\n            type: 'number',\n            required: true,\n            label: 'Subtotal (₹)'\n        },\n        {\n            name: 'shipping',\n            type: 'number',\n            label: 'Shipping Cost (₹)',\n            defaultValue: 0\n        },\n        {\n            name: 'tax',\n            type: 'number',\n            label: 'Tax (₹)',\n            defaultValue: 0\n        },\n        {\n            name: 'total',\n            type: 'number',\n            required: true,\n            label: 'Total Amount (₹)'\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            label: 'Order Status',\n            options: [\n                {\n                    label: 'Pending',\n                    value: 'pending'\n                },\n                {\n                    label: 'Confirmed',\n                    value: 'confirmed'\n                },\n                {\n                    label: 'Processing',\n                    value: 'processing'\n                },\n                {\n                    label: 'Shipped',\n                    value: 'shipped'\n                },\n                {\n                    label: 'Delivered',\n                    value: 'delivered'\n                },\n                {\n                    label: 'Cancelled',\n                    value: 'cancelled'\n                }\n            ],\n            defaultValue: 'pending'\n        },\n        {\n            name: 'paymentStatus',\n            type: 'select',\n            label: 'Payment Status',\n            options: [\n                {\n                    label: 'Pending',\n                    value: 'pending'\n                },\n                {\n                    label: 'Paid',\n                    value: 'paid'\n                },\n                {\n                    label: 'Failed',\n                    value: 'failed'\n                },\n                {\n                    label: 'Refunded',\n                    value: 'refunded'\n                }\n            ],\n            defaultValue: 'pending'\n        },\n        {\n            name: 'notes',\n            type: 'textarea',\n            label: 'Order Notes'\n        }\n    ],\n    hooks: {\n        beforeChange: [\n            ({ data, operation })=>{\n                if (operation === 'create' && !data.orderNumber) {\n                    // Generate order number\n                    const timestamp = Date.now();\n                    data.orderNumber = `ALK-${timestamp}`;\n                }\n                return data;\n            }\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Orders.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Pages.ts":
/*!**********************************!*\
  !*** ./src/collections/Pages.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Pages: () => (/* binding */ Pages)\n/* harmony export */ });\nconst Pages = {\n    slug: 'pages',\n    admin: {\n        useAsTitle: 'title',\n        defaultColumns: [\n            'title',\n            'slug',\n            'status'\n        ]\n    },\n    fields: [\n        {\n            name: 'title',\n            type: 'text',\n            required: true,\n            label: 'Page Title'\n        },\n        {\n            name: 'slug',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: 'URL Slug',\n            admin: {\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            label: 'Status',\n            options: [\n                {\n                    label: 'Draft',\n                    value: 'draft'\n                },\n                {\n                    label: 'Published',\n                    value: 'published'\n                }\n            ],\n            defaultValue: 'draft',\n            admin: {\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'metaTitle',\n            type: 'text',\n            label: 'Meta Title',\n            admin: {\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'metaDescription',\n            type: 'textarea',\n            label: 'Meta Description',\n            admin: {\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'hero',\n            type: 'group',\n            label: 'Hero Section',\n            fields: [\n                {\n                    name: 'enabled',\n                    type: 'checkbox',\n                    label: 'Enable Hero Section',\n                    defaultValue: false\n                },\n                {\n                    name: 'headline',\n                    type: 'text',\n                    label: 'Headline',\n                    admin: {\n                        condition: (data)=>data.hero?.enabled\n                    }\n                },\n                {\n                    name: 'subheadline',\n                    type: 'text',\n                    label: 'Subheadline',\n                    admin: {\n                        condition: (data)=>data.hero?.enabled\n                    }\n                },\n                {\n                    name: 'backgroundImage',\n                    type: 'upload',\n                    relationTo: 'media',\n                    label: 'Background Image',\n                    admin: {\n                        condition: (data)=>data.hero?.enabled\n                    }\n                },\n                {\n                    name: 'ctaText',\n                    type: 'text',\n                    label: 'CTA Button Text',\n                    admin: {\n                        condition: (data)=>data.hero?.enabled\n                    }\n                },\n                {\n                    name: 'ctaLink',\n                    type: 'text',\n                    label: 'CTA Button Link',\n                    admin: {\n                        condition: (data)=>data.hero?.enabled\n                    }\n                }\n            ]\n        },\n        {\n            name: 'blocks',\n            type: 'blocks',\n            label: 'Page Blocks',\n            blocks: [\n                {\n                    slug: 'content',\n                    labels: {\n                        singular: 'Content Block',\n                        plural: 'Content Blocks'\n                    },\n                    fields: [\n                        {\n                            name: 'content',\n                            type: 'richText',\n                            required: true\n                        }\n                    ]\n                },\n                {\n                    slug: 'productGrid',\n                    labels: {\n                        singular: 'Product Grid',\n                        plural: 'Product Grids'\n                    },\n                    fields: [\n                        {\n                            name: 'title',\n                            type: 'text',\n                            label: 'Section Title'\n                        },\n                        {\n                            name: 'products',\n                            type: 'relationship',\n                            relationTo: 'products',\n                            hasMany: true,\n                            label: 'Products to Display'\n                        },\n                        {\n                            name: 'showFeaturedOnly',\n                            type: 'checkbox',\n                            label: 'Show Featured Products Only',\n                            defaultValue: false\n                        },\n                        {\n                            name: 'limit',\n                            type: 'number',\n                            label: 'Number of Products to Show',\n                            defaultValue: 6,\n                            min: 1,\n                            max: 20\n                        }\n                    ]\n                },\n                {\n                    slug: 'testimonials',\n                    labels: {\n                        singular: 'Testimonials Section',\n                        plural: 'Testimonials Sections'\n                    },\n                    fields: [\n                        {\n                            name: 'title',\n                            type: 'text',\n                            label: 'Section Title',\n                            defaultValue: 'What Our Customers Say'\n                        },\n                        {\n                            name: 'testimonials',\n                            type: 'array',\n                            label: 'Testimonials',\n                            fields: [\n                                {\n                                    name: 'name',\n                                    type: 'text',\n                                    required: true,\n                                    label: 'Customer Name'\n                                },\n                                {\n                                    name: 'location',\n                                    type: 'text',\n                                    label: 'Location'\n                                },\n                                {\n                                    name: 'review',\n                                    type: 'textarea',\n                                    required: true,\n                                    label: 'Review Text'\n                                },\n                                {\n                                    name: 'rating',\n                                    type: 'number',\n                                    required: true,\n                                    label: 'Rating (1-5)',\n                                    min: 1,\n                                    max: 5\n                                },\n                                {\n                                    name: 'image',\n                                    type: 'upload',\n                                    relationTo: 'media',\n                                    label: 'Customer Photo'\n                                }\n                            ]\n                        }\n                    ]\n                }\n            ]\n        }\n    ],\n    hooks: {\n        beforeChange: [\n            ({ data })=>{\n                if (data.title && !data.slug) {\n                    data.slug = data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');\n                }\n                return data;\n            }\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Pages.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Products.ts":
/*!*************************************!*\
  !*** ./src/collections/Products.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Products: () => (/* binding */ Products)\n/* harmony export */ });\nconst Products = {\n    slug: 'products',\n    admin: {\n        useAsTitle: 'name',\n        defaultColumns: [\n            'name',\n            'price',\n            'scent',\n            'isFeatured',\n            'inStock'\n        ]\n    },\n    fields: [\n        {\n            name: 'name',\n            type: 'text',\n            required: true,\n            label: 'Product Name'\n        },\n        {\n            name: 'description',\n            type: 'richText',\n            required: true,\n            label: 'Description'\n        },\n        {\n            name: 'price',\n            type: 'number',\n            required: true,\n            label: 'Price (₹)',\n            min: 0\n        },\n        {\n            name: 'scent',\n            type: 'text',\n            required: true,\n            label: 'Scent Profile'\n        },\n        {\n            name: 'scentNotes',\n            type: 'array',\n            label: 'Scent Notes',\n            fields: [\n                {\n                    name: 'note',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'type',\n                    type: 'select',\n                    options: [\n                        {\n                            label: 'Top Note',\n                            value: 'top'\n                        },\n                        {\n                            label: 'Middle Note',\n                            value: 'middle'\n                        },\n                        {\n                            label: 'Base Note',\n                            value: 'base'\n                        }\n                    ],\n                    required: true\n                }\n            ]\n        },\n        {\n            name: 'glassType',\n            type: 'select',\n            required: true,\n            label: 'Glass Type',\n            options: [\n                {\n                    label: 'Clear Glass Jar',\n                    value: 'clear-jar'\n                },\n                {\n                    label: 'Frosted Glass Jar',\n                    value: 'frosted-jar'\n                },\n                {\n                    label: 'Colored Glass Jar',\n                    value: 'colored-jar'\n                },\n                {\n                    label: 'Glass Tumbler',\n                    value: 'tumbler'\n                },\n                {\n                    label: 'Glass Votive',\n                    value: 'votive'\n                }\n            ]\n        },\n        {\n            name: 'glassDescription',\n            type: 'textarea',\n            label: 'Glass Description'\n        },\n        {\n            name: 'images',\n            type: 'array',\n            required: true,\n            minRows: 1,\n            maxRows: 10,\n            label: 'Product Images',\n            fields: [\n                {\n                    name: 'image',\n                    type: 'upload',\n                    relationTo: 'media',\n                    required: true\n                },\n                {\n                    name: 'alt',\n                    type: 'text',\n                    required: true,\n                    label: 'Alt Text'\n                }\n            ]\n        },\n        {\n            name: 'categories',\n            type: 'relationship',\n            relationTo: 'categories',\n            hasMany: true,\n            label: 'Categories'\n        },\n        {\n            name: 'isFeatured',\n            type: 'checkbox',\n            label: 'Featured Product',\n            defaultValue: false\n        },\n        {\n            name: 'inStock',\n            type: 'checkbox',\n            label: 'In Stock',\n            defaultValue: true\n        },\n        {\n            name: 'stockQuantity',\n            type: 'number',\n            label: 'Stock Quantity',\n            min: 0,\n            defaultValue: 0\n        },\n        {\n            name: 'weight',\n            type: 'text',\n            label: 'Weight'\n        },\n        {\n            name: 'burnTime',\n            type: 'text',\n            label: 'Burn Time'\n        },\n        {\n            name: 'dimensions',\n            type: 'group',\n            label: 'Dimensions',\n            fields: [\n                {\n                    name: 'height',\n                    type: 'text',\n                    label: 'Height'\n                },\n                {\n                    name: 'diameter',\n                    type: 'text',\n                    label: 'Diameter'\n                }\n            ]\n        },\n        {\n            name: 'slug',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: 'URL Slug',\n            admin: {\n                position: 'sidebar'\n            }\n        }\n    ],\n    hooks: {\n        beforeChange: [\n            ({ data })=>{\n                if (data.name && !data.slug) {\n                    data.slug = data.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');\n                }\n                return data;\n            }\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Products.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Users.ts":
/*!**********************************!*\
  !*** ./src/collections/Users.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Users: () => (/* binding */ Users)\n/* harmony export */ });\nconst Users = {\n    slug: 'users',\n    admin: {\n        useAsTitle: 'email'\n    },\n    auth: true,\n    fields: []\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29sbGVjdGlvbnMvVXNlcnMudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUVPLE1BQU1BLFFBQTBCO0lBQ3JDQyxNQUFNO0lBQ05DLE9BQU87UUFDTEMsWUFBWTtJQUNkO0lBQ0FDLE1BQU07SUFDTkMsUUFBUSxFQUdQO0FBQ0gsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9zcmMvY29sbGVjdGlvbnMvVXNlcnMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBDb2xsZWN0aW9uQ29uZmlnIH0gZnJvbSAncGF5bG9hZCdcblxuZXhwb3J0IGNvbnN0IFVzZXJzOiBDb2xsZWN0aW9uQ29uZmlnID0ge1xuICBzbHVnOiAndXNlcnMnLFxuICBhZG1pbjoge1xuICAgIHVzZUFzVGl0bGU6ICdlbWFpbCcsXG4gIH0sXG4gIGF1dGg6IHRydWUsXG4gIGZpZWxkczogW1xuICAgIC8vIEVtYWlsIGFkZGVkIGJ5IGRlZmF1bHRcbiAgICAvLyBBZGQgbW9yZSBmaWVsZHMgYXMgbmVlZGVkXG4gIF0sXG59XG4iXSwibmFtZXMiOlsiVXNlcnMiLCJzbHVnIiwiYWRtaW4iLCJ1c2VBc1RpdGxlIiwiYXV0aCIsImZpZWxkcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Users.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/About.tsx":
/*!**********************************!*\
  !*** ./src/components/About.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   About: () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n\n\n\nfunction About({ title = \"Our Story\", content = \"Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.\", image, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `py-16 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl lg:text-4xl font-bold text-gradient-gold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose prose-lg max-w-none\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: content\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: \"Each candle is carefully crafted with premium soy wax and housed in beautiful glass containers that can be repurposed long after the candle has burned. From floral to woody scents, our collection captures the essence of different moods and seasons.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: \"Based in the heart of Ahmedabad, we draw inspiration from the city's rich textile heritage and vibrant colors, translating these elements into fragrances that tell a story.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"gold\",\n                                            size: \"lg\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/products\",\n                                                children: \"Explore Our Collection\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            size: \"lg\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/contact\",\n                                                children: \"Get in Touch\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: image,\n                                    alt: \"About Alokita Candles\",\n                                    className: \"w-full h-96 lg:h-[500px] object-cover rounded-lg shadow-lg\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-96 lg:h-[500px] bg-gradient-to-br from-gold-100 via-rose-100 to-gold-200 rounded-lg shadow-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83D\\uDD6F️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gold-600 font-medium\",\n                                                children: \"Handcrafted with Love\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 -right-4 w-24 h-24 bg-gold-200/30 rounded-full blur-xl\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 -left-4 w-32 h-32 bg-rose-200/30 rounded-full blur-xl\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-2xl\",\n                                        children: \"\\uD83C\\uDF3F\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2\",\n                                    children: \"Natural Ingredients\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Made with premium soy wax and natural fragrances for a clean burn.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-br from-rose-400 to-rose-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-2xl\",\n                                        children: \"\\uD83C\\uDFFA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2\",\n                                    children: \"Elegant Glass\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Beautiful glass containers that can be repurposed after use.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-br from-gold-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-2xl\",\n                                        children: \"✨\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2\",\n                                    children: \"Handcrafted\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Each candle is carefully made by hand with attention to detail.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/About.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9BYm91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF5QjtBQUNzQjtBQVN4QyxTQUFTRSxNQUFNLEVBQ3BCQyxRQUFRLFdBQVcsRUFDbkJDLFVBQVUsbU1BQW1NLEVBQzdNQyxLQUFLLEVBQ0xDLFlBQVksRUFBRSxFQUNIO0lBQ1gscUJBQ0UsOERBQUNDO1FBQVFELFdBQVcsQ0FBQyxNQUFNLEVBQUVBLFdBQVc7a0JBQ3RDLDRFQUFDRTtZQUFJRixXQUFVOzs4QkFDYiw4REFBQ0U7b0JBQUlGLFdBQVU7O3NDQUViLDhEQUFDRTs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNHO29DQUFHSCxXQUFVOzhDQUNYSDs7Ozs7OzhDQUdILDhEQUFDSztvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUNJOzRDQUFFSixXQUFVO3NEQUNWRjs7Ozs7O3NEQUdILDhEQUFDTTs0Q0FBRUosV0FBVTtzREFBZ0M7Ozs7OztzREFNN0MsOERBQUNJOzRDQUFFSixXQUFVO3NEQUFnQzs7Ozs7Ozs7Ozs7OzhDQU0vQyw4REFBQ0U7b0NBQUlGLFdBQVU7O3NEQUNiLDhEQUFDTCx5REFBTUE7NENBQUNVLFNBQVE7NENBQU9DLE1BQUs7NENBQUtDLE9BQU87c0RBQ3RDLDRFQUFDQztnREFBRUMsTUFBSzswREFBWTs7Ozs7Ozs7Ozs7c0RBRXRCLDhEQUFDZCx5REFBTUE7NENBQUNVLFNBQVE7NENBQVVDLE1BQUs7NENBQUtDLE9BQU87c0RBQ3pDLDRFQUFDQztnREFBRUMsTUFBSzswREFBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXpCLDhEQUFDUDs0QkFBSUYsV0FBVTs7Z0NBQ1pELHNCQUNDLDhEQUFDVztvQ0FDQ0MsS0FBS1o7b0NBQ0xhLEtBQUk7b0NBQ0paLFdBQVU7Ozs7O3lEQUdaLDhEQUFDRTtvQ0FBSUYsV0FBVTs4Q0FDYiw0RUFBQ0U7d0NBQUlGLFdBQVU7OzBEQUNiLDhEQUFDRTtnREFBSUYsV0FBVTswREFBZ0I7Ozs7OzswREFDL0IsOERBQUNJO2dEQUFFSixXQUFVOzBEQUE0Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTS9DLDhEQUFDRTtvQ0FBSUYsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDRTtvQ0FBSUYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUtuQiw4REFBQ0U7b0JBQUlGLFdBQVU7O3NDQUNiLDhEQUFDRTs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNFO29DQUFJRixXQUFVOzhDQUNiLDRFQUFDYTt3Q0FBS2IsV0FBVTtrREFBc0I7Ozs7Ozs7Ozs7OzhDQUV4Qyw4REFBQ2M7b0NBQUdkLFdBQVU7OENBQTZCOzs7Ozs7OENBQzNDLDhEQUFDSTtvQ0FBRUosV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OztzQ0FHdkMsOERBQUNFOzRCQUFJRixXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQUlGLFdBQVU7OENBQ2IsNEVBQUNhO3dDQUFLYixXQUFVO2tEQUFzQjs7Ozs7Ozs7Ozs7OENBRXhDLDhEQUFDYztvQ0FBR2QsV0FBVTs4Q0FBNkI7Ozs7Ozs4Q0FDM0MsOERBQUNJO29DQUFFSixXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7O3NDQUd2Qyw4REFBQ0U7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBSUYsV0FBVTs4Q0FDYiw0RUFBQ2E7d0NBQUtiLFdBQVU7a0RBQXNCOzs7Ozs7Ozs7Ozs4Q0FFeEMsOERBQUNjO29DQUFHZCxXQUFVOzhDQUE2Qjs7Ozs7OzhDQUMzQyw4REFBQ0k7b0NBQUVKLFdBQVU7OENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1qRCIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9zcmMvY29tcG9uZW50cy9BYm91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcblxuaW50ZXJmYWNlIEFib3V0UHJvcHMge1xuICB0aXRsZT86IHN0cmluZ1xuICBjb250ZW50Pzogc3RyaW5nXG4gIGltYWdlPzogc3RyaW5nXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQWJvdXQoe1xuICB0aXRsZSA9IFwiT3VyIFN0b3J5XCIsXG4gIGNvbnRlbnQgPSBcIkluc3BpcmVkIGJ5IHRoZSB2aWJyYW50IGN1bHR1cmUgb2YgQWhtZWRhYmFkLCBBbG9raXRhIENhbmRsZXMgYmxlbmQgcHJlbWl1bSBmcmFncmFuY2VzIHdpdGggYXJ0aXNhbmFsIGNyYWZ0c21hbnNoaXAuIE91ciBjYW5kbGVzIGFyZSBwb3VyZWQgaW4gZ2xhc3MsIGRlc2lnbmVkIHRvIGVsZXZhdGUgeW91ciBhbWJpYW5jZSBhbmQgbW9vZC5cIixcbiAgaW1hZ2UsXG4gIGNsYXNzTmFtZSA9IFwiXCJcbn06IEFib3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8c2VjdGlvbiBjbGFzc05hbWU9e2BweS0xNiAke2NsYXNzTmFtZX1gfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTEyIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbGc6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JhZGllbnQtZ29sZFwiPlxuICAgICAgICAgICAgICB7dGl0bGV9XG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIHByb3NlLWxnIG1heC13LW5vbmVcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICB7Y29udGVudH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICBFYWNoIGNhbmRsZSBpcyBjYXJlZnVsbHkgY3JhZnRlZCB3aXRoIHByZW1pdW0gc295IHdheCBhbmQgaG91c2VkIGluIGJlYXV0aWZ1bCBnbGFzcyBjb250YWluZXJzIFxuICAgICAgICAgICAgICAgIHRoYXQgY2FuIGJlIHJlcHVycG9zZWQgbG9uZyBhZnRlciB0aGUgY2FuZGxlIGhhcyBidXJuZWQuIEZyb20gZmxvcmFsIHRvIHdvb2R5IHNjZW50cywgb3VyIFxuICAgICAgICAgICAgICAgIGNvbGxlY3Rpb24gY2FwdHVyZXMgdGhlIGVzc2VuY2Ugb2YgZGlmZmVyZW50IG1vb2RzIGFuZCBzZWFzb25zLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgIEJhc2VkIGluIHRoZSBoZWFydCBvZiBBaG1lZGFiYWQsIHdlIGRyYXcgaW5zcGlyYXRpb24gZnJvbSB0aGUgY2l0eSdzIHJpY2ggdGV4dGlsZSBoZXJpdGFnZSBcbiAgICAgICAgICAgICAgICBhbmQgdmlicmFudCBjb2xvcnMsIHRyYW5zbGF0aW5nIHRoZXNlIGVsZW1lbnRzIGludG8gZnJhZ3JhbmNlcyB0aGF0IHRlbGwgYSBzdG9yeS5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnb2xkXCIgc2l6ZT1cImxnXCIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiL3Byb2R1Y3RzXCI+RXhwbG9yZSBPdXIgQ29sbGVjdGlvbjwvYT5cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwibGdcIiBhc0NoaWxkPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvY29udGFjdFwiPkdldCBpbiBUb3VjaDwvYT5cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBJbWFnZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICB7aW1hZ2UgPyAoXG4gICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICBzcmM9e2ltYWdlfVxuICAgICAgICAgICAgICAgIGFsdD1cIkFib3V0IEFsb2tpdGEgQ2FuZGxlc1wiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtOTYgbGc6aC1bNTAwcHhdIG9iamVjdC1jb3ZlciByb3VuZGVkLWxnIHNoYWRvdy1sZ1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLTk2IGxnOmgtWzUwMHB4XSBiZy1ncmFkaWVudC10by1iciBmcm9tLWdvbGQtMTAwIHZpYS1yb3NlLTEwMCB0by1nb2xkLTIwMCByb3VuZGVkLWxnIHNoYWRvdy1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgbWItNFwiPvCfla/vuI88L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ29sZC02MDAgZm9udC1tZWRpdW1cIj5IYW5kY3JhZnRlZCB3aXRoIExvdmU8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIERlY29yYXRpdmUgZWxlbWVudHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtNCAtcmlnaHQtNCB3LTI0IGgtMjQgYmctZ29sZC0yMDAvMzAgcm91bmRlZC1mdWxsIGJsdXIteGxcIiAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTQgLWxlZnQtNCB3LTMyIGgtMzIgYmctcm9zZS0yMDAvMzAgcm91bmRlZC1mdWxsIGJsdXIteGxcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRmVhdHVyZXMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTYgZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ29sZC00MDAgdG8tZ29sZC02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtMnhsXCI+8J+Mvzwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0yXCI+TmF0dXJhbCBJbmdyZWRpZW50czwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5NYWRlIHdpdGggcHJlbWl1bSBzb3kgd2F4IGFuZCBuYXR1cmFsIGZyYWdyYW5jZXMgZm9yIGEgY2xlYW4gYnVybi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1yb3NlLTQwMCB0by1yb3NlLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC0yeGxcIj7wn4+6PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTJcIj5FbGVnYW50IEdsYXNzPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPkJlYXV0aWZ1bCBnbGFzcyBjb250YWluZXJzIHRoYXQgY2FuIGJlIHJlcHVycG9zZWQgYWZ0ZXIgdXNlLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1iciBmcm9tLWdvbGQtNDAwIHRvLXJvc2UtNDAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LTJ4bFwiPuKcqDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0yXCI+SGFuZGNyYWZ0ZWQ8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+RWFjaCBjYW5kbGUgaXMgY2FyZWZ1bGx5IG1hZGUgYnkgaGFuZCB3aXRoIGF0dGVudGlvbiB0byBkZXRhaWwuPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQnV0dG9uIiwiQWJvdXQiLCJ0aXRsZSIsImNvbnRlbnQiLCJpbWFnZSIsImNsYXNzTmFtZSIsInNlY3Rpb24iLCJkaXYiLCJoMiIsInAiLCJ2YXJpYW50Iiwic2l6ZSIsImFzQ2hpbGQiLCJhIiwiaHJlZiIsImltZyIsInNyYyIsImFsdCIsInNwYW4iLCJoMyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/About.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gradient-gold mb-4\",\n                                    children: \"Alokita Candles\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4 max-w-md\",\n                                    children: \"Handcrafted scented candles from Ahmedabad, cased in elegant glass. Each candle is made with premium ingredients and designed to elevate your space.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-gold-400 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                    lineNumber: 20,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-gold-400 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-gold-400 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                    lineNumber: 30,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-gold-400 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/products\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"All Products\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/categories/floral\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"Floral Candles\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/categories/woody\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"Woody Candles\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/about\",\n                                                className: \"text-gray-300 hover:text-gold-400 transition-colors\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDCCD Ahmedabad, Gujarat, India\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDCDE +91 98765 43210\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"✉️ <EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"\\uD83D\\uDD52 Mon-Sat: 9AM-6PM\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"\\xa9 2024 Alokita Candles. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-6 mt-4 md:mt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/privacy\",\n                                    className: \"text-gray-400 hover:text-gold-400 text-sm transition-colors\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/terms\",\n                                    className: \"text-gray-400 hover:text-gold-400 text-sm transition-colors\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/shipping\",\n                                    className: \"text-gray-400 hover:text-gold-400 text-sm transition-colors\",\n                                    children: \"Shipping Info\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n\n\n\nfunction Hero({ headline = \"Illuminate Your Space with Alokita Candles\", subheadline = \"Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.\", backgroundImage, ctaText = \"Shop Now\", ctaLink = \"/products\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    backgroundImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: backgroundImage,\n                        alt: \"Alokita Candles Hero\",\n                        className: \"w-full h-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full bg-gradient-to-br from-rose-50 via-white to-gold-50\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"block\",\n                            children: headline\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg sm:text-xl lg:text-2xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed\",\n                        children: subheadline\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"gold\",\n                                size: \"lg\",\n                                className: \"text-lg px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: ctaLink,\n                                    children: ctaText\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"lg\",\n                                className: \"text-lg px-8 py-3 bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/about\",\n                                    children: \"Our Story\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-10 -left-10 w-20 h-20 bg-gold-300/20 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-10 -right-10 w-32 h-32 bg-rose-300/20 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Hero.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Hero.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navigation: () => (/* binding */ Navigation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Navigation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx",
"Navigation",
);

/***/ }),

/***/ "(rsc)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCard: () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(rsc)/./src/components/ui/badge.tsx\");\n\n\n\n\n\nfunction ProductCard({ id, name, price, scent, image, isFeatured = false, inStock = true, slug }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"group overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square overflow-hidden\",\n                children: [\n                    image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: image.url,\n                        alt: image.alt,\n                        className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full bg-gradient-to-br from-gold-100 to-rose-100 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gold-600 text-4xl\",\n                            children: \"\\uD83D\\uDD6F️\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-3 left-3 flex flex-col gap-2\",\n                        children: [\n                            isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"gold\",\n                                className: \"shadow-sm\",\n                                children: \"Featured\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            !inStock && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"destructive\",\n                                className: \"shadow-sm\",\n                                children: \"Out of Stock\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"bg-white/90 hover:bg-white text-black border-white/50\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: `/products/${slug}`,\n                                children: \"Quick View\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-lg mb-2 line-clamp-2 group-hover:text-gold-600 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: `/products/${slug}`,\n                            className: \"hover:underline\",\n                            children: name\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-3 line-clamp-1\",\n                        children: scent\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-gold-600\",\n                                children: [\n                                    \"₹\",\n                                    price.toLocaleString('en-IN')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: inStock ? \"default\" : \"outline\",\n                                disabled: !inStock,\n                                className: \"ml-2\",\n                                children: inStock ? \"Add to Cart\" : \"Notify Me\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductCard.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ProductCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ProductGrid.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductGrid.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductGrid: () => (/* binding */ ProductGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProductCard */ \"(rsc)/./src/components/ProductCard.tsx\");\n\n\n\nfunction ProductGrid({ products, title, className = \"\" }) {\n    if (!products || products.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: `py-16 ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: [\n                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-center mb-12 text-gradient-gold\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground text-lg\",\n                            children: \"No products available at the moment.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `py-16 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-3xl font-bold text-center mb-12 text-gradient-gold\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: products.map((product)=>{\n                        const firstImage = product.images?.[0];\n                        let imageData = undefined;\n                        if (firstImage) {\n                            if (typeof firstImage.image === 'string') {\n                                imageData = {\n                                    url: firstImage.image,\n                                    alt: firstImage.alt\n                                };\n                            } else if (firstImage.image && typeof firstImage.image === 'object') {\n                                const mediaObj = firstImage.image;\n                                if (mediaObj.url) {\n                                    imageData = {\n                                        url: mediaObj.url,\n                                        alt: mediaObj.alt || firstImage.alt\n                                    };\n                                }\n                            }\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_2__.ProductCard, {\n                            id: product.id,\n                            name: product.name,\n                            price: product.price,\n                            scent: product.scent,\n                            image: imageData,\n                            isFeatured: product.isFeatured || false,\n                            inStock: product.inStock || false,\n                            slug: product.slug\n                        }, product.id, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ProductGrid.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ProductGrid.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Testimonials.tsx":
/*!*****************************************!*\
  !*** ./src/components/Testimonials.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Testimonials: () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n\n\n\nfunction StarRating({ rating }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-1\",\n        children: [\n            1,\n            2,\n            3,\n            4,\n            5\n        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: `w-5 h-5 ${star <= rating ? 'text-gold-400 fill-current' : 'text-gray-300'}`,\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this)\n            }, star, false, {\n                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\nfunction Testimonials({ title = \"What Our Customers Say\", testimonials, className = \"\" }) {\n    if (!testimonials || testimonials.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `py-16 bg-gradient-to-br from-rose-50 to-gold-50 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-3xl font-bold text-center mb-12 text-gradient-gold\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-white/80 backdrop-blur-sm border-gold-200/50 hover:shadow-lg transition-all duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 mb-4\",\n                                        children: [\n                                            testimonial.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: testimonial.image.url,\n                                                alt: testimonial.image.alt,\n                                                className: \"w-12 h-12 rounded-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-gradient-to-br from-gold-200 to-rose-200 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gold-600 font-semibold text-lg\",\n                                                    children: testimonial.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    testimonial.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: testimonial.location\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                        rating: testimonial.rating\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                        className: \"mt-4 text-gray-700 italic\",\n                                        children: [\n                                            '\"',\n                                            testimonial.review,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Testimonials.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Testimonials.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            gold: \"border-transparent bg-gold-100 text-gold-800 hover:bg-gold-200\",\n            rose: \"border-transparent bg-rose-100 text-rose-800 hover:bg-rose-200\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/badge.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.0_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            gold: \"bg-gradient-to-r from-gold-400 to-gold-600 text-white hover:from-gold-500 hover:to-gold-700\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwwUkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtRQUNSO1FBQ0FDLE1BQU07WUFDSlAsU0FBUztZQUNUUSxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQUMsaUJBQWlCO1FBQ2ZaLFNBQVM7UUFDVFEsTUFBTTtJQUNSO0FBQ0Y7QUFTRixNQUFNSyx1QkFBU25CLDZDQUFnQixDQUM3QixDQUFDLEVBQUVxQixTQUFTLEVBQUVmLE9BQU8sRUFBRVEsSUFBSSxFQUFFUSxVQUFVLEtBQUssRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQ3hELE1BQU1DLE9BQU9ILFVBQVVyQixzREFBSUEsR0FBRztJQUM5QixxQkFDRSw4REFBQ3dCO1FBQ0NKLFdBQVdsQiw4Q0FBRUEsQ0FBQ0MsZUFBZTtZQUFFRTtZQUFTUTtZQUFNTztRQUFVO1FBQ3hERyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE9BQU9PLFdBQVcsR0FBRztBQUVZIiwic291cmNlcyI6WyIvVXNlcnMvaGFyc2hsdWhhci9sYWxpdCBkb24vYWxva2l0YS1jYW5kbGVzL3NyYy9jb21wb25lbnRzL3VpL2J1dHRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFNsb3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNsb3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0gY3ZhKFxuICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3aGl0ZXNwYWNlLW5vd3JhcCByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gcmluZy1vZmZzZXQtYmFja2dyb3VuZCB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OiBcImJnLXByaW1hcnkgdGV4dC1wcmltYXJ5LWZvcmVncm91bmQgaG92ZXI6YmctcHJpbWFyeS85MFwiLFxuICAgICAgICBkZXN0cnVjdGl2ZTpcbiAgICAgICAgICBcImJnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBob3ZlcjpiZy1kZXN0cnVjdGl2ZS85MFwiLFxuICAgICAgICBvdXRsaW5lOlxuICAgICAgICAgIFwiYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIHNlY29uZGFyeTpcbiAgICAgICAgICBcImJnLXNlY29uZGFyeSB0ZXh0LXNlY29uZGFyeS1mb3JlZ3JvdW5kIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxuICAgICAgICBnaG9zdDogXCJob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBsaW5rOiBcInRleHQtcHJpbWFyeSB1bmRlcmxpbmUtb2Zmc2V0LTQgaG92ZXI6dW5kZXJsaW5lXCIsXG4gICAgICAgIGdvbGQ6IFwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWdvbGQtNDAwIHRvLWdvbGQtNjAwIHRleHQtd2hpdGUgaG92ZXI6ZnJvbS1nb2xkLTUwMCBob3Zlcjp0by1nb2xkLTcwMFwiLFxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgZGVmYXVsdDogXCJoLTEwIHB4LTQgcHktMlwiLFxuICAgICAgICBzbTogXCJoLTkgcm91bmRlZC1tZCBweC0zXCIsXG4gICAgICAgIGxnOiBcImgtMTEgcm91bmRlZC1tZCBweC04XCIsXG4gICAgICAgIGljb246IFwiaC0xMCB3LTEwXCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgIHNpemU6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH0sXG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xuICBhc0NoaWxkPzogYm9vbGVhblxufVxuXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImJ1dHRvblwiXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb21wXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiQnV0dG9uXCJcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiY24iLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJnb2xkIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJwcm9wcyIsInJlZiIsIkNvbXAiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/.pnpm/tailwind-merge@3.3.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9zcmMvbGliL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/payload.config.ts":
/*!*******************************!*\
  !*** ./src/payload.config.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _payloadcms_db_mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @payloadcms/db-mongodb */ \"@payloadcms/db-mongodb\");\n/* harmony import */ var _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @payloadcms/payload-cloud */ \"@payloadcms/payload-cloud\");\n/* harmony import */ var _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @payloadcms/richtext-lexical */ \"(rsc)/./node_modules/.pnpm/@payloadcms+richtext-lexical@3.40.0_@faceless-ui+modal@3.0.0-beta.2_react-dom@19.1.0_re_4384e6b8de42eb8ebbc595e286082c41/node_modules/@payloadcms/richtext-lexical/dist/index.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sharp */ \"sharp\");\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(sharp__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _collections_Users__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./collections/Users */ \"(rsc)/./src/collections/Users.ts\");\n/* harmony import */ var _collections_Media__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./collections/Media */ \"(rsc)/./src/collections/Media.ts\");\n/* harmony import */ var _collections_Products__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./collections/Products */ \"(rsc)/./src/collections/Products.ts\");\n/* harmony import */ var _collections_Categories__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./collections/Categories */ \"(rsc)/./src/collections/Categories.ts\");\n/* harmony import */ var _collections_Orders__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./collections/Orders */ \"(rsc)/./src/collections/Orders.ts\");\n/* harmony import */ var _collections_Pages__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./collections/Pages */ \"(rsc)/./src/collections/Pages.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_payloadcms_db_mongodb__WEBPACK_IMPORTED_MODULE_0__, _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__, payload__WEBPACK_IMPORTED_MODULE_3__, _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_12__]);\n([_payloadcms_db_mongodb__WEBPACK_IMPORTED_MODULE_0__, _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__, payload__WEBPACK_IMPORTED_MODULE_3__, _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_12__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// storage-adapter-import-placeholder\n // database-adapter-import\n\n\n\n\n\n\n\n\n\n\n\n\nconst filename = (0,url__WEBPACK_IMPORTED_MODULE_4__.fileURLToPath)(\"file:///Users/<USER>/lalit%20don/alokita-candles/src/payload.config.ts\");\nconst dirname = path__WEBPACK_IMPORTED_MODULE_2___default().dirname(filename);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,payload__WEBPACK_IMPORTED_MODULE_3__.buildConfig)({\n    admin: {\n        user: _collections_Users__WEBPACK_IMPORTED_MODULE_6__.Users.slug,\n        importMap: {\n            baseDir: path__WEBPACK_IMPORTED_MODULE_2___default().resolve(dirname)\n        }\n    },\n    collections: [\n        _collections_Users__WEBPACK_IMPORTED_MODULE_6__.Users,\n        _collections_Media__WEBPACK_IMPORTED_MODULE_7__.Media,\n        _collections_Products__WEBPACK_IMPORTED_MODULE_8__.Products,\n        _collections_Categories__WEBPACK_IMPORTED_MODULE_9__.Categories,\n        _collections_Orders__WEBPACK_IMPORTED_MODULE_10__.Orders,\n        _collections_Pages__WEBPACK_IMPORTED_MODULE_11__.Pages\n    ],\n    editor: (0,_payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_12__.lexicalEditor)(),\n    secret: process.env.PAYLOAD_SECRET || '',\n    typescript: {\n        outputFile: path__WEBPACK_IMPORTED_MODULE_2___default().resolve(dirname, 'payload-types.ts')\n    },\n    // database-adapter-config-start\n    db: (0,_payloadcms_db_mongodb__WEBPACK_IMPORTED_MODULE_0__.mongooseAdapter)({\n        url: process.env.DATABASE_URI || ''\n    }),\n    // database-adapter-config-end\n    sharp: (sharp__WEBPACK_IMPORTED_MODULE_5___default()),\n    plugins: [\n        (0,_payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__.payloadCloudPlugin)()\n    ]\n}));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/payload.config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fnode_modules%2F.pnpm%2Fnext%4015.3.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0_sass%401.77.4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMF9zYXNzQDEuNzcuNC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGYXBwJTJGKGZyb250ZW5kKSUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaGFyc2hsdWhhciUyRmxhbGl0JTIwZG9uJTJGYWxva2l0YS1jYW5kbGVzJTJGc3JjJTJGY29tcG9uZW50cyUyRk5hdmlnYXRpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTmF2aWdhdGlvbiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQThJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJOYXZpZ2F0aW9uXCJdICovIFwiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9zcmMvY29tcG9uZW50cy9OYXZpZ2F0aW9uLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp%2F(frontend)%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\nfunction Navigation() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white/95 backdrop-blur-sm border-b border-gold-200/50 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"text-2xl font-bold text-gradient-gold\",\n                                children: \"Alokita Candles\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-10 flex items-baseline space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/products\",\n                                        className: \"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors\",\n                                        children: \"Products\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/categories\",\n                                        className: \"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors\",\n                                        children: \"Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/about\",\n                                        className: \"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/contact\",\n                                        className: \"text-gray-700 hover:text-gold-600 px-3 py-2 text-sm font-medium transition-colors\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: \"Cart (0)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"gold\",\n                                    size: \"sm\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/admin\",\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-700 hover:text-gold-600 focus:outline-none focus:text-gold-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gold-200/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/products\",\n                                className: \"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors\",\n                                children: \"Products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/categories\",\n                                className: \"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors\",\n                                children: \"Categories\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/about\",\n                                className: \"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/contact\",\n                                className: \"text-gray-700 hover:text-gold-600 block px-3 py-2 text-base font-medium transition-colors\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 px-3 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"flex-1\",\n                                        children: \"Cart (0)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"gold\",\n                                        size: \"sm\",\n                                        className: \"flex-1\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/admin\",\n                                            children: \"Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/Navigation.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.0_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            gold: \"bg-gradient-to-r from-gold-400 to-gold-600 text-white hover:from-gold-500 hover:to-gold-700\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/lalit don/alokita-candles/src/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@3.3.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9zcmMvbGliL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@payloadcms/db-mongodb":
/*!*****************************************!*\
  !*** external "@payloadcms/db-mongodb" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@payloadcms/db-mongodb");;

/***/ }),

/***/ "@payloadcms/payload-cloud":
/*!********************************************!*\
  !*** external "@payloadcms/payload-cloud" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@payloadcms/payload-cloud");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "payload":
/*!**************************!*\
  !*** external "payload" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("payload");;

/***/ }),

/***/ "payload/shared":
/*!*********************************!*\
  !*** external "payload/shared" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("payload/shared");;

/***/ }),

/***/ "sharp":
/*!************************!*\
  !*** external "sharp" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("sharp");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4","vendor-chunks/@payloadcms+richtext-lexical@3.40.0_@faceless-ui+modal@3.0.0-beta.2_react-dom@19.1.0_re_4384e6b8de42eb8ebbc595e286082c41","vendor-chunks/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/uuid@10.0.0","vendor-chunks/qs-esm@7.0.2","vendor-chunks/tailwind-merge@3.3.0","vendor-chunks/clsx@2.1.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@19.1.0_react@19.1.0","vendor-chunks/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.0_react@19.1.0","vendor-chunks/lexical@0.28.0","vendor-chunks/jsox@1.2.121","vendor-chunks/@lexical+utils@0.28.0","vendor-chunks/@lexical+table@0.28.0","vendor-chunks/@lexical+selection@0.28.0","vendor-chunks/@lexical+rich-text@0.28.0","vendor-chunks/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_yjs@13.6.24","vendor-chunks/@lexical+list@0.28.0","vendor-chunks/@lexical+html@0.28.0","vendor-chunks/@lexical+headless@0.28.0","vendor-chunks/@lexical+clipboard@0.28.0","vendor-chunks/escape-html@1.0.3","vendor-chunks/bson-objectid@2.0.4"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(frontend)%2Fpage&page=%2F(frontend)%2Fpage&appPaths=%2F(frontend)%2Fpage&pagePath=private-next-app-dir%2F(frontend)%2Fpage.tsx&appDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fharshluhar%2Flalit%20don%2Falokita-candles&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();