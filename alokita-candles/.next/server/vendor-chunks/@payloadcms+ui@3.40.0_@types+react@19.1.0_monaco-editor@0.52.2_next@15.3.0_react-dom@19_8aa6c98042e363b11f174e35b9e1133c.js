"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c";
exports.ids = ["vendor-chunks/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/addFieldStatePromise.js":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/addFieldStatePromise.js ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addFieldStatePromise: () => (/* binding */ addFieldStatePromise)\n/* harmony export */ });\n/* harmony import */ var bson_objectid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bson-objectid */ \"(rsc)/./node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js\");\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var payload_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! payload/shared */ \"payload/shared\");\n/* harmony import */ var _utilities_resolveFilterOptions_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utilities/resolveFilterOptions.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/utilities/resolveFilterOptions.js\");\n/* harmony import */ var _iterateFields_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./iterateFields.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_1__, payload_shared__WEBPACK_IMPORTED_MODULE_2__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_3__]);\n([payload__WEBPACK_IMPORTED_MODULE_1__, payload_shared__WEBPACK_IMPORTED_MODULE_2__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst ObjectId = bson_objectid__WEBPACK_IMPORTED_MODULE_0__[\"default\"] || bson_objectid__WEBPACK_IMPORTED_MODULE_0__;\n/**\n * Flattens the fields schema and fields data.\n * The output is the field path (e.g. array.0.name) mapped to a FormField object.\n */\nconst addFieldStatePromise = async args => {\n  const {\n    id,\n    addErrorPathToParent: addErrorPathToParentArg,\n    anyParentLocalized = false,\n    blockData,\n    clientFieldSchemaMap,\n    collectionSlug,\n    data,\n    field,\n    fieldSchemaMap,\n    filter,\n    forceFullValue = false,\n    fullData,\n    includeSchema = false,\n    indexPath,\n    mockRSCs,\n    omitParents = false,\n    operation,\n    parentPath,\n    parentPermissions,\n    parentSchemaPath,\n    passesCondition,\n    path,\n    preferences,\n    previousFormState,\n    renderAllFields,\n    renderFieldFn,\n    req,\n    schemaPath,\n    select,\n    selectMode,\n    skipConditionChecks = false,\n    skipValidation = false,\n    state\n  } = args;\n  if (!args.clientFieldSchemaMap && args.renderFieldFn) {\n    console.warn('clientFieldSchemaMap is not passed to addFieldStatePromise - this will reduce performance');\n  }\n  let fieldPermissions = true;\n  const fieldState = {};\n  const lastRenderedPath = previousFormState?.[path]?.lastRenderedPath;\n  // Append only if true to avoid sending '$undefined' through the network\n  if (lastRenderedPath) {\n    fieldState.lastRenderedPath = lastRenderedPath;\n  }\n  // If we're rendering all fields, no need to flag this as added by server\n  const addedByServer = !renderAllFields && !previousFormState?.[path];\n  // Append only if true to avoid sending '$undefined' through the network\n  if (addedByServer) {\n    fieldState.addedByServer = true;\n  }\n  // Append only if true to avoid sending '$undefined' through the network\n  if (passesCondition === false) {\n    fieldState.passesCondition = false;\n  }\n  // Append only if true to avoid sending '$undefined' through the network\n  if (includeSchema) {\n    fieldState.fieldSchema = field;\n  }\n  if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldAffectsData)(field) && !(0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsHiddenOrDisabled)(field)) {\n    fieldPermissions = parentPermissions === true ? parentPermissions : (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.deepCopyObjectSimple)(parentPermissions?.[field.name]);\n    let hasPermission = fieldPermissions === true || (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.deepCopyObjectSimple)(fieldPermissions?.read);\n    if (typeof field?.access?.read === 'function') {\n      hasPermission = await field.access.read({\n        id,\n        blockData,\n        data: fullData,\n        req,\n        siblingData: data\n      });\n    } else {\n      hasPermission = true;\n    }\n    if (!hasPermission) {\n      return;\n    }\n    const validate = field.validate;\n    let validationResult = true;\n    if (typeof validate === 'function' && !skipValidation && passesCondition) {\n      let jsonError;\n      if (field.type === 'json' && typeof data[field.name] === 'string') {\n        try {\n          JSON.parse(data[field.name]);\n        } catch (e) {\n          jsonError = e;\n        }\n      }\n      try {\n        validationResult = await validate(data?.[field.name], {\n          ...field,\n          id,\n          blockData,\n          collectionSlug,\n          data: fullData,\n          event: 'onChange',\n          // @AlessioGr added `jsonError` in https://github.com/payloadcms/payload/commit/c7ea62a39473408c3ea912c4fbf73e11be4b538d\n          // @ts-expect-error-next-line\n          jsonError,\n          operation,\n          preferences,\n          previousValue: previousFormState?.[path]?.initialValue,\n          req,\n          siblingData: data\n        });\n      } catch (err) {\n        validationResult = `Error validating field at path: ${path}`;\n        req.payload.logger.error({\n          err,\n          msg: validationResult\n        });\n      }\n    }\n    const addErrorPathToParent = errorPath => {\n      if (typeof addErrorPathToParentArg === 'function') {\n        addErrorPathToParentArg(errorPath);\n      }\n      if (!fieldState.errorPaths) {\n        fieldState.errorPaths = [];\n      }\n      if (!fieldState.errorPaths.includes(errorPath)) {\n        fieldState.errorPaths.push(errorPath);\n        fieldState.valid = false;\n      }\n    };\n    if (typeof validationResult === 'string') {\n      fieldState.errorMessage = validationResult;\n      fieldState.valid = false;\n      addErrorPathToParent(path);\n    }\n    switch (field.type) {\n      case 'array':\n        {\n          const arrayValue = Array.isArray(data[field.name]) ? data[field.name] : [];\n          const arraySelect = select?.[field.name];\n          const {\n            promises,\n            rows\n          } = arrayValue.reduce((acc, row, i) => {\n            const parentPath = path + '.' + i;\n            row.id = row?.id || new ObjectId().toHexString();\n            if (!omitParents && (!filter || filter(args))) {\n              const idKey = parentPath + '.id';\n              state[idKey] = {\n                initialValue: row.id,\n                value: row.id\n              };\n              if (includeSchema) {\n                state[idKey].fieldSchema = field.fields.find(field => (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsID)(field));\n              }\n            }\n            acc.promises.push((0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n              id,\n              addErrorPathToParent,\n              anyParentLocalized: field.localized || anyParentLocalized,\n              blockData,\n              clientFieldSchemaMap,\n              collectionSlug,\n              data: row,\n              fields: field.fields,\n              fieldSchemaMap,\n              filter,\n              forceFullValue,\n              fullData,\n              includeSchema,\n              mockRSCs,\n              omitParents,\n              operation,\n              parentIndexPath: '',\n              parentPassesCondition: passesCondition,\n              parentPath,\n              parentSchemaPath: schemaPath,\n              permissions: fieldPermissions === true ? fieldPermissions : fieldPermissions?.fields || {},\n              preferences,\n              previousFormState,\n              renderAllFields,\n              renderFieldFn,\n              req,\n              select: typeof arraySelect === 'object' ? arraySelect : undefined,\n              selectMode,\n              skipConditionChecks,\n              skipValidation,\n              state\n            }));\n            if (!acc.rows) {\n              acc.rows = [];\n            }\n            const previousRows = previousFormState?.[path]?.rows || [];\n            // First, check if `previousFormState` has a matching row\n            const previousRow = previousRows.find(prevRow => prevRow.id === row.id);\n            const newRow = {\n              id: row.id,\n              isLoading: false\n            };\n            if (previousRow?.lastRenderedPath) {\n              newRow.lastRenderedPath = previousRow.lastRenderedPath;\n            }\n            acc.rows.push(newRow);\n            const collapsedRowIDsFromPrefs = preferences?.fields?.[path]?.collapsed;\n            const collapsed = (() => {\n              if (previousRow) {\n                return previousRow.collapsed ?? false;\n              }\n              // If previousFormState is undefined, check preferences\n              if (collapsedRowIDsFromPrefs !== undefined) {\n                return collapsedRowIDsFromPrefs.includes(row.id) // Check if collapsed in preferences\n                ;\n              }\n              // If neither exists, fallback to `field.admin.initCollapsed`\n              return field.admin.initCollapsed;\n            })();\n            if (collapsed) {\n              acc.rows[acc.rows.length - 1].collapsed = collapsed;\n            }\n            return acc;\n          }, {\n            promises: [],\n            rows: undefined\n          });\n          // Wait for all promises and update fields with the results\n          await Promise.all(promises);\n          if (rows) {\n            fieldState.rows = rows;\n          }\n          // Add values to field state\n          if (data[field.name] !== null) {\n            fieldState.value = forceFullValue ? arrayValue : arrayValue.length;\n            fieldState.initialValue = forceFullValue ? arrayValue : arrayValue.length;\n            if (arrayValue.length > 0) {\n              fieldState.disableFormData = true;\n            }\n          }\n          // Add field to state\n          if (!omitParents && (!filter || filter(args))) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n      case 'blocks':\n        {\n          const blocksValue = Array.isArray(data[field.name]) ? data[field.name] : [];\n          const {\n            promises,\n            rowMetadata\n          } = blocksValue.reduce((acc, row, i) => {\n            const blockTypeToMatch = row.blockType;\n            const block = req.payload.blocks[blockTypeToMatch] ?? (field.blockReferences ?? field.blocks).find(blockType => typeof blockType !== 'string' && blockType.slug === blockTypeToMatch);\n            if (!block) {\n              throw new Error(`Block with type \"${row.blockType}\" was found in block data, but no block with that type is defined in the config for field with schema path ${schemaPath}.`);\n            }\n            const {\n              blockSelect,\n              blockSelectMode\n            } = (0,payload__WEBPACK_IMPORTED_MODULE_1__.getBlockSelect)({\n              block,\n              select: select?.[field.name],\n              selectMode\n            });\n            const parentPath = path + '.' + i;\n            if (block) {\n              row.id = row?.id || new ObjectId().toHexString();\n              if (!omitParents && (!filter || filter(args))) {\n                // Handle block `id` field\n                const idKey = parentPath + '.id';\n                state[idKey] = {\n                  initialValue: row.id,\n                  value: row.id\n                };\n                if (includeSchema) {\n                  state[idKey].fieldSchema = includeSchema ? block.fields.find(blockField => (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsID)(blockField)) : undefined;\n                }\n                // Handle `blockType` field\n                const fieldKey = parentPath + '.blockType';\n                state[fieldKey] = {\n                  initialValue: row.blockType,\n                  value: row.blockType\n                };\n                if (addedByServer) {\n                  state[fieldKey].addedByServer = addedByServer;\n                }\n                if (includeSchema) {\n                  state[fieldKey].fieldSchema = block.fields.find(blockField => 'name' in blockField && blockField.name === 'blockType');\n                }\n                // Handle `blockName` field\n                const blockNameKey = parentPath + '.blockName';\n                state[blockNameKey] = {};\n                if (row.blockName) {\n                  state[blockNameKey].initialValue = row.blockName;\n                  state[blockNameKey].value = row.blockName;\n                }\n                if (includeSchema) {\n                  state[blockNameKey].fieldSchema = block.fields.find(blockField => 'name' in blockField && blockField.name === 'blockName');\n                }\n              }\n              acc.promises.push((0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n                id,\n                addErrorPathToParent,\n                anyParentLocalized: field.localized || anyParentLocalized,\n                blockData: row,\n                clientFieldSchemaMap,\n                collectionSlug,\n                data: row,\n                fields: block.fields,\n                fieldSchemaMap,\n                filter,\n                forceFullValue,\n                fullData,\n                includeSchema,\n                mockRSCs,\n                omitParents,\n                operation,\n                parentIndexPath: '',\n                parentPassesCondition: passesCondition,\n                parentPath,\n                parentSchemaPath: schemaPath + '.' + block.slug,\n                permissions: fieldPermissions === true ? fieldPermissions : parentPermissions?.[field.name]?.blocks?.[block.slug] === true ? true : parentPermissions?.[field.name]?.blocks?.[block.slug]?.fields || {},\n                preferences,\n                previousFormState,\n                renderAllFields,\n                renderFieldFn,\n                req,\n                select: typeof blockSelect === 'object' ? blockSelect : undefined,\n                selectMode: blockSelectMode,\n                skipConditionChecks,\n                skipValidation,\n                state\n              }));\n              const previousRows = previousFormState?.[path]?.rows || [];\n              // First, check if `previousFormState` has a matching row\n              const previousRow = previousRows.find(prevRow => prevRow.id === row.id);\n              const newRow = {\n                id: row.id,\n                blockType: row.blockType,\n                isLoading: false\n              };\n              if (previousRow?.lastRenderedPath) {\n                newRow.lastRenderedPath = previousRow.lastRenderedPath;\n              }\n              acc.rowMetadata.push(newRow);\n              const collapsedRowIDs = preferences?.fields?.[path]?.collapsed;\n              const collapsed = collapsedRowIDs === undefined ? field.admin.initCollapsed : collapsedRowIDs.includes(row.id);\n              if (collapsed) {\n                acc.rowMetadata[acc.rowMetadata.length - 1].collapsed = collapsed;\n              }\n            }\n            return acc;\n          }, {\n            promises: [],\n            rowMetadata: []\n          });\n          await Promise.all(promises);\n          // Add values to field state\n          if (data[field.name] === null) {\n            fieldState.value = null;\n            fieldState.initialValue = null;\n          } else {\n            fieldState.value = forceFullValue ? blocksValue : blocksValue.length;\n            fieldState.initialValue = forceFullValue ? blocksValue : blocksValue.length;\n            if (blocksValue.length > 0) {\n              fieldState.disableFormData = true;\n            }\n          }\n          fieldState.rows = rowMetadata;\n          // Add field to state\n          if (!omitParents && (!filter || filter(args))) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n      case 'group':\n        {\n          if (!filter || filter(args)) {\n            fieldState.disableFormData = true;\n            state[path] = fieldState;\n          }\n          const groupSelect = select?.[field.name];\n          await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n            id,\n            addErrorPathToParent,\n            anyParentLocalized: field.localized || anyParentLocalized,\n            blockData,\n            clientFieldSchemaMap,\n            collectionSlug,\n            data: data?.[field.name] || {},\n            fields: field.fields,\n            fieldSchemaMap,\n            filter,\n            forceFullValue,\n            fullData,\n            includeSchema,\n            mockRSCs,\n            omitParents,\n            operation,\n            parentIndexPath: '',\n            parentPassesCondition: passesCondition,\n            parentPath: path,\n            parentSchemaPath: schemaPath,\n            permissions: typeof fieldPermissions === 'boolean' ? fieldPermissions : fieldPermissions?.fields,\n            preferences,\n            previousFormState,\n            renderAllFields,\n            renderFieldFn,\n            req,\n            select: typeof groupSelect === 'object' ? groupSelect : undefined,\n            selectMode,\n            skipConditionChecks,\n            skipValidation,\n            state\n          });\n          break;\n        }\n      case 'relationship':\n      case 'upload':\n        {\n          if (field.filterOptions) {\n            if (typeof field.filterOptions === 'object') {\n              if (typeof field.relationTo === 'string') {\n                fieldState.filterOptions = {\n                  [field.relationTo]: field.filterOptions\n                };\n              } else {\n                fieldState.filterOptions = field.relationTo.reduce((acc, relation) => {\n                  acc[relation] = field.filterOptions;\n                  return acc;\n                }, {});\n              }\n            }\n            if (typeof field.filterOptions === 'function') {\n              const query = await (0,_utilities_resolveFilterOptions_js__WEBPACK_IMPORTED_MODULE_4__.resolveFilterOptions)(field.filterOptions, {\n                id,\n                blockData,\n                data: fullData,\n                relationTo: field.relationTo,\n                req,\n                siblingData: data,\n                user: req.user\n              });\n              fieldState.filterOptions = query;\n            }\n          }\n          if (field.hasMany) {\n            const relationshipValue = Array.isArray(data[field.name]) ? data[field.name].map(relationship => {\n              if (Array.isArray(field.relationTo)) {\n                return {\n                  relationTo: relationship.relationTo,\n                  value: relationship.value && typeof relationship.value === 'object' ? relationship.value?.id : relationship.value\n                };\n              }\n              if (typeof relationship === 'object' && relationship !== null) {\n                return relationship.id;\n              }\n              return relationship;\n            }) : undefined;\n            fieldState.value = relationshipValue;\n            fieldState.initialValue = relationshipValue;\n          } else if (Array.isArray(field.relationTo)) {\n            if (data[field.name] && typeof data[field.name] === 'object' && 'relationTo' in data[field.name] && 'value' in data[field.name]) {\n              const value = typeof data[field.name]?.value === 'object' && data[field.name]?.value && 'id' in data[field.name].value ? data[field.name].value.id : data[field.name].value;\n              const relationshipValue = {\n                relationTo: data[field.name]?.relationTo,\n                value\n              };\n              fieldState.value = relationshipValue;\n              fieldState.initialValue = relationshipValue;\n            }\n          } else {\n            const relationshipValue = data[field.name] && typeof data[field.name] === 'object' && 'id' in data[field.name] ? data[field.name].id : data[field.name];\n            fieldState.value = relationshipValue;\n            fieldState.initialValue = relationshipValue;\n          }\n          if (!filter || filter(args)) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n      case 'select':\n        {\n          if (typeof field.filterOptions === 'function') {\n            fieldState.selectFilterOptions = field.filterOptions({\n              data: fullData,\n              options: field.options,\n              req,\n              siblingData: data\n            });\n          }\n          if (data[field.name] !== undefined) {\n            fieldState.value = data[field.name];\n            fieldState.initialValue = data[field.name];\n          }\n          if (!filter || filter(args)) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n      default:\n        {\n          if (data[field.name] !== undefined) {\n            fieldState.value = data[field.name];\n            fieldState.initialValue = data[field.name];\n          }\n          // Add field to state\n          if (!filter || filter(args)) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n    }\n  } else if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldHasSubFields)(field) && !(0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldAffectsData)(field)) {\n    // Handle field types that do not use names (row, collapsible, unnamed group etc)\n    if (!filter || filter(args)) {\n      state[path] = {\n        disableFormData: true\n      };\n      if (passesCondition === false) {\n        state[path].passesCondition = false;\n      }\n    }\n    await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n      id,\n      mockRSCs,\n      select,\n      selectMode,\n      // passthrough parent functionality\n      addErrorPathToParent: addErrorPathToParentArg,\n      anyParentLocalized: (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsLocalized)(field) || anyParentLocalized,\n      blockData,\n      clientFieldSchemaMap,\n      collectionSlug,\n      data,\n      fields: field.fields,\n      fieldSchemaMap,\n      filter,\n      forceFullValue,\n      fullData,\n      includeSchema,\n      omitParents,\n      operation,\n      parentIndexPath: indexPath,\n      parentPassesCondition: passesCondition,\n      parentPath,\n      parentSchemaPath,\n      permissions: parentPermissions,\n      preferences,\n      previousFormState,\n      renderAllFields,\n      renderFieldFn,\n      req,\n      skipConditionChecks,\n      skipValidation,\n      state\n    });\n  } else if (field.type === 'tabs') {\n    const promises = field.tabs.map((tab, tabIndex) => {\n      const isNamedTab = (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.tabHasName)(tab);\n      let tabSelect;\n      const {\n        indexPath: tabIndexPath,\n        path: tabPath,\n        schemaPath: tabSchemaPath\n      } = (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.getFieldPaths)({\n        field: {\n          ...tab,\n          type: 'tab'\n        },\n        index: tabIndex,\n        parentIndexPath: indexPath,\n        parentPath,\n        parentSchemaPath\n      });\n      let childPermissions = undefined;\n      if (isNamedTab) {\n        if (parentPermissions === true) {\n          childPermissions = true;\n        } else {\n          const tabPermissions = parentPermissions?.[tab.name];\n          if (tabPermissions === true) {\n            childPermissions = true;\n          } else {\n            childPermissions = tabPermissions?.fields;\n          }\n        }\n        if (typeof select?.[tab.name] === 'object') {\n          tabSelect = select?.[tab.name];\n        }\n      } else {\n        childPermissions = parentPermissions;\n        tabSelect = select;\n      }\n      const pathSegments = path ? path.split('.') : [];\n      // If passesCondition is false then this should always result to false\n      // If the tab has no admin.condition provided then fallback to passesCondition and let that decide the result\n      let tabPassesCondition = passesCondition;\n      if (passesCondition && typeof tab.admin?.condition === 'function') {\n        tabPassesCondition = tab.admin.condition(fullData, data, {\n          blockData,\n          operation,\n          path: pathSegments,\n          user: req.user\n        });\n      }\n      if (tab?.id) {\n        state[tab.id] = {\n          passesCondition: tabPassesCondition\n        };\n      }\n      return (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n        id,\n        addErrorPathToParent: addErrorPathToParentArg,\n        anyParentLocalized: tab.localized || anyParentLocalized,\n        blockData,\n        clientFieldSchemaMap,\n        collectionSlug,\n        data: isNamedTab ? data?.[tab.name] || {} : data,\n        fields: tab.fields,\n        fieldSchemaMap,\n        filter,\n        forceFullValue,\n        fullData,\n        includeSchema,\n        mockRSCs,\n        omitParents,\n        operation,\n        parentIndexPath: isNamedTab ? '' : tabIndexPath,\n        parentPassesCondition: tabPassesCondition,\n        parentPath: isNamedTab ? tabPath : parentPath,\n        parentSchemaPath: isNamedTab ? tabSchemaPath : parentSchemaPath,\n        permissions: childPermissions,\n        preferences,\n        previousFormState,\n        renderAllFields,\n        renderFieldFn,\n        req,\n        select: tabSelect,\n        selectMode,\n        skipConditionChecks,\n        skipValidation,\n        state\n      });\n    });\n    await Promise.all(promises);\n  } else if (field.type === 'ui') {\n    if (!filter || filter(args)) {\n      state[path] = fieldState;\n      state[path].disableFormData = true;\n    }\n  }\n  if (renderFieldFn && !(0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsHiddenOrDisabled)(field)) {\n    const fieldConfig = fieldSchemaMap.get(schemaPath);\n    if (!fieldConfig && !mockRSCs) {\n      if (schemaPath.endsWith('.blockType')) {\n        return;\n      } else {\n        throw new Error(`Field config not found for ${schemaPath}`);\n      }\n    }\n    if (!state[path]) {\n      // Some fields (ie `Tab`) do not live in form state\n      // therefore we cannot attach customComponents to them\n      return;\n    }\n    if (addedByServer) {\n      state[path].addedByServer = addedByServer;\n    }\n    renderFieldFn({\n      id,\n      clientFieldSchemaMap,\n      collectionSlug,\n      data: fullData,\n      fieldConfig: fieldConfig,\n      fieldSchemaMap,\n      fieldState: state[path],\n      formState: state,\n      indexPath,\n      lastRenderedPath,\n      mockRSCs,\n      operation,\n      parentPath,\n      parentSchemaPath,\n      path,\n      permissions: fieldPermissions,\n      preferences,\n      previousFieldState: previousFormState?.[path],\n      renderAllFields,\n      req,\n      schemaPath,\n      siblingData: data\n    });\n  }\n};\n//# sourceMappingURL=addFieldStatePromise.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/addFieldStatePromise.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/index.js":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/index.js ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDefaultValues: () => (/* binding */ calculateDefaultValues)\n/* harmony export */ });\n/* harmony import */ var _iterateFields_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./iterateFields.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_iterateFields_js__WEBPACK_IMPORTED_MODULE_0__]);\n_iterateFields_js__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst calculateDefaultValues = async ({\n  id,\n  data,\n  fields,\n  locale,\n  req,\n  select,\n  selectMode,\n  user\n}) => {\n  await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_0__.iterateFields)({\n    id,\n    data,\n    fields,\n    locale,\n    req,\n    select,\n    selectMode,\n    siblingData: data,\n    user\n  });\n  return data;\n};\n//# sourceMappingURL=index.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrdWlAMy40MC4wX0B0eXBlcytyZWFjdEAxOS4xLjBfbW9uYWNvLWVkaXRvckAwLjUyLjJfbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5XzhhYTZjOTgwNDJlMzYzYjExZjE3NGUzNWI5ZTExMzNjL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy91aS9kaXN0L2Zvcm1zL2ZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlL2NhbGN1bGF0ZURlZmF1bHRWYWx1ZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUQ7QUFDNUM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELFFBQVEsZ0VBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxpQyIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrdWlAMy40MC4wX0B0eXBlcytyZWFjdEAxOS4xLjBfbW9uYWNvLWVkaXRvckAwLjUyLjJfbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5XzhhYTZjOTgwNDJlMzYzYjExZjE3NGUzNWI5ZTExMzNjL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy91aS9kaXN0L2Zvcm1zL2ZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlL2NhbGN1bGF0ZURlZmF1bHRWYWx1ZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXRlcmF0ZUZpZWxkcyB9IGZyb20gJy4vaXRlcmF0ZUZpZWxkcy5qcyc7XG5leHBvcnQgY29uc3QgY2FsY3VsYXRlRGVmYXVsdFZhbHVlcyA9IGFzeW5jICh7XG4gIGlkLFxuICBkYXRhLFxuICBmaWVsZHMsXG4gIGxvY2FsZSxcbiAgcmVxLFxuICBzZWxlY3QsXG4gIHNlbGVjdE1vZGUsXG4gIHVzZXJcbn0pID0+IHtcbiAgYXdhaXQgaXRlcmF0ZUZpZWxkcyh7XG4gICAgaWQsXG4gICAgZGF0YSxcbiAgICBmaWVsZHMsXG4gICAgbG9jYWxlLFxuICAgIHJlcSxcbiAgICBzZWxlY3QsXG4gICAgc2VsZWN0TW9kZSxcbiAgICBzaWJsaW5nRGF0YTogZGF0YSxcbiAgICB1c2VyXG4gIH0pO1xuICByZXR1cm4gZGF0YTtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iterateFields: () => (/* binding */ iterateFields)\n/* harmony export */ });\n/* harmony import */ var _promise_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./promise.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/promise.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_promise_js__WEBPACK_IMPORTED_MODULE_0__]);\n_promise_js__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst iterateFields = async ({\n  id,\n  data,\n  fields,\n  locale,\n  req,\n  select,\n  selectMode,\n  siblingData,\n  user\n}) => {\n  const promises = [];\n  fields.forEach(field => {\n    promises.push((0,_promise_js__WEBPACK_IMPORTED_MODULE_0__.defaultValuePromise)({\n      id,\n      data,\n      field,\n      locale,\n      req,\n      select,\n      selectMode,\n      siblingData,\n      user\n    }));\n  });\n  await Promise.all(promises);\n};\n//# sourceMappingURL=iterateFields.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrdWlAMy40MC4wX0B0eXBlcytyZWFjdEAxOS4xLjBfbW9uYWNvLWVkaXRvckAwLjUyLjJfbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5XzhhYTZjOTgwNDJlMzYzYjExZjE3NGUzNWI5ZTExMzNjL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy91aS9kaXN0L2Zvcm1zL2ZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlL2NhbGN1bGF0ZURlZmF1bHRWYWx1ZXMvaXRlcmF0ZUZpZWxkcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtRDtBQUM1QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0Esa0JBQWtCLGdFQUFtQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIO0FBQ0E7QUFDQSx5QyIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrdWlAMy40MC4wX0B0eXBlcytyZWFjdEAxOS4xLjBfbW9uYWNvLWVkaXRvckAwLjUyLjJfbmV4dEAxNS4zLjBfcmVhY3QtZG9tQDE5XzhhYTZjOTgwNDJlMzYzYjExZjE3NGUzNWI5ZTExMzNjL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy91aS9kaXN0L2Zvcm1zL2ZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlL2NhbGN1bGF0ZURlZmF1bHRWYWx1ZXMvaXRlcmF0ZUZpZWxkcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWZhdWx0VmFsdWVQcm9taXNlIH0gZnJvbSAnLi9wcm9taXNlLmpzJztcbmV4cG9ydCBjb25zdCBpdGVyYXRlRmllbGRzID0gYXN5bmMgKHtcbiAgaWQsXG4gIGRhdGEsXG4gIGZpZWxkcyxcbiAgbG9jYWxlLFxuICByZXEsXG4gIHNlbGVjdCxcbiAgc2VsZWN0TW9kZSxcbiAgc2libGluZ0RhdGEsXG4gIHVzZXJcbn0pID0+IHtcbiAgY29uc3QgcHJvbWlzZXMgPSBbXTtcbiAgZmllbGRzLmZvckVhY2goZmllbGQgPT4ge1xuICAgIHByb21pc2VzLnB1c2goZGVmYXVsdFZhbHVlUHJvbWlzZSh7XG4gICAgICBpZCxcbiAgICAgIGRhdGEsXG4gICAgICBmaWVsZCxcbiAgICAgIGxvY2FsZSxcbiAgICAgIHJlcSxcbiAgICAgIHNlbGVjdCxcbiAgICAgIHNlbGVjdE1vZGUsXG4gICAgICBzaWJsaW5nRGF0YSxcbiAgICAgIHVzZXJcbiAgICB9KSk7XG4gIH0pO1xuICBhd2FpdCBQcm9taXNlLmFsbChwcm9taXNlcyk7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXRlcmF0ZUZpZWxkcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/promise.js":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/promise.js ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultValuePromise: () => (/* binding */ defaultValuePromise)\n/* harmony export */ });\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var payload_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload/shared */ \"payload/shared\");\n/* harmony import */ var _iterateFields_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./iterateFields.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_2__]);\n([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// TODO: Make this works for rich text subfields\nconst defaultValuePromise = async ({\n  id,\n  data,\n  field,\n  locale,\n  req,\n  select,\n  selectMode,\n  siblingData,\n  user\n}) => {\n  const shouldContinue = (0,payload__WEBPACK_IMPORTED_MODULE_0__.stripUnselectedFields)({\n    field,\n    select,\n    selectMode,\n    siblingDoc: siblingData\n  });\n  if (!shouldContinue) {\n    return;\n  }\n  if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.fieldAffectsData)(field)) {\n    if (typeof siblingData[field.name] === 'undefined' && typeof field.defaultValue !== 'undefined') {\n      try {\n        siblingData[field.name] = await (0,payload__WEBPACK_IMPORTED_MODULE_0__.getDefaultValue)({\n          defaultValue: field.defaultValue,\n          locale,\n          req,\n          user,\n          value: siblingData[field.name]\n        });\n      } catch (err) {\n        req.payload.logger.error({\n          err,\n          msg: `Error calculating default value for field: ${field.name}`\n        });\n      }\n    }\n  }\n  // Traverse subfields\n  switch (field.type) {\n    case 'array':\n      {\n        const rows = siblingData[field.name];\n        if (Array.isArray(rows)) {\n          const promises = [];\n          const arraySelect = select?.[field.name];\n          rows.forEach(row => {\n            promises.push((0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n              id,\n              data,\n              fields: field.fields,\n              locale,\n              req,\n              select: typeof arraySelect === 'object' ? arraySelect : undefined,\n              selectMode,\n              siblingData: row,\n              user\n            }));\n          });\n          await Promise.all(promises);\n        }\n        break;\n      }\n    case 'blocks':\n      {\n        const rows = siblingData[field.name];\n        if (Array.isArray(rows)) {\n          const promises = [];\n          rows.forEach(row => {\n            const blockTypeToMatch = row.blockType;\n            const block = req.payload.blocks[blockTypeToMatch] ?? (field.blockReferences ?? field.blocks).find(blockType => typeof blockType !== 'string' && blockType.slug === blockTypeToMatch);\n            const {\n              blockSelect,\n              blockSelectMode\n            } = (0,payload__WEBPACK_IMPORTED_MODULE_0__.getBlockSelect)({\n              block,\n              select: select?.[field.name],\n              selectMode\n            });\n            if (block) {\n              row.blockType = blockTypeToMatch;\n              promises.push((0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n                id,\n                data,\n                fields: block.fields,\n                locale,\n                req,\n                select: typeof blockSelect === 'object' ? blockSelect : undefined,\n                selectMode: blockSelectMode,\n                siblingData: row,\n                user\n              }));\n            }\n          });\n          await Promise.all(promises);\n        }\n        break;\n      }\n    case 'collapsible':\n    case 'row':\n      {\n        await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n          id,\n          data,\n          fields: field.fields,\n          locale,\n          req,\n          select,\n          selectMode,\n          siblingData,\n          user\n        });\n        break;\n      }\n    case 'group':\n      {\n        if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.fieldAffectsData)(field)) {\n          if (typeof siblingData[field.name] !== 'object') {\n            siblingData[field.name] = {};\n          }\n          const groupData = siblingData[field.name];\n          const groupSelect = select?.[field.name];\n          await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n            id,\n            data,\n            fields: field.fields,\n            locale,\n            req,\n            select: typeof groupSelect === 'object' ? groupSelect : undefined,\n            selectMode,\n            siblingData: groupData,\n            user\n          });\n        } else {\n          await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n            id,\n            data,\n            fields: field.fields,\n            locale,\n            req,\n            select,\n            selectMode,\n            siblingData,\n            user\n          });\n        }\n        break;\n      }\n    case 'tab':\n      {\n        let tabSiblingData;\n        const isNamedTab = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.tabHasName)(field);\n        let tabSelect;\n        if (isNamedTab) {\n          if (typeof siblingData[field.name] !== 'object') {\n            siblingData[field.name] = {};\n          }\n          tabSiblingData = siblingData[field.name];\n          if (typeof select?.[field.name] === 'object') {\n            tabSelect = select?.[field.name];\n          }\n        } else {\n          tabSiblingData = siblingData;\n          tabSelect = select;\n        }\n        await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n          id,\n          data,\n          fields: field.fields,\n          locale,\n          req,\n          select: tabSelect,\n          selectMode,\n          siblingData: tabSiblingData,\n          user\n        });\n        break;\n      }\n    case 'tabs':\n      {\n        await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n          id,\n          data,\n          fields: field.tabs.map(tab => ({\n            ...tab,\n            type: 'tab'\n          })),\n          locale,\n          req,\n          select,\n          selectMode,\n          siblingData,\n          user\n        });\n        break;\n      }\n    default:\n      {\n        break;\n      }\n  }\n};\n//# sourceMappingURL=promise.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/promise.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/index.js":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/index.js ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fieldSchemasToFormState: () => (/* binding */ fieldSchemasToFormState),\n/* harmony export */   iterateFields: () => (/* reexport safe */ _iterateFields_js__WEBPACK_IMPORTED_MODULE_1__.iterateFields)\n/* harmony export */ });\n/* harmony import */ var _calculateDefaultValues_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./calculateDefaultValues/index.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/index.js\");\n/* harmony import */ var _iterateFields_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./iterateFields.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_calculateDefaultValues_index_js__WEBPACK_IMPORTED_MODULE_0__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_1__]);\n([_calculateDefaultValues_index_js__WEBPACK_IMPORTED_MODULE_0__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst fieldSchemasToFormState = async ({\n  id,\n  clientFieldSchemaMap,\n  collectionSlug,\n  data = {},\n  documentData,\n  fields,\n  fieldSchemaMap,\n  initialBlockData,\n  mockRSCs,\n  operation,\n  permissions,\n  preferences,\n  previousFormState,\n  renderAllFields,\n  renderFieldFn,\n  req,\n  schemaPath,\n  select,\n  selectMode,\n  skipValidation\n}) => {\n  if (!clientFieldSchemaMap && renderFieldFn) {\n    console.warn('clientFieldSchemaMap is not passed to fieldSchemasToFormState - this will reduce performance');\n  }\n  if (fields && fields.length) {\n    const state = {};\n    const dataWithDefaultValues = {\n      ...data\n    };\n    await (0,_calculateDefaultValues_index_js__WEBPACK_IMPORTED_MODULE_0__.calculateDefaultValues)({\n      id,\n      data: dataWithDefaultValues,\n      fields,\n      locale: req.locale,\n      req,\n      select,\n      selectMode,\n      siblingData: dataWithDefaultValues,\n      user: req.user\n    });\n    let fullData = dataWithDefaultValues;\n    if (documentData) {\n      // By the time this function is used to get form state for nested forms, their default values should have already been calculated\n      // => no need to run calculateDefaultValues here\n      fullData = documentData;\n    }\n    await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_1__.iterateFields)({\n      id,\n      addErrorPathToParent: null,\n      blockData: initialBlockData,\n      clientFieldSchemaMap,\n      collectionSlug,\n      data: dataWithDefaultValues,\n      fields,\n      fieldSchemaMap,\n      fullData,\n      mockRSCs,\n      operation,\n      parentIndexPath: '',\n      parentPassesCondition: true,\n      parentPath: '',\n      parentSchemaPath: schemaPath,\n      permissions,\n      preferences,\n      previousFormState,\n      renderAllFields,\n      renderFieldFn,\n      req,\n      select,\n      selectMode,\n      skipValidation,\n      state\n    });\n    return state;\n  }\n  return {};\n};\n\n//# sourceMappingURL=index.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iterateFields: () => (/* binding */ iterateFields)\n/* harmony export */ });\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var payload_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload/shared */ \"payload/shared\");\n/* harmony import */ var _addFieldStatePromise_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./addFieldStatePromise.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/addFieldStatePromise.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__, _addFieldStatePromise_js__WEBPACK_IMPORTED_MODULE_2__]);\n([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__, _addFieldStatePromise_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n/**\n * Flattens the fields schema and fields data\n */\nconst iterateFields = async ({\n  id,\n  addErrorPathToParent: addErrorPathToParentArg,\n  anyParentLocalized = false,\n  blockData,\n  clientFieldSchemaMap,\n  collectionSlug,\n  data,\n  fields,\n  fieldSchemaMap,\n  filter,\n  forceFullValue = false,\n  fullData,\n  includeSchema = false,\n  mockRSCs,\n  omitParents = false,\n  operation,\n  parentIndexPath,\n  parentPassesCondition = true,\n  parentPath,\n  parentSchemaPath,\n  permissions,\n  preferences,\n  previousFormState,\n  renderAllFields,\n  renderFieldFn: renderFieldFn,\n  req,\n  select,\n  selectMode,\n  skipConditionChecks = false,\n  skipValidation = false,\n  state = {}\n}) => {\n  const promises = [];\n  fields.forEach((field, fieldIndex) => {\n    let passesCondition = true;\n    const {\n      indexPath,\n      path,\n      schemaPath\n    } = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.getFieldPaths)({\n      field,\n      index: fieldIndex,\n      parentIndexPath: 'name' in field ? '' : parentIndexPath,\n      parentPath,\n      parentSchemaPath\n    });\n    if (path !== 'id') {\n      const shouldContinue = (0,payload__WEBPACK_IMPORTED_MODULE_0__.stripUnselectedFields)({\n        field,\n        select,\n        selectMode,\n        siblingDoc: data\n      });\n      if (!shouldContinue) {\n        return;\n      }\n    }\n    const pathSegments = path ? path.split('.') : [];\n    if (!skipConditionChecks) {\n      try {\n        passesCondition = Boolean((field?.admin?.condition ? Boolean(field.admin.condition(fullData || {}, data || {}, {\n          blockData,\n          operation,\n          path: pathSegments,\n          user: req.user\n        })) : true) && parentPassesCondition);\n      } catch (err) {\n        passesCondition = false;\n        req.payload.logger.error({\n          err,\n          msg: `Error evaluating field condition at path: ${path}`\n        });\n      }\n    }\n    promises.push((0,_addFieldStatePromise_js__WEBPACK_IMPORTED_MODULE_2__.addFieldStatePromise)({\n      id,\n      addErrorPathToParent: addErrorPathToParentArg,\n      anyParentLocalized,\n      blockData,\n      clientFieldSchemaMap,\n      collectionSlug,\n      data,\n      field,\n      fieldIndex,\n      fieldSchemaMap,\n      filter,\n      forceFullValue,\n      fullData,\n      includeSchema,\n      indexPath,\n      mockRSCs,\n      omitParents,\n      operation,\n      parentIndexPath,\n      parentPath,\n      parentPermissions: permissions,\n      parentSchemaPath,\n      passesCondition,\n      path,\n      preferences,\n      previousFormState,\n      renderAllFields,\n      renderFieldFn,\n      req,\n      schemaPath,\n      select,\n      selectMode,\n      skipConditionChecks,\n      skipValidation,\n      state\n    }));\n  });\n  await Promise.all(promises);\n};\n//# sourceMappingURL=iterateFields.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/utilities/buildFieldSchemaMap/traverseFields.js":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/utilities/buildFieldSchemaMap/traverseFields.js ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   traverseFields: () => (/* binding */ traverseFields)\n/* harmony export */ });\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var payload_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload/shared */ \"payload/shared\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__]);\n([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst traverseFields = ({\n  config,\n  fields,\n  i18n,\n  parentIndexPath,\n  parentSchemaPath,\n  schemaMap\n}) => {\n  for (const [index, field] of fields.entries()) {\n    const {\n      indexPath,\n      schemaPath\n    } = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.getFieldPaths)({\n      field,\n      index,\n      parentIndexPath: 'name' in field ? '' : parentIndexPath,\n      parentPath: '',\n      parentSchemaPath\n    });\n    schemaMap.set(schemaPath, field);\n    switch (field.type) {\n      case 'array':\n        traverseFields({\n          config,\n          fields: field.fields,\n          i18n,\n          parentIndexPath: '',\n          parentSchemaPath: schemaPath,\n          schemaMap\n        });\n        break;\n      case 'blocks':\n        ;\n        (field.blockReferences ?? field.blocks).map(_block => {\n          // TODO: iterate over blocks mapped to block slug in v4, or pass through payload.blocks\n          const block = typeof _block === 'string' ? config.blocks.find(b => b.slug === _block) : _block;\n          const blockSchemaPath = `${schemaPath}.${block.slug}`;\n          schemaMap.set(blockSchemaPath, block);\n          traverseFields({\n            config,\n            fields: block.fields,\n            i18n,\n            parentIndexPath: '',\n            parentSchemaPath: blockSchemaPath,\n            schemaMap\n          });\n        });\n        break;\n      case 'collapsible':\n      case 'row':\n        traverseFields({\n          config,\n          fields: field.fields,\n          i18n,\n          parentIndexPath: indexPath,\n          parentSchemaPath,\n          schemaMap\n        });\n        break;\n      case 'group':\n        if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.fieldAffectsData)(field)) {\n          traverseFields({\n            config,\n            fields: field.fields,\n            i18n,\n            parentIndexPath: '',\n            parentSchemaPath: schemaPath,\n            schemaMap\n          });\n        } else {\n          traverseFields({\n            config,\n            fields: field.fields,\n            i18n,\n            parentIndexPath: indexPath,\n            parentSchemaPath,\n            schemaMap\n          });\n        }\n        break;\n      case 'richText':\n        if (!field?.editor) {\n          throw new payload__WEBPACK_IMPORTED_MODULE_0__.MissingEditorProp(field) // while we allow disabling editor functionality, you should not have any richText fields defined if you do not have an editor\n          ;\n        }\n        if (typeof field.editor === 'function') {\n          throw new Error('Attempted to access unsanitized rich text editor.');\n        }\n        if (typeof field.editor.generateSchemaMap === 'function') {\n          field.editor.generateSchemaMap({\n            config,\n            field,\n            i18n,\n            schemaMap,\n            schemaPath\n          });\n        }\n        break;\n      case 'tabs':\n        field.tabs.map((tab, tabIndex) => {\n          const isNamedTab = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.tabHasName)(tab);\n          const {\n            indexPath: tabIndexPath,\n            schemaPath: tabSchemaPath\n          } = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.getFieldPaths)({\n            field: {\n              ...tab,\n              type: 'tab'\n            },\n            index: tabIndex,\n            parentIndexPath: indexPath,\n            parentPath: '',\n            parentSchemaPath\n          });\n          schemaMap.set(tabSchemaPath, tab);\n          traverseFields({\n            config,\n            fields: tab.fields,\n            i18n,\n            parentIndexPath: isNamedTab ? '' : tabIndexPath,\n            parentSchemaPath: isNamedTab ? tabSchemaPath : parentSchemaPath,\n            schemaMap\n          });\n        });\n        break;\n    }\n  }\n};\n//# sourceMappingURL=traverseFields.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/utilities/buildFieldSchemaMap/traverseFields.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/utilities/resolveFilterOptions.js":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/utilities/resolveFilterOptions.js ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveFilterOptions: () => (/* binding */ resolveFilterOptions)\n/* harmony export */ });\nconst resolveFilterOptions = async (filterOptions, options) => {\n  const {\n    relationTo\n  } = options;\n  const relations = Array.isArray(relationTo) ? relationTo : [relationTo];\n  const query = {};\n  if (typeof filterOptions !== 'undefined') {\n    await Promise.all(relations.map(async relation => {\n      query[relation] = typeof filterOptions === 'function' ? await filterOptions({\n        ...options,\n        relationTo: relation\n      }) : filterOptions;\n      if (query[relation] === true) {\n        query[relation] = {};\n      }\n      // this is an ugly way to prevent results from being returned\n      if (query[relation] === false) {\n        query[relation] = {\n          id: {\n            exists: false\n          }\n        };\n      }\n    }));\n  }\n  return query;\n};\n//# sourceMappingURL=resolveFilterOptions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.40.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_react-dom@19_8aa6c98042e363b11f174e35b9e1133c/node_modules/@payloadcms/ui/dist/utilities/resolveFilterOptions.js\n");

/***/ })

};
;