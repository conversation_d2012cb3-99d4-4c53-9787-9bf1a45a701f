"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+clipboard@0.28.0";
exports.ids = ["vendor-chunks/@lexical+clipboard@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $generateJSONFromSelectedNodes: () => (/* binding */ $generateJSONFromSelectedNodes),\n/* harmony export */   $generateNodesFromSerializedNodes: () => (/* binding */ $generateNodesFromSerializedNodes),\n/* harmony export */   $getClipboardDataFromSelection: () => (/* binding */ $getClipboardDataFromSelection),\n/* harmony export */   $getHtmlContent: () => (/* binding */ $getHtmlContent),\n/* harmony export */   $getLexicalContent: () => (/* binding */ $getLexicalContent),\n/* harmony export */   $insertDataTransferForPlainText: () => (/* binding */ $insertDataTransferForPlainText),\n/* harmony export */   $insertDataTransferForRichText: () => (/* binding */ $insertDataTransferForRichText),\n/* harmony export */   $insertGeneratedNodes: () => (/* binding */ $insertGeneratedNodes),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   setLexicalClipboardDataTransfer: () => (/* binding */ setLexicalClipboardDataTransfer)\n/* harmony export */ });\n/* harmony import */ var _lexical_html__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/html */ \"(rsc)/./node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/utils */ \"(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Returns the *currently selected* Lexical content as an HTML string, relying on the\n * logic defined in the exportDOM methods on the LexicalNode classes. Note that\n * this will not return the HTML content of the entire editor (unless all the content is included\n * in the current selection).\n *\n * @param editor - LexicalEditor instance to get HTML content from\n * @param selection - The selection to use (default is $getSelection())\n * @returns a string of HTML content\n */\nfunction $getHtmlContent(editor, selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)()) {\n  if (selection == null) {\n    {\n      formatDevErrorMessage(`Expected valid LexicalSelection`);\n    }\n  }\n\n  // If we haven't selected anything\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed() || selection.getNodes().length === 0) {\n    return '';\n  }\n  return (0,_lexical_html__WEBPACK_IMPORTED_MODULE_1__.$generateHtmlFromNodes)(editor, selection);\n}\n\n/**\n * Returns the *currently selected* Lexical content as a JSON string, relying on the\n * logic defined in the exportJSON methods on the LexicalNode classes. Note that\n * this will not return the JSON content of the entire editor (unless all the content is included\n * in the current selection).\n *\n * @param editor  - LexicalEditor instance to get the JSON content from\n * @param selection - The selection to use (default is $getSelection())\n * @returns\n */\nfunction $getLexicalContent(editor, selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)()) {\n  if (selection == null) {\n    {\n      formatDevErrorMessage(`Expected valid LexicalSelection`);\n    }\n  }\n\n  // If we haven't selected anything\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed() || selection.getNodes().length === 0) {\n    return null;\n  }\n  return JSON.stringify($generateJSONFromSelectedNodes(editor, selection));\n}\n\n/**\n * Attempts to insert content of the mime-types text/plain or text/uri-list from\n * the provided DataTransfer object into the editor at the provided selection.\n * text/uri-list is only used if text/plain is not also provided.\n *\n * @param dataTransfer an object conforming to the [DataTransfer interface] (https://html.spec.whatwg.org/multipage/dnd.html#the-datatransfer-interface)\n * @param selection the selection to use as the insertion point for the content in the DataTransfer object\n */\nfunction $insertDataTransferForPlainText(dataTransfer, selection) {\n  const text = dataTransfer.getData('text/plain') || dataTransfer.getData('text/uri-list');\n  if (text != null) {\n    selection.insertRawText(text);\n  }\n}\n\n/**\n * Attempts to insert content of the mime-types application/x-lexical-editor, text/html,\n * text/plain, or text/uri-list (in descending order of priority) from the provided DataTransfer\n * object into the editor at the provided selection.\n *\n * @param dataTransfer an object conforming to the [DataTransfer interface] (https://html.spec.whatwg.org/multipage/dnd.html#the-datatransfer-interface)\n * @param selection the selection to use as the insertion point for the content in the DataTransfer object\n * @param editor the LexicalEditor the content is being inserted into.\n */\nfunction $insertDataTransferForRichText(dataTransfer, selection, editor) {\n  const lexicalString = dataTransfer.getData('application/x-lexical-editor');\n  if (lexicalString) {\n    try {\n      const payload = JSON.parse(lexicalString);\n      if (payload.namespace === editor._config.namespace && Array.isArray(payload.nodes)) {\n        const nodes = $generateNodesFromSerializedNodes(payload.nodes);\n        return $insertGeneratedNodes(editor, nodes, selection);\n      }\n    } catch (_unused) {\n      // Fail silently.\n    }\n  }\n  const htmlString = dataTransfer.getData('text/html');\n  const plainString = dataTransfer.getData('text/plain');\n\n  // Skip HTML handling if it matches the plain text representation.\n  // This avoids unnecessary processing for plain text strings created by\n  // iOS Safari autocorrect, which incorrectly includes a `text/html` type.\n  if (htmlString && plainString !== htmlString) {\n    try {\n      const parser = new DOMParser();\n      const dom = parser.parseFromString(trustHTML(htmlString), 'text/html');\n      const nodes = (0,_lexical_html__WEBPACK_IMPORTED_MODULE_1__.$generateNodesFromDOM)(editor, dom);\n      return $insertGeneratedNodes(editor, nodes, selection);\n    } catch (_unused2) {\n      // Fail silently.\n    }\n  }\n\n  // Multi-line plain text in rich text mode pasted as separate paragraphs\n  // instead of single paragraph with linebreaks.\n  // Webkit-specific: Supports read 'text/uri-list' in clipboard.\n  const text = plainString || dataTransfer.getData('text/uri-list');\n  if (text != null) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const parts = text.split(/(\\r?\\n|\\t)/);\n      if (parts[parts.length - 1] === '') {\n        parts.pop();\n      }\n      for (let i = 0; i < parts.length; i++) {\n        const currentSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(currentSelection)) {\n          const part = parts[i];\n          if (part === '\\n' || part === '\\r\\n') {\n            currentSelection.insertParagraph();\n          } else if (part === '\\t') {\n            currentSelection.insertNodes([(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTabNode)()]);\n          } else {\n            currentSelection.insertText(part);\n          }\n        }\n      }\n    } else {\n      selection.insertRawText(text);\n    }\n  }\n}\nfunction trustHTML(html) {\n  if (window.trustedTypes && window.trustedTypes.createPolicy) {\n    const policy = window.trustedTypes.createPolicy('lexical', {\n      createHTML: input => input\n    });\n    return policy.createHTML(html);\n  }\n  return html;\n}\n\n/**\n * Inserts Lexical nodes into the editor using different strategies depending on\n * some simple selection-based heuristics. If you're looking for a generic way to\n * to insert nodes into the editor at a specific selection point, you probably want\n * {@link lexical.$insertNodes}\n *\n * @param editor LexicalEditor instance to insert the nodes into.\n * @param nodes The nodes to insert.\n * @param selection The selection to insert the nodes into.\n */\nfunction $insertGeneratedNodes(editor, nodes, selection) {\n  if (!editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND, {\n    nodes,\n    selection\n  })) {\n    selection.insertNodes(nodes);\n  }\n  return;\n}\nfunction exportNodeToJSON(node) {\n  const serializedNode = node.exportJSON();\n  const nodeClass = node.constructor;\n  if (serializedNode.type !== nodeClass.getType()) {\n    {\n      formatDevErrorMessage(`LexicalNode: Node ${nodeClass.name} does not implement .exportJSON().`);\n    }\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n    const serializedChildren = serializedNode.children;\n    if (!Array.isArray(serializedChildren)) {\n      {\n        formatDevErrorMessage(`LexicalNode: Node ${nodeClass.name} is an element but .exportJSON() does not have a children array.`);\n      }\n    }\n  }\n  return serializedNode;\n}\nfunction $appendNodesToJSON(editor, selection, currentNode, targetArray = []) {\n  let shouldInclude = selection !== null ? currentNode.isSelected(selection) : true;\n  const shouldExclude = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && currentNode.excludeFromCopy('html');\n  let target = currentNode;\n  if (selection !== null) {\n    let clone = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties)(currentNode);\n    clone = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(clone) && selection !== null ? (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_2__.$sliceSelectedTextNodeContent)(selection, clone) : clone;\n    target = clone;\n  }\n  const children = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(target) ? target.getChildren() : [];\n  const serializedNode = exportNodeToJSON(target);\n\n  // TODO: TextNode calls getTextContent() (NOT node.__text) within its exportJSON method\n  // which uses getLatest() to get the text from the original node with the same key.\n  // This is a deeper issue with the word \"clone\" here, it's still a reference to the\n  // same node as far as the LexicalEditor is concerned since it shares a key.\n  // We need a way to create a clone of a Node in memory with its own key, but\n  // until then this hack will work for the selected text extract use case.\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target)) {\n    const text = target.__text;\n    // If an uncollapsed selection ends or starts at the end of a line of specialized,\n    // TextNodes, such as code tokens, we will get a 'blank' TextNode here, i.e., one\n    // with text of length 0. We don't want this, it makes a confusing mess. Reset!\n    if (text.length > 0) {\n      serializedNode.text = text;\n    } else {\n      shouldInclude = false;\n    }\n  }\n  for (let i = 0; i < children.length; i++) {\n    const childNode = children[i];\n    const shouldIncludeChild = $appendNodesToJSON(editor, selection, childNode, serializedNode.children);\n    if (!shouldInclude && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && shouldIncludeChild && currentNode.extractWithChild(childNode, selection, 'clone')) {\n      shouldInclude = true;\n    }\n  }\n  if (shouldInclude && !shouldExclude) {\n    targetArray.push(serializedNode);\n  } else if (Array.isArray(serializedNode.children)) {\n    for (let i = 0; i < serializedNode.children.length; i++) {\n      const serializedChildNode = serializedNode.children[i];\n      targetArray.push(serializedChildNode);\n    }\n  }\n  return shouldInclude;\n}\n\n// TODO why $ function with Editor instance?\n/**\n * Gets the Lexical JSON of the nodes inside the provided Selection.\n *\n * @param editor LexicalEditor to get the JSON content from.\n * @param selection Selection to get the JSON content from.\n * @returns an object with the editor namespace and a list of serializable nodes as JavaScript objects.\n */\nfunction $generateJSONFromSelectedNodes(editor, selection) {\n  const nodes = [];\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const topLevelChildren = root.getChildren();\n  for (let i = 0; i < topLevelChildren.length; i++) {\n    const topLevelNode = topLevelChildren[i];\n    $appendNodesToJSON(editor, selection, topLevelNode, nodes);\n  }\n  return {\n    namespace: editor._config.namespace,\n    nodes\n  };\n}\n\n/**\n * This method takes an array of objects conforming to the BaseSeralizedNode interface and returns\n * an Array containing instances of the corresponding LexicalNode classes registered on the editor.\n * Normally, you'd get an Array of BaseSerialized nodes from {@link $generateJSONFromSelectedNodes}\n *\n * @param serializedNodes an Array of objects conforming to the BaseSerializedNode interface.\n * @returns an Array of Lexical Node objects.\n */\nfunction $generateNodesFromSerializedNodes(serializedNodes) {\n  const nodes = [];\n  for (let i = 0; i < serializedNodes.length; i++) {\n    const serializedNode = serializedNodes[i];\n    const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$parseSerializedNode)(serializedNode);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n      (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_2__.$addNodeStyle)(node);\n    }\n    nodes.push(node);\n  }\n  return nodes;\n}\nconst EVENT_LATENCY = 50;\nlet clipboardEventTimeout = null;\n\n// TODO custom selection\n// TODO potentially have a node customizable version for plain text\n/**\n * Copies the content of the current selection to the clipboard in\n * text/plain, text/html, and application/x-lexical-editor (Lexical JSON)\n * formats.\n *\n * @param editor the LexicalEditor instance to copy content from\n * @param event the native browser ClipboardEvent to add the content to.\n * @returns\n */\nasync function copyToClipboard(editor, event, data) {\n  if (clipboardEventTimeout !== null) {\n    // Prevent weird race conditions that can happen when this function is run multiple times\n    // synchronously. In the future, we can do better, we can cancel/override the previously running job.\n    return false;\n  }\n  if (event !== null) {\n    return new Promise((resolve, reject) => {\n      editor.update(() => {\n        resolve($copyToClipboardEvent(editor, event, data));\n      });\n    });\n  }\n  const rootElement = editor.getRootElement();\n  const editorWindow = editor._window || window;\n  const windowDocument = window.document;\n  const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(editorWindow);\n  if (rootElement === null || domSelection === null) {\n    return false;\n  }\n  const element = windowDocument.createElement('span');\n  element.style.cssText = 'position: fixed; top: -1000px;';\n  element.append(windowDocument.createTextNode('#'));\n  rootElement.append(element);\n  const range = new Range();\n  range.setStart(element, 0);\n  range.setEnd(element, 1);\n  domSelection.removeAllRanges();\n  domSelection.addRange(range);\n  return new Promise((resolve, reject) => {\n    const removeListener = editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.COPY_COMMAND, secondEvent => {\n      if ((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_3__.objectKlassEquals)(secondEvent, ClipboardEvent)) {\n        removeListener();\n        if (clipboardEventTimeout !== null) {\n          window.clearTimeout(clipboardEventTimeout);\n          clipboardEventTimeout = null;\n        }\n        resolve($copyToClipboardEvent(editor, secondEvent, data));\n      }\n      // Block the entire copy flow while we wait for the next ClipboardEvent\n      return true;\n    }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL);\n    // If the above hack execCommand hack works, this timeout code should never fire. Otherwise,\n    // the listener will be quickly freed so that the user can reuse it again\n    clipboardEventTimeout = window.setTimeout(() => {\n      removeListener();\n      clipboardEventTimeout = null;\n      resolve(false);\n    }, EVENT_LATENCY);\n    windowDocument.execCommand('copy');\n    element.remove();\n  });\n}\n\n// TODO shouldn't pass editor (pass namespace directly)\nfunction $copyToClipboardEvent(editor, event, data) {\n  if (data === undefined) {\n    const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(editor._window);\n    if (!domSelection) {\n      return false;\n    }\n    const anchorDOM = domSelection.anchorNode;\n    const focusDOM = domSelection.focusNode;\n    if (anchorDOM !== null && focusDOM !== null && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isSelectionWithinEditor)(editor, anchorDOM, focusDOM)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (selection === null) {\n      return false;\n    }\n    data = $getClipboardDataFromSelection(selection);\n  }\n  event.preventDefault();\n  const clipboardData = event.clipboardData;\n  if (clipboardData === null) {\n    return false;\n  }\n  setLexicalClipboardDataTransfer(clipboardData, data);\n  return true;\n}\nconst clipboardDataFunctions = [['text/html', $getHtmlContent], ['application/x-lexical-editor', $getLexicalContent]];\n\n/**\n * Serialize the content of the current selection to strings in\n * text/plain, text/html, and application/x-lexical-editor (Lexical JSON)\n * formats (as available).\n *\n * @param selection the selection to serialize (defaults to $getSelection())\n * @returns LexicalClipboardData\n */\nfunction $getClipboardDataFromSelection(selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)()) {\n  const clipboardData = {\n    'text/plain': selection ? selection.getTextContent() : ''\n  };\n  if (selection) {\n    const editor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getEditor)();\n    for (const [mimeType, $editorFn] of clipboardDataFunctions) {\n      const v = $editorFn(editor, selection);\n      if (v !== null) {\n        clipboardData[mimeType] = v;\n      }\n    }\n  }\n  return clipboardData;\n}\n\n/**\n * Call setData on the given clipboardData for each MIME type present\n * in the given data (from {@link $getClipboardDataFromSelection})\n *\n * @param clipboardData the event.clipboardData to populate from data\n * @param data The lexical data\n */\nfunction setLexicalClipboardDataTransfer(clipboardData, data) {\n  for (const k in data) {\n    const v = data[k];\n    if (v !== undefined) {\n      clipboardData.setData(k, v);\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs\n");

/***/ })

};
;