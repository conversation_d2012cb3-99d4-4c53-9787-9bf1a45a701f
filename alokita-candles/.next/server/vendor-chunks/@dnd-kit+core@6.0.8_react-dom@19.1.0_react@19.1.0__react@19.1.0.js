"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit+core@6.0.8_react-dom@19.1.0_react@19.1.0__react@19.1.0";
exports.ids = ["vendor-chunks/@dnd-kit+core@6.0.8_react-dom@19.1.0_react@19.1.0__react@19.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@dnd-kit+core@6.0.8_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@dnd-kit/core/dist/core.esm.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@dnd-kit+core@6.0.8_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@dnd-kit/core/dist/core.esm.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoScrollActivator: () => (/* binding */ AutoScrollActivator),\n/* harmony export */   DndContext: () => (/* binding */ DndContext),\n/* harmony export */   DragOverlay: () => (/* binding */ DragOverlay),\n/* harmony export */   KeyboardCode: () => (/* binding */ KeyboardCode),\n/* harmony export */   KeyboardSensor: () => (/* binding */ KeyboardSensor),\n/* harmony export */   MeasuringFrequency: () => (/* binding */ MeasuringFrequency),\n/* harmony export */   MeasuringStrategy: () => (/* binding */ MeasuringStrategy),\n/* harmony export */   MouseSensor: () => (/* binding */ MouseSensor),\n/* harmony export */   PointerSensor: () => (/* binding */ PointerSensor),\n/* harmony export */   TouchSensor: () => (/* binding */ TouchSensor),\n/* harmony export */   TraversalOrder: () => (/* binding */ TraversalOrder),\n/* harmony export */   applyModifiers: () => (/* binding */ applyModifiers),\n/* harmony export */   closestCenter: () => (/* binding */ closestCenter),\n/* harmony export */   closestCorners: () => (/* binding */ closestCorners),\n/* harmony export */   defaultAnnouncements: () => (/* binding */ defaultAnnouncements),\n/* harmony export */   defaultCoordinates: () => (/* binding */ defaultCoordinates),\n/* harmony export */   defaultDropAnimation: () => (/* binding */ defaultDropAnimationConfiguration),\n/* harmony export */   defaultDropAnimationSideEffects: () => (/* binding */ defaultDropAnimationSideEffects),\n/* harmony export */   defaultScreenReaderInstructions: () => (/* binding */ defaultScreenReaderInstructions),\n/* harmony export */   getClientRect: () => (/* binding */ getClientRect),\n/* harmony export */   getFirstCollision: () => (/* binding */ getFirstCollision),\n/* harmony export */   getScrollableAncestors: () => (/* binding */ getScrollableAncestors),\n/* harmony export */   pointerWithin: () => (/* binding */ pointerWithin),\n/* harmony export */   rectIntersection: () => (/* binding */ rectIntersection),\n/* harmony export */   useDndContext: () => (/* binding */ useDndContext),\n/* harmony export */   useDndMonitor: () => (/* binding */ useDndMonitor),\n/* harmony export */   useDraggable: () => (/* binding */ useDraggable),\n/* harmony export */   useDroppable: () => (/* binding */ useDroppable),\n/* harmony export */   useSensor: () => (/* binding */ useSensor),\n/* harmony export */   useSensors: () => (/* binding */ useSensors)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/.pnpm/@dnd-kit+utilities@3.2.2_react@19.1.0/node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/accessibility */ \"(ssr)/./node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@19.1.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\");\n\n\n\n\n\nconst DndMonitorContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n\nfunction useDndMonitor(listener) {\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DndMonitorContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n\nfunction useDndMonitorProvider() {\n  const [listeners] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new Set());\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\n\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n\n};\n\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = (0,_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.useAnnouncement)();\n  const liveRegionId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndLiveRegion\");\n  const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor((0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n\n  }), [announce, announcements]));\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(markup, container) : markup;\n}\n\nvar Action;\n\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\n\nfunction noop() {}\n\nfunction useSensor(sensor, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\n\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => [...sensors].filter(sensor => sensor != null), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\n\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n  return property ? firstCollision[property] : firstCollision;\n}\n\n/**\r\n * Returns the coordinates of the center of a given ClientRect\r\n */\n\nfunction centerOfRectangle(rect, left, top) {\n  if (left === void 0) {\n    left = rect.left;\n  }\n\n  if (top === void 0) {\n    top = rect.top;\n  }\n\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5\n  };\n}\n/**\r\n * Returns the closest rectangles from an array of rectangles to the center of a given\r\n * rectangle.\r\n */\n\n\nconst closestCenter = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const centerRect = centerOfRectangle(collisionRect, collisionRect.left, collisionRect.top);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: distBetween\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the closest rectangles from an array of rectangles to the corners of\r\n * another rectangle.\r\n */\n\nconst closestCorners = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\nfunction adjustScale(transform, rect1, rect2) {\n  return { ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\n\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\n\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((acc, adjustment) => ({ ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), { ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\n\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n\n  return null;\n}\n\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n\n  let rect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\n\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n  }\n\n  return computedStyle.position === 'fixed';\n}\n\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n  }\n\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n\n    if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(node) || (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isSVGElement)(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\nfunction getScrollableElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element;\n  }\n\n  if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isNode)(element)) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(element) || element === (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(element).scrollingElement) {\n    return window;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(element)) {\n    return element;\n  }\n\n  return null;\n}\n\nfunction getScrollXCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\n\nvar Direction;\n\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n\nfunction isDocumentScrollingElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n\n  return {\n    direction,\n    speed\n  };\n}\n\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\n\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  if (!element) {\n    return;\n  }\n\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\n\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = { ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n\n}\n\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n\n    this.target = target;\n  }\n\n  add(eventName, handler, options) {\n    var _this$target2;\n\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n\n}\n\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target);\n  return target instanceof EventTarget ? target : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n}\n\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n\nvar EventName;\n\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\n\nvar KeyboardCode;\n\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n})(KeyboardCode || (KeyboardCode = {}));\n\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n\n    case KeyboardCode.Left:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n\n    case KeyboardCode.Down:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n\n    case KeyboardCode.Up:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n\n  return undefined;\n};\n\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target));\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  handleKeyDown(event) {\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(event, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n\n      if (activator && event.target !== activator) {\n        return false;\n      }\n\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n\n    return false;\n  }\n}];\n\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.initialCoordinates = (_getEventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        return;\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n\n  handleMove(event) {\n    var _getEventCoordinates2;\n\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = (_getEventCoordinates2 = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(initialCoordinates, coordinates);\n\n    if (!activated && activationConstraint) {\n      // Constraint validation\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n\n        return;\n      }\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  handleEnd() {\n    const {\n      onEnd\n    } = this.props;\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel() {\n    const {\n      onCancel\n    } = this.props;\n    this.detach();\n    onCancel();\n  }\n\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  removeTextSelection() {\n    var _this$document$getSel;\n\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n\n}\n\nconst events = {\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(event.target);\n    super(props, events, listenerTarget);\n  }\n\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\n\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(props.event.target));\n  }\n\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$2 = {\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n\n    if (touches.length > 1) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nvar AutoScrollActivator;\n\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\n\nvar TraversalOrder;\n\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\n\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useInterval)();\n  const scrollSpeed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const autoScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n\n      if (!scrollContainerRect) {\n        continue;\n      }\n\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect), // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\n\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(delta);\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\n\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id !== null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(cachedNode => {\n    var _ref;\n\n    if (id === null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\n\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\n\nvar MeasuringStrategy;\n\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\n\nvar MeasuringFrequency;\n\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\n\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(containers);\n  const disabled = isDisabled();\n  const disabledRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(disabled);\n  const measureDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n\n    if (disabledRef.current) {\n      return;\n    }\n\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const droppableRects = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n\n      return map;\n    }\n\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    measureDroppableContainers();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n\n      default:\n        return !dragging;\n    }\n  }\n}\n\nfunction useInitialValue(value, computeFn) {\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!value) {\n      return null;\n    }\n\n    if (previousValue) {\n      return previousValue;\n    }\n\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\n\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const mutationObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const resizeObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\n\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\n\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n\n  const [rect, measureRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, null);\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n\n  function reducer(currentRect) {\n    if (!element) {\n      return null;\n    }\n\n    if (element.isConnected === false) {\n      var _ref;\n\n      // Fall back to last rect we measured if the element is\n      // no longer connected to the DOM.\n      return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n    }\n\n    const newRect = measure(element);\n\n    if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n      return currentRect;\n    }\n\n    return newRect;\n  }\n}\n\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\n\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(node);\n  const ancestors = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n\n    return getScrollableAncestors(node);\n  }, [node]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\n\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const prevElements = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n\n  const initialScrollOffsets = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    initialScrollOffsets.current = null;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\n\nfunction useSensorSetup(sensors) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM) {\n      return;\n    }\n\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  }, // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\n\nfunction useSyntheticListeners(listeners, id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\n\nfunction useWindowRect(element) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => element ? getWindowClientRect(element) : null, [element]);\n}\n\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(firstElement) : null);\n  const [rects, measureRects] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, defaultValue$2);\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n\n  if (elements.length > 0 && rects === defaultValue$2) {\n    measureRects();\n  }\n\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (elements.length) {\n      elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      measureRects();\n    }\n  }, [elements]);\n  return rects;\n\n  function reducer() {\n    if (!elements.length) {\n      return defaultValue$2;\n    }\n\n    return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n  }\n}\n\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n\n  const firstChild = node.children[0];\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(firstChild) ? firstChild : node;\n}\n\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(entries => {\n    for (const {\n      target\n    } of entries) {\n      if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? { ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\n\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\n\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n\n  toArray() {\n    return Array.from(this.values());\n  }\n\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n\n}\n\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultPublicContext);\n\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return { ...state,\n        draggable: { ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n\n    case Action.DragMove:\n      if (!state.draggable.active) {\n        return state;\n      }\n\n      return { ...state,\n        draggable: { ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return { ...state,\n        draggable: { ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, { ...element,\n          disabled\n        });\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    default:\n      {\n        return state;\n      }\n  }\n}\n\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previousActivatorEvent = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(activatorEvent);\n  const previousActiveId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.findFirstFocusableNode)(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\n\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\n\nfunction useMeasuringConfiguration(config) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    draggable: { ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: { ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: { ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\n\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n\nconst ActiveDraggableContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({ ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\n\nconst DndContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId ? draggableNodes.get(activeId) : null;\n  const activeRects = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    initial: null,\n    translated: null\n  });\n  const active = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _node$data;\n\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [activeSensor, setActiveSensor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [activatorEvent, setActivatorEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const latestProps = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(props, Object.values(props));\n  const draggableDescribedById = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => activatorEvent ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const instantiateSensor = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n\n    if (activeRef.current == null) {\n      return;\n    }\n\n    const activeNode = draggableNodes.get(activeRef.current);\n\n    if (!activeNode) {\n      return;\n    }\n\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n\n        if (id == null) {\n          return;\n        }\n\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n        });\n      },\n\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      setActiveSensor(sensorInstance);\n      setActivatorEvent(event.nativeEvent);\n    });\n\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n\n        activeRef.current = null;\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n\n      if ( // Another sensor is already instantiating\n      activeRef.current !== null || // No active draggable\n      !activeDraggableNode || // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n\n    if (!active || !activatorEvent) {\n      return;\n    }\n\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({ ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: internalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PublicContext.Provider, {\n    value: publicContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Accessibility, { ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return { ...autoScroll,\n        enabled\n      };\n    }\n\n    return {\n      enabled\n    };\n  }\n});\n\nconst NullContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Droppable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const [activatorNode, setActivatorNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\n\nfunction useDndContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PublicContext);\n}\n\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    disabled\n  });\n  const resizeObserverConnected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const callbackId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = { ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\n\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousChildren = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, children, clonedChildren ? (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\n\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\n\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\n\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nconst PositionedOverlay = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n\n  if (!rect) {\n    return null;\n  }\n\n  const scaleAdjustedTransform = adjustScale ? transform : { ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = { ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\n\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(initial)\n  }, {\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(final)\n  }];\n};\n\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n\n    const {\n      transform\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = { ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({ ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\n\nlet key = 0;\nfunction useKey(id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n\nconst DragOverlay = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NullifiedContextProvider, null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\n\n\n//# sourceMappingURL=core.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@dnd-kit+core@6.0.8_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@dnd-kit/core/dist/core.esm.js\n");

/***/ })

};
;