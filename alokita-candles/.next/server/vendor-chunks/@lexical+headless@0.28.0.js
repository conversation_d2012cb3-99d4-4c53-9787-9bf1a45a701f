"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+headless@0.28.0";
exports.ids = ["vendor-chunks/@lexical+headless@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+headless@0.28.0/node_modules/@lexical/headless/LexicalHeadless.dev.mjs":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+headless@0.28.0/node_modules/@lexical/headless/LexicalHeadless.dev.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHeadlessEditor: () => (/* binding */ createHeadlessEditor)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n/**\n * Generates a headless editor that allows lexical to be used without the need for a DOM, eg in Node.js.\n * Throws an error when unsupported methods are used.\n * @param editorConfig - The optional lexical editor configuration.\n * @returns - The configured headless editor.\n */\nfunction createHeadlessEditor(editorConfig) {\n  const editor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createEditor)(editorConfig);\n  editor._headless = true;\n  const unsupportedMethods = ['registerDecoratorListener', 'registerRootListener', 'registerMutationListener', 'getRootElement', 'setRootElement', 'getElementByKey', 'focus', 'blur'];\n  unsupportedMethods.forEach(method => {\n    editor[method] = () => {\n      throw new Error(`${method} is not supported in headless mode`);\n    };\n  });\n  return editor;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxleGljYWwraGVhZGxlc3NAMC4yOC4wL25vZGVfbW9kdWxlcy9AbGV4aWNhbC9oZWFkbGVzcy9MZXhpY2FsSGVhZGxlc3MuZGV2Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1Qzs7QUFFdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHFEQUFZO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLFFBQVE7QUFDakM7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFZ0MiLCJzb3VyY2VzIjpbIi9Vc2Vycy9oYXJzaGx1aGFyL2xhbGl0IGRvbi9hbG9raXRhLWNhbmRsZXMvbm9kZV9tb2R1bGVzLy5wbnBtL0BsZXhpY2FsK2hlYWRsZXNzQDAuMjguMC9ub2RlX21vZHVsZXMvQGxleGljYWwvaGVhZGxlc3MvTGV4aWNhbEhlYWRsZXNzLmRldi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqL1xuXG5pbXBvcnQgeyBjcmVhdGVFZGl0b3IgfSBmcm9tICdsZXhpY2FsJztcblxuLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqL1xuXG5cbi8qKlxuICogR2VuZXJhdGVzIGEgaGVhZGxlc3MgZWRpdG9yIHRoYXQgYWxsb3dzIGxleGljYWwgdG8gYmUgdXNlZCB3aXRob3V0IHRoZSBuZWVkIGZvciBhIERPTSwgZWcgaW4gTm9kZS5qcy5cbiAqIFRocm93cyBhbiBlcnJvciB3aGVuIHVuc3VwcG9ydGVkIG1ldGhvZHMgYXJlIHVzZWQuXG4gKiBAcGFyYW0gZWRpdG9yQ29uZmlnIC0gVGhlIG9wdGlvbmFsIGxleGljYWwgZWRpdG9yIGNvbmZpZ3VyYXRpb24uXG4gKiBAcmV0dXJucyAtIFRoZSBjb25maWd1cmVkIGhlYWRsZXNzIGVkaXRvci5cbiAqL1xuZnVuY3Rpb24gY3JlYXRlSGVhZGxlc3NFZGl0b3IoZWRpdG9yQ29uZmlnKSB7XG4gIGNvbnN0IGVkaXRvciA9IGNyZWF0ZUVkaXRvcihlZGl0b3JDb25maWcpO1xuICBlZGl0b3IuX2hlYWRsZXNzID0gdHJ1ZTtcbiAgY29uc3QgdW5zdXBwb3J0ZWRNZXRob2RzID0gWydyZWdpc3RlckRlY29yYXRvckxpc3RlbmVyJywgJ3JlZ2lzdGVyUm9vdExpc3RlbmVyJywgJ3JlZ2lzdGVyTXV0YXRpb25MaXN0ZW5lcicsICdnZXRSb290RWxlbWVudCcsICdzZXRSb290RWxlbWVudCcsICdnZXRFbGVtZW50QnlLZXknLCAnZm9jdXMnLCAnYmx1ciddO1xuICB1bnN1cHBvcnRlZE1ldGhvZHMuZm9yRWFjaChtZXRob2QgPT4ge1xuICAgIGVkaXRvclttZXRob2RdID0gKCkgPT4ge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGAke21ldGhvZH0gaXMgbm90IHN1cHBvcnRlZCBpbiBoZWFkbGVzcyBtb2RlYCk7XG4gICAgfTtcbiAgfSk7XG4gIHJldHVybiBlZGl0b3I7XG59XG5cbmV4cG9ydCB7IGNyZWF0ZUhlYWRsZXNzRWRpdG9yIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+headless@0.28.0/node_modules/@lexical/headless/LexicalHeadless.dev.mjs\n");

/***/ })

};
;