"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+list@0.28.0";
exports.ids = ["vendor-chunks/@lexical+list@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+list@0.28.0/node_modules/@lexical/list/LexicalList.dev.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+list@0.28.0/node_modules/@lexical/list/LexicalList.dev.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $createListItemNode: () => (/* binding */ $createListItemNode),\n/* harmony export */   $createListNode: () => (/* binding */ $createListNode),\n/* harmony export */   $getListDepth: () => (/* binding */ $getListDepth),\n/* harmony export */   $handleListInsertParagraph: () => (/* binding */ $handleListInsertParagraph),\n/* harmony export */   $insertList: () => (/* binding */ $insertList),\n/* harmony export */   $isListItemNode: () => (/* binding */ $isListItemNode),\n/* harmony export */   $isListNode: () => (/* binding */ $isListNode),\n/* harmony export */   $removeList: () => (/* binding */ $removeList),\n/* harmony export */   INSERT_CHECK_LIST_COMMAND: () => (/* binding */ INSERT_CHECK_LIST_COMMAND),\n/* harmony export */   INSERT_ORDERED_LIST_COMMAND: () => (/* binding */ INSERT_ORDERED_LIST_COMMAND),\n/* harmony export */   INSERT_UNORDERED_LIST_COMMAND: () => (/* binding */ INSERT_UNORDERED_LIST_COMMAND),\n/* harmony export */   ListItemNode: () => (/* binding */ ListItemNode),\n/* harmony export */   ListNode: () => (/* binding */ ListNode),\n/* harmony export */   REMOVE_LIST_COMMAND: () => (/* binding */ REMOVE_LIST_COMMAND),\n/* harmony export */   insertList: () => (/* binding */ insertList),\n/* harmony export */   registerList: () => (/* binding */ registerList),\n/* harmony export */   removeList: () => (/* binding */ removeList)\n/* harmony export */ });\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Checks the depth of listNode from the root node.\n * @param listNode - The ListNode to be checked.\n * @returns The depth of the ListNode.\n */\nfunction $getListDepth(listNode) {\n  let depth = 1;\n  let parent = listNode.getParent();\n  while (parent != null) {\n    if ($isListItemNode(parent)) {\n      const parentList = parent.getParent();\n      if ($isListNode(parentList)) {\n        depth++;\n        parent = parentList.getParent();\n        continue;\n      }\n      {\n        formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);\n      }\n    }\n    return depth;\n  }\n  return depth;\n}\n\n/**\n * Finds the nearest ancestral ListNode and returns it, throws an invariant if listItem is not a ListItemNode.\n * @param listItem - The node to be checked.\n * @returns The ListNode found.\n */\nfunction $getTopListNode(listItem) {\n  let list = listItem.getParent();\n  if (!$isListNode(list)) {\n    {\n      formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);\n    }\n  }\n  let parent = list;\n  while (parent !== null) {\n    parent = parent.getParent();\n    if ($isListNode(parent)) {\n      list = parent;\n    }\n  }\n  return list;\n}\n\n/**\n * A recursive Depth-First Search (Postorder Traversal) that finds all of a node's children\n * that are of type ListItemNode and returns them in an array.\n * @param node - The ListNode to start the search.\n * @returns An array containing all nodes of type ListItemNode found.\n */\n// This should probably be $getAllChildrenOfType\nfunction $getAllListItems(node) {\n  let listItemNodes = [];\n  const listChildren = node.getChildren().filter($isListItemNode);\n  for (let i = 0; i < listChildren.length; i++) {\n    const listItemNode = listChildren[i];\n    const firstChild = listItemNode.getFirstChild();\n    if ($isListNode(firstChild)) {\n      listItemNodes = listItemNodes.concat($getAllListItems(firstChild));\n    } else {\n      listItemNodes.push(listItemNode);\n    }\n  }\n  return listItemNodes;\n}\n\n/**\n * Checks to see if the passed node is a ListItemNode and has a ListNode as a child.\n * @param node - The node to be checked.\n * @returns true if the node is a ListItemNode and has a ListNode child, false otherwise.\n */\nfunction isNestedListNode(node) {\n  return $isListItemNode(node) && $isListNode(node.getFirstChild());\n}\n\n/**\n * Takes a deeply nested ListNode or ListItemNode and traverses up the branch to delete the first\n * ancestral ListNode (which could be the root ListNode) or ListItemNode with siblings, essentially\n * bringing the deeply nested node up the branch once. Would remove sublist if it has siblings.\n * Should not break ListItem -> List -> ListItem chain as empty List/ItemNodes should be removed on .remove().\n * @param sublist - The nested ListNode or ListItemNode to be brought up the branch.\n */\nfunction $removeHighestEmptyListParent(sublist) {\n  // Nodes may be repeatedly indented, to create deeply nested lists that each\n  // contain just one bullet.\n  // Our goal is to remove these (empty) deeply nested lists. The easiest\n  // way to do that is crawl back up the tree until we find a node that has siblings\n  // (e.g. is actually part of the list contents) and delete that, or delete\n  // the root of the list (if no list nodes have siblings.)\n  let emptyListPtr = sublist;\n  while (emptyListPtr.getNextSibling() == null && emptyListPtr.getPreviousSibling() == null) {\n    const parent = emptyListPtr.getParent();\n    if (parent == null || !($isListItemNode(parent) || $isListNode(parent))) {\n      break;\n    }\n    emptyListPtr = parent;\n  }\n  emptyListPtr.remove();\n}\n\n/**\n * Wraps a node into a ListItemNode.\n * @param node - The node to be wrapped into a ListItemNode\n * @returns The ListItemNode which the passed node is wrapped in.\n */\nfunction $wrapInListItem(node) {\n  const listItemWrapper = $createListItemNode();\n  return listItemWrapper.append(node);\n}\n\nfunction $isSelectingEmptyListItem(anchorNode, nodes) {\n  return $isListItemNode(anchorNode) && (nodes.length === 0 || nodes.length === 1 && anchorNode.is(nodes[0]) && anchorNode.getChildrenSize() === 0);\n}\n\n/**\n * Inserts a new ListNode. If the selection's anchor node is an empty ListItemNode and is a child of\n * the root/shadow root, it will replace the ListItemNode with a ListNode and the old ListItemNode.\n * Otherwise it will replace its parent with a new ListNode and re-insert the ListItemNode and any previous children.\n * If the selection's anchor node is not an empty ListItemNode, it will add a new ListNode or merge an existing ListNode,\n * unless the the node is a leaf node, in which case it will attempt to find a ListNode up the branch and replace it with\n * a new ListNode, or create a new ListNode at the nearest root/shadow root.\n * @param listType - The type of list, \"number\" | \"bullet\" | \"check\".\n */\nfunction $insertList(listType) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (selection !== null) {\n    let nodes = selection.getNodes();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const anchorAndFocus = selection.getStartEndPoints();\n      if (!(anchorAndFocus !== null)) {\n        formatDevErrorMessage(`insertList: anchor should be defined`);\n      }\n      const [anchor] = anchorAndFocus;\n      const anchorNode = anchor.getNode();\n      const anchorNodeParent = anchorNode.getParent();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(anchorNode)) {\n        const firstChild = anchorNode.getFirstChild();\n        if (firstChild) {\n          nodes = firstChild.selectStart().getNodes();\n        } else {\n          const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n          anchorNode.append(paragraph);\n          nodes = paragraph.select().getNodes();\n        }\n      } else if ($isSelectingEmptyListItem(anchorNode, nodes)) {\n        const list = $createListNode(listType);\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(anchorNodeParent)) {\n          anchorNode.replace(list);\n          const listItem = $createListItemNode();\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(anchorNode)) {\n            listItem.setFormat(anchorNode.getFormatType());\n            listItem.setIndent(anchorNode.getIndent());\n          }\n          list.append(listItem);\n        } else if ($isListItemNode(anchorNode)) {\n          const parent = anchorNode.getParentOrThrow();\n          append(list, parent.getChildren());\n          parent.replace(list);\n        }\n        return;\n      }\n    }\n    const handled = new Set();\n    for (let i = 0; i < nodes.length; i++) {\n      const node = nodes[i];\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && node.isEmpty() && !$isListItemNode(node) && !handled.has(node.getKey())) {\n        $createListOrMerge(node, listType);\n        continue;\n      }\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLeafNode)(node)) {\n        let parent = node.getParent();\n        while (parent != null) {\n          const parentKey = parent.getKey();\n          if ($isListNode(parent)) {\n            if (!handled.has(parentKey)) {\n              const newListNode = $createListNode(listType);\n              append(newListNode, parent.getChildren());\n              parent.replace(newListNode);\n              handled.add(parentKey);\n            }\n            break;\n          } else {\n            const nextParent = parent.getParent();\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(nextParent) && !handled.has(parentKey)) {\n              handled.add(parentKey);\n              $createListOrMerge(parent, listType);\n              break;\n            }\n            parent = nextParent;\n          }\n        }\n      }\n    }\n  }\n}\nfunction append(node, nodesToAppend) {\n  node.splice(node.getChildrenSize(), 0, nodesToAppend);\n}\nfunction $createListOrMerge(node, listType) {\n  if ($isListNode(node)) {\n    return node;\n  }\n  const previousSibling = node.getPreviousSibling();\n  const nextSibling = node.getNextSibling();\n  const listItem = $createListItemNode();\n  append(listItem, node.getChildren());\n  let targetList;\n  if ($isListNode(previousSibling) && listType === previousSibling.getListType()) {\n    previousSibling.append(listItem);\n    // if the same type of list is on both sides, merge them.\n    if ($isListNode(nextSibling) && listType === nextSibling.getListType()) {\n      append(previousSibling, nextSibling.getChildren());\n      nextSibling.remove();\n    }\n    targetList = previousSibling;\n  } else if ($isListNode(nextSibling) && listType === nextSibling.getListType()) {\n    nextSibling.getFirstChildOrThrow().insertBefore(listItem);\n    targetList = nextSibling;\n  } else {\n    const list = $createListNode(listType);\n    list.append(listItem);\n    node.replace(list);\n    targetList = list;\n  }\n  // listItem needs to be attached to root prior to setting indent\n  listItem.setFormat(node.getFormatType());\n  listItem.setIndent(node.getIndent());\n  node.remove();\n  return targetList;\n}\n\n/**\n * A recursive function that goes through each list and their children, including nested lists,\n * appending list2 children after list1 children and updating ListItemNode values.\n * @param list1 - The first list to be merged.\n * @param list2 - The second list to be merged.\n */\nfunction mergeLists(list1, list2) {\n  const listItem1 = list1.getLastChild();\n  const listItem2 = list2.getFirstChild();\n  if (listItem1 && listItem2 && isNestedListNode(listItem1) && isNestedListNode(listItem2)) {\n    mergeLists(listItem1.getFirstChild(), listItem2.getFirstChild());\n    listItem2.remove();\n  }\n  const toMerge = list2.getChildren();\n  if (toMerge.length > 0) {\n    list1.append(...toMerge);\n  }\n  list2.remove();\n}\n\n/**\n * Searches for the nearest ancestral ListNode and removes it. If selection is an empty ListItemNode\n * it will remove the whole list, including the ListItemNode. For each ListItemNode in the ListNode,\n * removeList will also generate new ParagraphNodes in the removed ListNode's place. Any child node\n * inside a ListItemNode will be appended to the new ParagraphNodes.\n */\nfunction $removeList() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    const listNodes = new Set();\n    const nodes = selection.getNodes();\n    const anchorNode = selection.anchor.getNode();\n    if ($isSelectingEmptyListItem(anchorNode, nodes)) {\n      listNodes.add($getTopListNode(anchorNode));\n    } else {\n      for (let i = 0; i < nodes.length; i++) {\n        const node = nodes[i];\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLeafNode)(node)) {\n          const listItemNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$getNearestNodeOfType)(node, ListItemNode);\n          if (listItemNode != null) {\n            listNodes.add($getTopListNode(listItemNode));\n          }\n        }\n      }\n    }\n    for (const listNode of listNodes) {\n      let insertionPoint = listNode;\n      const listItems = $getAllListItems(listNode);\n      for (const listItemNode of listItems) {\n        const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)().setTextStyle(selection.style).setTextFormat(selection.format);\n        append(paragraph, listItemNode.getChildren());\n        insertionPoint.insertAfter(paragraph);\n        insertionPoint = paragraph;\n\n        // When the anchor and focus fall on the textNode\n        // we don't have to change the selection because the textNode will be appended to\n        // the newly generated paragraph.\n        // When selection is in empty nested list item, selection is actually on the listItemNode.\n        // When the corresponding listItemNode is deleted and replaced by the newly generated paragraph\n        // we should manually set the selection's focus and anchor to the newly generated paragraph.\n        if (listItemNode.__key === selection.anchor.key) {\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.anchor, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(paragraph, 'next')));\n        }\n        if (listItemNode.__key === selection.focus.key) {\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.focus, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(paragraph, 'next')));\n        }\n        listItemNode.remove();\n      }\n      listNode.remove();\n    }\n  }\n}\n\n/**\n * Takes the value of a child ListItemNode and makes it the value the ListItemNode\n * should be if it isn't already. Also ensures that checked is undefined if the\n * parent does not have a list type of 'check'.\n * @param list - The list whose children are updated.\n */\nfunction updateChildrenListItemValue(list) {\n  const isNotChecklist = list.getListType() !== 'check';\n  let value = list.getStart();\n  for (const child of list.getChildren()) {\n    if ($isListItemNode(child)) {\n      if (child.getValue() !== value) {\n        child.setValue(value);\n      }\n      if (isNotChecklist && child.getLatest().__checked != null) {\n        child.setChecked(undefined);\n      }\n      if (!$isListNode(child.getFirstChild())) {\n        value++;\n      }\n    }\n  }\n}\n\n/**\n * Merge the next sibling list if same type.\n * <ul> will merge with <ul>, but NOT <ul> with <ol>.\n * @param list - The list whose next sibling should be potentially merged\n */\nfunction mergeNextSiblingListIfSameType(list) {\n  const nextSibling = list.getNextSibling();\n  if ($isListNode(nextSibling) && list.getListType() === nextSibling.getListType()) {\n    mergeLists(list, nextSibling);\n  }\n}\n\n/**\n * Adds an empty ListNode/ListItemNode chain at listItemNode, so as to\n * create an indent effect. Won't indent ListItemNodes that have a ListNode as\n * a child, but does merge sibling ListItemNodes if one has a nested ListNode.\n * @param listItemNode - The ListItemNode to be indented.\n */\nfunction $handleIndent(listItemNode) {\n  // go through each node and decide where to move it.\n  const removed = new Set();\n  if (isNestedListNode(listItemNode) || removed.has(listItemNode.getKey())) {\n    return;\n  }\n  const parent = listItemNode.getParent();\n\n  // We can cast both of the below `isNestedListNode` only returns a boolean type instead of a user-defined type guards\n  const nextSibling = listItemNode.getNextSibling();\n  const previousSibling = listItemNode.getPreviousSibling();\n  // if there are nested lists on either side, merge them all together.\n\n  if (isNestedListNode(nextSibling) && isNestedListNode(previousSibling)) {\n    const innerList = previousSibling.getFirstChild();\n    if ($isListNode(innerList)) {\n      innerList.append(listItemNode);\n      const nextInnerList = nextSibling.getFirstChild();\n      if ($isListNode(nextInnerList)) {\n        const children = nextInnerList.getChildren();\n        append(innerList, children);\n        nextSibling.remove();\n        removed.add(nextSibling.getKey());\n      }\n    }\n  } else if (isNestedListNode(nextSibling)) {\n    // if the ListItemNode is next to a nested ListNode, merge them\n    const innerList = nextSibling.getFirstChild();\n    if ($isListNode(innerList)) {\n      const firstChild = innerList.getFirstChild();\n      if (firstChild !== null) {\n        firstChild.insertBefore(listItemNode);\n      }\n    }\n  } else if (isNestedListNode(previousSibling)) {\n    const innerList = previousSibling.getFirstChild();\n    if ($isListNode(innerList)) {\n      innerList.append(listItemNode);\n    }\n  } else {\n    // otherwise, we need to create a new nested ListNode\n\n    if ($isListNode(parent)) {\n      const newListItem = $createListItemNode().setTextFormat(parent.getTextFormat()).setTextStyle(parent.getTextStyle());\n      const newList = $createListNode(parent.getListType()).setTextFormat(parent.getTextFormat()).setTextStyle(parent.getTextStyle());\n      newListItem.append(newList);\n      newList.append(listItemNode);\n      if (previousSibling) {\n        previousSibling.insertAfter(newListItem);\n      } else if (nextSibling) {\n        nextSibling.insertBefore(newListItem);\n      } else {\n        parent.append(newListItem);\n      }\n    }\n  }\n}\n\n/**\n * Removes an indent by removing an empty ListNode/ListItemNode chain. An indented ListItemNode\n * has a great grandparent node of type ListNode, which is where the ListItemNode will reside\n * within as a child.\n * @param listItemNode - The ListItemNode to remove the indent (outdent).\n */\nfunction $handleOutdent(listItemNode) {\n  // go through each node and decide where to move it.\n\n  if (isNestedListNode(listItemNode)) {\n    return;\n  }\n  const parentList = listItemNode.getParent();\n  const grandparentListItem = parentList ? parentList.getParent() : undefined;\n  const greatGrandparentList = grandparentListItem ? grandparentListItem.getParent() : undefined;\n  // If it doesn't have these ancestors, it's not indented.\n\n  if ($isListNode(greatGrandparentList) && $isListItemNode(grandparentListItem) && $isListNode(parentList)) {\n    // if it's the first child in it's parent list, insert it into the\n    // great grandparent list before the grandparent\n    const firstChild = parentList ? parentList.getFirstChild() : undefined;\n    const lastChild = parentList ? parentList.getLastChild() : undefined;\n    if (listItemNode.is(firstChild)) {\n      grandparentListItem.insertBefore(listItemNode);\n      if (parentList.isEmpty()) {\n        grandparentListItem.remove();\n      }\n      // if it's the last child in it's parent list, insert it into the\n      // great grandparent list after the grandparent.\n    } else if (listItemNode.is(lastChild)) {\n      grandparentListItem.insertAfter(listItemNode);\n      if (parentList.isEmpty()) {\n        grandparentListItem.remove();\n      }\n    } else {\n      // otherwise, we need to split the siblings into two new nested lists\n      const listType = parentList.getListType();\n      const previousSiblingsListItem = $createListItemNode();\n      const previousSiblingsList = $createListNode(listType);\n      previousSiblingsListItem.append(previousSiblingsList);\n      listItemNode.getPreviousSiblings().forEach(sibling => previousSiblingsList.append(sibling));\n      const nextSiblingsListItem = $createListItemNode();\n      const nextSiblingsList = $createListNode(listType);\n      nextSiblingsListItem.append(nextSiblingsList);\n      append(nextSiblingsList, listItemNode.getNextSiblings());\n      // put the sibling nested lists on either side of the grandparent list item in the great grandparent.\n      grandparentListItem.insertBefore(previousSiblingsListItem);\n      grandparentListItem.insertAfter(nextSiblingsListItem);\n      // replace the grandparent list item (now between the siblings) with the outdented list item.\n      grandparentListItem.replace(listItemNode);\n    }\n  }\n}\n\n/**\n * Attempts to insert a ParagraphNode at selection and selects the new node. The selection must contain a ListItemNode\n * or a node that does not already contain text. If its grandparent is the root/shadow root, it will get the ListNode\n * (which should be the parent node) and insert the ParagraphNode as a sibling to the ListNode. If the ListNode is\n * nested in a ListItemNode instead, it will add the ParagraphNode after the grandparent ListItemNode.\n * Throws an invariant if the selection is not a child of a ListNode.\n * @returns true if a ParagraphNode was inserted succesfully, false if there is no selection\n * or the selection does not contain a ListItemNode or the node already holds text.\n */\nfunction $handleListInsertParagraph() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || !selection.isCollapsed()) {\n    return false;\n  }\n  // Only run this code on empty list items\n  const anchor = selection.anchor.getNode();\n  if (!$isListItemNode(anchor) || anchor.getChildrenSize() !== 0) {\n    return false;\n  }\n  const topListNode = $getTopListNode(anchor);\n  const parent = anchor.getParent();\n  if (!$isListNode(parent)) {\n    formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);\n  }\n  const grandparent = parent.getParent();\n  let replacementNode;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(grandparent)) {\n    replacementNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    topListNode.insertAfter(replacementNode);\n  } else if ($isListItemNode(grandparent)) {\n    replacementNode = $createListItemNode();\n    grandparent.insertAfter(replacementNode);\n  } else {\n    return false;\n  }\n  replacementNode.setTextStyle(selection.style).setTextFormat(selection.format).select();\n  const nextSiblings = anchor.getNextSiblings();\n  if (nextSiblings.length > 0) {\n    const newList = $createListNode(parent.getListType());\n    if ($isListItemNode(replacementNode)) {\n      const newListItem = $createListItemNode();\n      newListItem.append(newList);\n      replacementNode.insertAfter(newListItem);\n    } else {\n      replacementNode.insertAfter(newList);\n    }\n    newList.append(...nextSiblings);\n  }\n\n  // Don't leave hanging nested empty lists\n  $removeHighestEmptyListParent(anchor);\n  return true;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction normalizeClassNames(...classNames) {\n  const rval = [];\n  for (const className of classNames) {\n    if (className && typeof className === 'string') {\n      for (const [s] of className.matchAll(/\\S+/g)) {\n        rval.push(s);\n      }\n    }\n  }\n  return rval;\n}\n\nfunction applyMarkerStyles(dom, node, prevNode) {\n  const styles = (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_2__.getStyleObjectFromCSS)(node.__textStyle);\n  for (const k in styles) {\n    dom.style.setProperty(`--listitem-marker-${k}`, styles[k]);\n  }\n  if (prevNode) {\n    for (const k in (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_2__.getStyleObjectFromCSS)(prevNode.__textStyle)) {\n      if (!(k in styles)) {\n        dom.style.removeProperty(`--listitem-marker-${k}`);\n      }\n    }\n  }\n}\n\n/** @noInheritDoc */\nclass ListItemNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  /** @internal */\n\n  static getType() {\n    return 'listitem';\n  }\n  static clone(node) {\n    return new ListItemNode(node.__value, node.__checked, node.__key);\n  }\n  constructor(value, checked, key) {\n    super(key);\n    this.__value = value === undefined ? 1 : value;\n    this.__checked = checked;\n  }\n  createDOM(config) {\n    const element = document.createElement('li');\n    const parent = this.getParent();\n    if ($isListNode(parent) && parent.getListType() === 'check') {\n      updateListItemChecked(element, this, null);\n    }\n    element.value = this.__value;\n    $setListItemThemeClassNames(element, config.theme, this);\n    const nextStyle = this.__style;\n    if (nextStyle) {\n      element.style.cssText = nextStyle;\n    }\n    applyMarkerStyles(element, this, null);\n    return element;\n  }\n  updateDOM(prevNode, dom, config) {\n    const parent = this.getParent();\n    if ($isListNode(parent) && parent.getListType() === 'check') {\n      updateListItemChecked(dom, this, prevNode);\n    }\n    // @ts-expect-error - this is always HTMLListItemElement\n    dom.value = this.__value;\n    $setListItemThemeClassNames(dom, config.theme, this);\n    const prevStyle = prevNode.__style;\n    const nextStyle = this.__style;\n    if (prevStyle !== nextStyle) {\n      if (nextStyle === '') {\n        dom.removeAttribute('style');\n      } else {\n        dom.style.cssText = nextStyle;\n      }\n    }\n    applyMarkerStyles(dom, this, prevNode);\n    return false;\n  }\n  static transform() {\n    return node => {\n      if (!$isListItemNode(node)) {\n        formatDevErrorMessage(`node is not a ListItemNode`);\n      }\n      if (node.__checked == null) {\n        return;\n      }\n      const parent = node.getParent();\n      if ($isListNode(parent)) {\n        if (parent.getListType() !== 'check' && node.getChecked() != null) {\n          node.setChecked(undefined);\n        }\n      }\n    };\n  }\n  static importDOM() {\n    return {\n      li: () => ({\n        conversion: $convertListItemElement,\n        priority: 0\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createListItemNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setValue(serializedNode.value).setChecked(serializedNode.checked);\n  }\n  exportDOM(editor) {\n    const element = this.createDOM(editor._config);\n    element.style.textAlign = this.getFormatType();\n    const direction = this.getDirection();\n    if (direction) {\n      element.dir = direction;\n    }\n    return {\n      element\n    };\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      checked: this.getChecked(),\n      value: this.getValue()\n    };\n  }\n  append(...nodes) {\n    for (let i = 0; i < nodes.length; i++) {\n      const node = nodes[i];\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && this.canMergeWith(node)) {\n        const children = node.getChildren();\n        this.append(...children);\n        node.remove();\n      } else {\n        super.append(node);\n      }\n    }\n    return this;\n  }\n  replace(replaceWithNode, includeChildren) {\n    if ($isListItemNode(replaceWithNode)) {\n      return super.replace(replaceWithNode);\n    }\n    this.setIndent(0);\n    const list = this.getParentOrThrow();\n    if (!$isListNode(list)) {\n      return replaceWithNode;\n    }\n    if (list.__first === this.getKey()) {\n      list.insertBefore(replaceWithNode);\n    } else if (list.__last === this.getKey()) {\n      list.insertAfter(replaceWithNode);\n    } else {\n      // Split the list\n      const newList = $createListNode(list.getListType());\n      let nextSibling = this.getNextSibling();\n      while (nextSibling) {\n        const nodeToAppend = nextSibling;\n        nextSibling = nextSibling.getNextSibling();\n        newList.append(nodeToAppend);\n      }\n      list.insertAfter(replaceWithNode);\n      replaceWithNode.insertAfter(newList);\n    }\n    if (includeChildren) {\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(replaceWithNode)) {\n        formatDevErrorMessage(`includeChildren should only be true for ElementNodes`);\n      }\n      this.getChildren().forEach(child => {\n        replaceWithNode.append(child);\n      });\n    }\n    this.remove();\n    if (list.getChildrenSize() === 0) {\n      list.remove();\n    }\n    return replaceWithNode;\n  }\n  insertAfter(node, restoreSelection = true) {\n    const listNode = this.getParentOrThrow();\n    if (!$isListNode(listNode)) {\n      {\n        formatDevErrorMessage(`insertAfter: list node is not parent of list item node`);\n      }\n    }\n    if ($isListItemNode(node)) {\n      return super.insertAfter(node, restoreSelection);\n    }\n    const siblings = this.getNextSiblings();\n\n    // Split the lists and insert the node in between them\n    listNode.insertAfter(node, restoreSelection);\n    if (siblings.length !== 0) {\n      const newListNode = $createListNode(listNode.getListType());\n      siblings.forEach(sibling => newListNode.append(sibling));\n      node.insertAfter(newListNode, restoreSelection);\n    }\n    return node;\n  }\n  remove(preserveEmptyParent) {\n    const prevSibling = this.getPreviousSibling();\n    const nextSibling = this.getNextSibling();\n    super.remove(preserveEmptyParent);\n    if (prevSibling && nextSibling && isNestedListNode(prevSibling) && isNestedListNode(nextSibling)) {\n      mergeLists(prevSibling.getFirstChild(), nextSibling.getFirstChild());\n      nextSibling.remove();\n    }\n  }\n  insertNewAfter(_, restoreSelection = true) {\n    const newElement = $createListItemNode().updateFromJSON(this.exportJSON()).setChecked(this.getChecked() ? false : undefined);\n    this.insertAfter(newElement, restoreSelection);\n    return newElement;\n  }\n  collapseAtStart(selection) {\n    const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const children = this.getChildren();\n    children.forEach(child => paragraph.append(child));\n    const listNode = this.getParentOrThrow();\n    const listNodeParent = listNode.getParentOrThrow();\n    const isIndented = $isListItemNode(listNodeParent);\n    if (listNode.getChildrenSize() === 1) {\n      if (isIndented) {\n        // if the list node is nested, we just want to remove it,\n        // effectively unindenting it.\n        listNode.remove();\n        listNodeParent.select();\n      } else {\n        listNode.insertBefore(paragraph);\n        listNode.remove();\n        // If we have selection on the list item, we'll need to move it\n        // to the paragraph\n        const anchor = selection.anchor;\n        const focus = selection.focus;\n        const key = paragraph.getKey();\n        if (anchor.type === 'element' && anchor.getNode().is(this)) {\n          anchor.set(key, anchor.offset, 'element');\n        }\n        if (focus.type === 'element' && focus.getNode().is(this)) {\n          focus.set(key, focus.offset, 'element');\n        }\n      }\n    } else {\n      listNode.insertBefore(paragraph);\n      this.remove();\n    }\n    return true;\n  }\n  getValue() {\n    const self = this.getLatest();\n    return self.__value;\n  }\n  setValue(value) {\n    const self = this.getWritable();\n    self.__value = value;\n    return self;\n  }\n  getChecked() {\n    const self = this.getLatest();\n    let listType;\n    const parent = this.getParent();\n    if ($isListNode(parent)) {\n      listType = parent.getListType();\n    }\n    return listType === 'check' ? Boolean(self.__checked) : undefined;\n  }\n  setChecked(checked) {\n    const self = this.getWritable();\n    self.__checked = checked;\n    return self;\n  }\n  toggleChecked() {\n    const self = this.getWritable();\n    return self.setChecked(!self.__checked);\n  }\n  getIndent() {\n    // If we don't have a parent, we are likely serializing\n    const parent = this.getParent();\n    if (parent === null || !this.isAttached()) {\n      return this.getLatest().__indent;\n    }\n    // ListItemNode should always have a ListNode for a parent.\n    let listNodeParent = parent.getParentOrThrow();\n    let indentLevel = 0;\n    while ($isListItemNode(listNodeParent)) {\n      listNodeParent = listNodeParent.getParentOrThrow().getParentOrThrow();\n      indentLevel++;\n    }\n    return indentLevel;\n  }\n  setIndent(indent) {\n    if (!(typeof indent === 'number')) {\n      formatDevErrorMessage(`Invalid indent value.`);\n    }\n    indent = Math.floor(indent);\n    if (!(indent >= 0)) {\n      formatDevErrorMessage(`Indent value must be non-negative.`);\n    }\n    let currentIndent = this.getIndent();\n    while (currentIndent !== indent) {\n      if (currentIndent < indent) {\n        $handleIndent(this);\n        currentIndent++;\n      } else {\n        $handleOutdent(this);\n        currentIndent--;\n      }\n    }\n    return this;\n  }\n\n  /** @deprecated @internal */\n  canInsertAfter(node) {\n    return $isListItemNode(node);\n  }\n\n  /** @deprecated @internal */\n  canReplaceWith(replacement) {\n    return $isListItemNode(replacement);\n  }\n  canMergeWith(node) {\n    return $isListItemNode(node) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isParagraphNode)(node);\n  }\n  extractWithChild(child, selection) {\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const anchorNode = selection.anchor.getNode();\n    const focusNode = selection.focus.getNode();\n    return this.isParentOf(anchorNode) && this.isParentOf(focusNode) && this.getTextContent().length === selection.getTextContent().length;\n  }\n  isParentRequired() {\n    return true;\n  }\n  createParentElementNode() {\n    return $createListNode('bullet');\n  }\n  canMergeWhenEmpty() {\n    return true;\n  }\n}\nfunction $setListItemThemeClassNames(dom, editorThemeClasses, node) {\n  const classesToAdd = [];\n  const classesToRemove = [];\n  const listTheme = editorThemeClasses.list;\n  const listItemClassName = listTheme ? listTheme.listitem : undefined;\n  let nestedListItemClassName;\n  if (listTheme && listTheme.nested) {\n    nestedListItemClassName = listTheme.nested.listitem;\n  }\n  if (listItemClassName !== undefined) {\n    classesToAdd.push(...normalizeClassNames(listItemClassName));\n  }\n  if (listTheme) {\n    const parentNode = node.getParent();\n    const isCheckList = $isListNode(parentNode) && parentNode.getListType() === 'check';\n    const checked = node.getChecked();\n    if (!isCheckList || checked) {\n      classesToRemove.push(listTheme.listitemUnchecked);\n    }\n    if (!isCheckList || !checked) {\n      classesToRemove.push(listTheme.listitemChecked);\n    }\n    if (isCheckList) {\n      classesToAdd.push(checked ? listTheme.listitemChecked : listTheme.listitemUnchecked);\n    }\n  }\n  if (nestedListItemClassName !== undefined) {\n    const nestedListItemClasses = normalizeClassNames(nestedListItemClassName);\n    if (node.getChildren().some(child => $isListNode(child))) {\n      classesToAdd.push(...nestedListItemClasses);\n    } else {\n      classesToRemove.push(...nestedListItemClasses);\n    }\n  }\n  if (classesToRemove.length > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, ...classesToRemove);\n  }\n  if (classesToAdd.length > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, ...classesToAdd);\n  }\n}\nfunction updateListItemChecked(dom, listItemNode, prevListItemNode, listNode) {\n  // Only add attributes for leaf list items\n  if ($isListNode(listItemNode.getFirstChild())) {\n    dom.removeAttribute('role');\n    dom.removeAttribute('tabIndex');\n    dom.removeAttribute('aria-checked');\n  } else {\n    dom.setAttribute('role', 'checkbox');\n    dom.setAttribute('tabIndex', '-1');\n    if (!prevListItemNode || listItemNode.__checked !== prevListItemNode.__checked) {\n      dom.setAttribute('aria-checked', listItemNode.getChecked() ? 'true' : 'false');\n    }\n  }\n}\nfunction $convertListItemElement(domNode) {\n  const isGitHubCheckList = domNode.classList.contains('task-list-item');\n  if (isGitHubCheckList) {\n    for (const child of domNode.children) {\n      if (child.tagName === 'INPUT') {\n        return $convertCheckboxInput(child);\n      }\n    }\n  }\n  const ariaCheckedAttr = domNode.getAttribute('aria-checked');\n  const checked = ariaCheckedAttr === 'true' ? true : ariaCheckedAttr === 'false' ? false : undefined;\n  return {\n    node: $createListItemNode(checked)\n  };\n}\nfunction $convertCheckboxInput(domNode) {\n  const isCheckboxInput = domNode.getAttribute('type') === 'checkbox';\n  if (!isCheckboxInput) {\n    return {\n      node: null\n    };\n  }\n  const checked = domNode.hasAttribute('checked');\n  return {\n    node: $createListItemNode(checked)\n  };\n}\n\n/**\n * Creates a new List Item node, passing true/false will convert it to a checkbox input.\n * @param checked - Is the List Item a checkbox and, if so, is it checked? undefined/null: not a checkbox, true/false is a checkbox and checked/unchecked, respectively.\n * @returns The new List Item.\n */\nfunction $createListItemNode(checked) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new ListItemNode(undefined, checked));\n}\n\n/**\n * Checks to see if the node is a ListItemNode.\n * @param node - The node to be checked.\n * @returns true if the node is a ListItemNode, false otherwise.\n */\nfunction $isListItemNode(node) {\n  return node instanceof ListItemNode;\n}\n\n/** @noInheritDoc */\nclass ListNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  static getType() {\n    return 'list';\n  }\n  static clone(node) {\n    const listType = node.__listType || TAG_TO_LIST_TYPE[node.__tag];\n    return new ListNode(listType, node.__start, node.__key);\n  }\n  constructor(listType = 'number', start = 1, key) {\n    super(key);\n    const _listType = TAG_TO_LIST_TYPE[listType] || listType;\n    this.__listType = _listType;\n    this.__tag = _listType === 'number' ? 'ol' : 'ul';\n    this.__start = start;\n  }\n  getTag() {\n    return this.__tag;\n  }\n  setListType(type) {\n    const writable = this.getWritable();\n    writable.__listType = type;\n    writable.__tag = type === 'number' ? 'ol' : 'ul';\n    return writable;\n  }\n  getListType() {\n    return this.__listType;\n  }\n  getStart() {\n    return this.__start;\n  }\n  setStart(start) {\n    const self = this.getWritable();\n    self.__start = start;\n    return self;\n  }\n\n  // View\n\n  createDOM(config, _editor) {\n    const tag = this.__tag;\n    const dom = document.createElement(tag);\n    if (this.__start !== 1) {\n      dom.setAttribute('start', String(this.__start));\n    }\n    // @ts-expect-error Internal field.\n    dom.__lexicalListType = this.__listType;\n    $setListThemeClassNames(dom, config.theme, this);\n    return dom;\n  }\n  updateDOM(prevNode, dom, config) {\n    if (prevNode.__tag !== this.__tag) {\n      return true;\n    }\n    $setListThemeClassNames(dom, config.theme, this);\n    return false;\n  }\n  static transform() {\n    return node => {\n      if (!$isListNode(node)) {\n        formatDevErrorMessage(`node is not a ListNode`);\n      }\n      mergeNextSiblingListIfSameType(node);\n      updateChildrenListItemValue(node);\n    };\n  }\n  static importDOM() {\n    return {\n      ol: () => ({\n        conversion: $convertListNode,\n        priority: 0\n      }),\n      ul: () => ({\n        conversion: $convertListNode,\n        priority: 0\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createListNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setListType(serializedNode.listType).setStart(serializedNode.start);\n  }\n  exportDOM(editor) {\n    const element = this.createDOM(editor._config, editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n      if (this.__start !== 1) {\n        element.setAttribute('start', String(this.__start));\n      }\n      if (this.__listType === 'check') {\n        element.setAttribute('__lexicalListType', 'check');\n      }\n    }\n    return {\n      element\n    };\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      listType: this.getListType(),\n      start: this.getStart(),\n      tag: this.getTag()\n    };\n  }\n  canBeEmpty() {\n    return false;\n  }\n  canIndent() {\n    return false;\n  }\n  splice(start, deleteCount, nodesToInsert) {\n    let listItemNodesToInsert = nodesToInsert;\n    for (let i = 0; i < nodesToInsert.length; i++) {\n      const node = nodesToInsert[i];\n      if (!$isListItemNode(node)) {\n        if (listItemNodesToInsert === nodesToInsert) {\n          listItemNodesToInsert = [...nodesToInsert];\n        }\n        listItemNodesToInsert[i] = $createListItemNode().append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && !($isListNode(node) || node.isInline()) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(node.getTextContent()) : node);\n      }\n    }\n    return super.splice(start, deleteCount, listItemNodesToInsert);\n  }\n  extractWithChild(child) {\n    return $isListItemNode(child);\n  }\n}\nfunction $setListThemeClassNames(dom, editorThemeClasses, node) {\n  const classesToAdd = [];\n  const classesToRemove = [];\n  const listTheme = editorThemeClasses.list;\n  if (listTheme !== undefined) {\n    const listLevelsClassNames = listTheme[`${node.__tag}Depth`] || [];\n    const listDepth = $getListDepth(node) - 1;\n    const normalizedListDepth = listDepth % listLevelsClassNames.length;\n    const listLevelClassName = listLevelsClassNames[normalizedListDepth];\n    const listClassName = listTheme[node.__tag];\n    let nestedListClassName;\n    const nestedListTheme = listTheme.nested;\n    const checklistClassName = listTheme.checklist;\n    if (nestedListTheme !== undefined && nestedListTheme.list) {\n      nestedListClassName = nestedListTheme.list;\n    }\n    if (listClassName !== undefined) {\n      classesToAdd.push(listClassName);\n    }\n    if (checklistClassName !== undefined && node.__listType === 'check') {\n      classesToAdd.push(checklistClassName);\n    }\n    if (listLevelClassName !== undefined) {\n      classesToAdd.push(...normalizeClassNames(listLevelClassName));\n      for (let i = 0; i < listLevelsClassNames.length; i++) {\n        if (i !== normalizedListDepth) {\n          classesToRemove.push(node.__tag + i);\n        }\n      }\n    }\n    if (nestedListClassName !== undefined) {\n      const nestedListItemClasses = normalizeClassNames(nestedListClassName);\n      if (listDepth > 1) {\n        classesToAdd.push(...nestedListItemClasses);\n      } else {\n        classesToRemove.push(...nestedListItemClasses);\n      }\n    }\n  }\n  if (classesToRemove.length > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, ...classesToRemove);\n  }\n  if (classesToAdd.length > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, ...classesToAdd);\n  }\n}\n\n/*\n * This function normalizes the children of a ListNode after the conversion from HTML,\n * ensuring that they are all ListItemNodes and contain either a single nested ListNode\n * or some other inline content.\n */\nfunction $normalizeChildren(nodes) {\n  const normalizedListItems = [];\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if ($isListItemNode(node)) {\n      normalizedListItems.push(node);\n      const children = node.getChildren();\n      if (children.length > 1) {\n        children.forEach(child => {\n          if ($isListNode(child)) {\n            normalizedListItems.push($wrapInListItem(child));\n          }\n        });\n      }\n    } else {\n      normalizedListItems.push($wrapInListItem(node));\n    }\n  }\n  return normalizedListItems;\n}\nfunction isDomChecklist(domNode) {\n  if (domNode.getAttribute('__lexicallisttype') === 'check' ||\n  // is github checklist\n  domNode.classList.contains('contains-task-list')) {\n    return true;\n  }\n  // if children are checklist items, the node is a checklist ul. Applicable for googledoc checklist pasting.\n  for (const child of domNode.childNodes) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(child) && child.hasAttribute('aria-checked')) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction $convertListNode(domNode) {\n  const nodeName = domNode.nodeName.toLowerCase();\n  let node = null;\n  if (nodeName === 'ol') {\n    // @ts-ignore\n    const start = domNode.start;\n    node = $createListNode('number', start);\n  } else if (nodeName === 'ul') {\n    if (isDomChecklist(domNode)) {\n      node = $createListNode('check');\n    } else {\n      node = $createListNode('bullet');\n    }\n  }\n  return {\n    after: $normalizeChildren,\n    node\n  };\n}\nconst TAG_TO_LIST_TYPE = {\n  ol: 'number',\n  ul: 'bullet'\n};\n\n/**\n * Creates a ListNode of listType.\n * @param listType - The type of list to be created. Can be 'number', 'bullet', or 'check'.\n * @param start - Where an ordered list starts its count, start = 1 if left undefined.\n * @returns The new ListNode\n */\nfunction $createListNode(listType = 'number', start = 1) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new ListNode(listType, start));\n}\n\n/**\n * Checks to see if the node is a ListNode.\n * @param node - The node to be checked.\n * @returns true if the node is a ListNode, false otherwise.\n */\nfunction $isListNode(node) {\n  return node instanceof ListNode;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst INSERT_UNORDERED_LIST_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('INSERT_UNORDERED_LIST_COMMAND');\nconst INSERT_ORDERED_LIST_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('INSERT_ORDERED_LIST_COMMAND');\nconst INSERT_CHECK_LIST_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('INSERT_CHECK_LIST_COMMAND');\nconst REMOVE_LIST_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('REMOVE_LIST_COMMAND');\nfunction registerList(editor) {\n  const removeListener = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(INSERT_ORDERED_LIST_COMMAND, () => {\n    $insertList('number');\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_LOW), editor.registerCommand(INSERT_UNORDERED_LIST_COMMAND, () => {\n    $insertList('bullet');\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_LOW), editor.registerCommand(REMOVE_LIST_COMMAND, () => {\n    $removeList();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_LOW), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, () => $handleListInsertParagraph(), lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_LOW), editor.registerNodeTransform(ListItemNode, node => {\n    const firstChild = node.getFirstChild();\n    if (firstChild) {\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(firstChild)) {\n        const style = firstChild.getStyle();\n        const format = firstChild.getFormat();\n        if (node.getTextStyle() !== style) {\n          node.setTextStyle(style);\n        }\n        if (node.getTextFormat() !== format) {\n          node.setTextFormat(format);\n        }\n      }\n    } else {\n      // If it's empty, check the selection\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && (selection.style !== node.getTextStyle() || selection.format !== node.getTextFormat()) && selection.isCollapsed() && node.is(selection.anchor.getNode())) {\n        node.setTextStyle(selection.style).setTextFormat(selection.format);\n      }\n    }\n  }), editor.registerNodeTransform(lexical__WEBPACK_IMPORTED_MODULE_0__.TextNode, node => {\n    const listItemParentNode = node.getParent();\n    if ($isListItemNode(listItemParentNode) && node.is(listItemParentNode.getFirstChild())) {\n      const style = node.getStyle();\n      const format = node.getFormat();\n      if (style !== listItemParentNode.getTextStyle() || format !== listItemParentNode.getTextFormat()) {\n        listItemParentNode.setTextStyle(style).setTextFormat(format);\n      }\n    }\n  }));\n  return removeListener;\n}\n\n/**\n * @deprecated use {@link $insertList} from an update or command listener.\n *\n * Inserts a new ListNode. If the selection's anchor node is an empty ListItemNode and is a child of\n * the root/shadow root, it will replace the ListItemNode with a ListNode and the old ListItemNode.\n * Otherwise it will replace its parent with a new ListNode and re-insert the ListItemNode and any previous children.\n * If the selection's anchor node is not an empty ListItemNode, it will add a new ListNode or merge an existing ListNode,\n * unless the the node is a leaf node, in which case it will attempt to find a ListNode up the branch and replace it with\n * a new ListNode, or create a new ListNode at the nearest root/shadow root.\n * @param editor - The lexical editor.\n * @param listType - The type of list, \"number\" | \"bullet\" | \"check\".\n */\nfunction insertList(editor, listType) {\n  editor.update(() => $insertList(listType));\n}\n\n/**\n * @deprecated use {@link $removeList} from an update or command listener.\n *\n * Searches for the nearest ancestral ListNode and removes it. If selection is an empty ListItemNode\n * it will remove the whole list, including the ListItemNode. For each ListItemNode in the ListNode,\n * removeList will also generate new ParagraphNodes in the removed ListNode's place. Any child node\n * inside a ListItemNode will be appended to the new ParagraphNodes.\n * @param editor - The lexical editor.\n */\nfunction removeList(editor) {\n  editor.update(() => $removeList());\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxleGljYWwrbGlzdEAwLjI4LjAvbm9kZV9tb2R1bGVzL0BsZXhpY2FsL2xpc3QvTGV4aWNhbExpc3QuZGV2Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwSTtBQUMyTTtBQUMxUjs7QUFFM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IseUJBQXlCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixzREFBYTtBQUNqQztBQUNBO0FBQ0EsUUFBUSwwREFBaUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLDREQUFtQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1YsNEJBQTRCLDZEQUFvQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxZQUFZLDREQUFtQjtBQUMvQjtBQUNBO0FBQ0EsY0FBYyx1REFBYztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBLFVBQVUsdURBQWM7QUFDeEI7QUFDQTtBQUNBO0FBQ0EsVUFBVSxvREFBVztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0EsZ0JBQWdCLDREQUFtQjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isc0RBQWE7QUFDakMsTUFBTSwwREFBaUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixzQkFBc0Isa0JBQWtCO0FBQ3hDO0FBQ0EsWUFBWSxvREFBVztBQUN2QiwrQkFBK0IscUVBQXFCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDZEQUFvQjtBQUM5QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLDJEQUFrQixtQkFBbUIsd0RBQWUsQ0FBQyx1REFBYztBQUM3RTtBQUNBO0FBQ0EsVUFBVSwyREFBa0Isa0JBQWtCLHdEQUFlLENBQUMsdURBQWM7QUFDNUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHNEQUFhO0FBQ2pDLE9BQU8sMERBQWlCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLDREQUFtQjtBQUN6QixzQkFBc0IsNkRBQW9CO0FBQzFDO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGlCQUFpQix5RUFBcUI7QUFDdEM7QUFDQSwrQ0FBK0MsRUFBRTtBQUNqRDtBQUNBO0FBQ0Esb0JBQW9CLHlFQUFxQjtBQUN6QztBQUNBLHNEQUFzRCxFQUFFO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMkJBQTJCLGdEQUFXO0FBQ3RDOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBLFVBQVUsdURBQWM7QUFDeEI7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsdURBQWM7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBb0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyx5REFBZ0I7QUFDcEQ7QUFDQTtBQUNBLFNBQVMsMERBQWlCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyRUFBMkI7QUFDL0I7QUFDQTtBQUNBLElBQUksc0VBQXNCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyw4REFBcUI7QUFDOUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHVCQUF1QixnREFBVztBQUNsQzs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDBCQUEwQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLHVEQUFjLG9EQUFvRCx3REFBZTtBQUNqSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QyxXQUFXO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixpQ0FBaUM7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJFQUEyQjtBQUMvQjtBQUNBO0FBQ0EsSUFBSSxzRUFBc0I7QUFDMUI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixrQkFBa0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBYTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDhEQUFxQjtBQUM5Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHNDQUFzQyxzREFBYTtBQUNuRCxvQ0FBb0Msc0RBQWE7QUFDakQsa0NBQWtDLHNEQUFhO0FBQy9DLDRCQUE0QixzREFBYTtBQUN6QztBQUNBLHlCQUF5Qiw2REFBYTtBQUN0QztBQUNBO0FBQ0EsR0FBRyxFQUFFLHlEQUFvQjtBQUN6QjtBQUNBO0FBQ0EsR0FBRyxFQUFFLHlEQUFvQjtBQUN6QjtBQUNBO0FBQ0EsR0FBRyxFQUFFLHlEQUFvQiwwQkFBMEIsNkRBQXdCLHNDQUFzQyx5REFBb0I7QUFDckk7QUFDQTtBQUNBLFVBQVUsb0RBQVc7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0Esd0JBQXdCLHNEQUFhO0FBQ3JDLFVBQVUsMERBQWlCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUcsZ0NBQWdDLDZDQUFRO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBLG9CQUFvQixtQkFBbUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IsbUJBQW1CO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU2VCIsInNvdXJjZXMiOlsiL1VzZXJzL2hhcnNobHVoYXIvbGFsaXQgZG9uL2Fsb2tpdGEtY2FuZGxlcy9ub2RlX21vZHVsZXMvLnBucG0vQGxleGljYWwrbGlzdEAwLjI4LjAvbm9kZV9tb2R1bGVzL0BsZXhpY2FsL2xpc3QvTGV4aWNhbExpc3QuZGV2Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cbmltcG9ydCB7ICRnZXROZWFyZXN0Tm9kZU9mVHlwZSwgcmVtb3ZlQ2xhc3NOYW1lc0Zyb21FbGVtZW50LCBhZGRDbGFzc05hbWVzVG9FbGVtZW50LCBpc0hUTUxFbGVtZW50LCBtZXJnZVJlZ2lzdGVyIH0gZnJvbSAnQGxleGljYWwvdXRpbHMnO1xuaW1wb3J0IHsgJGdldFNlbGVjdGlvbiwgJGlzUmFuZ2VTZWxlY3Rpb24sICRpc1Jvb3RPclNoYWRvd1Jvb3QsICRjcmVhdGVQYXJhZ3JhcGhOb2RlLCAkaXNFbGVtZW50Tm9kZSwgJGlzTGVhZk5vZGUsICRzZXRQb2ludEZyb21DYXJldCwgJG5vcm1hbGl6ZUNhcmV0LCAkZ2V0Q2hpbGRDYXJldCwgRWxlbWVudE5vZGUsICRpc1BhcmFncmFwaE5vZGUsICRhcHBseU5vZGVSZXBsYWNlbWVudCwgJGNyZWF0ZVRleHROb2RlLCBjcmVhdGVDb21tYW5kLCBDT01NQU5EX1BSSU9SSVRZX0xPVywgSU5TRVJUX1BBUkFHUkFQSF9DT01NQU5ELCAkaXNUZXh0Tm9kZSwgVGV4dE5vZGUgfSBmcm9tICdsZXhpY2FsJztcbmltcG9ydCB7IGdldFN0eWxlT2JqZWN0RnJvbUNTUyB9IGZyb20gJ0BsZXhpY2FsL3NlbGVjdGlvbic7XG5cbi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKi9cblxuLy8gRG8gbm90IHJlcXVpcmUgdGhpcyBtb2R1bGUgZGlyZWN0bHkhIFVzZSBub3JtYWwgYGludmFyaWFudGAgY2FsbHMuXG5cbmZ1bmN0aW9uIGZvcm1hdERldkVycm9yTWVzc2FnZShtZXNzYWdlKSB7XG4gIHRocm93IG5ldyBFcnJvcihtZXNzYWdlKTtcbn1cblxuLyoqXG4gKiBDaGVja3MgdGhlIGRlcHRoIG9mIGxpc3ROb2RlIGZyb20gdGhlIHJvb3Qgbm9kZS5cbiAqIEBwYXJhbSBsaXN0Tm9kZSAtIFRoZSBMaXN0Tm9kZSB0byBiZSBjaGVja2VkLlxuICogQHJldHVybnMgVGhlIGRlcHRoIG9mIHRoZSBMaXN0Tm9kZS5cbiAqL1xuZnVuY3Rpb24gJGdldExpc3REZXB0aChsaXN0Tm9kZSkge1xuICBsZXQgZGVwdGggPSAxO1xuICBsZXQgcGFyZW50ID0gbGlzdE5vZGUuZ2V0UGFyZW50KCk7XG4gIHdoaWxlIChwYXJlbnQgIT0gbnVsbCkge1xuICAgIGlmICgkaXNMaXN0SXRlbU5vZGUocGFyZW50KSkge1xuICAgICAgY29uc3QgcGFyZW50TGlzdCA9IHBhcmVudC5nZXRQYXJlbnQoKTtcbiAgICAgIGlmICgkaXNMaXN0Tm9kZShwYXJlbnRMaXN0KSkge1xuICAgICAgICBkZXB0aCsrO1xuICAgICAgICBwYXJlbnQgPSBwYXJlbnRMaXN0LmdldFBhcmVudCgpO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIHtcbiAgICAgICAgZm9ybWF0RGV2RXJyb3JNZXNzYWdlKGBBIExpc3RJdGVtTm9kZSBtdXN0IGhhdmUgYSBMaXN0Tm9kZSBmb3IgYSBwYXJlbnQuYCk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBkZXB0aDtcbiAgfVxuICByZXR1cm4gZGVwdGg7XG59XG5cbi8qKlxuICogRmluZHMgdGhlIG5lYXJlc3QgYW5jZXN0cmFsIExpc3ROb2RlIGFuZCByZXR1cm5zIGl0LCB0aHJvd3MgYW4gaW52YXJpYW50IGlmIGxpc3RJdGVtIGlzIG5vdCBhIExpc3RJdGVtTm9kZS5cbiAqIEBwYXJhbSBsaXN0SXRlbSAtIFRoZSBub2RlIHRvIGJlIGNoZWNrZWQuXG4gKiBAcmV0dXJucyBUaGUgTGlzdE5vZGUgZm91bmQuXG4gKi9cbmZ1bmN0aW9uICRnZXRUb3BMaXN0Tm9kZShsaXN0SXRlbSkge1xuICBsZXQgbGlzdCA9IGxpc3RJdGVtLmdldFBhcmVudCgpO1xuICBpZiAoISRpc0xpc3ROb2RlKGxpc3QpKSB7XG4gICAge1xuICAgICAgZm9ybWF0RGV2RXJyb3JNZXNzYWdlKGBBIExpc3RJdGVtTm9kZSBtdXN0IGhhdmUgYSBMaXN0Tm9kZSBmb3IgYSBwYXJlbnQuYCk7XG4gICAgfVxuICB9XG4gIGxldCBwYXJlbnQgPSBsaXN0O1xuICB3aGlsZSAocGFyZW50ICE9PSBudWxsKSB7XG4gICAgcGFyZW50ID0gcGFyZW50LmdldFBhcmVudCgpO1xuICAgIGlmICgkaXNMaXN0Tm9kZShwYXJlbnQpKSB7XG4gICAgICBsaXN0ID0gcGFyZW50O1xuICAgIH1cbiAgfVxuICByZXR1cm4gbGlzdDtcbn1cblxuLyoqXG4gKiBBIHJlY3Vyc2l2ZSBEZXB0aC1GaXJzdCBTZWFyY2ggKFBvc3RvcmRlciBUcmF2ZXJzYWwpIHRoYXQgZmluZHMgYWxsIG9mIGEgbm9kZSdzIGNoaWxkcmVuXG4gKiB0aGF0IGFyZSBvZiB0eXBlIExpc3RJdGVtTm9kZSBhbmQgcmV0dXJucyB0aGVtIGluIGFuIGFycmF5LlxuICogQHBhcmFtIG5vZGUgLSBUaGUgTGlzdE5vZGUgdG8gc3RhcnQgdGhlIHNlYXJjaC5cbiAqIEByZXR1cm5zIEFuIGFycmF5IGNvbnRhaW5pbmcgYWxsIG5vZGVzIG9mIHR5cGUgTGlzdEl0ZW1Ob2RlIGZvdW5kLlxuICovXG4vLyBUaGlzIHNob3VsZCBwcm9iYWJseSBiZSAkZ2V0QWxsQ2hpbGRyZW5PZlR5cGVcbmZ1bmN0aW9uICRnZXRBbGxMaXN0SXRlbXMobm9kZSkge1xuICBsZXQgbGlzdEl0ZW1Ob2RlcyA9IFtdO1xuICBjb25zdCBsaXN0Q2hpbGRyZW4gPSBub2RlLmdldENoaWxkcmVuKCkuZmlsdGVyKCRpc0xpc3RJdGVtTm9kZSk7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbGlzdENoaWxkcmVuLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgbGlzdEl0ZW1Ob2RlID0gbGlzdENoaWxkcmVuW2ldO1xuICAgIGNvbnN0IGZpcnN0Q2hpbGQgPSBsaXN0SXRlbU5vZGUuZ2V0Rmlyc3RDaGlsZCgpO1xuICAgIGlmICgkaXNMaXN0Tm9kZShmaXJzdENoaWxkKSkge1xuICAgICAgbGlzdEl0ZW1Ob2RlcyA9IGxpc3RJdGVtTm9kZXMuY29uY2F0KCRnZXRBbGxMaXN0SXRlbXMoZmlyc3RDaGlsZCkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBsaXN0SXRlbU5vZGVzLnB1c2gobGlzdEl0ZW1Ob2RlKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGxpc3RJdGVtTm9kZXM7XG59XG5cbi8qKlxuICogQ2hlY2tzIHRvIHNlZSBpZiB0aGUgcGFzc2VkIG5vZGUgaXMgYSBMaXN0SXRlbU5vZGUgYW5kIGhhcyBhIExpc3ROb2RlIGFzIGEgY2hpbGQuXG4gKiBAcGFyYW0gbm9kZSAtIFRoZSBub2RlIHRvIGJlIGNoZWNrZWQuXG4gKiBAcmV0dXJucyB0cnVlIGlmIHRoZSBub2RlIGlzIGEgTGlzdEl0ZW1Ob2RlIGFuZCBoYXMgYSBMaXN0Tm9kZSBjaGlsZCwgZmFsc2Ugb3RoZXJ3aXNlLlxuICovXG5mdW5jdGlvbiBpc05lc3RlZExpc3ROb2RlKG5vZGUpIHtcbiAgcmV0dXJuICRpc0xpc3RJdGVtTm9kZShub2RlKSAmJiAkaXNMaXN0Tm9kZShub2RlLmdldEZpcnN0Q2hpbGQoKSk7XG59XG5cbi8qKlxuICogVGFrZXMgYSBkZWVwbHkgbmVzdGVkIExpc3ROb2RlIG9yIExpc3RJdGVtTm9kZSBhbmQgdHJhdmVyc2VzIHVwIHRoZSBicmFuY2ggdG8gZGVsZXRlIHRoZSBmaXJzdFxuICogYW5jZXN0cmFsIExpc3ROb2RlICh3aGljaCBjb3VsZCBiZSB0aGUgcm9vdCBMaXN0Tm9kZSkgb3IgTGlzdEl0ZW1Ob2RlIHdpdGggc2libGluZ3MsIGVzc2VudGlhbGx5XG4gKiBicmluZ2luZyB0aGUgZGVlcGx5IG5lc3RlZCBub2RlIHVwIHRoZSBicmFuY2ggb25jZS4gV291bGQgcmVtb3ZlIHN1Ymxpc3QgaWYgaXQgaGFzIHNpYmxpbmdzLlxuICogU2hvdWxkIG5vdCBicmVhayBMaXN0SXRlbSAtPiBMaXN0IC0+IExpc3RJdGVtIGNoYWluIGFzIGVtcHR5IExpc3QvSXRlbU5vZGVzIHNob3VsZCBiZSByZW1vdmVkIG9uIC5yZW1vdmUoKS5cbiAqIEBwYXJhbSBzdWJsaXN0IC0gVGhlIG5lc3RlZCBMaXN0Tm9kZSBvciBMaXN0SXRlbU5vZGUgdG8gYmUgYnJvdWdodCB1cCB0aGUgYnJhbmNoLlxuICovXG5mdW5jdGlvbiAkcmVtb3ZlSGlnaGVzdEVtcHR5TGlzdFBhcmVudChzdWJsaXN0KSB7XG4gIC8vIE5vZGVzIG1heSBiZSByZXBlYXRlZGx5IGluZGVudGVkLCB0byBjcmVhdGUgZGVlcGx5IG5lc3RlZCBsaXN0cyB0aGF0IGVhY2hcbiAgLy8gY29udGFpbiBqdXN0IG9uZSBidWxsZXQuXG4gIC8vIE91ciBnb2FsIGlzIHRvIHJlbW92ZSB0aGVzZSAoZW1wdHkpIGRlZXBseSBuZXN0ZWQgbGlzdHMuIFRoZSBlYXNpZXN0XG4gIC8vIHdheSB0byBkbyB0aGF0IGlzIGNyYXdsIGJhY2sgdXAgdGhlIHRyZWUgdW50aWwgd2UgZmluZCBhIG5vZGUgdGhhdCBoYXMgc2libGluZ3NcbiAgLy8gKGUuZy4gaXMgYWN0dWFsbHkgcGFydCBvZiB0aGUgbGlzdCBjb250ZW50cykgYW5kIGRlbGV0ZSB0aGF0LCBvciBkZWxldGVcbiAgLy8gdGhlIHJvb3Qgb2YgdGhlIGxpc3QgKGlmIG5vIGxpc3Qgbm9kZXMgaGF2ZSBzaWJsaW5ncy4pXG4gIGxldCBlbXB0eUxpc3RQdHIgPSBzdWJsaXN0O1xuICB3aGlsZSAoZW1wdHlMaXN0UHRyLmdldE5leHRTaWJsaW5nKCkgPT0gbnVsbCAmJiBlbXB0eUxpc3RQdHIuZ2V0UHJldmlvdXNTaWJsaW5nKCkgPT0gbnVsbCkge1xuICAgIGNvbnN0IHBhcmVudCA9IGVtcHR5TGlzdFB0ci5nZXRQYXJlbnQoKTtcbiAgICBpZiAocGFyZW50ID09IG51bGwgfHwgISgkaXNMaXN0SXRlbU5vZGUocGFyZW50KSB8fCAkaXNMaXN0Tm9kZShwYXJlbnQpKSkge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICAgIGVtcHR5TGlzdFB0ciA9IHBhcmVudDtcbiAgfVxuICBlbXB0eUxpc3RQdHIucmVtb3ZlKCk7XG59XG5cbi8qKlxuICogV3JhcHMgYSBub2RlIGludG8gYSBMaXN0SXRlbU5vZGUuXG4gKiBAcGFyYW0gbm9kZSAtIFRoZSBub2RlIHRvIGJlIHdyYXBwZWQgaW50byBhIExpc3RJdGVtTm9kZVxuICogQHJldHVybnMgVGhlIExpc3RJdGVtTm9kZSB3aGljaCB0aGUgcGFzc2VkIG5vZGUgaXMgd3JhcHBlZCBpbi5cbiAqL1xuZnVuY3Rpb24gJHdyYXBJbkxpc3RJdGVtKG5vZGUpIHtcbiAgY29uc3QgbGlzdEl0ZW1XcmFwcGVyID0gJGNyZWF0ZUxpc3RJdGVtTm9kZSgpO1xuICByZXR1cm4gbGlzdEl0ZW1XcmFwcGVyLmFwcGVuZChub2RlKTtcbn1cblxuZnVuY3Rpb24gJGlzU2VsZWN0aW5nRW1wdHlMaXN0SXRlbShhbmNob3JOb2RlLCBub2Rlcykge1xuICByZXR1cm4gJGlzTGlzdEl0ZW1Ob2RlKGFuY2hvck5vZGUpICYmIChub2Rlcy5sZW5ndGggPT09IDAgfHwgbm9kZXMubGVuZ3RoID09PSAxICYmIGFuY2hvck5vZGUuaXMobm9kZXNbMF0pICYmIGFuY2hvck5vZGUuZ2V0Q2hpbGRyZW5TaXplKCkgPT09IDApO1xufVxuXG4vKipcbiAqIEluc2VydHMgYSBuZXcgTGlzdE5vZGUuIElmIHRoZSBzZWxlY3Rpb24ncyBhbmNob3Igbm9kZSBpcyBhbiBlbXB0eSBMaXN0SXRlbU5vZGUgYW5kIGlzIGEgY2hpbGQgb2ZcbiAqIHRoZSByb290L3NoYWRvdyByb290LCBpdCB3aWxsIHJlcGxhY2UgdGhlIExpc3RJdGVtTm9kZSB3aXRoIGEgTGlzdE5vZGUgYW5kIHRoZSBvbGQgTGlzdEl0ZW1Ob2RlLlxuICogT3RoZXJ3aXNlIGl0IHdpbGwgcmVwbGFjZSBpdHMgcGFyZW50IHdpdGggYSBuZXcgTGlzdE5vZGUgYW5kIHJlLWluc2VydCB0aGUgTGlzdEl0ZW1Ob2RlIGFuZCBhbnkgcHJldmlvdXMgY2hpbGRyZW4uXG4gKiBJZiB0aGUgc2VsZWN0aW9uJ3MgYW5jaG9yIG5vZGUgaXMgbm90IGFuIGVtcHR5IExpc3RJdGVtTm9kZSwgaXQgd2lsbCBhZGQgYSBuZXcgTGlzdE5vZGUgb3IgbWVyZ2UgYW4gZXhpc3RpbmcgTGlzdE5vZGUsXG4gKiB1bmxlc3MgdGhlIHRoZSBub2RlIGlzIGEgbGVhZiBub2RlLCBpbiB3aGljaCBjYXNlIGl0IHdpbGwgYXR0ZW1wdCB0byBmaW5kIGEgTGlzdE5vZGUgdXAgdGhlIGJyYW5jaCBhbmQgcmVwbGFjZSBpdCB3aXRoXG4gKiBhIG5ldyBMaXN0Tm9kZSwgb3IgY3JlYXRlIGEgbmV3IExpc3ROb2RlIGF0IHRoZSBuZWFyZXN0IHJvb3Qvc2hhZG93IHJvb3QuXG4gKiBAcGFyYW0gbGlzdFR5cGUgLSBUaGUgdHlwZSBvZiBsaXN0LCBcIm51bWJlclwiIHwgXCJidWxsZXRcIiB8IFwiY2hlY2tcIi5cbiAqL1xuZnVuY3Rpb24gJGluc2VydExpc3QobGlzdFR5cGUpIHtcbiAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICBpZiAoc2VsZWN0aW9uICE9PSBudWxsKSB7XG4gICAgbGV0IG5vZGVzID0gc2VsZWN0aW9uLmdldE5vZGVzKCk7XG4gICAgaWYgKCRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIGNvbnN0IGFuY2hvckFuZEZvY3VzID0gc2VsZWN0aW9uLmdldFN0YXJ0RW5kUG9pbnRzKCk7XG4gICAgICBpZiAoIShhbmNob3JBbmRGb2N1cyAhPT0gbnVsbCkpIHtcbiAgICAgICAgZm9ybWF0RGV2RXJyb3JNZXNzYWdlKGBpbnNlcnRMaXN0OiBhbmNob3Igc2hvdWxkIGJlIGRlZmluZWRgKTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IFthbmNob3JdID0gYW5jaG9yQW5kRm9jdXM7XG4gICAgICBjb25zdCBhbmNob3JOb2RlID0gYW5jaG9yLmdldE5vZGUoKTtcbiAgICAgIGNvbnN0IGFuY2hvck5vZGVQYXJlbnQgPSBhbmNob3JOb2RlLmdldFBhcmVudCgpO1xuICAgICAgaWYgKCRpc1Jvb3RPclNoYWRvd1Jvb3QoYW5jaG9yTm9kZSkpIHtcbiAgICAgICAgY29uc3QgZmlyc3RDaGlsZCA9IGFuY2hvck5vZGUuZ2V0Rmlyc3RDaGlsZCgpO1xuICAgICAgICBpZiAoZmlyc3RDaGlsZCkge1xuICAgICAgICAgIG5vZGVzID0gZmlyc3RDaGlsZC5zZWxlY3RTdGFydCgpLmdldE5vZGVzKCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc3QgcGFyYWdyYXBoID0gJGNyZWF0ZVBhcmFncmFwaE5vZGUoKTtcbiAgICAgICAgICBhbmNob3JOb2RlLmFwcGVuZChwYXJhZ3JhcGgpO1xuICAgICAgICAgIG5vZGVzID0gcGFyYWdyYXBoLnNlbGVjdCgpLmdldE5vZGVzKCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoJGlzU2VsZWN0aW5nRW1wdHlMaXN0SXRlbShhbmNob3JOb2RlLCBub2RlcykpIHtcbiAgICAgICAgY29uc3QgbGlzdCA9ICRjcmVhdGVMaXN0Tm9kZShsaXN0VHlwZSk7XG4gICAgICAgIGlmICgkaXNSb290T3JTaGFkb3dSb290KGFuY2hvck5vZGVQYXJlbnQpKSB7XG4gICAgICAgICAgYW5jaG9yTm9kZS5yZXBsYWNlKGxpc3QpO1xuICAgICAgICAgIGNvbnN0IGxpc3RJdGVtID0gJGNyZWF0ZUxpc3RJdGVtTm9kZSgpO1xuICAgICAgICAgIGlmICgkaXNFbGVtZW50Tm9kZShhbmNob3JOb2RlKSkge1xuICAgICAgICAgICAgbGlzdEl0ZW0uc2V0Rm9ybWF0KGFuY2hvck5vZGUuZ2V0Rm9ybWF0VHlwZSgpKTtcbiAgICAgICAgICAgIGxpc3RJdGVtLnNldEluZGVudChhbmNob3JOb2RlLmdldEluZGVudCgpKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgbGlzdC5hcHBlbmQobGlzdEl0ZW0pO1xuICAgICAgICB9IGVsc2UgaWYgKCRpc0xpc3RJdGVtTm9kZShhbmNob3JOb2RlKSkge1xuICAgICAgICAgIGNvbnN0IHBhcmVudCA9IGFuY2hvck5vZGUuZ2V0UGFyZW50T3JUaHJvdygpO1xuICAgICAgICAgIGFwcGVuZChsaXN0LCBwYXJlbnQuZ2V0Q2hpbGRyZW4oKSk7XG4gICAgICAgICAgcGFyZW50LnJlcGxhY2UobGlzdCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH1cbiAgICBjb25zdCBoYW5kbGVkID0gbmV3IFNldCgpO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbm9kZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IG5vZGUgPSBub2Rlc1tpXTtcbiAgICAgIGlmICgkaXNFbGVtZW50Tm9kZShub2RlKSAmJiBub2RlLmlzRW1wdHkoKSAmJiAhJGlzTGlzdEl0ZW1Ob2RlKG5vZGUpICYmICFoYW5kbGVkLmhhcyhub2RlLmdldEtleSgpKSkge1xuICAgICAgICAkY3JlYXRlTGlzdE9yTWVyZ2Uobm9kZSwgbGlzdFR5cGUpO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGlmICgkaXNMZWFmTm9kZShub2RlKSkge1xuICAgICAgICBsZXQgcGFyZW50ID0gbm9kZS5nZXRQYXJlbnQoKTtcbiAgICAgICAgd2hpbGUgKHBhcmVudCAhPSBudWxsKSB7XG4gICAgICAgICAgY29uc3QgcGFyZW50S2V5ID0gcGFyZW50LmdldEtleSgpO1xuICAgICAgICAgIGlmICgkaXNMaXN0Tm9kZShwYXJlbnQpKSB7XG4gICAgICAgICAgICBpZiAoIWhhbmRsZWQuaGFzKHBhcmVudEtleSkpIHtcbiAgICAgICAgICAgICAgY29uc3QgbmV3TGlzdE5vZGUgPSAkY3JlYXRlTGlzdE5vZGUobGlzdFR5cGUpO1xuICAgICAgICAgICAgICBhcHBlbmQobmV3TGlzdE5vZGUsIHBhcmVudC5nZXRDaGlsZHJlbigpKTtcbiAgICAgICAgICAgICAgcGFyZW50LnJlcGxhY2UobmV3TGlzdE5vZGUpO1xuICAgICAgICAgICAgICBoYW5kbGVkLmFkZChwYXJlbnRLZXkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IG5leHRQYXJlbnQgPSBwYXJlbnQuZ2V0UGFyZW50KCk7XG4gICAgICAgICAgICBpZiAoJGlzUm9vdE9yU2hhZG93Um9vdChuZXh0UGFyZW50KSAmJiAhaGFuZGxlZC5oYXMocGFyZW50S2V5KSkge1xuICAgICAgICAgICAgICBoYW5kbGVkLmFkZChwYXJlbnRLZXkpO1xuICAgICAgICAgICAgICAkY3JlYXRlTGlzdE9yTWVyZ2UocGFyZW50LCBsaXN0VHlwZSk7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcGFyZW50ID0gbmV4dFBhcmVudDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uIGFwcGVuZChub2RlLCBub2Rlc1RvQXBwZW5kKSB7XG4gIG5vZGUuc3BsaWNlKG5vZGUuZ2V0Q2hpbGRyZW5TaXplKCksIDAsIG5vZGVzVG9BcHBlbmQpO1xufVxuZnVuY3Rpb24gJGNyZWF0ZUxpc3RPck1lcmdlKG5vZGUsIGxpc3RUeXBlKSB7XG4gIGlmICgkaXNMaXN0Tm9kZShub2RlKSkge1xuICAgIHJldHVybiBub2RlO1xuICB9XG4gIGNvbnN0IHByZXZpb3VzU2libGluZyA9IG5vZGUuZ2V0UHJldmlvdXNTaWJsaW5nKCk7XG4gIGNvbnN0IG5leHRTaWJsaW5nID0gbm9kZS5nZXROZXh0U2libGluZygpO1xuICBjb25zdCBsaXN0SXRlbSA9ICRjcmVhdGVMaXN0SXRlbU5vZGUoKTtcbiAgYXBwZW5kKGxpc3RJdGVtLCBub2RlLmdldENoaWxkcmVuKCkpO1xuICBsZXQgdGFyZ2V0TGlzdDtcbiAgaWYgKCRpc0xpc3ROb2RlKHByZXZpb3VzU2libGluZykgJiYgbGlzdFR5cGUgPT09IHByZXZpb3VzU2libGluZy5nZXRMaXN0VHlwZSgpKSB7XG4gICAgcHJldmlvdXNTaWJsaW5nLmFwcGVuZChsaXN0SXRlbSk7XG4gICAgLy8gaWYgdGhlIHNhbWUgdHlwZSBvZiBsaXN0IGlzIG9uIGJvdGggc2lkZXMsIG1lcmdlIHRoZW0uXG4gICAgaWYgKCRpc0xpc3ROb2RlKG5leHRTaWJsaW5nKSAmJiBsaXN0VHlwZSA9PT0gbmV4dFNpYmxpbmcuZ2V0TGlzdFR5cGUoKSkge1xuICAgICAgYXBwZW5kKHByZXZpb3VzU2libGluZywgbmV4dFNpYmxpbmcuZ2V0Q2hpbGRyZW4oKSk7XG4gICAgICBuZXh0U2libGluZy5yZW1vdmUoKTtcbiAgICB9XG4gICAgdGFyZ2V0TGlzdCA9IHByZXZpb3VzU2libGluZztcbiAgfSBlbHNlIGlmICgkaXNMaXN0Tm9kZShuZXh0U2libGluZykgJiYgbGlzdFR5cGUgPT09IG5leHRTaWJsaW5nLmdldExpc3RUeXBlKCkpIHtcbiAgICBuZXh0U2libGluZy5nZXRGaXJzdENoaWxkT3JUaHJvdygpLmluc2VydEJlZm9yZShsaXN0SXRlbSk7XG4gICAgdGFyZ2V0TGlzdCA9IG5leHRTaWJsaW5nO1xuICB9IGVsc2Uge1xuICAgIGNvbnN0IGxpc3QgPSAkY3JlYXRlTGlzdE5vZGUobGlzdFR5cGUpO1xuICAgIGxpc3QuYXBwZW5kKGxpc3RJdGVtKTtcbiAgICBub2RlLnJlcGxhY2UobGlzdCk7XG4gICAgdGFyZ2V0TGlzdCA9IGxpc3Q7XG4gIH1cbiAgLy8gbGlzdEl0ZW0gbmVlZHMgdG8gYmUgYXR0YWNoZWQgdG8gcm9vdCBwcmlvciB0byBzZXR0aW5nIGluZGVudFxuICBsaXN0SXRlbS5zZXRGb3JtYXQobm9kZS5nZXRGb3JtYXRUeXBlKCkpO1xuICBsaXN0SXRlbS5zZXRJbmRlbnQobm9kZS5nZXRJbmRlbnQoKSk7XG4gIG5vZGUucmVtb3ZlKCk7XG4gIHJldHVybiB0YXJnZXRMaXN0O1xufVxuXG4vKipcbiAqIEEgcmVjdXJzaXZlIGZ1bmN0aW9uIHRoYXQgZ29lcyB0aHJvdWdoIGVhY2ggbGlzdCBhbmQgdGhlaXIgY2hpbGRyZW4sIGluY2x1ZGluZyBuZXN0ZWQgbGlzdHMsXG4gKiBhcHBlbmRpbmcgbGlzdDIgY2hpbGRyZW4gYWZ0ZXIgbGlzdDEgY2hpbGRyZW4gYW5kIHVwZGF0aW5nIExpc3RJdGVtTm9kZSB2YWx1ZXMuXG4gKiBAcGFyYW0gbGlzdDEgLSBUaGUgZmlyc3QgbGlzdCB0byBiZSBtZXJnZWQuXG4gKiBAcGFyYW0gbGlzdDIgLSBUaGUgc2Vjb25kIGxpc3QgdG8gYmUgbWVyZ2VkLlxuICovXG5mdW5jdGlvbiBtZXJnZUxpc3RzKGxpc3QxLCBsaXN0Mikge1xuICBjb25zdCBsaXN0SXRlbTEgPSBsaXN0MS5nZXRMYXN0Q2hpbGQoKTtcbiAgY29uc3QgbGlzdEl0ZW0yID0gbGlzdDIuZ2V0Rmlyc3RDaGlsZCgpO1xuICBpZiAobGlzdEl0ZW0xICYmIGxpc3RJdGVtMiAmJiBpc05lc3RlZExpc3ROb2RlKGxpc3RJdGVtMSkgJiYgaXNOZXN0ZWRMaXN0Tm9kZShsaXN0SXRlbTIpKSB7XG4gICAgbWVyZ2VMaXN0cyhsaXN0SXRlbTEuZ2V0Rmlyc3RDaGlsZCgpLCBsaXN0SXRlbTIuZ2V0Rmlyc3RDaGlsZCgpKTtcbiAgICBsaXN0SXRlbTIucmVtb3ZlKCk7XG4gIH1cbiAgY29uc3QgdG9NZXJnZSA9IGxpc3QyLmdldENoaWxkcmVuKCk7XG4gIGlmICh0b01lcmdlLmxlbmd0aCA+IDApIHtcbiAgICBsaXN0MS5hcHBlbmQoLi4udG9NZXJnZSk7XG4gIH1cbiAgbGlzdDIucmVtb3ZlKCk7XG59XG5cbi8qKlxuICogU2VhcmNoZXMgZm9yIHRoZSBuZWFyZXN0IGFuY2VzdHJhbCBMaXN0Tm9kZSBhbmQgcmVtb3ZlcyBpdC4gSWYgc2VsZWN0aW9uIGlzIGFuIGVtcHR5IExpc3RJdGVtTm9kZVxuICogaXQgd2lsbCByZW1vdmUgdGhlIHdob2xlIGxpc3QsIGluY2x1ZGluZyB0aGUgTGlzdEl0ZW1Ob2RlLiBGb3IgZWFjaCBMaXN0SXRlbU5vZGUgaW4gdGhlIExpc3ROb2RlLFxuICogcmVtb3ZlTGlzdCB3aWxsIGFsc28gZ2VuZXJhdGUgbmV3IFBhcmFncmFwaE5vZGVzIGluIHRoZSByZW1vdmVkIExpc3ROb2RlJ3MgcGxhY2UuIEFueSBjaGlsZCBub2RlXG4gKiBpbnNpZGUgYSBMaXN0SXRlbU5vZGUgd2lsbCBiZSBhcHBlbmRlZCB0byB0aGUgbmV3IFBhcmFncmFwaE5vZGVzLlxuICovXG5mdW5jdGlvbiAkcmVtb3ZlTGlzdCgpIHtcbiAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICBpZiAoJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgIGNvbnN0IGxpc3ROb2RlcyA9IG5ldyBTZXQoKTtcbiAgICBjb25zdCBub2RlcyA9IHNlbGVjdGlvbi5nZXROb2RlcygpO1xuICAgIGNvbnN0IGFuY2hvck5vZGUgPSBzZWxlY3Rpb24uYW5jaG9yLmdldE5vZGUoKTtcbiAgICBpZiAoJGlzU2VsZWN0aW5nRW1wdHlMaXN0SXRlbShhbmNob3JOb2RlLCBub2RlcykpIHtcbiAgICAgIGxpc3ROb2Rlcy5hZGQoJGdldFRvcExpc3ROb2RlKGFuY2hvck5vZGUpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBub2Rlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBub2RlID0gbm9kZXNbaV07XG4gICAgICAgIGlmICgkaXNMZWFmTm9kZShub2RlKSkge1xuICAgICAgICAgIGNvbnN0IGxpc3RJdGVtTm9kZSA9ICRnZXROZWFyZXN0Tm9kZU9mVHlwZShub2RlLCBMaXN0SXRlbU5vZGUpO1xuICAgICAgICAgIGlmIChsaXN0SXRlbU5vZGUgIT0gbnVsbCkge1xuICAgICAgICAgICAgbGlzdE5vZGVzLmFkZCgkZ2V0VG9wTGlzdE5vZGUobGlzdEl0ZW1Ob2RlKSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIGZvciAoY29uc3QgbGlzdE5vZGUgb2YgbGlzdE5vZGVzKSB7XG4gICAgICBsZXQgaW5zZXJ0aW9uUG9pbnQgPSBsaXN0Tm9kZTtcbiAgICAgIGNvbnN0IGxpc3RJdGVtcyA9ICRnZXRBbGxMaXN0SXRlbXMobGlzdE5vZGUpO1xuICAgICAgZm9yIChjb25zdCBsaXN0SXRlbU5vZGUgb2YgbGlzdEl0ZW1zKSB7XG4gICAgICAgIGNvbnN0IHBhcmFncmFwaCA9ICRjcmVhdGVQYXJhZ3JhcGhOb2RlKCkuc2V0VGV4dFN0eWxlKHNlbGVjdGlvbi5zdHlsZSkuc2V0VGV4dEZvcm1hdChzZWxlY3Rpb24uZm9ybWF0KTtcbiAgICAgICAgYXBwZW5kKHBhcmFncmFwaCwgbGlzdEl0ZW1Ob2RlLmdldENoaWxkcmVuKCkpO1xuICAgICAgICBpbnNlcnRpb25Qb2ludC5pbnNlcnRBZnRlcihwYXJhZ3JhcGgpO1xuICAgICAgICBpbnNlcnRpb25Qb2ludCA9IHBhcmFncmFwaDtcblxuICAgICAgICAvLyBXaGVuIHRoZSBhbmNob3IgYW5kIGZvY3VzIGZhbGwgb24gdGhlIHRleHROb2RlXG4gICAgICAgIC8vIHdlIGRvbid0IGhhdmUgdG8gY2hhbmdlIHRoZSBzZWxlY3Rpb24gYmVjYXVzZSB0aGUgdGV4dE5vZGUgd2lsbCBiZSBhcHBlbmRlZCB0b1xuICAgICAgICAvLyB0aGUgbmV3bHkgZ2VuZXJhdGVkIHBhcmFncmFwaC5cbiAgICAgICAgLy8gV2hlbiBzZWxlY3Rpb24gaXMgaW4gZW1wdHkgbmVzdGVkIGxpc3QgaXRlbSwgc2VsZWN0aW9uIGlzIGFjdHVhbGx5IG9uIHRoZSBsaXN0SXRlbU5vZGUuXG4gICAgICAgIC8vIFdoZW4gdGhlIGNvcnJlc3BvbmRpbmcgbGlzdEl0ZW1Ob2RlIGlzIGRlbGV0ZWQgYW5kIHJlcGxhY2VkIGJ5IHRoZSBuZXdseSBnZW5lcmF0ZWQgcGFyYWdyYXBoXG4gICAgICAgIC8vIHdlIHNob3VsZCBtYW51YWxseSBzZXQgdGhlIHNlbGVjdGlvbidzIGZvY3VzIGFuZCBhbmNob3IgdG8gdGhlIG5ld2x5IGdlbmVyYXRlZCBwYXJhZ3JhcGguXG4gICAgICAgIGlmIChsaXN0SXRlbU5vZGUuX19rZXkgPT09IHNlbGVjdGlvbi5hbmNob3Iua2V5KSB7XG4gICAgICAgICAgJHNldFBvaW50RnJvbUNhcmV0KHNlbGVjdGlvbi5hbmNob3IsICRub3JtYWxpemVDYXJldCgkZ2V0Q2hpbGRDYXJldChwYXJhZ3JhcGgsICduZXh0JykpKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAobGlzdEl0ZW1Ob2RlLl9fa2V5ID09PSBzZWxlY3Rpb24uZm9jdXMua2V5KSB7XG4gICAgICAgICAgJHNldFBvaW50RnJvbUNhcmV0KHNlbGVjdGlvbi5mb2N1cywgJG5vcm1hbGl6ZUNhcmV0KCRnZXRDaGlsZENhcmV0KHBhcmFncmFwaCwgJ25leHQnKSkpO1xuICAgICAgICB9XG4gICAgICAgIGxpc3RJdGVtTm9kZS5yZW1vdmUoKTtcbiAgICAgIH1cbiAgICAgIGxpc3ROb2RlLnJlbW92ZSgpO1xuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIFRha2VzIHRoZSB2YWx1ZSBvZiBhIGNoaWxkIExpc3RJdGVtTm9kZSBhbmQgbWFrZXMgaXQgdGhlIHZhbHVlIHRoZSBMaXN0SXRlbU5vZGVcbiAqIHNob3VsZCBiZSBpZiBpdCBpc24ndCBhbHJlYWR5LiBBbHNvIGVuc3VyZXMgdGhhdCBjaGVja2VkIGlzIHVuZGVmaW5lZCBpZiB0aGVcbiAqIHBhcmVudCBkb2VzIG5vdCBoYXZlIGEgbGlzdCB0eXBlIG9mICdjaGVjaycuXG4gKiBAcGFyYW0gbGlzdCAtIFRoZSBsaXN0IHdob3NlIGNoaWxkcmVuIGFyZSB1cGRhdGVkLlxuICovXG5mdW5jdGlvbiB1cGRhdGVDaGlsZHJlbkxpc3RJdGVtVmFsdWUobGlzdCkge1xuICBjb25zdCBpc05vdENoZWNrbGlzdCA9IGxpc3QuZ2V0TGlzdFR5cGUoKSAhPT0gJ2NoZWNrJztcbiAgbGV0IHZhbHVlID0gbGlzdC5nZXRTdGFydCgpO1xuICBmb3IgKGNvbnN0IGNoaWxkIG9mIGxpc3QuZ2V0Q2hpbGRyZW4oKSkge1xuICAgIGlmICgkaXNMaXN0SXRlbU5vZGUoY2hpbGQpKSB7XG4gICAgICBpZiAoY2hpbGQuZ2V0VmFsdWUoKSAhPT0gdmFsdWUpIHtcbiAgICAgICAgY2hpbGQuc2V0VmFsdWUodmFsdWUpO1xuICAgICAgfVxuICAgICAgaWYgKGlzTm90Q2hlY2tsaXN0ICYmIGNoaWxkLmdldExhdGVzdCgpLl9fY2hlY2tlZCAhPSBudWxsKSB7XG4gICAgICAgIGNoaWxkLnNldENoZWNrZWQodW5kZWZpbmVkKTtcbiAgICAgIH1cbiAgICAgIGlmICghJGlzTGlzdE5vZGUoY2hpbGQuZ2V0Rmlyc3RDaGlsZCgpKSkge1xuICAgICAgICB2YWx1ZSsrO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIE1lcmdlIHRoZSBuZXh0IHNpYmxpbmcgbGlzdCBpZiBzYW1lIHR5cGUuXG4gKiA8dWw+IHdpbGwgbWVyZ2Ugd2l0aCA8dWw+LCBidXQgTk9UIDx1bD4gd2l0aCA8b2w+LlxuICogQHBhcmFtIGxpc3QgLSBUaGUgbGlzdCB3aG9zZSBuZXh0IHNpYmxpbmcgc2hvdWxkIGJlIHBvdGVudGlhbGx5IG1lcmdlZFxuICovXG5mdW5jdGlvbiBtZXJnZU5leHRTaWJsaW5nTGlzdElmU2FtZVR5cGUobGlzdCkge1xuICBjb25zdCBuZXh0U2libGluZyA9IGxpc3QuZ2V0TmV4dFNpYmxpbmcoKTtcbiAgaWYgKCRpc0xpc3ROb2RlKG5leHRTaWJsaW5nKSAmJiBsaXN0LmdldExpc3RUeXBlKCkgPT09IG5leHRTaWJsaW5nLmdldExpc3RUeXBlKCkpIHtcbiAgICBtZXJnZUxpc3RzKGxpc3QsIG5leHRTaWJsaW5nKTtcbiAgfVxufVxuXG4vKipcbiAqIEFkZHMgYW4gZW1wdHkgTGlzdE5vZGUvTGlzdEl0ZW1Ob2RlIGNoYWluIGF0IGxpc3RJdGVtTm9kZSwgc28gYXMgdG9cbiAqIGNyZWF0ZSBhbiBpbmRlbnQgZWZmZWN0LiBXb24ndCBpbmRlbnQgTGlzdEl0ZW1Ob2RlcyB0aGF0IGhhdmUgYSBMaXN0Tm9kZSBhc1xuICogYSBjaGlsZCwgYnV0IGRvZXMgbWVyZ2Ugc2libGluZyBMaXN0SXRlbU5vZGVzIGlmIG9uZSBoYXMgYSBuZXN0ZWQgTGlzdE5vZGUuXG4gKiBAcGFyYW0gbGlzdEl0ZW1Ob2RlIC0gVGhlIExpc3RJdGVtTm9kZSB0byBiZSBpbmRlbnRlZC5cbiAqL1xuZnVuY3Rpb24gJGhhbmRsZUluZGVudChsaXN0SXRlbU5vZGUpIHtcbiAgLy8gZ28gdGhyb3VnaCBlYWNoIG5vZGUgYW5kIGRlY2lkZSB3aGVyZSB0byBtb3ZlIGl0LlxuICBjb25zdCByZW1vdmVkID0gbmV3IFNldCgpO1xuICBpZiAoaXNOZXN0ZWRMaXN0Tm9kZShsaXN0SXRlbU5vZGUpIHx8IHJlbW92ZWQuaGFzKGxpc3RJdGVtTm9kZS5nZXRLZXkoKSkpIHtcbiAgICByZXR1cm47XG4gIH1cbiAgY29uc3QgcGFyZW50ID0gbGlzdEl0ZW1Ob2RlLmdldFBhcmVudCgpO1xuXG4gIC8vIFdlIGNhbiBjYXN0IGJvdGggb2YgdGhlIGJlbG93IGBpc05lc3RlZExpc3ROb2RlYCBvbmx5IHJldHVybnMgYSBib29sZWFuIHR5cGUgaW5zdGVhZCBvZiBhIHVzZXItZGVmaW5lZCB0eXBlIGd1YXJkc1xuICBjb25zdCBuZXh0U2libGluZyA9IGxpc3RJdGVtTm9kZS5nZXROZXh0U2libGluZygpO1xuICBjb25zdCBwcmV2aW91c1NpYmxpbmcgPSBsaXN0SXRlbU5vZGUuZ2V0UHJldmlvdXNTaWJsaW5nKCk7XG4gIC8vIGlmIHRoZXJlIGFyZSBuZXN0ZWQgbGlzdHMgb24gZWl0aGVyIHNpZGUsIG1lcmdlIHRoZW0gYWxsIHRvZ2V0aGVyLlxuXG4gIGlmIChpc05lc3RlZExpc3ROb2RlKG5leHRTaWJsaW5nKSAmJiBpc05lc3RlZExpc3ROb2RlKHByZXZpb3VzU2libGluZykpIHtcbiAgICBjb25zdCBpbm5lckxpc3QgPSBwcmV2aW91c1NpYmxpbmcuZ2V0Rmlyc3RDaGlsZCgpO1xuICAgIGlmICgkaXNMaXN0Tm9kZShpbm5lckxpc3QpKSB7XG4gICAgICBpbm5lckxpc3QuYXBwZW5kKGxpc3RJdGVtTm9kZSk7XG4gICAgICBjb25zdCBuZXh0SW5uZXJMaXN0ID0gbmV4dFNpYmxpbmcuZ2V0Rmlyc3RDaGlsZCgpO1xuICAgICAgaWYgKCRpc0xpc3ROb2RlKG5leHRJbm5lckxpc3QpKSB7XG4gICAgICAgIGNvbnN0IGNoaWxkcmVuID0gbmV4dElubmVyTGlzdC5nZXRDaGlsZHJlbigpO1xuICAgICAgICBhcHBlbmQoaW5uZXJMaXN0LCBjaGlsZHJlbik7XG4gICAgICAgIG5leHRTaWJsaW5nLnJlbW92ZSgpO1xuICAgICAgICByZW1vdmVkLmFkZChuZXh0U2libGluZy5nZXRLZXkoKSk7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2UgaWYgKGlzTmVzdGVkTGlzdE5vZGUobmV4dFNpYmxpbmcpKSB7XG4gICAgLy8gaWYgdGhlIExpc3RJdGVtTm9kZSBpcyBuZXh0IHRvIGEgbmVzdGVkIExpc3ROb2RlLCBtZXJnZSB0aGVtXG4gICAgY29uc3QgaW5uZXJMaXN0ID0gbmV4dFNpYmxpbmcuZ2V0Rmlyc3RDaGlsZCgpO1xuICAgIGlmICgkaXNMaXN0Tm9kZShpbm5lckxpc3QpKSB7XG4gICAgICBjb25zdCBmaXJzdENoaWxkID0gaW5uZXJMaXN0LmdldEZpcnN0Q2hpbGQoKTtcbiAgICAgIGlmIChmaXJzdENoaWxkICE9PSBudWxsKSB7XG4gICAgICAgIGZpcnN0Q2hpbGQuaW5zZXJ0QmVmb3JlKGxpc3RJdGVtTm9kZSk7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2UgaWYgKGlzTmVzdGVkTGlzdE5vZGUocHJldmlvdXNTaWJsaW5nKSkge1xuICAgIGNvbnN0IGlubmVyTGlzdCA9IHByZXZpb3VzU2libGluZy5nZXRGaXJzdENoaWxkKCk7XG4gICAgaWYgKCRpc0xpc3ROb2RlKGlubmVyTGlzdCkpIHtcbiAgICAgIGlubmVyTGlzdC5hcHBlbmQobGlzdEl0ZW1Ob2RlKTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgLy8gb3RoZXJ3aXNlLCB3ZSBuZWVkIHRvIGNyZWF0ZSBhIG5ldyBuZXN0ZWQgTGlzdE5vZGVcblxuICAgIGlmICgkaXNMaXN0Tm9kZShwYXJlbnQpKSB7XG4gICAgICBjb25zdCBuZXdMaXN0SXRlbSA9ICRjcmVhdGVMaXN0SXRlbU5vZGUoKS5zZXRUZXh0Rm9ybWF0KHBhcmVudC5nZXRUZXh0Rm9ybWF0KCkpLnNldFRleHRTdHlsZShwYXJlbnQuZ2V0VGV4dFN0eWxlKCkpO1xuICAgICAgY29uc3QgbmV3TGlzdCA9ICRjcmVhdGVMaXN0Tm9kZShwYXJlbnQuZ2V0TGlzdFR5cGUoKSkuc2V0VGV4dEZvcm1hdChwYXJlbnQuZ2V0VGV4dEZvcm1hdCgpKS5zZXRUZXh0U3R5bGUocGFyZW50LmdldFRleHRTdHlsZSgpKTtcbiAgICAgIG5ld0xpc3RJdGVtLmFwcGVuZChuZXdMaXN0KTtcbiAgICAgIG5ld0xpc3QuYXBwZW5kKGxpc3RJdGVtTm9kZSk7XG4gICAgICBpZiAocHJldmlvdXNTaWJsaW5nKSB7XG4gICAgICAgIHByZXZpb3VzU2libGluZy5pbnNlcnRBZnRlcihuZXdMaXN0SXRlbSk7XG4gICAgICB9IGVsc2UgaWYgKG5leHRTaWJsaW5nKSB7XG4gICAgICAgIG5leHRTaWJsaW5nLmluc2VydEJlZm9yZShuZXdMaXN0SXRlbSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBwYXJlbnQuYXBwZW5kKG5ld0xpc3RJdGVtKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBSZW1vdmVzIGFuIGluZGVudCBieSByZW1vdmluZyBhbiBlbXB0eSBMaXN0Tm9kZS9MaXN0SXRlbU5vZGUgY2hhaW4uIEFuIGluZGVudGVkIExpc3RJdGVtTm9kZVxuICogaGFzIGEgZ3JlYXQgZ3JhbmRwYXJlbnQgbm9kZSBvZiB0eXBlIExpc3ROb2RlLCB3aGljaCBpcyB3aGVyZSB0aGUgTGlzdEl0ZW1Ob2RlIHdpbGwgcmVzaWRlXG4gKiB3aXRoaW4gYXMgYSBjaGlsZC5cbiAqIEBwYXJhbSBsaXN0SXRlbU5vZGUgLSBUaGUgTGlzdEl0ZW1Ob2RlIHRvIHJlbW92ZSB0aGUgaW5kZW50IChvdXRkZW50KS5cbiAqL1xuZnVuY3Rpb24gJGhhbmRsZU91dGRlbnQobGlzdEl0ZW1Ob2RlKSB7XG4gIC8vIGdvIHRocm91Z2ggZWFjaCBub2RlIGFuZCBkZWNpZGUgd2hlcmUgdG8gbW92ZSBpdC5cblxuICBpZiAoaXNOZXN0ZWRMaXN0Tm9kZShsaXN0SXRlbU5vZGUpKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGNvbnN0IHBhcmVudExpc3QgPSBsaXN0SXRlbU5vZGUuZ2V0UGFyZW50KCk7XG4gIGNvbnN0IGdyYW5kcGFyZW50TGlzdEl0ZW0gPSBwYXJlbnRMaXN0ID8gcGFyZW50TGlzdC5nZXRQYXJlbnQoKSA6IHVuZGVmaW5lZDtcbiAgY29uc3QgZ3JlYXRHcmFuZHBhcmVudExpc3QgPSBncmFuZHBhcmVudExpc3RJdGVtID8gZ3JhbmRwYXJlbnRMaXN0SXRlbS5nZXRQYXJlbnQoKSA6IHVuZGVmaW5lZDtcbiAgLy8gSWYgaXQgZG9lc24ndCBoYXZlIHRoZXNlIGFuY2VzdG9ycywgaXQncyBub3QgaW5kZW50ZWQuXG5cbiAgaWYgKCRpc0xpc3ROb2RlKGdyZWF0R3JhbmRwYXJlbnRMaXN0KSAmJiAkaXNMaXN0SXRlbU5vZGUoZ3JhbmRwYXJlbnRMaXN0SXRlbSkgJiYgJGlzTGlzdE5vZGUocGFyZW50TGlzdCkpIHtcbiAgICAvLyBpZiBpdCdzIHRoZSBmaXJzdCBjaGlsZCBpbiBpdCdzIHBhcmVudCBsaXN0LCBpbnNlcnQgaXQgaW50byB0aGVcbiAgICAvLyBncmVhdCBncmFuZHBhcmVudCBsaXN0IGJlZm9yZSB0aGUgZ3JhbmRwYXJlbnRcbiAgICBjb25zdCBmaXJzdENoaWxkID0gcGFyZW50TGlzdCA/IHBhcmVudExpc3QuZ2V0Rmlyc3RDaGlsZCgpIDogdW5kZWZpbmVkO1xuICAgIGNvbnN0IGxhc3RDaGlsZCA9IHBhcmVudExpc3QgPyBwYXJlbnRMaXN0LmdldExhc3RDaGlsZCgpIDogdW5kZWZpbmVkO1xuICAgIGlmIChsaXN0SXRlbU5vZGUuaXMoZmlyc3RDaGlsZCkpIHtcbiAgICAgIGdyYW5kcGFyZW50TGlzdEl0ZW0uaW5zZXJ0QmVmb3JlKGxpc3RJdGVtTm9kZSk7XG4gICAgICBpZiAocGFyZW50TGlzdC5pc0VtcHR5KCkpIHtcbiAgICAgICAgZ3JhbmRwYXJlbnRMaXN0SXRlbS5yZW1vdmUoKTtcbiAgICAgIH1cbiAgICAgIC8vIGlmIGl0J3MgdGhlIGxhc3QgY2hpbGQgaW4gaXQncyBwYXJlbnQgbGlzdCwgaW5zZXJ0IGl0IGludG8gdGhlXG4gICAgICAvLyBncmVhdCBncmFuZHBhcmVudCBsaXN0IGFmdGVyIHRoZSBncmFuZHBhcmVudC5cbiAgICB9IGVsc2UgaWYgKGxpc3RJdGVtTm9kZS5pcyhsYXN0Q2hpbGQpKSB7XG4gICAgICBncmFuZHBhcmVudExpc3RJdGVtLmluc2VydEFmdGVyKGxpc3RJdGVtTm9kZSk7XG4gICAgICBpZiAocGFyZW50TGlzdC5pc0VtcHR5KCkpIHtcbiAgICAgICAgZ3JhbmRwYXJlbnRMaXN0SXRlbS5yZW1vdmUoKTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gb3RoZXJ3aXNlLCB3ZSBuZWVkIHRvIHNwbGl0IHRoZSBzaWJsaW5ncyBpbnRvIHR3byBuZXcgbmVzdGVkIGxpc3RzXG4gICAgICBjb25zdCBsaXN0VHlwZSA9IHBhcmVudExpc3QuZ2V0TGlzdFR5cGUoKTtcbiAgICAgIGNvbnN0IHByZXZpb3VzU2libGluZ3NMaXN0SXRlbSA9ICRjcmVhdGVMaXN0SXRlbU5vZGUoKTtcbiAgICAgIGNvbnN0IHByZXZpb3VzU2libGluZ3NMaXN0ID0gJGNyZWF0ZUxpc3ROb2RlKGxpc3RUeXBlKTtcbiAgICAgIHByZXZpb3VzU2libGluZ3NMaXN0SXRlbS5hcHBlbmQocHJldmlvdXNTaWJsaW5nc0xpc3QpO1xuICAgICAgbGlzdEl0ZW1Ob2RlLmdldFByZXZpb3VzU2libGluZ3MoKS5mb3JFYWNoKHNpYmxpbmcgPT4gcHJldmlvdXNTaWJsaW5nc0xpc3QuYXBwZW5kKHNpYmxpbmcpKTtcbiAgICAgIGNvbnN0IG5leHRTaWJsaW5nc0xpc3RJdGVtID0gJGNyZWF0ZUxpc3RJdGVtTm9kZSgpO1xuICAgICAgY29uc3QgbmV4dFNpYmxpbmdzTGlzdCA9ICRjcmVhdGVMaXN0Tm9kZShsaXN0VHlwZSk7XG4gICAgICBuZXh0U2libGluZ3NMaXN0SXRlbS5hcHBlbmQobmV4dFNpYmxpbmdzTGlzdCk7XG4gICAgICBhcHBlbmQobmV4dFNpYmxpbmdzTGlzdCwgbGlzdEl0ZW1Ob2RlLmdldE5leHRTaWJsaW5ncygpKTtcbiAgICAgIC8vIHB1dCB0aGUgc2libGluZyBuZXN0ZWQgbGlzdHMgb24gZWl0aGVyIHNpZGUgb2YgdGhlIGdyYW5kcGFyZW50IGxpc3QgaXRlbSBpbiB0aGUgZ3JlYXQgZ3JhbmRwYXJlbnQuXG4gICAgICBncmFuZHBhcmVudExpc3RJdGVtLmluc2VydEJlZm9yZShwcmV2aW91c1NpYmxpbmdzTGlzdEl0ZW0pO1xuICAgICAgZ3JhbmRwYXJlbnRMaXN0SXRlbS5pbnNlcnRBZnRlcihuZXh0U2libGluZ3NMaXN0SXRlbSk7XG4gICAgICAvLyByZXBsYWNlIHRoZSBncmFuZHBhcmVudCBsaXN0IGl0ZW0gKG5vdyBiZXR3ZWVuIHRoZSBzaWJsaW5ncykgd2l0aCB0aGUgb3V0ZGVudGVkIGxpc3QgaXRlbS5cbiAgICAgIGdyYW5kcGFyZW50TGlzdEl0ZW0ucmVwbGFjZShsaXN0SXRlbU5vZGUpO1xuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIEF0dGVtcHRzIHRvIGluc2VydCBhIFBhcmFncmFwaE5vZGUgYXQgc2VsZWN0aW9uIGFuZCBzZWxlY3RzIHRoZSBuZXcgbm9kZS4gVGhlIHNlbGVjdGlvbiBtdXN0IGNvbnRhaW4gYSBMaXN0SXRlbU5vZGVcbiAqIG9yIGEgbm9kZSB0aGF0IGRvZXMgbm90IGFscmVhZHkgY29udGFpbiB0ZXh0LiBJZiBpdHMgZ3JhbmRwYXJlbnQgaXMgdGhlIHJvb3Qvc2hhZG93IHJvb3QsIGl0IHdpbGwgZ2V0IHRoZSBMaXN0Tm9kZVxuICogKHdoaWNoIHNob3VsZCBiZSB0aGUgcGFyZW50IG5vZGUpIGFuZCBpbnNlcnQgdGhlIFBhcmFncmFwaE5vZGUgYXMgYSBzaWJsaW5nIHRvIHRoZSBMaXN0Tm9kZS4gSWYgdGhlIExpc3ROb2RlIGlzXG4gKiBuZXN0ZWQgaW4gYSBMaXN0SXRlbU5vZGUgaW5zdGVhZCwgaXQgd2lsbCBhZGQgdGhlIFBhcmFncmFwaE5vZGUgYWZ0ZXIgdGhlIGdyYW5kcGFyZW50IExpc3RJdGVtTm9kZS5cbiAqIFRocm93cyBhbiBpbnZhcmlhbnQgaWYgdGhlIHNlbGVjdGlvbiBpcyBub3QgYSBjaGlsZCBvZiBhIExpc3ROb2RlLlxuICogQHJldHVybnMgdHJ1ZSBpZiBhIFBhcmFncmFwaE5vZGUgd2FzIGluc2VydGVkIHN1Y2Nlc2Z1bGx5LCBmYWxzZSBpZiB0aGVyZSBpcyBubyBzZWxlY3Rpb25cbiAqIG9yIHRoZSBzZWxlY3Rpb24gZG9lcyBub3QgY29udGFpbiBhIExpc3RJdGVtTm9kZSBvciB0aGUgbm9kZSBhbHJlYWR5IGhvbGRzIHRleHQuXG4gKi9cbmZ1bmN0aW9uICRoYW5kbGVMaXN0SW5zZXJ0UGFyYWdyYXBoKCkge1xuICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gIGlmICghJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSB8fCAhc2VsZWN0aW9uLmlzQ29sbGFwc2VkKCkpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgLy8gT25seSBydW4gdGhpcyBjb2RlIG9uIGVtcHR5IGxpc3QgaXRlbXNcbiAgY29uc3QgYW5jaG9yID0gc2VsZWN0aW9uLmFuY2hvci5nZXROb2RlKCk7XG4gIGlmICghJGlzTGlzdEl0ZW1Ob2RlKGFuY2hvcikgfHwgYW5jaG9yLmdldENoaWxkcmVuU2l6ZSgpICE9PSAwKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGNvbnN0IHRvcExpc3ROb2RlID0gJGdldFRvcExpc3ROb2RlKGFuY2hvcik7XG4gIGNvbnN0IHBhcmVudCA9IGFuY2hvci5nZXRQYXJlbnQoKTtcbiAgaWYgKCEkaXNMaXN0Tm9kZShwYXJlbnQpKSB7XG4gICAgZm9ybWF0RGV2RXJyb3JNZXNzYWdlKGBBIExpc3RJdGVtTm9kZSBtdXN0IGhhdmUgYSBMaXN0Tm9kZSBmb3IgYSBwYXJlbnQuYCk7XG4gIH1cbiAgY29uc3QgZ3JhbmRwYXJlbnQgPSBwYXJlbnQuZ2V0UGFyZW50KCk7XG4gIGxldCByZXBsYWNlbWVudE5vZGU7XG4gIGlmICgkaXNSb290T3JTaGFkb3dSb290KGdyYW5kcGFyZW50KSkge1xuICAgIHJlcGxhY2VtZW50Tm9kZSA9ICRjcmVhdGVQYXJhZ3JhcGhOb2RlKCk7XG4gICAgdG9wTGlzdE5vZGUuaW5zZXJ0QWZ0ZXIocmVwbGFjZW1lbnROb2RlKTtcbiAgfSBlbHNlIGlmICgkaXNMaXN0SXRlbU5vZGUoZ3JhbmRwYXJlbnQpKSB7XG4gICAgcmVwbGFjZW1lbnROb2RlID0gJGNyZWF0ZUxpc3RJdGVtTm9kZSgpO1xuICAgIGdyYW5kcGFyZW50Lmluc2VydEFmdGVyKHJlcGxhY2VtZW50Tm9kZSk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIHJlcGxhY2VtZW50Tm9kZS5zZXRUZXh0U3R5bGUoc2VsZWN0aW9uLnN0eWxlKS5zZXRUZXh0Rm9ybWF0KHNlbGVjdGlvbi5mb3JtYXQpLnNlbGVjdCgpO1xuICBjb25zdCBuZXh0U2libGluZ3MgPSBhbmNob3IuZ2V0TmV4dFNpYmxpbmdzKCk7XG4gIGlmIChuZXh0U2libGluZ3MubGVuZ3RoID4gMCkge1xuICAgIGNvbnN0IG5ld0xpc3QgPSAkY3JlYXRlTGlzdE5vZGUocGFyZW50LmdldExpc3RUeXBlKCkpO1xuICAgIGlmICgkaXNMaXN0SXRlbU5vZGUocmVwbGFjZW1lbnROb2RlKSkge1xuICAgICAgY29uc3QgbmV3TGlzdEl0ZW0gPSAkY3JlYXRlTGlzdEl0ZW1Ob2RlKCk7XG4gICAgICBuZXdMaXN0SXRlbS5hcHBlbmQobmV3TGlzdCk7XG4gICAgICByZXBsYWNlbWVudE5vZGUuaW5zZXJ0QWZ0ZXIobmV3TGlzdEl0ZW0pO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXBsYWNlbWVudE5vZGUuaW5zZXJ0QWZ0ZXIobmV3TGlzdCk7XG4gICAgfVxuICAgIG5ld0xpc3QuYXBwZW5kKC4uLm5leHRTaWJsaW5ncyk7XG4gIH1cblxuICAvLyBEb24ndCBsZWF2ZSBoYW5naW5nIG5lc3RlZCBlbXB0eSBsaXN0c1xuICAkcmVtb3ZlSGlnaGVzdEVtcHR5TGlzdFBhcmVudChhbmNob3IpO1xuICByZXR1cm4gdHJ1ZTtcbn1cblxuLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqL1xuXG5mdW5jdGlvbiBub3JtYWxpemVDbGFzc05hbWVzKC4uLmNsYXNzTmFtZXMpIHtcbiAgY29uc3QgcnZhbCA9IFtdO1xuICBmb3IgKGNvbnN0IGNsYXNzTmFtZSBvZiBjbGFzc05hbWVzKSB7XG4gICAgaWYgKGNsYXNzTmFtZSAmJiB0eXBlb2YgY2xhc3NOYW1lID09PSAnc3RyaW5nJykge1xuICAgICAgZm9yIChjb25zdCBbc10gb2YgY2xhc3NOYW1lLm1hdGNoQWxsKC9cXFMrL2cpKSB7XG4gICAgICAgIHJ2YWwucHVzaChzKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIHJ2YWw7XG59XG5cbmZ1bmN0aW9uIGFwcGx5TWFya2VyU3R5bGVzKGRvbSwgbm9kZSwgcHJldk5vZGUpIHtcbiAgY29uc3Qgc3R5bGVzID0gZ2V0U3R5bGVPYmplY3RGcm9tQ1NTKG5vZGUuX190ZXh0U3R5bGUpO1xuICBmb3IgKGNvbnN0IGsgaW4gc3R5bGVzKSB7XG4gICAgZG9tLnN0eWxlLnNldFByb3BlcnR5KGAtLWxpc3RpdGVtLW1hcmtlci0ke2t9YCwgc3R5bGVzW2tdKTtcbiAgfVxuICBpZiAocHJldk5vZGUpIHtcbiAgICBmb3IgKGNvbnN0IGsgaW4gZ2V0U3R5bGVPYmplY3RGcm9tQ1NTKHByZXZOb2RlLl9fdGV4dFN0eWxlKSkge1xuICAgICAgaWYgKCEoayBpbiBzdHlsZXMpKSB7XG4gICAgICAgIGRvbS5zdHlsZS5yZW1vdmVQcm9wZXJ0eShgLS1saXN0aXRlbS1tYXJrZXItJHtrfWApO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vKiogQG5vSW5oZXJpdERvYyAqL1xuY2xhc3MgTGlzdEl0ZW1Ob2RlIGV4dGVuZHMgRWxlbWVudE5vZGUge1xuICAvKiogQGludGVybmFsICovXG5cbiAgLyoqIEBpbnRlcm5hbCAqL1xuXG4gIHN0YXRpYyBnZXRUeXBlKCkge1xuICAgIHJldHVybiAnbGlzdGl0ZW0nO1xuICB9XG4gIHN0YXRpYyBjbG9uZShub2RlKSB7XG4gICAgcmV0dXJuIG5ldyBMaXN0SXRlbU5vZGUobm9kZS5fX3ZhbHVlLCBub2RlLl9fY2hlY2tlZCwgbm9kZS5fX2tleSk7XG4gIH1cbiAgY29uc3RydWN0b3IodmFsdWUsIGNoZWNrZWQsIGtleSkge1xuICAgIHN1cGVyKGtleSk7XG4gICAgdGhpcy5fX3ZhbHVlID0gdmFsdWUgPT09IHVuZGVmaW5lZCA/IDEgOiB2YWx1ZTtcbiAgICB0aGlzLl9fY2hlY2tlZCA9IGNoZWNrZWQ7XG4gIH1cbiAgY3JlYXRlRE9NKGNvbmZpZykge1xuICAgIGNvbnN0IGVsZW1lbnQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdsaScpO1xuICAgIGNvbnN0IHBhcmVudCA9IHRoaXMuZ2V0UGFyZW50KCk7XG4gICAgaWYgKCRpc0xpc3ROb2RlKHBhcmVudCkgJiYgcGFyZW50LmdldExpc3RUeXBlKCkgPT09ICdjaGVjaycpIHtcbiAgICAgIHVwZGF0ZUxpc3RJdGVtQ2hlY2tlZChlbGVtZW50LCB0aGlzLCBudWxsKTtcbiAgICB9XG4gICAgZWxlbWVudC52YWx1ZSA9IHRoaXMuX192YWx1ZTtcbiAgICAkc2V0TGlzdEl0ZW1UaGVtZUNsYXNzTmFtZXMoZWxlbWVudCwgY29uZmlnLnRoZW1lLCB0aGlzKTtcbiAgICBjb25zdCBuZXh0U3R5bGUgPSB0aGlzLl9fc3R5bGU7XG4gICAgaWYgKG5leHRTdHlsZSkge1xuICAgICAgZWxlbWVudC5zdHlsZS5jc3NUZXh0ID0gbmV4dFN0eWxlO1xuICAgIH1cbiAgICBhcHBseU1hcmtlclN0eWxlcyhlbGVtZW50LCB0aGlzLCBudWxsKTtcbiAgICByZXR1cm4gZWxlbWVudDtcbiAgfVxuICB1cGRhdGVET00ocHJldk5vZGUsIGRvbSwgY29uZmlnKSB7XG4gICAgY29uc3QgcGFyZW50ID0gdGhpcy5nZXRQYXJlbnQoKTtcbiAgICBpZiAoJGlzTGlzdE5vZGUocGFyZW50KSAmJiBwYXJlbnQuZ2V0TGlzdFR5cGUoKSA9PT0gJ2NoZWNrJykge1xuICAgICAgdXBkYXRlTGlzdEl0ZW1DaGVja2VkKGRvbSwgdGhpcywgcHJldk5vZGUpO1xuICAgIH1cbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIC0gdGhpcyBpcyBhbHdheXMgSFRNTExpc3RJdGVtRWxlbWVudFxuICAgIGRvbS52YWx1ZSA9IHRoaXMuX192YWx1ZTtcbiAgICAkc2V0TGlzdEl0ZW1UaGVtZUNsYXNzTmFtZXMoZG9tLCBjb25maWcudGhlbWUsIHRoaXMpO1xuICAgIGNvbnN0IHByZXZTdHlsZSA9IHByZXZOb2RlLl9fc3R5bGU7XG4gICAgY29uc3QgbmV4dFN0eWxlID0gdGhpcy5fX3N0eWxlO1xuICAgIGlmIChwcmV2U3R5bGUgIT09IG5leHRTdHlsZSkge1xuICAgICAgaWYgKG5leHRTdHlsZSA9PT0gJycpIHtcbiAgICAgICAgZG9tLnJlbW92ZUF0dHJpYnV0ZSgnc3R5bGUnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGRvbS5zdHlsZS5jc3NUZXh0ID0gbmV4dFN0eWxlO1xuICAgICAgfVxuICAgIH1cbiAgICBhcHBseU1hcmtlclN0eWxlcyhkb20sIHRoaXMsIHByZXZOb2RlKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgc3RhdGljIHRyYW5zZm9ybSgpIHtcbiAgICByZXR1cm4gbm9kZSA9PiB7XG4gICAgICBpZiAoISRpc0xpc3RJdGVtTm9kZShub2RlKSkge1xuICAgICAgICBmb3JtYXREZXZFcnJvck1lc3NhZ2UoYG5vZGUgaXMgbm90IGEgTGlzdEl0ZW1Ob2RlYCk7XG4gICAgICB9XG4gICAgICBpZiAobm9kZS5fX2NoZWNrZWQgPT0gbnVsbCkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBjb25zdCBwYXJlbnQgPSBub2RlLmdldFBhcmVudCgpO1xuICAgICAgaWYgKCRpc0xpc3ROb2RlKHBhcmVudCkpIHtcbiAgICAgICAgaWYgKHBhcmVudC5nZXRMaXN0VHlwZSgpICE9PSAnY2hlY2snICYmIG5vZGUuZ2V0Q2hlY2tlZCgpICE9IG51bGwpIHtcbiAgICAgICAgICBub2RlLnNldENoZWNrZWQodW5kZWZpbmVkKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG4gIH1cbiAgc3RhdGljIGltcG9ydERPTSgpIHtcbiAgICByZXR1cm4ge1xuICAgICAgbGk6ICgpID0+ICh7XG4gICAgICAgIGNvbnZlcnNpb246ICRjb252ZXJ0TGlzdEl0ZW1FbGVtZW50LFxuICAgICAgICBwcmlvcml0eTogMFxuICAgICAgfSlcbiAgICB9O1xuICB9XG4gIHN0YXRpYyBpbXBvcnRKU09OKHNlcmlhbGl6ZWROb2RlKSB7XG4gICAgcmV0dXJuICRjcmVhdGVMaXN0SXRlbU5vZGUoKS51cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSk7XG4gIH1cbiAgdXBkYXRlRnJvbUpTT04oc2VyaWFsaXplZE5vZGUpIHtcbiAgICByZXR1cm4gc3VwZXIudXBkYXRlRnJvbUpTT04oc2VyaWFsaXplZE5vZGUpLnNldFZhbHVlKHNlcmlhbGl6ZWROb2RlLnZhbHVlKS5zZXRDaGVja2VkKHNlcmlhbGl6ZWROb2RlLmNoZWNrZWQpO1xuICB9XG4gIGV4cG9ydERPTShlZGl0b3IpIHtcbiAgICBjb25zdCBlbGVtZW50ID0gdGhpcy5jcmVhdGVET00oZWRpdG9yLl9jb25maWcpO1xuICAgIGVsZW1lbnQuc3R5bGUudGV4dEFsaWduID0gdGhpcy5nZXRGb3JtYXRUeXBlKCk7XG4gICAgY29uc3QgZGlyZWN0aW9uID0gdGhpcy5nZXREaXJlY3Rpb24oKTtcbiAgICBpZiAoZGlyZWN0aW9uKSB7XG4gICAgICBlbGVtZW50LmRpciA9IGRpcmVjdGlvbjtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIGVsZW1lbnRcbiAgICB9O1xuICB9XG4gIGV4cG9ydEpTT04oKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLnN1cGVyLmV4cG9ydEpTT04oKSxcbiAgICAgIGNoZWNrZWQ6IHRoaXMuZ2V0Q2hlY2tlZCgpLFxuICAgICAgdmFsdWU6IHRoaXMuZ2V0VmFsdWUoKVxuICAgIH07XG4gIH1cbiAgYXBwZW5kKC4uLm5vZGVzKSB7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBub2Rlcy5sZW5ndGg7IGkrKykge1xuICAgICAgY29uc3Qgbm9kZSA9IG5vZGVzW2ldO1xuICAgICAgaWYgKCRpc0VsZW1lbnROb2RlKG5vZGUpICYmIHRoaXMuY2FuTWVyZ2VXaXRoKG5vZGUpKSB7XG4gICAgICAgIGNvbnN0IGNoaWxkcmVuID0gbm9kZS5nZXRDaGlsZHJlbigpO1xuICAgICAgICB0aGlzLmFwcGVuZCguLi5jaGlsZHJlbik7XG4gICAgICAgIG5vZGUucmVtb3ZlKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzdXBlci5hcHBlbmQobm9kZSk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIHJlcGxhY2UocmVwbGFjZVdpdGhOb2RlLCBpbmNsdWRlQ2hpbGRyZW4pIHtcbiAgICBpZiAoJGlzTGlzdEl0ZW1Ob2RlKHJlcGxhY2VXaXRoTm9kZSkpIHtcbiAgICAgIHJldHVybiBzdXBlci5yZXBsYWNlKHJlcGxhY2VXaXRoTm9kZSk7XG4gICAgfVxuICAgIHRoaXMuc2V0SW5kZW50KDApO1xuICAgIGNvbnN0IGxpc3QgPSB0aGlzLmdldFBhcmVudE9yVGhyb3coKTtcbiAgICBpZiAoISRpc0xpc3ROb2RlKGxpc3QpKSB7XG4gICAgICByZXR1cm4gcmVwbGFjZVdpdGhOb2RlO1xuICAgIH1cbiAgICBpZiAobGlzdC5fX2ZpcnN0ID09PSB0aGlzLmdldEtleSgpKSB7XG4gICAgICBsaXN0Lmluc2VydEJlZm9yZShyZXBsYWNlV2l0aE5vZGUpO1xuICAgIH0gZWxzZSBpZiAobGlzdC5fX2xhc3QgPT09IHRoaXMuZ2V0S2V5KCkpIHtcbiAgICAgIGxpc3QuaW5zZXJ0QWZ0ZXIocmVwbGFjZVdpdGhOb2RlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gU3BsaXQgdGhlIGxpc3RcbiAgICAgIGNvbnN0IG5ld0xpc3QgPSAkY3JlYXRlTGlzdE5vZGUobGlzdC5nZXRMaXN0VHlwZSgpKTtcbiAgICAgIGxldCBuZXh0U2libGluZyA9IHRoaXMuZ2V0TmV4dFNpYmxpbmcoKTtcbiAgICAgIHdoaWxlIChuZXh0U2libGluZykge1xuICAgICAgICBjb25zdCBub2RlVG9BcHBlbmQgPSBuZXh0U2libGluZztcbiAgICAgICAgbmV4dFNpYmxpbmcgPSBuZXh0U2libGluZy5nZXROZXh0U2libGluZygpO1xuICAgICAgICBuZXdMaXN0LmFwcGVuZChub2RlVG9BcHBlbmQpO1xuICAgICAgfVxuICAgICAgbGlzdC5pbnNlcnRBZnRlcihyZXBsYWNlV2l0aE5vZGUpO1xuICAgICAgcmVwbGFjZVdpdGhOb2RlLmluc2VydEFmdGVyKG5ld0xpc3QpO1xuICAgIH1cbiAgICBpZiAoaW5jbHVkZUNoaWxkcmVuKSB7XG4gICAgICBpZiAoISRpc0VsZW1lbnROb2RlKHJlcGxhY2VXaXRoTm9kZSkpIHtcbiAgICAgICAgZm9ybWF0RGV2RXJyb3JNZXNzYWdlKGBpbmNsdWRlQ2hpbGRyZW4gc2hvdWxkIG9ubHkgYmUgdHJ1ZSBmb3IgRWxlbWVudE5vZGVzYCk7XG4gICAgICB9XG4gICAgICB0aGlzLmdldENoaWxkcmVuKCkuZm9yRWFjaChjaGlsZCA9PiB7XG4gICAgICAgIHJlcGxhY2VXaXRoTm9kZS5hcHBlbmQoY2hpbGQpO1xuICAgICAgfSk7XG4gICAgfVxuICAgIHRoaXMucmVtb3ZlKCk7XG4gICAgaWYgKGxpc3QuZ2V0Q2hpbGRyZW5TaXplKCkgPT09IDApIHtcbiAgICAgIGxpc3QucmVtb3ZlKCk7XG4gICAgfVxuICAgIHJldHVybiByZXBsYWNlV2l0aE5vZGU7XG4gIH1cbiAgaW5zZXJ0QWZ0ZXIobm9kZSwgcmVzdG9yZVNlbGVjdGlvbiA9IHRydWUpIHtcbiAgICBjb25zdCBsaXN0Tm9kZSA9IHRoaXMuZ2V0UGFyZW50T3JUaHJvdygpO1xuICAgIGlmICghJGlzTGlzdE5vZGUobGlzdE5vZGUpKSB7XG4gICAgICB7XG4gICAgICAgIGZvcm1hdERldkVycm9yTWVzc2FnZShgaW5zZXJ0QWZ0ZXI6IGxpc3Qgbm9kZSBpcyBub3QgcGFyZW50IG9mIGxpc3QgaXRlbSBub2RlYCk7XG4gICAgICB9XG4gICAgfVxuICAgIGlmICgkaXNMaXN0SXRlbU5vZGUobm9kZSkpIHtcbiAgICAgIHJldHVybiBzdXBlci5pbnNlcnRBZnRlcihub2RlLCByZXN0b3JlU2VsZWN0aW9uKTtcbiAgICB9XG4gICAgY29uc3Qgc2libGluZ3MgPSB0aGlzLmdldE5leHRTaWJsaW5ncygpO1xuXG4gICAgLy8gU3BsaXQgdGhlIGxpc3RzIGFuZCBpbnNlcnQgdGhlIG5vZGUgaW4gYmV0d2VlbiB0aGVtXG4gICAgbGlzdE5vZGUuaW5zZXJ0QWZ0ZXIobm9kZSwgcmVzdG9yZVNlbGVjdGlvbik7XG4gICAgaWYgKHNpYmxpbmdzLmxlbmd0aCAhPT0gMCkge1xuICAgICAgY29uc3QgbmV3TGlzdE5vZGUgPSAkY3JlYXRlTGlzdE5vZGUobGlzdE5vZGUuZ2V0TGlzdFR5cGUoKSk7XG4gICAgICBzaWJsaW5ncy5mb3JFYWNoKHNpYmxpbmcgPT4gbmV3TGlzdE5vZGUuYXBwZW5kKHNpYmxpbmcpKTtcbiAgICAgIG5vZGUuaW5zZXJ0QWZ0ZXIobmV3TGlzdE5vZGUsIHJlc3RvcmVTZWxlY3Rpb24pO1xuICAgIH1cbiAgICByZXR1cm4gbm9kZTtcbiAgfVxuICByZW1vdmUocHJlc2VydmVFbXB0eVBhcmVudCkge1xuICAgIGNvbnN0IHByZXZTaWJsaW5nID0gdGhpcy5nZXRQcmV2aW91c1NpYmxpbmcoKTtcbiAgICBjb25zdCBuZXh0U2libGluZyA9IHRoaXMuZ2V0TmV4dFNpYmxpbmcoKTtcbiAgICBzdXBlci5yZW1vdmUocHJlc2VydmVFbXB0eVBhcmVudCk7XG4gICAgaWYgKHByZXZTaWJsaW5nICYmIG5leHRTaWJsaW5nICYmIGlzTmVzdGVkTGlzdE5vZGUocHJldlNpYmxpbmcpICYmIGlzTmVzdGVkTGlzdE5vZGUobmV4dFNpYmxpbmcpKSB7XG4gICAgICBtZXJnZUxpc3RzKHByZXZTaWJsaW5nLmdldEZpcnN0Q2hpbGQoKSwgbmV4dFNpYmxpbmcuZ2V0Rmlyc3RDaGlsZCgpKTtcbiAgICAgIG5leHRTaWJsaW5nLnJlbW92ZSgpO1xuICAgIH1cbiAgfVxuICBpbnNlcnROZXdBZnRlcihfLCByZXN0b3JlU2VsZWN0aW9uID0gdHJ1ZSkge1xuICAgIGNvbnN0IG5ld0VsZW1lbnQgPSAkY3JlYXRlTGlzdEl0ZW1Ob2RlKCkudXBkYXRlRnJvbUpTT04odGhpcy5leHBvcnRKU09OKCkpLnNldENoZWNrZWQodGhpcy5nZXRDaGVja2VkKCkgPyBmYWxzZSA6IHVuZGVmaW5lZCk7XG4gICAgdGhpcy5pbnNlcnRBZnRlcihuZXdFbGVtZW50LCByZXN0b3JlU2VsZWN0aW9uKTtcbiAgICByZXR1cm4gbmV3RWxlbWVudDtcbiAgfVxuICBjb2xsYXBzZUF0U3RhcnQoc2VsZWN0aW9uKSB7XG4gICAgY29uc3QgcGFyYWdyYXBoID0gJGNyZWF0ZVBhcmFncmFwaE5vZGUoKTtcbiAgICBjb25zdCBjaGlsZHJlbiA9IHRoaXMuZ2V0Q2hpbGRyZW4oKTtcbiAgICBjaGlsZHJlbi5mb3JFYWNoKGNoaWxkID0+IHBhcmFncmFwaC5hcHBlbmQoY2hpbGQpKTtcbiAgICBjb25zdCBsaXN0Tm9kZSA9IHRoaXMuZ2V0UGFyZW50T3JUaHJvdygpO1xuICAgIGNvbnN0IGxpc3ROb2RlUGFyZW50ID0gbGlzdE5vZGUuZ2V0UGFyZW50T3JUaHJvdygpO1xuICAgIGNvbnN0IGlzSW5kZW50ZWQgPSAkaXNMaXN0SXRlbU5vZGUobGlzdE5vZGVQYXJlbnQpO1xuICAgIGlmIChsaXN0Tm9kZS5nZXRDaGlsZHJlblNpemUoKSA9PT0gMSkge1xuICAgICAgaWYgKGlzSW5kZW50ZWQpIHtcbiAgICAgICAgLy8gaWYgdGhlIGxpc3Qgbm9kZSBpcyBuZXN0ZWQsIHdlIGp1c3Qgd2FudCB0byByZW1vdmUgaXQsXG4gICAgICAgIC8vIGVmZmVjdGl2ZWx5IHVuaW5kZW50aW5nIGl0LlxuICAgICAgICBsaXN0Tm9kZS5yZW1vdmUoKTtcbiAgICAgICAgbGlzdE5vZGVQYXJlbnQuc2VsZWN0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBsaXN0Tm9kZS5pbnNlcnRCZWZvcmUocGFyYWdyYXBoKTtcbiAgICAgICAgbGlzdE5vZGUucmVtb3ZlKCk7XG4gICAgICAgIC8vIElmIHdlIGhhdmUgc2VsZWN0aW9uIG9uIHRoZSBsaXN0IGl0ZW0sIHdlJ2xsIG5lZWQgdG8gbW92ZSBpdFxuICAgICAgICAvLyB0byB0aGUgcGFyYWdyYXBoXG4gICAgICAgIGNvbnN0IGFuY2hvciA9IHNlbGVjdGlvbi5hbmNob3I7XG4gICAgICAgIGNvbnN0IGZvY3VzID0gc2VsZWN0aW9uLmZvY3VzO1xuICAgICAgICBjb25zdCBrZXkgPSBwYXJhZ3JhcGguZ2V0S2V5KCk7XG4gICAgICAgIGlmIChhbmNob3IudHlwZSA9PT0gJ2VsZW1lbnQnICYmIGFuY2hvci5nZXROb2RlKCkuaXModGhpcykpIHtcbiAgICAgICAgICBhbmNob3Iuc2V0KGtleSwgYW5jaG9yLm9mZnNldCwgJ2VsZW1lbnQnKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZm9jdXMudHlwZSA9PT0gJ2VsZW1lbnQnICYmIGZvY3VzLmdldE5vZGUoKS5pcyh0aGlzKSkge1xuICAgICAgICAgIGZvY3VzLnNldChrZXksIGZvY3VzLm9mZnNldCwgJ2VsZW1lbnQnKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBsaXN0Tm9kZS5pbnNlcnRCZWZvcmUocGFyYWdyYXBoKTtcbiAgICAgIHRoaXMucmVtb3ZlKCk7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIGdldFZhbHVlKCkge1xuICAgIGNvbnN0IHNlbGYgPSB0aGlzLmdldExhdGVzdCgpO1xuICAgIHJldHVybiBzZWxmLl9fdmFsdWU7XG4gIH1cbiAgc2V0VmFsdWUodmFsdWUpIHtcbiAgICBjb25zdCBzZWxmID0gdGhpcy5nZXRXcml0YWJsZSgpO1xuICAgIHNlbGYuX192YWx1ZSA9IHZhbHVlO1xuICAgIHJldHVybiBzZWxmO1xuICB9XG4gIGdldENoZWNrZWQoKSB7XG4gICAgY29uc3Qgc2VsZiA9IHRoaXMuZ2V0TGF0ZXN0KCk7XG4gICAgbGV0IGxpc3RUeXBlO1xuICAgIGNvbnN0IHBhcmVudCA9IHRoaXMuZ2V0UGFyZW50KCk7XG4gICAgaWYgKCRpc0xpc3ROb2RlKHBhcmVudCkpIHtcbiAgICAgIGxpc3RUeXBlID0gcGFyZW50LmdldExpc3RUeXBlKCk7XG4gICAgfVxuICAgIHJldHVybiBsaXN0VHlwZSA9PT0gJ2NoZWNrJyA/IEJvb2xlYW4oc2VsZi5fX2NoZWNrZWQpIDogdW5kZWZpbmVkO1xuICB9XG4gIHNldENoZWNrZWQoY2hlY2tlZCkge1xuICAgIGNvbnN0IHNlbGYgPSB0aGlzLmdldFdyaXRhYmxlKCk7XG4gICAgc2VsZi5fX2NoZWNrZWQgPSBjaGVja2VkO1xuICAgIHJldHVybiBzZWxmO1xuICB9XG4gIHRvZ2dsZUNoZWNrZWQoKSB7XG4gICAgY29uc3Qgc2VsZiA9IHRoaXMuZ2V0V3JpdGFibGUoKTtcbiAgICByZXR1cm4gc2VsZi5zZXRDaGVja2VkKCFzZWxmLl9fY2hlY2tlZCk7XG4gIH1cbiAgZ2V0SW5kZW50KCkge1xuICAgIC8vIElmIHdlIGRvbid0IGhhdmUgYSBwYXJlbnQsIHdlIGFyZSBsaWtlbHkgc2VyaWFsaXppbmdcbiAgICBjb25zdCBwYXJlbnQgPSB0aGlzLmdldFBhcmVudCgpO1xuICAgIGlmIChwYXJlbnQgPT09IG51bGwgfHwgIXRoaXMuaXNBdHRhY2hlZCgpKSB7XG4gICAgICByZXR1cm4gdGhpcy5nZXRMYXRlc3QoKS5fX2luZGVudDtcbiAgICB9XG4gICAgLy8gTGlzdEl0ZW1Ob2RlIHNob3VsZCBhbHdheXMgaGF2ZSBhIExpc3ROb2RlIGZvciBhIHBhcmVudC5cbiAgICBsZXQgbGlzdE5vZGVQYXJlbnQgPSBwYXJlbnQuZ2V0UGFyZW50T3JUaHJvdygpO1xuICAgIGxldCBpbmRlbnRMZXZlbCA9IDA7XG4gICAgd2hpbGUgKCRpc0xpc3RJdGVtTm9kZShsaXN0Tm9kZVBhcmVudCkpIHtcbiAgICAgIGxpc3ROb2RlUGFyZW50ID0gbGlzdE5vZGVQYXJlbnQuZ2V0UGFyZW50T3JUaHJvdygpLmdldFBhcmVudE9yVGhyb3coKTtcbiAgICAgIGluZGVudExldmVsKys7XG4gICAgfVxuICAgIHJldHVybiBpbmRlbnRMZXZlbDtcbiAgfVxuICBzZXRJbmRlbnQoaW5kZW50KSB7XG4gICAgaWYgKCEodHlwZW9mIGluZGVudCA9PT0gJ251bWJlcicpKSB7XG4gICAgICBmb3JtYXREZXZFcnJvck1lc3NhZ2UoYEludmFsaWQgaW5kZW50IHZhbHVlLmApO1xuICAgIH1cbiAgICBpbmRlbnQgPSBNYXRoLmZsb29yKGluZGVudCk7XG4gICAgaWYgKCEoaW5kZW50ID49IDApKSB7XG4gICAgICBmb3JtYXREZXZFcnJvck1lc3NhZ2UoYEluZGVudCB2YWx1ZSBtdXN0IGJlIG5vbi1uZWdhdGl2ZS5gKTtcbiAgICB9XG4gICAgbGV0IGN1cnJlbnRJbmRlbnQgPSB0aGlzLmdldEluZGVudCgpO1xuICAgIHdoaWxlIChjdXJyZW50SW5kZW50ICE9PSBpbmRlbnQpIHtcbiAgICAgIGlmIChjdXJyZW50SW5kZW50IDwgaW5kZW50KSB7XG4gICAgICAgICRoYW5kbGVJbmRlbnQodGhpcyk7XG4gICAgICAgIGN1cnJlbnRJbmRlbnQrKztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgICRoYW5kbGVPdXRkZW50KHRoaXMpO1xuICAgICAgICBjdXJyZW50SW5kZW50LS07XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0aGlzO1xuICB9XG5cbiAgLyoqIEBkZXByZWNhdGVkIEBpbnRlcm5hbCAqL1xuICBjYW5JbnNlcnRBZnRlcihub2RlKSB7XG4gICAgcmV0dXJuICRpc0xpc3RJdGVtTm9kZShub2RlKTtcbiAgfVxuXG4gIC8qKiBAZGVwcmVjYXRlZCBAaW50ZXJuYWwgKi9cbiAgY2FuUmVwbGFjZVdpdGgocmVwbGFjZW1lbnQpIHtcbiAgICByZXR1cm4gJGlzTGlzdEl0ZW1Ob2RlKHJlcGxhY2VtZW50KTtcbiAgfVxuICBjYW5NZXJnZVdpdGgobm9kZSkge1xuICAgIHJldHVybiAkaXNMaXN0SXRlbU5vZGUobm9kZSkgfHwgJGlzUGFyYWdyYXBoTm9kZShub2RlKTtcbiAgfVxuICBleHRyYWN0V2l0aENoaWxkKGNoaWxkLCBzZWxlY3Rpb24pIHtcbiAgICBpZiAoISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3QgYW5jaG9yTm9kZSA9IHNlbGVjdGlvbi5hbmNob3IuZ2V0Tm9kZSgpO1xuICAgIGNvbnN0IGZvY3VzTm9kZSA9IHNlbGVjdGlvbi5mb2N1cy5nZXROb2RlKCk7XG4gICAgcmV0dXJuIHRoaXMuaXNQYXJlbnRPZihhbmNob3JOb2RlKSAmJiB0aGlzLmlzUGFyZW50T2YoZm9jdXNOb2RlKSAmJiB0aGlzLmdldFRleHRDb250ZW50KCkubGVuZ3RoID09PSBzZWxlY3Rpb24uZ2V0VGV4dENvbnRlbnQoKS5sZW5ndGg7XG4gIH1cbiAgaXNQYXJlbnRSZXF1aXJlZCgpIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuICBjcmVhdGVQYXJlbnRFbGVtZW50Tm9kZSgpIHtcbiAgICByZXR1cm4gJGNyZWF0ZUxpc3ROb2RlKCdidWxsZXQnKTtcbiAgfVxuICBjYW5NZXJnZVdoZW5FbXB0eSgpIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxufVxuZnVuY3Rpb24gJHNldExpc3RJdGVtVGhlbWVDbGFzc05hbWVzKGRvbSwgZWRpdG9yVGhlbWVDbGFzc2VzLCBub2RlKSB7XG4gIGNvbnN0IGNsYXNzZXNUb0FkZCA9IFtdO1xuICBjb25zdCBjbGFzc2VzVG9SZW1vdmUgPSBbXTtcbiAgY29uc3QgbGlzdFRoZW1lID0gZWRpdG9yVGhlbWVDbGFzc2VzLmxpc3Q7XG4gIGNvbnN0IGxpc3RJdGVtQ2xhc3NOYW1lID0gbGlzdFRoZW1lID8gbGlzdFRoZW1lLmxpc3RpdGVtIDogdW5kZWZpbmVkO1xuICBsZXQgbmVzdGVkTGlzdEl0ZW1DbGFzc05hbWU7XG4gIGlmIChsaXN0VGhlbWUgJiYgbGlzdFRoZW1lLm5lc3RlZCkge1xuICAgIG5lc3RlZExpc3RJdGVtQ2xhc3NOYW1lID0gbGlzdFRoZW1lLm5lc3RlZC5saXN0aXRlbTtcbiAgfVxuICBpZiAobGlzdEl0ZW1DbGFzc05hbWUgIT09IHVuZGVmaW5lZCkge1xuICAgIGNsYXNzZXNUb0FkZC5wdXNoKC4uLm5vcm1hbGl6ZUNsYXNzTmFtZXMobGlzdEl0ZW1DbGFzc05hbWUpKTtcbiAgfVxuICBpZiAobGlzdFRoZW1lKSB7XG4gICAgY29uc3QgcGFyZW50Tm9kZSA9IG5vZGUuZ2V0UGFyZW50KCk7XG4gICAgY29uc3QgaXNDaGVja0xpc3QgPSAkaXNMaXN0Tm9kZShwYXJlbnROb2RlKSAmJiBwYXJlbnROb2RlLmdldExpc3RUeXBlKCkgPT09ICdjaGVjayc7XG4gICAgY29uc3QgY2hlY2tlZCA9IG5vZGUuZ2V0Q2hlY2tlZCgpO1xuICAgIGlmICghaXNDaGVja0xpc3QgfHwgY2hlY2tlZCkge1xuICAgICAgY2xhc3Nlc1RvUmVtb3ZlLnB1c2gobGlzdFRoZW1lLmxpc3RpdGVtVW5jaGVja2VkKTtcbiAgICB9XG4gICAgaWYgKCFpc0NoZWNrTGlzdCB8fCAhY2hlY2tlZCkge1xuICAgICAgY2xhc3Nlc1RvUmVtb3ZlLnB1c2gobGlzdFRoZW1lLmxpc3RpdGVtQ2hlY2tlZCk7XG4gICAgfVxuICAgIGlmIChpc0NoZWNrTGlzdCkge1xuICAgICAgY2xhc3Nlc1RvQWRkLnB1c2goY2hlY2tlZCA/IGxpc3RUaGVtZS5saXN0aXRlbUNoZWNrZWQgOiBsaXN0VGhlbWUubGlzdGl0ZW1VbmNoZWNrZWQpO1xuICAgIH1cbiAgfVxuICBpZiAobmVzdGVkTGlzdEl0ZW1DbGFzc05hbWUgIT09IHVuZGVmaW5lZCkge1xuICAgIGNvbnN0IG5lc3RlZExpc3RJdGVtQ2xhc3NlcyA9IG5vcm1hbGl6ZUNsYXNzTmFtZXMobmVzdGVkTGlzdEl0ZW1DbGFzc05hbWUpO1xuICAgIGlmIChub2RlLmdldENoaWxkcmVuKCkuc29tZShjaGlsZCA9PiAkaXNMaXN0Tm9kZShjaGlsZCkpKSB7XG4gICAgICBjbGFzc2VzVG9BZGQucHVzaCguLi5uZXN0ZWRMaXN0SXRlbUNsYXNzZXMpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjbGFzc2VzVG9SZW1vdmUucHVzaCguLi5uZXN0ZWRMaXN0SXRlbUNsYXNzZXMpO1xuICAgIH1cbiAgfVxuICBpZiAoY2xhc3Nlc1RvUmVtb3ZlLmxlbmd0aCA+IDApIHtcbiAgICByZW1vdmVDbGFzc05hbWVzRnJvbUVsZW1lbnQoZG9tLCAuLi5jbGFzc2VzVG9SZW1vdmUpO1xuICB9XG4gIGlmIChjbGFzc2VzVG9BZGQubGVuZ3RoID4gMCkge1xuICAgIGFkZENsYXNzTmFtZXNUb0VsZW1lbnQoZG9tLCAuLi5jbGFzc2VzVG9BZGQpO1xuICB9XG59XG5mdW5jdGlvbiB1cGRhdGVMaXN0SXRlbUNoZWNrZWQoZG9tLCBsaXN0SXRlbU5vZGUsIHByZXZMaXN0SXRlbU5vZGUsIGxpc3ROb2RlKSB7XG4gIC8vIE9ubHkgYWRkIGF0dHJpYnV0ZXMgZm9yIGxlYWYgbGlzdCBpdGVtc1xuICBpZiAoJGlzTGlzdE5vZGUobGlzdEl0ZW1Ob2RlLmdldEZpcnN0Q2hpbGQoKSkpIHtcbiAgICBkb20ucmVtb3ZlQXR0cmlidXRlKCdyb2xlJyk7XG4gICAgZG9tLnJlbW92ZUF0dHJpYnV0ZSgndGFiSW5kZXgnKTtcbiAgICBkb20ucmVtb3ZlQXR0cmlidXRlKCdhcmlhLWNoZWNrZWQnKTtcbiAgfSBlbHNlIHtcbiAgICBkb20uc2V0QXR0cmlidXRlKCdyb2xlJywgJ2NoZWNrYm94Jyk7XG4gICAgZG9tLnNldEF0dHJpYnV0ZSgndGFiSW5kZXgnLCAnLTEnKTtcbiAgICBpZiAoIXByZXZMaXN0SXRlbU5vZGUgfHwgbGlzdEl0ZW1Ob2RlLl9fY2hlY2tlZCAhPT0gcHJldkxpc3RJdGVtTm9kZS5fX2NoZWNrZWQpIHtcbiAgICAgIGRvbS5zZXRBdHRyaWJ1dGUoJ2FyaWEtY2hlY2tlZCcsIGxpc3RJdGVtTm9kZS5nZXRDaGVja2VkKCkgPyAndHJ1ZScgOiAnZmFsc2UnKTtcbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uICRjb252ZXJ0TGlzdEl0ZW1FbGVtZW50KGRvbU5vZGUpIHtcbiAgY29uc3QgaXNHaXRIdWJDaGVja0xpc3QgPSBkb21Ob2RlLmNsYXNzTGlzdC5jb250YWlucygndGFzay1saXN0LWl0ZW0nKTtcbiAgaWYgKGlzR2l0SHViQ2hlY2tMaXN0KSB7XG4gICAgZm9yIChjb25zdCBjaGlsZCBvZiBkb21Ob2RlLmNoaWxkcmVuKSB7XG4gICAgICBpZiAoY2hpbGQudGFnTmFtZSA9PT0gJ0lOUFVUJykge1xuICAgICAgICByZXR1cm4gJGNvbnZlcnRDaGVja2JveElucHV0KGNoaWxkKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgY29uc3QgYXJpYUNoZWNrZWRBdHRyID0gZG9tTm9kZS5nZXRBdHRyaWJ1dGUoJ2FyaWEtY2hlY2tlZCcpO1xuICBjb25zdCBjaGVja2VkID0gYXJpYUNoZWNrZWRBdHRyID09PSAndHJ1ZScgPyB0cnVlIDogYXJpYUNoZWNrZWRBdHRyID09PSAnZmFsc2UnID8gZmFsc2UgOiB1bmRlZmluZWQ7XG4gIHJldHVybiB7XG4gICAgbm9kZTogJGNyZWF0ZUxpc3RJdGVtTm9kZShjaGVja2VkKVxuICB9O1xufVxuZnVuY3Rpb24gJGNvbnZlcnRDaGVja2JveElucHV0KGRvbU5vZGUpIHtcbiAgY29uc3QgaXNDaGVja2JveElucHV0ID0gZG9tTm9kZS5nZXRBdHRyaWJ1dGUoJ3R5cGUnKSA9PT0gJ2NoZWNrYm94JztcbiAgaWYgKCFpc0NoZWNrYm94SW5wdXQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgbm9kZTogbnVsbFxuICAgIH07XG4gIH1cbiAgY29uc3QgY2hlY2tlZCA9IGRvbU5vZGUuaGFzQXR0cmlidXRlKCdjaGVja2VkJyk7XG4gIHJldHVybiB7XG4gICAgbm9kZTogJGNyZWF0ZUxpc3RJdGVtTm9kZShjaGVja2VkKVxuICB9O1xufVxuXG4vKipcbiAqIENyZWF0ZXMgYSBuZXcgTGlzdCBJdGVtIG5vZGUsIHBhc3NpbmcgdHJ1ZS9mYWxzZSB3aWxsIGNvbnZlcnQgaXQgdG8gYSBjaGVja2JveCBpbnB1dC5cbiAqIEBwYXJhbSBjaGVja2VkIC0gSXMgdGhlIExpc3QgSXRlbSBhIGNoZWNrYm94IGFuZCwgaWYgc28sIGlzIGl0IGNoZWNrZWQ/IHVuZGVmaW5lZC9udWxsOiBub3QgYSBjaGVja2JveCwgdHJ1ZS9mYWxzZSBpcyBhIGNoZWNrYm94IGFuZCBjaGVja2VkL3VuY2hlY2tlZCwgcmVzcGVjdGl2ZWx5LlxuICogQHJldHVybnMgVGhlIG5ldyBMaXN0IEl0ZW0uXG4gKi9cbmZ1bmN0aW9uICRjcmVhdGVMaXN0SXRlbU5vZGUoY2hlY2tlZCkge1xuICByZXR1cm4gJGFwcGx5Tm9kZVJlcGxhY2VtZW50KG5ldyBMaXN0SXRlbU5vZGUodW5kZWZpbmVkLCBjaGVja2VkKSk7XG59XG5cbi8qKlxuICogQ2hlY2tzIHRvIHNlZSBpZiB0aGUgbm9kZSBpcyBhIExpc3RJdGVtTm9kZS5cbiAqIEBwYXJhbSBub2RlIC0gVGhlIG5vZGUgdG8gYmUgY2hlY2tlZC5cbiAqIEByZXR1cm5zIHRydWUgaWYgdGhlIG5vZGUgaXMgYSBMaXN0SXRlbU5vZGUsIGZhbHNlIG90aGVyd2lzZS5cbiAqL1xuZnVuY3Rpb24gJGlzTGlzdEl0ZW1Ob2RlKG5vZGUpIHtcbiAgcmV0dXJuIG5vZGUgaW5zdGFuY2VvZiBMaXN0SXRlbU5vZGU7XG59XG5cbi8qKiBAbm9Jbmhlcml0RG9jICovXG5jbGFzcyBMaXN0Tm9kZSBleHRlbmRzIEVsZW1lbnROb2RlIHtcbiAgLyoqIEBpbnRlcm5hbCAqL1xuXG4gIC8qKiBAaW50ZXJuYWwgKi9cblxuICAvKiogQGludGVybmFsICovXG5cbiAgc3RhdGljIGdldFR5cGUoKSB7XG4gICAgcmV0dXJuICdsaXN0JztcbiAgfVxuICBzdGF0aWMgY2xvbmUobm9kZSkge1xuICAgIGNvbnN0IGxpc3RUeXBlID0gbm9kZS5fX2xpc3RUeXBlIHx8IFRBR19UT19MSVNUX1RZUEVbbm9kZS5fX3RhZ107XG4gICAgcmV0dXJuIG5ldyBMaXN0Tm9kZShsaXN0VHlwZSwgbm9kZS5fX3N0YXJ0LCBub2RlLl9fa2V5KTtcbiAgfVxuICBjb25zdHJ1Y3RvcihsaXN0VHlwZSA9ICdudW1iZXInLCBzdGFydCA9IDEsIGtleSkge1xuICAgIHN1cGVyKGtleSk7XG4gICAgY29uc3QgX2xpc3RUeXBlID0gVEFHX1RPX0xJU1RfVFlQRVtsaXN0VHlwZV0gfHwgbGlzdFR5cGU7XG4gICAgdGhpcy5fX2xpc3RUeXBlID0gX2xpc3RUeXBlO1xuICAgIHRoaXMuX190YWcgPSBfbGlzdFR5cGUgPT09ICdudW1iZXInID8gJ29sJyA6ICd1bCc7XG4gICAgdGhpcy5fX3N0YXJ0ID0gc3RhcnQ7XG4gIH1cbiAgZ2V0VGFnKCkge1xuICAgIHJldHVybiB0aGlzLl9fdGFnO1xuICB9XG4gIHNldExpc3RUeXBlKHR5cGUpIHtcbiAgICBjb25zdCB3cml0YWJsZSA9IHRoaXMuZ2V0V3JpdGFibGUoKTtcbiAgICB3cml0YWJsZS5fX2xpc3RUeXBlID0gdHlwZTtcbiAgICB3cml0YWJsZS5fX3RhZyA9IHR5cGUgPT09ICdudW1iZXInID8gJ29sJyA6ICd1bCc7XG4gICAgcmV0dXJuIHdyaXRhYmxlO1xuICB9XG4gIGdldExpc3RUeXBlKCkge1xuICAgIHJldHVybiB0aGlzLl9fbGlzdFR5cGU7XG4gIH1cbiAgZ2V0U3RhcnQoKSB7XG4gICAgcmV0dXJuIHRoaXMuX19zdGFydDtcbiAgfVxuICBzZXRTdGFydChzdGFydCkge1xuICAgIGNvbnN0IHNlbGYgPSB0aGlzLmdldFdyaXRhYmxlKCk7XG4gICAgc2VsZi5fX3N0YXJ0ID0gc3RhcnQ7XG4gICAgcmV0dXJuIHNlbGY7XG4gIH1cblxuICAvLyBWaWV3XG5cbiAgY3JlYXRlRE9NKGNvbmZpZywgX2VkaXRvcikge1xuICAgIGNvbnN0IHRhZyA9IHRoaXMuX190YWc7XG4gICAgY29uc3QgZG9tID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCh0YWcpO1xuICAgIGlmICh0aGlzLl9fc3RhcnQgIT09IDEpIHtcbiAgICAgIGRvbS5zZXRBdHRyaWJ1dGUoJ3N0YXJ0JywgU3RyaW5nKHRoaXMuX19zdGFydCkpO1xuICAgIH1cbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIEludGVybmFsIGZpZWxkLlxuICAgIGRvbS5fX2xleGljYWxMaXN0VHlwZSA9IHRoaXMuX19saXN0VHlwZTtcbiAgICAkc2V0TGlzdFRoZW1lQ2xhc3NOYW1lcyhkb20sIGNvbmZpZy50aGVtZSwgdGhpcyk7XG4gICAgcmV0dXJuIGRvbTtcbiAgfVxuICB1cGRhdGVET00ocHJldk5vZGUsIGRvbSwgY29uZmlnKSB7XG4gICAgaWYgKHByZXZOb2RlLl9fdGFnICE9PSB0aGlzLl9fdGFnKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgJHNldExpc3RUaGVtZUNsYXNzTmFtZXMoZG9tLCBjb25maWcudGhlbWUsIHRoaXMpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBzdGF0aWMgdHJhbnNmb3JtKCkge1xuICAgIHJldHVybiBub2RlID0+IHtcbiAgICAgIGlmICghJGlzTGlzdE5vZGUobm9kZSkpIHtcbiAgICAgICAgZm9ybWF0RGV2RXJyb3JNZXNzYWdlKGBub2RlIGlzIG5vdCBhIExpc3ROb2RlYCk7XG4gICAgICB9XG4gICAgICBtZXJnZU5leHRTaWJsaW5nTGlzdElmU2FtZVR5cGUobm9kZSk7XG4gICAgICB1cGRhdGVDaGlsZHJlbkxpc3RJdGVtVmFsdWUobm9kZSk7XG4gICAgfTtcbiAgfVxuICBzdGF0aWMgaW1wb3J0RE9NKCkge1xuICAgIHJldHVybiB7XG4gICAgICBvbDogKCkgPT4gKHtcbiAgICAgICAgY29udmVyc2lvbjogJGNvbnZlcnRMaXN0Tm9kZSxcbiAgICAgICAgcHJpb3JpdHk6IDBcbiAgICAgIH0pLFxuICAgICAgdWw6ICgpID0+ICh7XG4gICAgICAgIGNvbnZlcnNpb246ICRjb252ZXJ0TGlzdE5vZGUsXG4gICAgICAgIHByaW9yaXR5OiAwXG4gICAgICB9KVxuICAgIH07XG4gIH1cbiAgc3RhdGljIGltcG9ydEpTT04oc2VyaWFsaXplZE5vZGUpIHtcbiAgICByZXR1cm4gJGNyZWF0ZUxpc3ROb2RlKCkudXBkYXRlRnJvbUpTT04oc2VyaWFsaXplZE5vZGUpO1xuICB9XG4gIHVwZGF0ZUZyb21KU09OKHNlcmlhbGl6ZWROb2RlKSB7XG4gICAgcmV0dXJuIHN1cGVyLnVwZGF0ZUZyb21KU09OKHNlcmlhbGl6ZWROb2RlKS5zZXRMaXN0VHlwZShzZXJpYWxpemVkTm9kZS5saXN0VHlwZSkuc2V0U3RhcnQoc2VyaWFsaXplZE5vZGUuc3RhcnQpO1xuICB9XG4gIGV4cG9ydERPTShlZGl0b3IpIHtcbiAgICBjb25zdCBlbGVtZW50ID0gdGhpcy5jcmVhdGVET00oZWRpdG9yLl9jb25maWcsIGVkaXRvcik7XG4gICAgaWYgKGlzSFRNTEVsZW1lbnQoZWxlbWVudCkpIHtcbiAgICAgIGlmICh0aGlzLl9fc3RhcnQgIT09IDEpIHtcbiAgICAgICAgZWxlbWVudC5zZXRBdHRyaWJ1dGUoJ3N0YXJ0JywgU3RyaW5nKHRoaXMuX19zdGFydCkpO1xuICAgICAgfVxuICAgICAgaWYgKHRoaXMuX19saXN0VHlwZSA9PT0gJ2NoZWNrJykge1xuICAgICAgICBlbGVtZW50LnNldEF0dHJpYnV0ZSgnX19sZXhpY2FsTGlzdFR5cGUnLCAnY2hlY2snKTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIGVsZW1lbnRcbiAgICB9O1xuICB9XG4gIGV4cG9ydEpTT04oKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLnN1cGVyLmV4cG9ydEpTT04oKSxcbiAgICAgIGxpc3RUeXBlOiB0aGlzLmdldExpc3RUeXBlKCksXG4gICAgICBzdGFydDogdGhpcy5nZXRTdGFydCgpLFxuICAgICAgdGFnOiB0aGlzLmdldFRhZygpXG4gICAgfTtcbiAgfVxuICBjYW5CZUVtcHR5KCkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBjYW5JbmRlbnQoKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIHNwbGljZShzdGFydCwgZGVsZXRlQ291bnQsIG5vZGVzVG9JbnNlcnQpIHtcbiAgICBsZXQgbGlzdEl0ZW1Ob2Rlc1RvSW5zZXJ0ID0gbm9kZXNUb0luc2VydDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IG5vZGVzVG9JbnNlcnQubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IG5vZGUgPSBub2Rlc1RvSW5zZXJ0W2ldO1xuICAgICAgaWYgKCEkaXNMaXN0SXRlbU5vZGUobm9kZSkpIHtcbiAgICAgICAgaWYgKGxpc3RJdGVtTm9kZXNUb0luc2VydCA9PT0gbm9kZXNUb0luc2VydCkge1xuICAgICAgICAgIGxpc3RJdGVtTm9kZXNUb0luc2VydCA9IFsuLi5ub2Rlc1RvSW5zZXJ0XTtcbiAgICAgICAgfVxuICAgICAgICBsaXN0SXRlbU5vZGVzVG9JbnNlcnRbaV0gPSAkY3JlYXRlTGlzdEl0ZW1Ob2RlKCkuYXBwZW5kKCRpc0VsZW1lbnROb2RlKG5vZGUpICYmICEoJGlzTGlzdE5vZGUobm9kZSkgfHwgbm9kZS5pc0lubGluZSgpKSA/ICRjcmVhdGVUZXh0Tm9kZShub2RlLmdldFRleHRDb250ZW50KCkpIDogbm9kZSk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBzdXBlci5zcGxpY2Uoc3RhcnQsIGRlbGV0ZUNvdW50LCBsaXN0SXRlbU5vZGVzVG9JbnNlcnQpO1xuICB9XG4gIGV4dHJhY3RXaXRoQ2hpbGQoY2hpbGQpIHtcbiAgICByZXR1cm4gJGlzTGlzdEl0ZW1Ob2RlKGNoaWxkKTtcbiAgfVxufVxuZnVuY3Rpb24gJHNldExpc3RUaGVtZUNsYXNzTmFtZXMoZG9tLCBlZGl0b3JUaGVtZUNsYXNzZXMsIG5vZGUpIHtcbiAgY29uc3QgY2xhc3Nlc1RvQWRkID0gW107XG4gIGNvbnN0IGNsYXNzZXNUb1JlbW92ZSA9IFtdO1xuICBjb25zdCBsaXN0VGhlbWUgPSBlZGl0b3JUaGVtZUNsYXNzZXMubGlzdDtcbiAgaWYgKGxpc3RUaGVtZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgY29uc3QgbGlzdExldmVsc0NsYXNzTmFtZXMgPSBsaXN0VGhlbWVbYCR7bm9kZS5fX3RhZ31EZXB0aGBdIHx8IFtdO1xuICAgIGNvbnN0IGxpc3REZXB0aCA9ICRnZXRMaXN0RGVwdGgobm9kZSkgLSAxO1xuICAgIGNvbnN0IG5vcm1hbGl6ZWRMaXN0RGVwdGggPSBsaXN0RGVwdGggJSBsaXN0TGV2ZWxzQ2xhc3NOYW1lcy5sZW5ndGg7XG4gICAgY29uc3QgbGlzdExldmVsQ2xhc3NOYW1lID0gbGlzdExldmVsc0NsYXNzTmFtZXNbbm9ybWFsaXplZExpc3REZXB0aF07XG4gICAgY29uc3QgbGlzdENsYXNzTmFtZSA9IGxpc3RUaGVtZVtub2RlLl9fdGFnXTtcbiAgICBsZXQgbmVzdGVkTGlzdENsYXNzTmFtZTtcbiAgICBjb25zdCBuZXN0ZWRMaXN0VGhlbWUgPSBsaXN0VGhlbWUubmVzdGVkO1xuICAgIGNvbnN0IGNoZWNrbGlzdENsYXNzTmFtZSA9IGxpc3RUaGVtZS5jaGVja2xpc3Q7XG4gICAgaWYgKG5lc3RlZExpc3RUaGVtZSAhPT0gdW5kZWZpbmVkICYmIG5lc3RlZExpc3RUaGVtZS5saXN0KSB7XG4gICAgICBuZXN0ZWRMaXN0Q2xhc3NOYW1lID0gbmVzdGVkTGlzdFRoZW1lLmxpc3Q7XG4gICAgfVxuICAgIGlmIChsaXN0Q2xhc3NOYW1lICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGNsYXNzZXNUb0FkZC5wdXNoKGxpc3RDbGFzc05hbWUpO1xuICAgIH1cbiAgICBpZiAoY2hlY2tsaXN0Q2xhc3NOYW1lICE9PSB1bmRlZmluZWQgJiYgbm9kZS5fX2xpc3RUeXBlID09PSAnY2hlY2snKSB7XG4gICAgICBjbGFzc2VzVG9BZGQucHVzaChjaGVja2xpc3RDbGFzc05hbWUpO1xuICAgIH1cbiAgICBpZiAobGlzdExldmVsQ2xhc3NOYW1lICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGNsYXNzZXNUb0FkZC5wdXNoKC4uLm5vcm1hbGl6ZUNsYXNzTmFtZXMobGlzdExldmVsQ2xhc3NOYW1lKSk7XG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxpc3RMZXZlbHNDbGFzc05hbWVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGlmIChpICE9PSBub3JtYWxpemVkTGlzdERlcHRoKSB7XG4gICAgICAgICAgY2xhc3Nlc1RvUmVtb3ZlLnB1c2gobm9kZS5fX3RhZyArIGkpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChuZXN0ZWRMaXN0Q2xhc3NOYW1lICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGNvbnN0IG5lc3RlZExpc3RJdGVtQ2xhc3NlcyA9IG5vcm1hbGl6ZUNsYXNzTmFtZXMobmVzdGVkTGlzdENsYXNzTmFtZSk7XG4gICAgICBpZiAobGlzdERlcHRoID4gMSkge1xuICAgICAgICBjbGFzc2VzVG9BZGQucHVzaCguLi5uZXN0ZWRMaXN0SXRlbUNsYXNzZXMpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY2xhc3Nlc1RvUmVtb3ZlLnB1c2goLi4ubmVzdGVkTGlzdEl0ZW1DbGFzc2VzKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgaWYgKGNsYXNzZXNUb1JlbW92ZS5sZW5ndGggPiAwKSB7XG4gICAgcmVtb3ZlQ2xhc3NOYW1lc0Zyb21FbGVtZW50KGRvbSwgLi4uY2xhc3Nlc1RvUmVtb3ZlKTtcbiAgfVxuICBpZiAoY2xhc3Nlc1RvQWRkLmxlbmd0aCA+IDApIHtcbiAgICBhZGRDbGFzc05hbWVzVG9FbGVtZW50KGRvbSwgLi4uY2xhc3Nlc1RvQWRkKTtcbiAgfVxufVxuXG4vKlxuICogVGhpcyBmdW5jdGlvbiBub3JtYWxpemVzIHRoZSBjaGlsZHJlbiBvZiBhIExpc3ROb2RlIGFmdGVyIHRoZSBjb252ZXJzaW9uIGZyb20gSFRNTCxcbiAqIGVuc3VyaW5nIHRoYXQgdGhleSBhcmUgYWxsIExpc3RJdGVtTm9kZXMgYW5kIGNvbnRhaW4gZWl0aGVyIGEgc2luZ2xlIG5lc3RlZCBMaXN0Tm9kZVxuICogb3Igc29tZSBvdGhlciBpbmxpbmUgY29udGVudC5cbiAqL1xuZnVuY3Rpb24gJG5vcm1hbGl6ZUNoaWxkcmVuKG5vZGVzKSB7XG4gIGNvbnN0IG5vcm1hbGl6ZWRMaXN0SXRlbXMgPSBbXTtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBub2Rlcy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IG5vZGUgPSBub2Rlc1tpXTtcbiAgICBpZiAoJGlzTGlzdEl0ZW1Ob2RlKG5vZGUpKSB7XG4gICAgICBub3JtYWxpemVkTGlzdEl0ZW1zLnB1c2gobm9kZSk7XG4gICAgICBjb25zdCBjaGlsZHJlbiA9IG5vZGUuZ2V0Q2hpbGRyZW4oKTtcbiAgICAgIGlmIChjaGlsZHJlbi5sZW5ndGggPiAxKSB7XG4gICAgICAgIGNoaWxkcmVuLmZvckVhY2goY2hpbGQgPT4ge1xuICAgICAgICAgIGlmICgkaXNMaXN0Tm9kZShjaGlsZCkpIHtcbiAgICAgICAgICAgIG5vcm1hbGl6ZWRMaXN0SXRlbXMucHVzaCgkd3JhcEluTGlzdEl0ZW0oY2hpbGQpKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBub3JtYWxpemVkTGlzdEl0ZW1zLnB1c2goJHdyYXBJbkxpc3RJdGVtKG5vZGUpKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG5vcm1hbGl6ZWRMaXN0SXRlbXM7XG59XG5mdW5jdGlvbiBpc0RvbUNoZWNrbGlzdChkb21Ob2RlKSB7XG4gIGlmIChkb21Ob2RlLmdldEF0dHJpYnV0ZSgnX19sZXhpY2FsbGlzdHR5cGUnKSA9PT0gJ2NoZWNrJyB8fFxuICAvLyBpcyBnaXRodWIgY2hlY2tsaXN0XG4gIGRvbU5vZGUuY2xhc3NMaXN0LmNvbnRhaW5zKCdjb250YWlucy10YXNrLWxpc3QnKSkge1xuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIC8vIGlmIGNoaWxkcmVuIGFyZSBjaGVja2xpc3QgaXRlbXMsIHRoZSBub2RlIGlzIGEgY2hlY2tsaXN0IHVsLiBBcHBsaWNhYmxlIGZvciBnb29nbGVkb2MgY2hlY2tsaXN0IHBhc3RpbmcuXG4gIGZvciAoY29uc3QgY2hpbGQgb2YgZG9tTm9kZS5jaGlsZE5vZGVzKSB7XG4gICAgaWYgKGlzSFRNTEVsZW1lbnQoY2hpbGQpICYmIGNoaWxkLmhhc0F0dHJpYnV0ZSgnYXJpYS1jaGVja2VkJykpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG5mdW5jdGlvbiAkY29udmVydExpc3ROb2RlKGRvbU5vZGUpIHtcbiAgY29uc3Qgbm9kZU5hbWUgPSBkb21Ob2RlLm5vZGVOYW1lLnRvTG93ZXJDYXNlKCk7XG4gIGxldCBub2RlID0gbnVsbDtcbiAgaWYgKG5vZGVOYW1lID09PSAnb2wnKSB7XG4gICAgLy8gQHRzLWlnbm9yZVxuICAgIGNvbnN0IHN0YXJ0ID0gZG9tTm9kZS5zdGFydDtcbiAgICBub2RlID0gJGNyZWF0ZUxpc3ROb2RlKCdudW1iZXInLCBzdGFydCk7XG4gIH0gZWxzZSBpZiAobm9kZU5hbWUgPT09ICd1bCcpIHtcbiAgICBpZiAoaXNEb21DaGVja2xpc3QoZG9tTm9kZSkpIHtcbiAgICAgIG5vZGUgPSAkY3JlYXRlTGlzdE5vZGUoJ2NoZWNrJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG5vZGUgPSAkY3JlYXRlTGlzdE5vZGUoJ2J1bGxldCcpO1xuICAgIH1cbiAgfVxuICByZXR1cm4ge1xuICAgIGFmdGVyOiAkbm9ybWFsaXplQ2hpbGRyZW4sXG4gICAgbm9kZVxuICB9O1xufVxuY29uc3QgVEFHX1RPX0xJU1RfVFlQRSA9IHtcbiAgb2w6ICdudW1iZXInLFxuICB1bDogJ2J1bGxldCdcbn07XG5cbi8qKlxuICogQ3JlYXRlcyBhIExpc3ROb2RlIG9mIGxpc3RUeXBlLlxuICogQHBhcmFtIGxpc3RUeXBlIC0gVGhlIHR5cGUgb2YgbGlzdCB0byBiZSBjcmVhdGVkLiBDYW4gYmUgJ251bWJlcicsICdidWxsZXQnLCBvciAnY2hlY2snLlxuICogQHBhcmFtIHN0YXJ0IC0gV2hlcmUgYW4gb3JkZXJlZCBsaXN0IHN0YXJ0cyBpdHMgY291bnQsIHN0YXJ0ID0gMSBpZiBsZWZ0IHVuZGVmaW5lZC5cbiAqIEByZXR1cm5zIFRoZSBuZXcgTGlzdE5vZGVcbiAqL1xuZnVuY3Rpb24gJGNyZWF0ZUxpc3ROb2RlKGxpc3RUeXBlID0gJ251bWJlcicsIHN0YXJ0ID0gMSkge1xuICByZXR1cm4gJGFwcGx5Tm9kZVJlcGxhY2VtZW50KG5ldyBMaXN0Tm9kZShsaXN0VHlwZSwgc3RhcnQpKTtcbn1cblxuLyoqXG4gKiBDaGVja3MgdG8gc2VlIGlmIHRoZSBub2RlIGlzIGEgTGlzdE5vZGUuXG4gKiBAcGFyYW0gbm9kZSAtIFRoZSBub2RlIHRvIGJlIGNoZWNrZWQuXG4gKiBAcmV0dXJucyB0cnVlIGlmIHRoZSBub2RlIGlzIGEgTGlzdE5vZGUsIGZhbHNlIG90aGVyd2lzZS5cbiAqL1xuZnVuY3Rpb24gJGlzTGlzdE5vZGUobm9kZSkge1xuICByZXR1cm4gbm9kZSBpbnN0YW5jZW9mIExpc3ROb2RlO1xufVxuXG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cbmNvbnN0IElOU0VSVF9VTk9SREVSRURfTElTVF9DT01NQU5EID0gY3JlYXRlQ29tbWFuZCgnSU5TRVJUX1VOT1JERVJFRF9MSVNUX0NPTU1BTkQnKTtcbmNvbnN0IElOU0VSVF9PUkRFUkVEX0xJU1RfQ09NTUFORCA9IGNyZWF0ZUNvbW1hbmQoJ0lOU0VSVF9PUkRFUkVEX0xJU1RfQ09NTUFORCcpO1xuY29uc3QgSU5TRVJUX0NIRUNLX0xJU1RfQ09NTUFORCA9IGNyZWF0ZUNvbW1hbmQoJ0lOU0VSVF9DSEVDS19MSVNUX0NPTU1BTkQnKTtcbmNvbnN0IFJFTU9WRV9MSVNUX0NPTU1BTkQgPSBjcmVhdGVDb21tYW5kKCdSRU1PVkVfTElTVF9DT01NQU5EJyk7XG5mdW5jdGlvbiByZWdpc3Rlckxpc3QoZWRpdG9yKSB7XG4gIGNvbnN0IHJlbW92ZUxpc3RlbmVyID0gbWVyZ2VSZWdpc3RlcihlZGl0b3IucmVnaXN0ZXJDb21tYW5kKElOU0VSVF9PUkRFUkVEX0xJU1RfQ09NTUFORCwgKCkgPT4ge1xuICAgICRpbnNlcnRMaXN0KCdudW1iZXInKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9MT1cpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKElOU0VSVF9VTk9SREVSRURfTElTVF9DT01NQU5ELCAoKSA9PiB7XG4gICAgJGluc2VydExpc3QoJ2J1bGxldCcpO1xuICAgIHJldHVybiB0cnVlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0xPVyksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoUkVNT1ZFX0xJU1RfQ09NTUFORCwgKCkgPT4ge1xuICAgICRyZW1vdmVMaXN0KCk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfTE9XKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChJTlNFUlRfUEFSQUdSQVBIX0NPTU1BTkQsICgpID0+ICRoYW5kbGVMaXN0SW5zZXJ0UGFyYWdyYXBoKCksIENPTU1BTkRfUFJJT1JJVFlfTE9XKSwgZWRpdG9yLnJlZ2lzdGVyTm9kZVRyYW5zZm9ybShMaXN0SXRlbU5vZGUsIG5vZGUgPT4ge1xuICAgIGNvbnN0IGZpcnN0Q2hpbGQgPSBub2RlLmdldEZpcnN0Q2hpbGQoKTtcbiAgICBpZiAoZmlyc3RDaGlsZCkge1xuICAgICAgaWYgKCRpc1RleHROb2RlKGZpcnN0Q2hpbGQpKSB7XG4gICAgICAgIGNvbnN0IHN0eWxlID0gZmlyc3RDaGlsZC5nZXRTdHlsZSgpO1xuICAgICAgICBjb25zdCBmb3JtYXQgPSBmaXJzdENoaWxkLmdldEZvcm1hdCgpO1xuICAgICAgICBpZiAobm9kZS5nZXRUZXh0U3R5bGUoKSAhPT0gc3R5bGUpIHtcbiAgICAgICAgICBub2RlLnNldFRleHRTdHlsZShzdHlsZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG5vZGUuZ2V0VGV4dEZvcm1hdCgpICE9PSBmb3JtYXQpIHtcbiAgICAgICAgICBub2RlLnNldFRleHRGb3JtYXQoZm9ybWF0KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBJZiBpdCdzIGVtcHR5LCBjaGVjayB0aGUgc2VsZWN0aW9uXG4gICAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgICBpZiAoJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSAmJiAoc2VsZWN0aW9uLnN0eWxlICE9PSBub2RlLmdldFRleHRTdHlsZSgpIHx8IHNlbGVjdGlvbi5mb3JtYXQgIT09IG5vZGUuZ2V0VGV4dEZvcm1hdCgpKSAmJiBzZWxlY3Rpb24uaXNDb2xsYXBzZWQoKSAmJiBub2RlLmlzKHNlbGVjdGlvbi5hbmNob3IuZ2V0Tm9kZSgpKSkge1xuICAgICAgICBub2RlLnNldFRleHRTdHlsZShzZWxlY3Rpb24uc3R5bGUpLnNldFRleHRGb3JtYXQoc2VsZWN0aW9uLmZvcm1hdCk7XG4gICAgICB9XG4gICAgfVxuICB9KSwgZWRpdG9yLnJlZ2lzdGVyTm9kZVRyYW5zZm9ybShUZXh0Tm9kZSwgbm9kZSA9PiB7XG4gICAgY29uc3QgbGlzdEl0ZW1QYXJlbnROb2RlID0gbm9kZS5nZXRQYXJlbnQoKTtcbiAgICBpZiAoJGlzTGlzdEl0ZW1Ob2RlKGxpc3RJdGVtUGFyZW50Tm9kZSkgJiYgbm9kZS5pcyhsaXN0SXRlbVBhcmVudE5vZGUuZ2V0Rmlyc3RDaGlsZCgpKSkge1xuICAgICAgY29uc3Qgc3R5bGUgPSBub2RlLmdldFN0eWxlKCk7XG4gICAgICBjb25zdCBmb3JtYXQgPSBub2RlLmdldEZvcm1hdCgpO1xuICAgICAgaWYgKHN0eWxlICE9PSBsaXN0SXRlbVBhcmVudE5vZGUuZ2V0VGV4dFN0eWxlKCkgfHwgZm9ybWF0ICE9PSBsaXN0SXRlbVBhcmVudE5vZGUuZ2V0VGV4dEZvcm1hdCgpKSB7XG4gICAgICAgIGxpc3RJdGVtUGFyZW50Tm9kZS5zZXRUZXh0U3R5bGUoc3R5bGUpLnNldFRleHRGb3JtYXQoZm9ybWF0KTtcbiAgICAgIH1cbiAgICB9XG4gIH0pKTtcbiAgcmV0dXJuIHJlbW92ZUxpc3RlbmVyO1xufVxuXG4vKipcbiAqIEBkZXByZWNhdGVkIHVzZSB7QGxpbmsgJGluc2VydExpc3R9IGZyb20gYW4gdXBkYXRlIG9yIGNvbW1hbmQgbGlzdGVuZXIuXG4gKlxuICogSW5zZXJ0cyBhIG5ldyBMaXN0Tm9kZS4gSWYgdGhlIHNlbGVjdGlvbidzIGFuY2hvciBub2RlIGlzIGFuIGVtcHR5IExpc3RJdGVtTm9kZSBhbmQgaXMgYSBjaGlsZCBvZlxuICogdGhlIHJvb3Qvc2hhZG93IHJvb3QsIGl0IHdpbGwgcmVwbGFjZSB0aGUgTGlzdEl0ZW1Ob2RlIHdpdGggYSBMaXN0Tm9kZSBhbmQgdGhlIG9sZCBMaXN0SXRlbU5vZGUuXG4gKiBPdGhlcndpc2UgaXQgd2lsbCByZXBsYWNlIGl0cyBwYXJlbnQgd2l0aCBhIG5ldyBMaXN0Tm9kZSBhbmQgcmUtaW5zZXJ0IHRoZSBMaXN0SXRlbU5vZGUgYW5kIGFueSBwcmV2aW91cyBjaGlsZHJlbi5cbiAqIElmIHRoZSBzZWxlY3Rpb24ncyBhbmNob3Igbm9kZSBpcyBub3QgYW4gZW1wdHkgTGlzdEl0ZW1Ob2RlLCBpdCB3aWxsIGFkZCBhIG5ldyBMaXN0Tm9kZSBvciBtZXJnZSBhbiBleGlzdGluZyBMaXN0Tm9kZSxcbiAqIHVubGVzcyB0aGUgdGhlIG5vZGUgaXMgYSBsZWFmIG5vZGUsIGluIHdoaWNoIGNhc2UgaXQgd2lsbCBhdHRlbXB0IHRvIGZpbmQgYSBMaXN0Tm9kZSB1cCB0aGUgYnJhbmNoIGFuZCByZXBsYWNlIGl0IHdpdGhcbiAqIGEgbmV3IExpc3ROb2RlLCBvciBjcmVhdGUgYSBuZXcgTGlzdE5vZGUgYXQgdGhlIG5lYXJlc3Qgcm9vdC9zaGFkb3cgcm9vdC5cbiAqIEBwYXJhbSBlZGl0b3IgLSBUaGUgbGV4aWNhbCBlZGl0b3IuXG4gKiBAcGFyYW0gbGlzdFR5cGUgLSBUaGUgdHlwZSBvZiBsaXN0LCBcIm51bWJlclwiIHwgXCJidWxsZXRcIiB8IFwiY2hlY2tcIi5cbiAqL1xuZnVuY3Rpb24gaW5zZXJ0TGlzdChlZGl0b3IsIGxpc3RUeXBlKSB7XG4gIGVkaXRvci51cGRhdGUoKCkgPT4gJGluc2VydExpc3QobGlzdFR5cGUpKTtcbn1cblxuLyoqXG4gKiBAZGVwcmVjYXRlZCB1c2Uge0BsaW5rICRyZW1vdmVMaXN0fSBmcm9tIGFuIHVwZGF0ZSBvciBjb21tYW5kIGxpc3RlbmVyLlxuICpcbiAqIFNlYXJjaGVzIGZvciB0aGUgbmVhcmVzdCBhbmNlc3RyYWwgTGlzdE5vZGUgYW5kIHJlbW92ZXMgaXQuIElmIHNlbGVjdGlvbiBpcyBhbiBlbXB0eSBMaXN0SXRlbU5vZGVcbiAqIGl0IHdpbGwgcmVtb3ZlIHRoZSB3aG9sZSBsaXN0LCBpbmNsdWRpbmcgdGhlIExpc3RJdGVtTm9kZS4gRm9yIGVhY2ggTGlzdEl0ZW1Ob2RlIGluIHRoZSBMaXN0Tm9kZSxcbiAqIHJlbW92ZUxpc3Qgd2lsbCBhbHNvIGdlbmVyYXRlIG5ldyBQYXJhZ3JhcGhOb2RlcyBpbiB0aGUgcmVtb3ZlZCBMaXN0Tm9kZSdzIHBsYWNlLiBBbnkgY2hpbGQgbm9kZVxuICogaW5zaWRlIGEgTGlzdEl0ZW1Ob2RlIHdpbGwgYmUgYXBwZW5kZWQgdG8gdGhlIG5ldyBQYXJhZ3JhcGhOb2Rlcy5cbiAqIEBwYXJhbSBlZGl0b3IgLSBUaGUgbGV4aWNhbCBlZGl0b3IuXG4gKi9cbmZ1bmN0aW9uIHJlbW92ZUxpc3QoZWRpdG9yKSB7XG4gIGVkaXRvci51cGRhdGUoKCkgPT4gJHJlbW92ZUxpc3QoKSk7XG59XG5cbmV4cG9ydCB7ICRjcmVhdGVMaXN0SXRlbU5vZGUsICRjcmVhdGVMaXN0Tm9kZSwgJGdldExpc3REZXB0aCwgJGhhbmRsZUxpc3RJbnNlcnRQYXJhZ3JhcGgsICRpbnNlcnRMaXN0LCAkaXNMaXN0SXRlbU5vZGUsICRpc0xpc3ROb2RlLCAkcmVtb3ZlTGlzdCwgSU5TRVJUX0NIRUNLX0xJU1RfQ09NTUFORCwgSU5TRVJUX09SREVSRURfTElTVF9DT01NQU5ELCBJTlNFUlRfVU5PUkRFUkVEX0xJU1RfQ09NTUFORCwgTGlzdEl0ZW1Ob2RlLCBMaXN0Tm9kZSwgUkVNT1ZFX0xJU1RfQ09NTUFORCwgaW5zZXJ0TGlzdCwgcmVnaXN0ZXJMaXN0LCByZW1vdmVMaXN0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+list@0.28.0/node_modules/@lexical/list/LexicalList.dev.mjs\n");

/***/ })

};
;