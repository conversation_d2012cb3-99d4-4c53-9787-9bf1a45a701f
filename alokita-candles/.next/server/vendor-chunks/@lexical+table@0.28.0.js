"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+table@0.28.0";
exports.ids = ["vendor-chunks/@lexical+table@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+table@0.28.0/node_modules/@lexical/table/LexicalTable.dev.mjs":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+table@0.28.0/node_modules/@lexical/table/LexicalTable.dev.mjs ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $computeTableMap: () => (/* binding */ $computeTableMap),\n/* harmony export */   $computeTableMapSkipCellCheck: () => (/* binding */ $computeTableMapSkipCellCheck),\n/* harmony export */   $createTableCellNode: () => (/* binding */ $createTableCellNode),\n/* harmony export */   $createTableNode: () => (/* binding */ $createTableNode),\n/* harmony export */   $createTableNodeWithDimensions: () => (/* binding */ $createTableNodeWithDimensions),\n/* harmony export */   $createTableRowNode: () => (/* binding */ $createTableRowNode),\n/* harmony export */   $createTableSelection: () => (/* binding */ $createTableSelection),\n/* harmony export */   $createTableSelectionFrom: () => (/* binding */ $createTableSelectionFrom),\n/* harmony export */   $deleteTableColumn: () => (/* binding */ $deleteTableColumn),\n/* harmony export */   $deleteTableColumn__EXPERIMENTAL: () => (/* binding */ $deleteTableColumn__EXPERIMENTAL),\n/* harmony export */   $deleteTableRow__EXPERIMENTAL: () => (/* binding */ $deleteTableRow__EXPERIMENTAL),\n/* harmony export */   $findCellNode: () => (/* binding */ $findCellNode),\n/* harmony export */   $findTableNode: () => (/* binding */ $findTableNode),\n/* harmony export */   $getElementForTableNode: () => (/* binding */ $getElementForTableNode),\n/* harmony export */   $getNodeTriplet: () => (/* binding */ $getNodeTriplet),\n/* harmony export */   $getTableAndElementByKey: () => (/* binding */ $getTableAndElementByKey),\n/* harmony export */   $getTableCellNodeFromLexicalNode: () => (/* binding */ $getTableCellNodeFromLexicalNode),\n/* harmony export */   $getTableCellNodeRect: () => (/* binding */ $getTableCellNodeRect),\n/* harmony export */   $getTableColumnIndexFromTableCellNode: () => (/* binding */ $getTableColumnIndexFromTableCellNode),\n/* harmony export */   $getTableNodeFromLexicalNodeOrThrow: () => (/* binding */ $getTableNodeFromLexicalNodeOrThrow),\n/* harmony export */   $getTableRowIndexFromTableCellNode: () => (/* binding */ $getTableRowIndexFromTableCellNode),\n/* harmony export */   $getTableRowNodeFromTableCellNodeOrThrow: () => (/* binding */ $getTableRowNodeFromTableCellNodeOrThrow),\n/* harmony export */   $insertTableColumn: () => (/* binding */ $insertTableColumn),\n/* harmony export */   $insertTableColumn__EXPERIMENTAL: () => (/* binding */ $insertTableColumn__EXPERIMENTAL),\n/* harmony export */   $insertTableRow: () => (/* binding */ $insertTableRow),\n/* harmony export */   $insertTableRow__EXPERIMENTAL: () => (/* binding */ $insertTableRow__EXPERIMENTAL),\n/* harmony export */   $isScrollableTablesActive: () => (/* binding */ $isScrollableTablesActive),\n/* harmony export */   $isTableCellNode: () => (/* binding */ $isTableCellNode),\n/* harmony export */   $isTableNode: () => (/* binding */ $isTableNode),\n/* harmony export */   $isTableRowNode: () => (/* binding */ $isTableRowNode),\n/* harmony export */   $isTableSelection: () => (/* binding */ $isTableSelection),\n/* harmony export */   $removeTableRowAtIndex: () => (/* binding */ $removeTableRowAtIndex),\n/* harmony export */   $unmergeCell: () => (/* binding */ $unmergeCell),\n/* harmony export */   INSERT_TABLE_COMMAND: () => (/* binding */ INSERT_TABLE_COMMAND),\n/* harmony export */   TableCellHeaderStates: () => (/* binding */ TableCellHeaderStates),\n/* harmony export */   TableCellNode: () => (/* binding */ TableCellNode),\n/* harmony export */   TableNode: () => (/* binding */ TableNode),\n/* harmony export */   TableObserver: () => (/* binding */ TableObserver),\n/* harmony export */   TableRowNode: () => (/* binding */ TableRowNode),\n/* harmony export */   applyTableHandlers: () => (/* binding */ applyTableHandlers),\n/* harmony export */   getDOMCellFromTarget: () => (/* binding */ getDOMCellFromTarget),\n/* harmony export */   getTableElement: () => (/* binding */ getTableElement),\n/* harmony export */   getTableObserverFromTableElement: () => (/* binding */ getTableObserverFromTableElement),\n/* harmony export */   registerTableCellUnmergeTransform: () => (/* binding */ registerTableCellUnmergeTransform),\n/* harmony export */   registerTablePlugin: () => (/* binding */ registerTablePlugin),\n/* harmony export */   registerTableSelectionObserver: () => (/* binding */ registerTableSelectionObserver),\n/* harmony export */   setScrollableTablesActive: () => (/* binding */ setScrollableTablesActive)\n/* harmony export */ });\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/clipboard */ \"(rsc)/./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst PIXEL_VALUE_REG_EXP = /^(\\d+(?:\\.\\d+)?)px$/;\n\n// .PlaygroundEditorTheme__tableCell width value from\n// packages/lexical-playground/src/themes/PlaygroundEditorTheme.css\nconst COLUMN_WIDTH = 75;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst TableCellHeaderStates = {\n  BOTH: 3,\n  COLUMN: 2,\n  NO_STATUS: 0,\n  ROW: 1\n};\n/** @noInheritDoc */\nclass TableCellNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  static getType() {\n    return 'tablecell';\n  }\n  static clone(node) {\n    return new TableCellNode(node.__headerState, node.__colSpan, node.__width, node.__key);\n  }\n  afterCloneFrom(node) {\n    super.afterCloneFrom(node);\n    this.__rowSpan = node.__rowSpan;\n    this.__backgroundColor = node.__backgroundColor;\n    this.__verticalAlign = node.__verticalAlign;\n  }\n  static importDOM() {\n    return {\n      td: node => ({\n        conversion: $convertTableCellNodeElement,\n        priority: 0\n      }),\n      th: node => ({\n        conversion: $convertTableCellNodeElement,\n        priority: 0\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createTableCellNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setHeaderStyles(serializedNode.headerState).setColSpan(serializedNode.colSpan || 1).setRowSpan(serializedNode.rowSpan || 1).setWidth(serializedNode.width || undefined).setBackgroundColor(serializedNode.backgroundColor || null).setVerticalAlign(serializedNode.verticalAlign || undefined);\n  }\n  constructor(headerState = TableCellHeaderStates.NO_STATUS, colSpan = 1, width, key) {\n    super(key);\n    this.__colSpan = colSpan;\n    this.__rowSpan = 1;\n    this.__headerState = headerState;\n    this.__width = width;\n    this.__backgroundColor = null;\n    this.__verticalAlign = undefined;\n  }\n  createDOM(config) {\n    const element = document.createElement(this.getTag());\n    if (this.__width) {\n      element.style.width = `${this.__width}px`;\n    }\n    if (this.__colSpan > 1) {\n      element.colSpan = this.__colSpan;\n    }\n    if (this.__rowSpan > 1) {\n      element.rowSpan = this.__rowSpan;\n    }\n    if (this.__backgroundColor !== null) {\n      element.style.backgroundColor = this.__backgroundColor;\n    }\n    if (isValidVerticalAlign(this.__verticalAlign)) {\n      element.style.verticalAlign = this.__verticalAlign;\n    }\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.tableCell, this.hasHeader() && config.theme.tableCellHeader);\n    return element;\n  }\n  exportDOM(editor) {\n    const output = super.exportDOM(editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(output.element)) {\n      const element = output.element;\n      element.setAttribute('data-temporary-table-cell-lexical-key', this.getKey());\n      element.style.border = '1px solid black';\n      if (this.__colSpan > 1) {\n        element.colSpan = this.__colSpan;\n      }\n      if (this.__rowSpan > 1) {\n        element.rowSpan = this.__rowSpan;\n      }\n      element.style.width = `${this.getWidth() || COLUMN_WIDTH}px`;\n      element.style.verticalAlign = this.getVerticalAlign() || 'top';\n      element.style.textAlign = 'start';\n      if (this.__backgroundColor === null && this.hasHeader()) {\n        element.style.backgroundColor = '#f2f3f5';\n      }\n    }\n    return output;\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      ...(isValidVerticalAlign(this.__verticalAlign) && {\n        verticalAlign: this.__verticalAlign\n      }),\n      backgroundColor: this.getBackgroundColor(),\n      colSpan: this.__colSpan,\n      headerState: this.__headerState,\n      rowSpan: this.__rowSpan,\n      width: this.getWidth()\n    };\n  }\n  getColSpan() {\n    return this.getLatest().__colSpan;\n  }\n  setColSpan(colSpan) {\n    const self = this.getWritable();\n    self.__colSpan = colSpan;\n    return self;\n  }\n  getRowSpan() {\n    return this.getLatest().__rowSpan;\n  }\n  setRowSpan(rowSpan) {\n    const self = this.getWritable();\n    self.__rowSpan = rowSpan;\n    return self;\n  }\n  getTag() {\n    return this.hasHeader() ? 'th' : 'td';\n  }\n  setHeaderStyles(headerState, mask = TableCellHeaderStates.BOTH) {\n    const self = this.getWritable();\n    self.__headerState = headerState & mask | self.__headerState & ~mask;\n    return self;\n  }\n  getHeaderStyles() {\n    return this.getLatest().__headerState;\n  }\n  setWidth(width) {\n    const self = this.getWritable();\n    self.__width = width;\n    return self;\n  }\n  getWidth() {\n    return this.getLatest().__width;\n  }\n  getBackgroundColor() {\n    return this.getLatest().__backgroundColor;\n  }\n  setBackgroundColor(newBackgroundColor) {\n    const self = this.getWritable();\n    self.__backgroundColor = newBackgroundColor;\n    return self;\n  }\n  getVerticalAlign() {\n    return this.getLatest().__verticalAlign;\n  }\n  setVerticalAlign(newVerticalAlign) {\n    const self = this.getWritable();\n    self.__verticalAlign = newVerticalAlign || undefined;\n    return self;\n  }\n  toggleHeaderStyle(headerStateToToggle) {\n    const self = this.getWritable();\n    if ((self.__headerState & headerStateToToggle) === headerStateToToggle) {\n      self.__headerState -= headerStateToToggle;\n    } else {\n      self.__headerState += headerStateToToggle;\n    }\n    return self;\n  }\n  hasHeaderState(headerState) {\n    return (this.getHeaderStyles() & headerState) === headerState;\n  }\n  hasHeader() {\n    return this.getLatest().__headerState !== TableCellHeaderStates.NO_STATUS;\n  }\n  updateDOM(prevNode) {\n    return prevNode.__headerState !== this.__headerState || prevNode.__width !== this.__width || prevNode.__colSpan !== this.__colSpan || prevNode.__rowSpan !== this.__rowSpan || prevNode.__backgroundColor !== this.__backgroundColor || prevNode.__verticalAlign !== this.__verticalAlign;\n  }\n  isShadowRoot() {\n    return true;\n  }\n  collapseAtStart() {\n    return true;\n  }\n  canBeEmpty() {\n    return false;\n  }\n  canIndent() {\n    return false;\n  }\n}\nfunction isValidVerticalAlign(verticalAlign) {\n  return verticalAlign === 'middle' || verticalAlign === 'bottom';\n}\nfunction $convertTableCellNodeElement(domNode) {\n  const domNode_ = domNode;\n  const nodeName = domNode.nodeName.toLowerCase();\n  let width = undefined;\n  if (PIXEL_VALUE_REG_EXP.test(domNode_.style.width)) {\n    width = parseFloat(domNode_.style.width);\n  }\n  const tableCellNode = $createTableCellNode(nodeName === 'th' ? TableCellHeaderStates.ROW : TableCellHeaderStates.NO_STATUS, domNode_.colSpan, width);\n  tableCellNode.__rowSpan = domNode_.rowSpan;\n  const backgroundColor = domNode_.style.backgroundColor;\n  if (backgroundColor !== '') {\n    tableCellNode.__backgroundColor = backgroundColor;\n  }\n  const verticalAlign = domNode_.style.verticalAlign;\n  if (isValidVerticalAlign(verticalAlign)) {\n    tableCellNode.__verticalAlign = verticalAlign;\n  }\n  const style = domNode_.style;\n  const textDecoration = (style && style.textDecoration || '').split(' ');\n  const hasBoldFontWeight = style.fontWeight === '700' || style.fontWeight === 'bold';\n  const hasLinethroughTextDecoration = textDecoration.includes('line-through');\n  const hasItalicFontStyle = style.fontStyle === 'italic';\n  const hasUnderlineTextDecoration = textDecoration.includes('underline');\n  return {\n    after: childLexicalNodes => {\n      const result = [];\n      let paragraphNode = null;\n      const removeSingleLineBreakNode = () => {\n        if (paragraphNode) {\n          const firstChild = paragraphNode.getFirstChild();\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLineBreakNode)(firstChild) && paragraphNode.getChildrenSize() === 1) {\n            firstChild.remove();\n          }\n        }\n      };\n      for (const child of childLexicalNodes) {\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isInlineElementOrDecoratorNode)(child) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(child) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLineBreakNode)(child)) {\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(child)) {\n            if (hasBoldFontWeight) {\n              child.toggleFormat('bold');\n            }\n            if (hasLinethroughTextDecoration) {\n              child.toggleFormat('strikethrough');\n            }\n            if (hasItalicFontStyle) {\n              child.toggleFormat('italic');\n            }\n            if (hasUnderlineTextDecoration) {\n              child.toggleFormat('underline');\n            }\n          }\n          if (paragraphNode) {\n            paragraphNode.append(child);\n          } else {\n            paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)().append(child);\n            result.push(paragraphNode);\n          }\n        } else {\n          result.push(child);\n          removeSingleLineBreakNode();\n          paragraphNode = null;\n        }\n      }\n      removeSingleLineBreakNode();\n      if (result.length === 0) {\n        result.push((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n      }\n      return result;\n    },\n    node: tableCellNode\n  };\n}\nfunction $createTableCellNode(headerState = TableCellHeaderStates.NO_STATUS, colSpan = 1, width) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new TableCellNode(headerState, colSpan, width));\n}\nfunction $isTableCellNode(node) {\n  return node instanceof TableCellNode;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst INSERT_TABLE_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('INSERT_TABLE_COMMAND');\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/** @noInheritDoc */\nclass TableRowNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  static getType() {\n    return 'tablerow';\n  }\n  static clone(node) {\n    return new TableRowNode(node.__height, node.__key);\n  }\n  static importDOM() {\n    return {\n      tr: node => ({\n        conversion: $convertTableRowElement,\n        priority: 0\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createTableRowNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setHeight(serializedNode.height);\n  }\n  constructor(height, key) {\n    super(key);\n    this.__height = height;\n  }\n  exportJSON() {\n    const height = this.getHeight();\n    return {\n      ...super.exportJSON(),\n      ...(height === undefined ? undefined : {\n        height\n      })\n    };\n  }\n  createDOM(config) {\n    const element = document.createElement('tr');\n    if (this.__height) {\n      element.style.height = `${this.__height}px`;\n    }\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.tableRow);\n    return element;\n  }\n  extractWithChild(child, selection, destination) {\n    return destination === 'html';\n  }\n  isShadowRoot() {\n    return true;\n  }\n  setHeight(height) {\n    const self = this.getWritable();\n    self.__height = height;\n    return self;\n  }\n  getHeight() {\n    return this.getLatest().__height;\n  }\n  updateDOM(prevNode) {\n    return prevNode.__height !== this.__height;\n  }\n  canBeEmpty() {\n    return false;\n  }\n  canIndent() {\n    return false;\n  }\n}\nfunction $convertTableRowElement(domNode) {\n  const domNode_ = domNode;\n  let height = undefined;\n  if (PIXEL_VALUE_REG_EXP.test(domNode_.style.height)) {\n    height = parseFloat(domNode_.style.height);\n  }\n  return {\n    after: children => (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$descendantsMatching)(children, $isTableCellNode),\n    node: $createTableRowNode(height)\n  };\n}\nfunction $createTableRowNode(height) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new TableRowNode(height));\n}\nfunction $isTableRowNode(node) {\n  return node instanceof TableRowNode;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst documentMode = CAN_USE_DOM && 'documentMode' in document ? document.documentMode : null;\nconst IS_FIREFOX = CAN_USE_DOM && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);\nCAN_USE_DOM && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;\n\nfunction $createTableNodeWithDimensions(rowCount, columnCount, includeHeaders = true) {\n  const tableNode = $createTableNode();\n  for (let iRow = 0; iRow < rowCount; iRow++) {\n    const tableRowNode = $createTableRowNode();\n    for (let iColumn = 0; iColumn < columnCount; iColumn++) {\n      let headerState = TableCellHeaderStates.NO_STATUS;\n      if (typeof includeHeaders === 'object') {\n        if (iRow === 0 && includeHeaders.rows) {\n          headerState |= TableCellHeaderStates.ROW;\n        }\n        if (iColumn === 0 && includeHeaders.columns) {\n          headerState |= TableCellHeaderStates.COLUMN;\n        }\n      } else if (includeHeaders) {\n        if (iRow === 0) {\n          headerState |= TableCellHeaderStates.ROW;\n        }\n        if (iColumn === 0) {\n          headerState |= TableCellHeaderStates.COLUMN;\n        }\n      }\n      const tableCellNode = $createTableCellNode(headerState);\n      const paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n      paragraphNode.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)());\n      tableCellNode.append(paragraphNode);\n      tableRowNode.append(tableCellNode);\n    }\n    tableNode.append(tableRowNode);\n  }\n  return tableNode;\n}\nfunction $getTableCellNodeFromLexicalNode(startingNode) {\n  const node = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(startingNode, n => $isTableCellNode(n));\n  if ($isTableCellNode(node)) {\n    return node;\n  }\n  return null;\n}\nfunction $getTableRowNodeFromTableCellNodeOrThrow(startingNode) {\n  const node = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(startingNode, n => $isTableRowNode(n));\n  if ($isTableRowNode(node)) {\n    return node;\n  }\n  throw new Error('Expected table cell to be inside of table row.');\n}\nfunction $getTableNodeFromLexicalNodeOrThrow(startingNode) {\n  const node = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(startingNode, n => $isTableNode(n));\n  if ($isTableNode(node)) {\n    return node;\n  }\n  throw new Error('Expected table cell to be inside of table.');\n}\nfunction $getTableRowIndexFromTableCellNode(tableCellNode) {\n  const tableRowNode = $getTableRowNodeFromTableCellNodeOrThrow(tableCellNode);\n  const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableRowNode);\n  return tableNode.getChildren().findIndex(n => n.is(tableRowNode));\n}\nfunction $getTableColumnIndexFromTableCellNode(tableCellNode) {\n  const tableRowNode = $getTableRowNodeFromTableCellNodeOrThrow(tableCellNode);\n  return tableRowNode.getChildren().findIndex(n => n.is(tableCellNode));\n}\nfunction $getTableCellSiblingsFromTableCellNode(tableCellNode, table) {\n  const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);\n  const {\n    x,\n    y\n  } = tableNode.getCordsFromCellNode(tableCellNode, table);\n  return {\n    above: tableNode.getCellNodeFromCords(x, y - 1, table),\n    below: tableNode.getCellNodeFromCords(x, y + 1, table),\n    left: tableNode.getCellNodeFromCords(x - 1, y, table),\n    right: tableNode.getCellNodeFromCords(x + 1, y, table)\n  };\n}\nfunction $removeTableRowAtIndex(tableNode, indexToDelete) {\n  const tableRows = tableNode.getChildren();\n  if (indexToDelete >= tableRows.length || indexToDelete < 0) {\n    throw new Error('Expected table cell to be inside of table row.');\n  }\n  const targetRowNode = tableRows[indexToDelete];\n  targetRowNode.remove();\n  return tableNode;\n}\nfunction $insertTableRow(tableNode, targetIndex, shouldInsertAfter = true, rowCount, table) {\n  const tableRows = tableNode.getChildren();\n  if (targetIndex >= tableRows.length || targetIndex < 0) {\n    throw new Error('Table row target index out of range');\n  }\n  const targetRowNode = tableRows[targetIndex];\n  if ($isTableRowNode(targetRowNode)) {\n    for (let r = 0; r < rowCount; r++) {\n      const tableRowCells = targetRowNode.getChildren();\n      const tableColumnCount = tableRowCells.length;\n      const newTableRowNode = $createTableRowNode();\n      for (let c = 0; c < tableColumnCount; c++) {\n        const tableCellFromTargetRow = tableRowCells[c];\n        if (!$isTableCellNode(tableCellFromTargetRow)) {\n          formatDevErrorMessage(`Expected table cell`);\n        }\n        const {\n          above,\n          below\n        } = $getTableCellSiblingsFromTableCellNode(tableCellFromTargetRow, table);\n        let headerState = TableCellHeaderStates.NO_STATUS;\n        const width = above && above.getWidth() || below && below.getWidth() || undefined;\n        if (above && above.hasHeaderState(TableCellHeaderStates.COLUMN) || below && below.hasHeaderState(TableCellHeaderStates.COLUMN)) {\n          headerState |= TableCellHeaderStates.COLUMN;\n        }\n        const tableCellNode = $createTableCellNode(headerState, 1, width);\n        tableCellNode.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n        newTableRowNode.append(tableCellNode);\n      }\n      if (shouldInsertAfter) {\n        targetRowNode.insertAfter(newTableRowNode);\n      } else {\n        targetRowNode.insertBefore(newTableRowNode);\n      }\n    }\n  } else {\n    throw new Error('Row before insertion index does not exist.');\n  }\n  return tableNode;\n}\nconst getHeaderState = (currentState, possibleState) => {\n  if (currentState === TableCellHeaderStates.BOTH || currentState === possibleState) {\n    return possibleState;\n  }\n  return TableCellHeaderStates.NO_STATUS;\n};\n\n/**\n * Inserts a table row before or after the current focus cell node,\n * taking into account any spans. If successful, returns the\n * inserted table row node.\n */\nfunction $insertTableRow__EXPERIMENTAL(insertAfter = true) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const anchor = selection.anchor.getNode();\n  const focus = selection.focus.getNode();\n  const [anchorCell] = $getNodeTriplet(anchor);\n  const [focusCell,, grid] = $getNodeTriplet(focus);\n  const [gridMap, focusCellMap, anchorCellMap] = $computeTableMap(grid, focusCell, anchorCell);\n  const columnCount = gridMap[0].length;\n  const {\n    startRow: anchorStartRow\n  } = anchorCellMap;\n  const {\n    startRow: focusStartRow\n  } = focusCellMap;\n  let insertedRow = null;\n  if (insertAfter) {\n    const insertAfterEndRow = Math.max(focusStartRow + focusCell.__rowSpan, anchorStartRow + anchorCell.__rowSpan) - 1;\n    const insertAfterEndRowMap = gridMap[insertAfterEndRow];\n    const newRow = $createTableRowNode();\n    for (let i = 0; i < columnCount; i++) {\n      const {\n        cell,\n        startRow\n      } = insertAfterEndRowMap[i];\n      if (startRow + cell.__rowSpan - 1 <= insertAfterEndRow) {\n        const currentCell = insertAfterEndRowMap[i].cell;\n        const currentCellHeaderState = currentCell.__headerState;\n        const headerState = getHeaderState(currentCellHeaderState, TableCellHeaderStates.COLUMN);\n        newRow.append($createTableCellNode(headerState).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n      } else {\n        cell.setRowSpan(cell.__rowSpan + 1);\n      }\n    }\n    const insertAfterEndRowNode = grid.getChildAtIndex(insertAfterEndRow);\n    if (!$isTableRowNode(insertAfterEndRowNode)) {\n      formatDevErrorMessage(`insertAfterEndRow is not a TableRowNode`);\n    }\n    insertAfterEndRowNode.insertAfter(newRow);\n    insertedRow = newRow;\n  } else {\n    const insertBeforeStartRow = Math.min(focusStartRow, anchorStartRow);\n    const insertBeforeStartRowMap = gridMap[insertBeforeStartRow];\n    const newRow = $createTableRowNode();\n    for (let i = 0; i < columnCount; i++) {\n      const {\n        cell,\n        startRow\n      } = insertBeforeStartRowMap[i];\n      if (startRow === insertBeforeStartRow) {\n        const currentCell = insertBeforeStartRowMap[i].cell;\n        const currentCellHeaderState = currentCell.__headerState;\n        const headerState = getHeaderState(currentCellHeaderState, TableCellHeaderStates.COLUMN);\n        newRow.append($createTableCellNode(headerState).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n      } else {\n        cell.setRowSpan(cell.__rowSpan + 1);\n      }\n    }\n    const insertBeforeStartRowNode = grid.getChildAtIndex(insertBeforeStartRow);\n    if (!$isTableRowNode(insertBeforeStartRowNode)) {\n      formatDevErrorMessage(`insertBeforeStartRow is not a TableRowNode`);\n    }\n    insertBeforeStartRowNode.insertBefore(newRow);\n    insertedRow = newRow;\n  }\n  return insertedRow;\n}\nfunction $insertTableColumn(tableNode, targetIndex, shouldInsertAfter = true, columnCount, table) {\n  const tableRows = tableNode.getChildren();\n  const tableCellsToBeInserted = [];\n  for (let r = 0; r < tableRows.length; r++) {\n    const currentTableRowNode = tableRows[r];\n    if ($isTableRowNode(currentTableRowNode)) {\n      for (let c = 0; c < columnCount; c++) {\n        const tableRowChildren = currentTableRowNode.getChildren();\n        if (targetIndex >= tableRowChildren.length || targetIndex < 0) {\n          throw new Error('Table column target index out of range');\n        }\n        const targetCell = tableRowChildren[targetIndex];\n        if (!$isTableCellNode(targetCell)) {\n          formatDevErrorMessage(`Expected table cell`);\n        }\n        const {\n          left,\n          right\n        } = $getTableCellSiblingsFromTableCellNode(targetCell, table);\n        let headerState = TableCellHeaderStates.NO_STATUS;\n        if (left && left.hasHeaderState(TableCellHeaderStates.ROW) || right && right.hasHeaderState(TableCellHeaderStates.ROW)) {\n          headerState |= TableCellHeaderStates.ROW;\n        }\n        const newTableCell = $createTableCellNode(headerState);\n        newTableCell.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n        tableCellsToBeInserted.push({\n          newTableCell,\n          targetCell\n        });\n      }\n    }\n  }\n  tableCellsToBeInserted.forEach(({\n    newTableCell,\n    targetCell\n  }) => {\n    if (shouldInsertAfter) {\n      targetCell.insertAfter(newTableCell);\n    } else {\n      targetCell.insertBefore(newTableCell);\n    }\n  });\n  return tableNode;\n}\n\n/**\n * Inserts a column before or after the current focus cell node,\n * taking into account any spans. If successful, returns the\n * first inserted cell node.\n */\nfunction $insertTableColumn__EXPERIMENTAL(insertAfter = true) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const anchor = selection.anchor.getNode();\n  const focus = selection.focus.getNode();\n  const [anchorCell] = $getNodeTriplet(anchor);\n  const [focusCell,, grid] = $getNodeTriplet(focus);\n  const [gridMap, focusCellMap, anchorCellMap] = $computeTableMap(grid, focusCell, anchorCell);\n  const rowCount = gridMap.length;\n  const startColumn = insertAfter ? Math.max(focusCellMap.startColumn, anchorCellMap.startColumn) : Math.min(focusCellMap.startColumn, anchorCellMap.startColumn);\n  const insertAfterColumn = insertAfter ? startColumn + focusCell.__colSpan - 1 : startColumn - 1;\n  const gridFirstChild = grid.getFirstChild();\n  if (!$isTableRowNode(gridFirstChild)) {\n    formatDevErrorMessage(`Expected firstTable child to be a row`);\n  }\n  let firstInsertedCell = null;\n  function $createTableCellNodeForInsertTableColumn(headerState = TableCellHeaderStates.NO_STATUS) {\n    const cell = $createTableCellNode(headerState).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n    if (firstInsertedCell === null) {\n      firstInsertedCell = cell;\n    }\n    return cell;\n  }\n  let loopRow = gridFirstChild;\n  rowLoop: for (let i = 0; i < rowCount; i++) {\n    if (i !== 0) {\n      const currentRow = loopRow.getNextSibling();\n      if (!$isTableRowNode(currentRow)) {\n        formatDevErrorMessage(`Expected row nextSibling to be a row`);\n      }\n      loopRow = currentRow;\n    }\n    const rowMap = gridMap[i];\n    const currentCellHeaderState = rowMap[insertAfterColumn < 0 ? 0 : insertAfterColumn].cell.__headerState;\n    const headerState = getHeaderState(currentCellHeaderState, TableCellHeaderStates.ROW);\n    if (insertAfterColumn < 0) {\n      $insertFirst(loopRow, $createTableCellNodeForInsertTableColumn(headerState));\n      continue;\n    }\n    const {\n      cell: currentCell,\n      startColumn: currentStartColumn,\n      startRow: currentStartRow\n    } = rowMap[insertAfterColumn];\n    if (currentStartColumn + currentCell.__colSpan - 1 <= insertAfterColumn) {\n      let insertAfterCell = currentCell;\n      let insertAfterCellRowStart = currentStartRow;\n      let prevCellIndex = insertAfterColumn;\n      while (insertAfterCellRowStart !== i && insertAfterCell.__rowSpan > 1) {\n        prevCellIndex -= currentCell.__colSpan;\n        if (prevCellIndex >= 0) {\n          const {\n            cell: cell_,\n            startRow: startRow_\n          } = rowMap[prevCellIndex];\n          insertAfterCell = cell_;\n          insertAfterCellRowStart = startRow_;\n        } else {\n          loopRow.append($createTableCellNodeForInsertTableColumn(headerState));\n          continue rowLoop;\n        }\n      }\n      insertAfterCell.insertAfter($createTableCellNodeForInsertTableColumn(headerState));\n    } else {\n      currentCell.setColSpan(currentCell.__colSpan + 1);\n    }\n  }\n  if (firstInsertedCell !== null) {\n    $moveSelectionToCell(firstInsertedCell);\n  }\n  const colWidths = grid.getColWidths();\n  if (colWidths) {\n    const newColWidths = [...colWidths];\n    const columnIndex = insertAfterColumn < 0 ? 0 : insertAfterColumn;\n    const newWidth = newColWidths[columnIndex];\n    newColWidths.splice(columnIndex, 0, newWidth);\n    grid.setColWidths(newColWidths);\n  }\n  return firstInsertedCell;\n}\nfunction $deleteTableColumn(tableNode, targetIndex) {\n  const tableRows = tableNode.getChildren();\n  for (let i = 0; i < tableRows.length; i++) {\n    const currentTableRowNode = tableRows[i];\n    if ($isTableRowNode(currentTableRowNode)) {\n      const tableRowChildren = currentTableRowNode.getChildren();\n      if (targetIndex >= tableRowChildren.length || targetIndex < 0) {\n        throw new Error('Table column target index out of range');\n      }\n      tableRowChildren[targetIndex].remove();\n    }\n  }\n  return tableNode;\n}\nfunction $deleteTableRow__EXPERIMENTAL() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const [anchor, focus] = selection.isBackward() ? [selection.focus.getNode(), selection.anchor.getNode()] : [selection.anchor.getNode(), selection.focus.getNode()];\n  const [anchorCell,, grid] = $getNodeTriplet(anchor);\n  const [focusCell] = $getNodeTriplet(focus);\n  const [gridMap, anchorCellMap, focusCellMap] = $computeTableMap(grid, anchorCell, focusCell);\n  const {\n    startRow: anchorStartRow\n  } = anchorCellMap;\n  const {\n    startRow: focusStartRow\n  } = focusCellMap;\n  const focusEndRow = focusStartRow + focusCell.__rowSpan - 1;\n  if (gridMap.length === focusEndRow - anchorStartRow + 1) {\n    // Empty grid\n    grid.remove();\n    return;\n  }\n  const columnCount = gridMap[0].length;\n  const selectedRowCount = anchorCell.__rowSpan;\n  const nextRow = gridMap[focusEndRow + 1];\n  const nextRowNode = grid.getChildAtIndex(focusEndRow + 1);\n  for (let row = focusEndRow; row >= anchorStartRow; row--) {\n    for (let column = columnCount - 1; column >= 0; column--) {\n      const {\n        cell,\n        startRow: cellStartRow,\n        startColumn: cellStartColumn\n      } = gridMap[row][column];\n      if (cellStartColumn !== column) {\n        // Don't repeat work for the same Cell\n        continue;\n      }\n      // Rows overflowing top have to be trimmed\n      if (row === anchorStartRow && cellStartRow < anchorStartRow) {\n        const overflowTop = anchorStartRow - cellStartRow;\n        cell.setRowSpan(cell.__rowSpan - Math.min(selectedRowCount, cell.__rowSpan - overflowTop));\n      }\n      // Rows overflowing bottom have to be trimmed and moved to the next row\n      if (cellStartRow >= anchorStartRow && cellStartRow + cell.__rowSpan - 1 > focusEndRow) {\n        cell.setRowSpan(cell.__rowSpan - (focusEndRow - cellStartRow + 1));\n        if (!(nextRowNode !== null)) {\n          formatDevErrorMessage(`Expected nextRowNode not to be null`);\n        }\n        let insertAfterCell = null;\n        for (let columnIndex = 0; columnIndex < column; columnIndex++) {\n          const currentCellMap = nextRow[columnIndex];\n          const currentCell = currentCellMap.cell;\n          // Checking the cell having startRow as same as nextRow\n          if (currentCellMap.startRow === row + 1) {\n            insertAfterCell = currentCell;\n          }\n          if (currentCell.__colSpan > 1) {\n            columnIndex += currentCell.__colSpan - 1;\n          }\n        }\n        if (insertAfterCell === null) {\n          $insertFirst(nextRowNode, cell);\n        } else {\n          insertAfterCell.insertAfter(cell);\n        }\n      }\n    }\n    const rowNode = grid.getChildAtIndex(row);\n    if (!$isTableRowNode(rowNode)) {\n      formatDevErrorMessage(`Expected TableNode childAtIndex(${String(row)}) to be RowNode`);\n    }\n    rowNode.remove();\n  }\n  if (nextRow !== undefined) {\n    const {\n      cell\n    } = nextRow[0];\n    $moveSelectionToCell(cell);\n  } else {\n    const previousRow = gridMap[anchorStartRow - 1];\n    const {\n      cell\n    } = previousRow[0];\n    $moveSelectionToCell(cell);\n  }\n}\nfunction $deleteTableColumn__EXPERIMENTAL() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const anchor = selection.anchor.getNode();\n  const focus = selection.focus.getNode();\n  const [anchorCell,, grid] = $getNodeTriplet(anchor);\n  const [focusCell] = $getNodeTriplet(focus);\n  const [gridMap, anchorCellMap, focusCellMap] = $computeTableMap(grid, anchorCell, focusCell);\n  const {\n    startColumn: anchorStartColumn\n  } = anchorCellMap;\n  const {\n    startRow: focusStartRow,\n    startColumn: focusStartColumn\n  } = focusCellMap;\n  const startColumn = Math.min(anchorStartColumn, focusStartColumn);\n  const endColumn = Math.max(anchorStartColumn + anchorCell.__colSpan - 1, focusStartColumn + focusCell.__colSpan - 1);\n  const selectedColumnCount = endColumn - startColumn + 1;\n  const columnCount = gridMap[0].length;\n  if (columnCount === endColumn - startColumn + 1) {\n    // Empty grid\n    grid.selectPrevious();\n    grid.remove();\n    return;\n  }\n  const rowCount = gridMap.length;\n  for (let row = 0; row < rowCount; row++) {\n    for (let column = startColumn; column <= endColumn; column++) {\n      const {\n        cell,\n        startColumn: cellStartColumn\n      } = gridMap[row][column];\n      if (cellStartColumn < startColumn) {\n        if (column === startColumn) {\n          const overflowLeft = startColumn - cellStartColumn;\n          // Overflowing left\n          cell.setColSpan(cell.__colSpan -\n          // Possible overflow right too\n          Math.min(selectedColumnCount, cell.__colSpan - overflowLeft));\n        }\n      } else if (cellStartColumn + cell.__colSpan - 1 > endColumn) {\n        if (column === endColumn) {\n          // Overflowing right\n          const inSelectedArea = endColumn - cellStartColumn + 1;\n          cell.setColSpan(cell.__colSpan - inSelectedArea);\n        }\n      } else {\n        cell.remove();\n      }\n    }\n  }\n  const focusRowMap = gridMap[focusStartRow];\n  const nextColumn = anchorStartColumn > focusStartColumn ? focusRowMap[anchorStartColumn + anchorCell.__colSpan] : focusRowMap[focusStartColumn + focusCell.__colSpan];\n  if (nextColumn !== undefined) {\n    const {\n      cell\n    } = nextColumn;\n    $moveSelectionToCell(cell);\n  } else {\n    const previousRow = focusStartColumn < anchorStartColumn ? focusRowMap[focusStartColumn - 1] : focusRowMap[anchorStartColumn - 1];\n    const {\n      cell\n    } = previousRow;\n    $moveSelectionToCell(cell);\n  }\n  const colWidths = grid.getColWidths();\n  if (colWidths) {\n    const newColWidths = [...colWidths];\n    newColWidths.splice(startColumn, selectedColumnCount);\n    grid.setColWidths(newColWidths);\n  }\n}\nfunction $moveSelectionToCell(cell) {\n  const firstDescendant = cell.getFirstDescendant();\n  if (firstDescendant == null) {\n    cell.selectStart();\n  } else {\n    firstDescendant.getParentOrThrow().selectStart();\n  }\n}\nfunction $insertFirst(parent, node) {\n  const firstChild = parent.getFirstChild();\n  if (firstChild !== null) {\n    firstChild.insertBefore(node);\n  } else {\n    parent.append(node);\n  }\n}\nfunction $unmergeCell() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const anchor = selection.anchor.getNode();\n  const [cell, row, grid] = $getNodeTriplet(anchor);\n  const colSpan = cell.__colSpan;\n  const rowSpan = cell.__rowSpan;\n  if (colSpan === 1 && rowSpan === 1) {\n    return;\n  }\n  const [map, cellMap] = $computeTableMap(grid, cell, cell);\n  const {\n    startColumn,\n    startRow\n  } = cellMap;\n  // Create a heuristic for what the style of the unmerged cells should be\n  // based on whether every row or column already had that state before the\n  // unmerge.\n  const baseColStyle = cell.__headerState & TableCellHeaderStates.COLUMN;\n  const colStyles = Array.from({\n    length: colSpan\n  }, (_v, i) => {\n    let colStyle = baseColStyle;\n    for (let rowIdx = 0; colStyle !== 0 && rowIdx < map.length; rowIdx++) {\n      colStyle &= map[rowIdx][i + startColumn].cell.__headerState;\n    }\n    return colStyle;\n  });\n  const baseRowStyle = cell.__headerState & TableCellHeaderStates.ROW;\n  const rowStyles = Array.from({\n    length: rowSpan\n  }, (_v, i) => {\n    let rowStyle = baseRowStyle;\n    for (let colIdx = 0; rowStyle !== 0 && colIdx < map[0].length; colIdx++) {\n      rowStyle &= map[i + startRow][colIdx].cell.__headerState;\n    }\n    return rowStyle;\n  });\n  if (colSpan > 1) {\n    for (let i = 1; i < colSpan; i++) {\n      cell.insertAfter($createTableCellNode(colStyles[i] | rowStyles[0]).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n    }\n    cell.setColSpan(1);\n  }\n  if (rowSpan > 1) {\n    let currentRowNode;\n    for (let i = 1; i < rowSpan; i++) {\n      const currentRow = startRow + i;\n      const currentRowMap = map[currentRow];\n      currentRowNode = (currentRowNode || row).getNextSibling();\n      if (!$isTableRowNode(currentRowNode)) {\n        formatDevErrorMessage(`Expected row next sibling to be a row`);\n      }\n      let insertAfterCell = null;\n      for (let column = 0; column < startColumn; column++) {\n        const currentCellMap = currentRowMap[column];\n        const currentCell = currentCellMap.cell;\n        if (currentCellMap.startRow === currentRow) {\n          insertAfterCell = currentCell;\n        }\n        if (currentCell.__colSpan > 1) {\n          column += currentCell.__colSpan - 1;\n        }\n      }\n      if (insertAfterCell === null) {\n        for (let j = colSpan - 1; j >= 0; j--) {\n          $insertFirst(currentRowNode, $createTableCellNode(colStyles[j] | rowStyles[i]).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n        }\n      } else {\n        for (let j = colSpan - 1; j >= 0; j--) {\n          insertAfterCell.insertAfter($createTableCellNode(colStyles[j] | rowStyles[i]).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n        }\n      }\n    }\n    cell.setRowSpan(1);\n  }\n}\nfunction $computeTableMap(tableNode, cellA, cellB) {\n  const [tableMap, cellAValue, cellBValue] = $computeTableMapSkipCellCheck(tableNode, cellA, cellB);\n  if (!(cellAValue !== null)) {\n    formatDevErrorMessage(`Anchor not found in Table`);\n  }\n  if (!(cellBValue !== null)) {\n    formatDevErrorMessage(`Focus not found in Table`);\n  }\n  return [tableMap, cellAValue, cellBValue];\n}\nfunction $computeTableMapSkipCellCheck(tableNode, cellA, cellB) {\n  const tableMap = [];\n  let cellAValue = null;\n  let cellBValue = null;\n  function getMapRow(i) {\n    let row = tableMap[i];\n    if (row === undefined) {\n      tableMap[i] = row = [];\n    }\n    return row;\n  }\n  const gridChildren = tableNode.getChildren();\n  for (let rowIdx = 0; rowIdx < gridChildren.length; rowIdx++) {\n    const row = gridChildren[rowIdx];\n    if (!$isTableRowNode(row)) {\n      formatDevErrorMessage(`Expected TableNode children to be TableRowNode`);\n    }\n    const startMapRow = getMapRow(rowIdx);\n    for (let cell = row.getFirstChild(), colIdx = 0; cell != null; cell = cell.getNextSibling()) {\n      if (!$isTableCellNode(cell)) {\n        formatDevErrorMessage(`Expected TableRowNode children to be TableCellNode`);\n      } // Skip past any columns that were merged from a higher row\n      while (startMapRow[colIdx] !== undefined) {\n        colIdx++;\n      }\n      const value = {\n        cell,\n        startColumn: colIdx,\n        startRow: rowIdx\n      };\n      const {\n        __rowSpan: rowSpan,\n        __colSpan: colSpan\n      } = cell;\n      for (let j = 0; j < rowSpan; j++) {\n        if (rowIdx + j >= gridChildren.length) {\n          // The table is non-rectangular with a rowSpan\n          // below the last <tr> in the table.\n          // We should probably handle this with a node transform\n          // to ensure that tables are always rectangular but this\n          // will avoid crashes such as #6584\n          // Note that there are probably still latent bugs\n          // regarding colSpan or general cell count mismatches.\n          break;\n        }\n        const mapRow = getMapRow(rowIdx + j);\n        for (let i = 0; i < colSpan; i++) {\n          mapRow[colIdx + i] = value;\n        }\n      }\n      if (cellA !== null && cellAValue === null && cellA.is(cell)) {\n        cellAValue = value;\n      }\n      if (cellB !== null && cellBValue === null && cellB.is(cell)) {\n        cellBValue = value;\n      }\n    }\n  }\n  return [tableMap, cellAValue, cellBValue];\n}\nfunction $getNodeTriplet(source) {\n  let cell;\n  if (source instanceof TableCellNode) {\n    cell = source;\n  } else if ('__type' in source) {\n    const cell_ = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(source, $isTableCellNode);\n    if (!$isTableCellNode(cell_)) {\n      formatDevErrorMessage(`Expected to find a parent TableCellNode`);\n    }\n    cell = cell_;\n  } else {\n    const cell_ = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(source.getNode(), $isTableCellNode);\n    if (!$isTableCellNode(cell_)) {\n      formatDevErrorMessage(`Expected to find a parent TableCellNode`);\n    }\n    cell = cell_;\n  }\n  const row = cell.getParent();\n  if (!$isTableRowNode(row)) {\n    formatDevErrorMessage(`Expected TableCellNode to have a parent TableRowNode`);\n  }\n  const grid = row.getParent();\n  if (!$isTableNode(grid)) {\n    formatDevErrorMessage(`Expected TableRowNode to have a parent TableNode`);\n  }\n  return [cell, row, grid];\n}\nfunction $computeTableCellRectSpans(map, boundary) {\n  const {\n    minColumn,\n    maxColumn,\n    minRow,\n    maxRow\n  } = boundary;\n  let topSpan = 1;\n  let leftSpan = 1;\n  let rightSpan = 1;\n  let bottomSpan = 1;\n  const topRow = map[minRow];\n  const bottomRow = map[maxRow];\n  for (let col = minColumn; col <= maxColumn; col++) {\n    topSpan = Math.max(topSpan, topRow[col].cell.__rowSpan);\n    bottomSpan = Math.max(bottomSpan, bottomRow[col].cell.__rowSpan);\n  }\n  for (let row = minRow; row <= maxRow; row++) {\n    leftSpan = Math.max(leftSpan, map[row][minColumn].cell.__colSpan);\n    rightSpan = Math.max(rightSpan, map[row][maxColumn].cell.__colSpan);\n  }\n  return {\n    bottomSpan,\n    leftSpan,\n    rightSpan,\n    topSpan\n  };\n}\nfunction $computeTableCellRectBoundary(map, cellAMap, cellBMap) {\n  // Initial boundaries based on the anchor and focus cells\n  let minColumn = Math.min(cellAMap.startColumn, cellBMap.startColumn);\n  let minRow = Math.min(cellAMap.startRow, cellBMap.startRow);\n  let maxColumn = Math.max(cellAMap.startColumn + cellAMap.cell.__colSpan - 1, cellBMap.startColumn + cellBMap.cell.__colSpan - 1);\n  let maxRow = Math.max(cellAMap.startRow + cellAMap.cell.__rowSpan - 1, cellBMap.startRow + cellBMap.cell.__rowSpan - 1);\n\n  // Keep expanding until we have a complete rectangle\n  let hasChanges;\n  do {\n    hasChanges = false;\n\n    // Check all cells in the table\n    for (let row = 0; row < map.length; row++) {\n      for (let col = 0; col < map[0].length; col++) {\n        const cell = map[row][col];\n        if (!cell) {\n          continue;\n        }\n        const cellEndCol = cell.startColumn + cell.cell.__colSpan - 1;\n        const cellEndRow = cell.startRow + cell.cell.__rowSpan - 1;\n\n        // Check if this cell intersects with our current selection rectangle\n        const intersectsHorizontally = cell.startColumn <= maxColumn && cellEndCol >= minColumn;\n        const intersectsVertically = cell.startRow <= maxRow && cellEndRow >= minRow;\n\n        // If the cell intersects either horizontally or vertically\n        if (intersectsHorizontally && intersectsVertically) {\n          // Expand boundaries to include this cell completely\n          const newMinColumn = Math.min(minColumn, cell.startColumn);\n          const newMaxColumn = Math.max(maxColumn, cellEndCol);\n          const newMinRow = Math.min(minRow, cell.startRow);\n          const newMaxRow = Math.max(maxRow, cellEndRow);\n\n          // Check if boundaries changed\n          if (newMinColumn !== minColumn || newMaxColumn !== maxColumn || newMinRow !== minRow || newMaxRow !== maxRow) {\n            minColumn = newMinColumn;\n            maxColumn = newMaxColumn;\n            minRow = newMinRow;\n            maxRow = newMaxRow;\n            hasChanges = true;\n          }\n        }\n      }\n    }\n  } while (hasChanges);\n  return {\n    maxColumn,\n    maxRow,\n    minColumn,\n    minRow\n  };\n}\nfunction $getTableCellNodeRect(tableCellNode) {\n  const [cellNode,, gridNode] = $getNodeTriplet(tableCellNode);\n  const rows = gridNode.getChildren();\n  const rowCount = rows.length;\n  const columnCount = rows[0].getChildren().length;\n\n  // Create a matrix of the same size as the table to track the position of each cell\n  const cellMatrix = new Array(rowCount);\n  for (let i = 0; i < rowCount; i++) {\n    cellMatrix[i] = new Array(columnCount);\n  }\n  for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {\n    const row = rows[rowIndex];\n    const cells = row.getChildren();\n    let columnIndex = 0;\n    for (let cellIndex = 0; cellIndex < cells.length; cellIndex++) {\n      // Find the next available position in the matrix, skip the position of merged cells\n      while (cellMatrix[rowIndex][columnIndex]) {\n        columnIndex++;\n      }\n      const cell = cells[cellIndex];\n      const rowSpan = cell.__rowSpan || 1;\n      const colSpan = cell.__colSpan || 1;\n\n      // Put the cell into the corresponding position in the matrix\n      for (let i = 0; i < rowSpan; i++) {\n        for (let j = 0; j < colSpan; j++) {\n          cellMatrix[rowIndex + i][columnIndex + j] = cell;\n        }\n      }\n\n      // Return to the original index, row span and column span of the cell.\n      if (cellNode === cell) {\n        return {\n          colSpan,\n          columnIndex,\n          rowIndex,\n          rowSpan\n        };\n      }\n      columnIndex += colSpan;\n    }\n  }\n  return null;\n}\n\nfunction $getCellNodes(tableSelection) {\n  const [[anchorNode, anchorCell, anchorRow, anchorTable], [focusNode, focusCell, focusRow, focusTable]] = ['anchor', 'focus'].map(k => {\n    const node = tableSelection[k].getNode();\n    const cellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, $isTableCellNode);\n    if (!$isTableCellNode(cellNode)) {\n      formatDevErrorMessage(`Expected TableSelection ${k} to be (or a child of) TableCellNode, got key ${node.getKey()} of type ${node.getType()}`);\n    }\n    const rowNode = cellNode.getParent();\n    if (!$isTableRowNode(rowNode)) {\n      formatDevErrorMessage(`Expected TableSelection ${k} cell parent to be a TableRowNode`);\n    }\n    const tableNode = rowNode.getParent();\n    if (!$isTableNode(tableNode)) {\n      formatDevErrorMessage(`Expected TableSelection ${k} row parent to be a TableNode`);\n    }\n    return [node, cellNode, rowNode, tableNode];\n  });\n  // TODO: nested tables may violate this\n  if (!anchorTable.is(focusTable)) {\n    formatDevErrorMessage(`Expected TableSelection anchor and focus to be in the same table`);\n  }\n  return {\n    anchorCell,\n    anchorNode,\n    anchorRow,\n    anchorTable,\n    focusCell,\n    focusNode,\n    focusRow,\n    focusTable\n  };\n}\nclass TableSelection {\n  constructor(tableKey, anchor, focus) {\n    this.anchor = anchor;\n    this.focus = focus;\n    anchor._selection = this;\n    focus._selection = this;\n    this._cachedNodes = null;\n    this.dirty = false;\n    this.tableKey = tableKey;\n  }\n  getStartEndPoints() {\n    return [this.anchor, this.focus];\n  }\n\n  /**\n   * {@link $createTableSelection} unfortunately makes it very easy to create\n   * nonsense selections, so we have a method to see if the selection probably\n   * makes sense.\n   *\n   * @returns true if the TableSelection is (probably) valid\n   */\n  isValid() {\n    return this.tableKey !== 'root' && this.anchor.key !== 'root' && this.anchor.type === 'element' && this.focus.key !== 'root' && this.focus.type === 'element';\n  }\n\n  /**\n   * Returns whether the Selection is \"backwards\", meaning the focus\n   * logically precedes the anchor in the EditorState.\n   * @returns true if the Selection is backwards, false otherwise.\n   */\n  isBackward() {\n    return this.focus.isBefore(this.anchor);\n  }\n  getCachedNodes() {\n    return this._cachedNodes;\n  }\n  setCachedNodes(nodes) {\n    this._cachedNodes = nodes;\n  }\n  is(selection) {\n    return $isTableSelection(selection) && this.tableKey === selection.tableKey && this.anchor.is(selection.anchor) && this.focus.is(selection.focus);\n  }\n  set(tableKey, anchorCellKey, focusCellKey) {\n    // note: closure compiler's acorn does not support ||=\n    this.dirty = this.dirty || tableKey !== this.tableKey || anchorCellKey !== this.anchor.key || focusCellKey !== this.focus.key;\n    this.tableKey = tableKey;\n    this.anchor.key = anchorCellKey;\n    this.focus.key = focusCellKey;\n    this._cachedNodes = null;\n  }\n  clone() {\n    return new TableSelection(this.tableKey, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createPoint)(this.anchor.key, this.anchor.offset, this.anchor.type), (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createPoint)(this.focus.key, this.focus.offset, this.focus.type));\n  }\n  isCollapsed() {\n    return false;\n  }\n  extract() {\n    return this.getNodes();\n  }\n  insertRawText(text) {\n    // Do nothing?\n  }\n  insertText() {\n    // Do nothing?\n  }\n\n  /**\n   * Returns whether the provided TextFormatType is present on the Selection.\n   * This will be true if any paragraph in table cells has the specified format.\n   *\n   * @param type the TextFormatType to check for.\n   * @returns true if the provided format is currently toggled on on the Selection, false otherwise.\n   */\n  hasFormat(type) {\n    let format = 0;\n    const cellNodes = this.getNodes().filter($isTableCellNode);\n    cellNodes.forEach(cellNode => {\n      const paragraph = cellNode.getFirstChild();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isParagraphNode)(paragraph)) {\n        format |= paragraph.getTextFormat();\n      }\n    });\n    const formatFlag = lexical__WEBPACK_IMPORTED_MODULE_0__.TEXT_TYPE_TO_FORMAT[type];\n    return (format & formatFlag) !== 0;\n  }\n  insertNodes(nodes) {\n    const focusNode = this.focus.getNode();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(focusNode)) {\n      formatDevErrorMessage(`Expected TableSelection focus to be an ElementNode`);\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeSelection__EXPERIMENTAL)(focusNode.select(0, focusNode.getChildrenSize()));\n    selection.insertNodes(nodes);\n  }\n\n  // TODO Deprecate this method. It's confusing when used with colspan|rowspan\n  getShape() {\n    const {\n      anchorCell,\n      focusCell\n    } = $getCellNodes(this);\n    const anchorCellNodeRect = $getTableCellNodeRect(anchorCell);\n    if (!(anchorCellNodeRect !== null)) {\n      formatDevErrorMessage(`getCellRect: expected to find AnchorNode`);\n    }\n    const focusCellNodeRect = $getTableCellNodeRect(focusCell);\n    if (!(focusCellNodeRect !== null)) {\n      formatDevErrorMessage(`getCellRect: expected to find focusCellNode`);\n    }\n    const startX = Math.min(anchorCellNodeRect.columnIndex, focusCellNodeRect.columnIndex);\n    const stopX = Math.max(anchorCellNodeRect.columnIndex + anchorCellNodeRect.colSpan - 1, focusCellNodeRect.columnIndex + focusCellNodeRect.colSpan - 1);\n    const startY = Math.min(anchorCellNodeRect.rowIndex, focusCellNodeRect.rowIndex);\n    const stopY = Math.max(anchorCellNodeRect.rowIndex + anchorCellNodeRect.rowSpan - 1, focusCellNodeRect.rowIndex + focusCellNodeRect.rowSpan - 1);\n    return {\n      fromX: Math.min(startX, stopX),\n      fromY: Math.min(startY, stopY),\n      toX: Math.max(startX, stopX),\n      toY: Math.max(startY, stopY)\n    };\n  }\n  getNodes() {\n    if (!this.isValid()) {\n      return [];\n    }\n    const cachedNodes = this._cachedNodes;\n    if (cachedNodes !== null) {\n      return cachedNodes;\n    }\n    const {\n      anchorTable: tableNode,\n      anchorCell,\n      focusCell\n    } = $getCellNodes(this);\n    const focusCellGrid = focusCell.getParents()[1];\n    if (focusCellGrid !== tableNode) {\n      if (!tableNode.isParentOf(focusCell)) {\n        // focus is on higher Grid level than anchor\n        const gridParent = tableNode.getParent();\n        if (!(gridParent != null)) {\n          formatDevErrorMessage(`Expected gridParent to have a parent`);\n        }\n        this.set(this.tableKey, gridParent.getKey(), focusCell.getKey());\n      } else {\n        // anchor is on higher Grid level than focus\n        const focusCellParent = focusCellGrid.getParent();\n        if (!(focusCellParent != null)) {\n          formatDevErrorMessage(`Expected focusCellParent to have a parent`);\n        }\n        this.set(this.tableKey, focusCell.getKey(), focusCellParent.getKey());\n      }\n      return this.getNodes();\n    }\n\n    // TODO Mapping the whole Grid every time not efficient. We need to compute the entire state only\n    // once (on load) and iterate on it as updates occur. However, to do this we need to have the\n    // ability to store a state. Killing TableSelection and moving the logic to the plugin would make\n    // this possible.\n    const [map, cellAMap, cellBMap] = $computeTableMap(tableNode, anchorCell, focusCell);\n    const {\n      minColumn,\n      maxColumn,\n      minRow,\n      maxRow\n    } = $computeTableCellRectBoundary(map, cellAMap, cellBMap);\n\n    // We use a Map here because merged cells in the grid would otherwise\n    // show up multiple times in the nodes array\n    const nodeMap = new Map([[tableNode.getKey(), tableNode]]);\n    let lastRow = null;\n    for (let i = minRow; i <= maxRow; i++) {\n      for (let j = minColumn; j <= maxColumn; j++) {\n        const {\n          cell\n        } = map[i][j];\n        const currentRow = cell.getParent();\n        if (!$isTableRowNode(currentRow)) {\n          formatDevErrorMessage(`Expected TableCellNode parent to be a TableRowNode`);\n        }\n        if (currentRow !== lastRow) {\n          nodeMap.set(currentRow.getKey(), currentRow);\n          lastRow = currentRow;\n        }\n        if (!nodeMap.has(cell.getKey())) {\n          $visitRecursively(cell, childNode => {\n            nodeMap.set(childNode.getKey(), childNode);\n          });\n        }\n      }\n    }\n    const nodes = Array.from(nodeMap.values());\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isCurrentlyReadOnlyMode)()) {\n      this._cachedNodes = nodes;\n    }\n    return nodes;\n  }\n  getTextContent() {\n    const nodes = this.getNodes().filter(node => $isTableCellNode(node));\n    let textContent = '';\n    for (let i = 0; i < nodes.length; i++) {\n      const node = nodes[i];\n      const row = node.__parent;\n      const nextRow = (nodes[i + 1] || {}).__parent;\n      textContent += node.getTextContent() + (nextRow !== row ? '\\n' : '\\t');\n    }\n    return textContent;\n  }\n}\nfunction $isTableSelection(x) {\n  return x instanceof TableSelection;\n}\nfunction $createTableSelection() {\n  // TODO this is a suboptimal design, it doesn't make sense to have\n  // a table selection that isn't associated with a table. This\n  // constructor should have required argumnets and in true we\n  // should check that they point to a table and are element points to\n  // cell nodes of that table.\n  const anchor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createPoint)('root', 0, 'element');\n  const focus = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createPoint)('root', 0, 'element');\n  return new TableSelection('root', anchor, focus);\n}\nfunction $createTableSelectionFrom(tableNode, anchorCell, focusCell) {\n  const tableNodeKey = tableNode.getKey();\n  const anchorCellKey = anchorCell.getKey();\n  const focusCellKey = focusCell.getKey();\n  {\n    if (!tableNode.isAttached()) {\n      formatDevErrorMessage(`$createTableSelectionFrom: tableNode ${tableNodeKey} is not attached`);\n    }\n    if (!tableNode.is($findTableNode(anchorCell))) {\n      formatDevErrorMessage(`$createTableSelectionFrom: anchorCell ${anchorCellKey} is not in table ${tableNodeKey}`);\n    }\n    if (!tableNode.is($findTableNode(focusCell))) {\n      formatDevErrorMessage(`$createTableSelectionFrom: focusCell ${focusCellKey} is not in table ${tableNodeKey}`);\n    } // TODO: Check for rectangular grid\n  }\n  const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  const nextSelection = $isTableSelection(prevSelection) ? prevSelection.clone() : $createTableSelection();\n  nextSelection.set(tableNode.getKey(), anchorCell.getKey(), focusCell.getKey());\n  return nextSelection;\n}\n\n/**\n * Depth first visitor\n * @param node The starting node\n * @param $visit The function to call for each node. If the function returns false, then children of this node will not be explored\n */\nfunction $visitRecursively(node, $visit) {\n  const stack = [[node]];\n  for (let currentArray = stack.at(-1); currentArray !== undefined && stack.length > 0; currentArray = stack.at(-1)) {\n    const currentNode = currentArray.pop();\n    if (currentNode === undefined) {\n      stack.pop();\n    } else if ($visit(currentNode) !== false && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n      stack.push(currentNode.getChildren());\n    }\n  }\n}\n\nfunction $getTableAndElementByKey(tableNodeKey, editor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getEditor)()) {\n  const tableNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(tableNodeKey);\n  if (!$isTableNode(tableNode)) {\n    formatDevErrorMessage(`TableObserver: Expected tableNodeKey ${tableNodeKey} to be a TableNode`);\n  }\n  const tableElement = getTableElement(tableNode, editor.getElementByKey(tableNodeKey));\n  if (!(tableElement !== null)) {\n    formatDevErrorMessage(`TableObserver: Expected to find TableElement in DOM for key ${tableNodeKey}`);\n  }\n  return {\n    tableElement,\n    tableNode\n  };\n}\nclass TableObserver {\n  constructor(editor, tableNodeKey) {\n    this.isHighlightingCells = false;\n    this.anchorX = -1;\n    this.anchorY = -1;\n    this.focusX = -1;\n    this.focusY = -1;\n    this.listenersToRemove = new Set();\n    this.tableNodeKey = tableNodeKey;\n    this.editor = editor;\n    this.table = {\n      columns: 0,\n      domRows: [],\n      rows: 0\n    };\n    this.tableSelection = null;\n    this.anchorCellNodeKey = null;\n    this.focusCellNodeKey = null;\n    this.anchorCell = null;\n    this.focusCell = null;\n    this.hasHijackedSelectionStyles = false;\n    this.isSelecting = false;\n    this.pointerType = null;\n    this.shouldCheckSelection = false;\n    this.abortController = new AbortController();\n    this.listenerOptions = {\n      signal: this.abortController.signal\n    };\n    this.nextFocus = null;\n    this.trackTable();\n  }\n  getTable() {\n    return this.table;\n  }\n  removeListeners() {\n    this.abortController.abort('removeListeners');\n    Array.from(this.listenersToRemove).forEach(removeListener => removeListener());\n    this.listenersToRemove.clear();\n  }\n  $lookup() {\n    return $getTableAndElementByKey(this.tableNodeKey, this.editor);\n  }\n  trackTable() {\n    const observer = new MutationObserver(records => {\n      this.editor.getEditorState().read(() => {\n        let gridNeedsRedraw = false;\n        for (let i = 0; i < records.length; i++) {\n          const record = records[i];\n          const target = record.target;\n          const nodeName = target.nodeName;\n          if (nodeName === 'TABLE' || nodeName === 'TBODY' || nodeName === 'THEAD' || nodeName === 'TR') {\n            gridNeedsRedraw = true;\n            break;\n          }\n        }\n        if (!gridNeedsRedraw) {\n          return;\n        }\n        const {\n          tableNode,\n          tableElement\n        } = this.$lookup();\n        this.table = getTable(tableNode, tableElement);\n      }, {\n        editor: this.editor\n      });\n    });\n    this.editor.getEditorState().read(() => {\n      const {\n        tableNode,\n        tableElement\n      } = this.$lookup();\n      this.table = getTable(tableNode, tableElement);\n      observer.observe(tableElement, {\n        attributes: true,\n        childList: true,\n        subtree: true\n      });\n    }, {\n      editor: this.editor\n    });\n  }\n  $clearHighlight() {\n    const editor = this.editor;\n    this.isHighlightingCells = false;\n    this.anchorX = -1;\n    this.anchorY = -1;\n    this.focusX = -1;\n    this.focusY = -1;\n    this.tableSelection = null;\n    this.anchorCellNodeKey = null;\n    this.focusCellNodeKey = null;\n    this.anchorCell = null;\n    this.focusCell = null;\n    this.hasHijackedSelectionStyles = false;\n    this.$enableHighlightStyle();\n    const {\n      tableNode,\n      tableElement\n    } = this.$lookup();\n    const grid = getTable(tableNode, tableElement);\n    $updateDOMForSelection(editor, grid, null);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)() !== null) {\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(null);\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n    }\n  }\n  $enableHighlightStyle() {\n    const editor = this.editor;\n    const {\n      tableElement\n    } = this.$lookup();\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(tableElement, editor._config.theme.tableSelection);\n    tableElement.classList.remove('disable-selection');\n    this.hasHijackedSelectionStyles = false;\n  }\n  $disableHighlightStyle() {\n    const {\n      tableElement\n    } = this.$lookup();\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(tableElement, this.editor._config.theme.tableSelection);\n    this.hasHijackedSelectionStyles = true;\n  }\n  $updateTableTableSelection(selection) {\n    if (selection !== null) {\n      if (!(selection.tableKey === this.tableNodeKey)) {\n        formatDevErrorMessage(`TableObserver.$updateTableTableSelection: selection.tableKey !== this.tableNodeKey ('${selection.tableKey}' !== '${this.tableNodeKey}')`);\n      }\n      const editor = this.editor;\n      this.tableSelection = selection;\n      this.isHighlightingCells = true;\n      this.$disableHighlightStyle();\n      this.updateDOMSelection();\n      $updateDOMForSelection(editor, this.table, this.tableSelection);\n    } else {\n      this.$clearHighlight();\n    }\n  }\n\n  /**\n   * @internal\n   * Firefox has a strange behavior where pressing the down arrow key from\n   * above the table will move the caret after the table and then lexical\n   * will select the last cell instead of the first.\n   * We do still want to let the browser handle caret movement but we will\n   * use this property to \"tag\" the update so that we can recheck the\n   * selection after the event is processed.\n   */\n  setShouldCheckSelection() {\n    this.shouldCheckSelection = true;\n  }\n  /**\n   * @internal\n   */\n  getAndClearShouldCheckSelection() {\n    if (this.shouldCheckSelection) {\n      this.shouldCheckSelection = false;\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * @internal\n   * When handling mousemove events we track what the focus cell should be, but\n   * the DOM selection may end up somewhere else entirely. We don't have an elegant\n   * way to handle this after the DOM selection has been resolved in a\n   * SELECTION_CHANGE_COMMAND callback.\n   */\n  setNextFocus(nextFocus) {\n    this.nextFocus = nextFocus;\n  }\n\n  /** @internal */\n  getAndClearNextFocus() {\n    const {\n      nextFocus\n    } = this;\n    if (nextFocus !== null) {\n      this.nextFocus = null;\n    }\n    return nextFocus;\n  }\n\n  /** @internal */\n  updateDOMSelection() {\n    if (this.anchorCell !== null && this.focusCell !== null) {\n      const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(this.editor._window);\n      // We are not using a native selection for tables, and if we\n      // set one then the reconciler will undo it.\n      // TODO - it would make sense to have one so that native\n      //        copy/paste worked. Right now we have to emulate with\n      //        keyboard events but it won't fire if trigged from the menu\n      if (domSelection && domSelection.rangeCount > 0) {\n        domSelection.removeAllRanges();\n      }\n    }\n  }\n  $setFocusCellForSelection(cell, ignoreStart = false) {\n    const editor = this.editor;\n    const {\n      tableNode\n    } = this.$lookup();\n    const cellX = cell.x;\n    const cellY = cell.y;\n    this.focusCell = cell;\n    if (!this.isHighlightingCells && (this.anchorX !== cellX || this.anchorY !== cellY || ignoreStart)) {\n      this.isHighlightingCells = true;\n      this.$disableHighlightStyle();\n    } else if (cellX === this.focusX && cellY === this.focusY) {\n      return false;\n    }\n    this.focusX = cellX;\n    this.focusY = cellY;\n    if (this.isHighlightingCells) {\n      const focusTableCellNode = $getNearestTableCellInTableFromDOMNode(tableNode, cell.elem);\n      if (this.tableSelection != null && this.anchorCellNodeKey != null && focusTableCellNode !== null) {\n        this.focusCellNodeKey = focusTableCellNode.getKey();\n        this.tableSelection = $createTableSelectionFrom(tableNode, this.$getAnchorTableCellOrThrow(), focusTableCellNode);\n        (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(this.tableSelection);\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n        $updateDOMForSelection(editor, this.table, this.tableSelection);\n        return true;\n      }\n    }\n    return false;\n  }\n  $getAnchorTableCell() {\n    return this.anchorCellNodeKey ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(this.anchorCellNodeKey) : null;\n  }\n  $getAnchorTableCellOrThrow() {\n    const anchorTableCell = this.$getAnchorTableCell();\n    if (!(anchorTableCell !== null)) {\n      formatDevErrorMessage(`TableObserver anchorTableCell is null`);\n    }\n    return anchorTableCell;\n  }\n  $getFocusTableCell() {\n    return this.focusCellNodeKey ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(this.focusCellNodeKey) : null;\n  }\n  $getFocusTableCellOrThrow() {\n    const focusTableCell = this.$getFocusTableCell();\n    if (!(focusTableCell !== null)) {\n      formatDevErrorMessage(`TableObserver focusTableCell is null`);\n    }\n    return focusTableCell;\n  }\n  $setAnchorCellForSelection(cell) {\n    this.isHighlightingCells = false;\n    this.anchorCell = cell;\n    this.anchorX = cell.x;\n    this.anchorY = cell.y;\n    const {\n      tableNode\n    } = this.$lookup();\n    const anchorTableCellNode = $getNearestTableCellInTableFromDOMNode(tableNode, cell.elem);\n    if (anchorTableCellNode !== null) {\n      const anchorNodeKey = anchorTableCellNode.getKey();\n      this.tableSelection = this.tableSelection != null ? this.tableSelection.clone() : $createTableSelection();\n      this.anchorCellNodeKey = anchorNodeKey;\n    }\n  }\n  $formatCells(type) {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isTableSelection(selection)) {\n      formatDevErrorMessage(`Expected Table selection`);\n    }\n    const formatSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n    const anchor = formatSelection.anchor;\n    const focus = formatSelection.focus;\n    const cellNodes = selection.getNodes().filter($isTableCellNode);\n    if (!(cellNodes.length > 0)) {\n      formatDevErrorMessage(`No table cells present`);\n    }\n    const paragraph = cellNodes[0].getFirstChild();\n    const alignFormatWith = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isParagraphNode)(paragraph) ? paragraph.getFormatFlags(type, null) : null;\n    cellNodes.forEach(cellNode => {\n      anchor.set(cellNode.getKey(), 0, 'element');\n      focus.set(cellNode.getKey(), cellNode.getChildrenSize(), 'element');\n      formatSelection.formatText(type, alignFormatWith);\n    });\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(selection);\n    this.editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n  }\n  $clearText() {\n    const {\n      editor\n    } = this;\n    const tableNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(this.tableNodeKey);\n    if (!$isTableNode(tableNode)) {\n      throw new Error('Expected TableNode.');\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isTableSelection(selection)) {\n      formatDevErrorMessage(`Expected TableSelection`);\n    }\n    const selectedNodes = selection.getNodes().filter($isTableCellNode);\n    if (selectedNodes.length === this.table.columns * this.table.rows) {\n      tableNode.selectPrevious();\n      const parent = tableNode.getParent();\n      // Delete entire table\n      tableNode.remove();\n      // Handle case when table was the only node\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(parent) && parent.isEmpty()) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, undefined);\n      }\n      return;\n    }\n    selectedNodes.forEach(cellNode => {\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(cellNode)) {\n        const paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n        const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)();\n        paragraphNode.append(textNode);\n        cellNode.append(paragraphNode);\n        cellNode.getChildren().forEach(child => {\n          if (child !== paragraphNode) {\n            child.remove();\n          }\n        });\n      }\n    });\n    $updateDOMForSelection(editor, this.table, null);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(null);\n    editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n  }\n}\n\nconst LEXICAL_ELEMENT_KEY = '__lexicalTableSelection';\nconst isPointerDownOnEvent = event => {\n  return (event.buttons & 1) === 1;\n};\nfunction isHTMLTableElement(el) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(el) && el.nodeName === 'TABLE';\n}\nfunction getTableElement(tableNode, dom) {\n  if (!dom) {\n    return dom;\n  }\n  const element = isHTMLTableElement(dom) ? dom : tableNode.getDOMSlot(dom).element;\n  if (!(element.nodeName === 'TABLE')) {\n    formatDevErrorMessage(`getTableElement: Expecting table in as DOM node for TableNode, not ${dom.nodeName}`);\n  }\n  return element;\n}\nfunction getEditorWindow(editor) {\n  return editor._window;\n}\nfunction $findParentTableCellNodeInTable(tableNode, node) {\n  for (let currentNode = node, lastTableCellNode = null; currentNode !== null; currentNode = currentNode.getParent()) {\n    if (tableNode.is(currentNode)) {\n      return lastTableCellNode;\n    } else if ($isTableCellNode(currentNode)) {\n      lastTableCellNode = currentNode;\n    }\n  }\n  return null;\n}\nconst ARROW_KEY_COMMANDS_WITH_DIRECTION = [[lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_DOWN_COMMAND, 'down'], [lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_UP_COMMAND, 'up'], [lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_LEFT_COMMAND, 'backward'], [lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_RIGHT_COMMAND, 'forward']];\nconst DELETE_TEXT_COMMANDS = [lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_WORD_COMMAND, lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_LINE_COMMAND, lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND];\nconst DELETE_KEY_COMMANDS = [lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_BACKSPACE_COMMAND, lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_DELETE_COMMAND];\nfunction applyTableHandlers(tableNode, element, editor, hasTabHandler) {\n  const rootElement = editor.getRootElement();\n  const editorWindow = getEditorWindow(editor);\n  if (!(rootElement !== null && editorWindow !== null)) {\n    formatDevErrorMessage(`applyTableHandlers: editor has no root element set`);\n  }\n  const tableObserver = new TableObserver(editor, tableNode.getKey());\n  const tableElement = getTableElement(tableNode, element);\n  attachTableObserverToTableElement(tableElement, tableObserver);\n  tableObserver.listenersToRemove.add(() => detatchTableObserverFromTableElement(tableElement, tableObserver));\n  const createPointerHandlers = () => {\n    if (tableObserver.isSelecting) {\n      return;\n    }\n    const onPointerUp = () => {\n      tableObserver.isSelecting = false;\n      editorWindow.removeEventListener('pointerup', onPointerUp);\n      editorWindow.removeEventListener('pointermove', onPointerMove);\n    };\n    const onPointerMove = moveEvent => {\n      if (!isPointerDownOnEvent(moveEvent) && tableObserver.isSelecting) {\n        tableObserver.isSelecting = false;\n        editorWindow.removeEventListener('pointerup', onPointerUp);\n        editorWindow.removeEventListener('pointermove', onPointerMove);\n        return;\n      }\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(moveEvent.target)) {\n        return;\n      }\n      let focusCell = null;\n      // In firefox the moveEvent.target may be captured so we must always\n      // consult the coordinates #7245\n      const override = !(IS_FIREFOX || tableElement.contains(moveEvent.target));\n      if (override) {\n        focusCell = getDOMCellInTableFromTarget(tableElement, moveEvent.target);\n      } else {\n        for (const el of document.elementsFromPoint(moveEvent.clientX, moveEvent.clientY)) {\n          focusCell = getDOMCellInTableFromTarget(tableElement, el);\n          if (focusCell) {\n            break;\n          }\n        }\n      }\n      if (focusCell && (tableObserver.focusCell === null || focusCell.elem !== tableObserver.focusCell.elem)) {\n        tableObserver.setNextFocus({\n          focusCell,\n          override\n        });\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n      }\n    };\n    tableObserver.isSelecting = true;\n    editorWindow.addEventListener('pointerup', onPointerUp, tableObserver.listenerOptions);\n    editorWindow.addEventListener('pointermove', onPointerMove, tableObserver.listenerOptions);\n  };\n  const onPointerDown = event => {\n    tableObserver.pointerType = event.pointerType;\n    if (event.button !== 0 || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target) || !editorWindow) {\n      return;\n    }\n    const targetCell = getDOMCellFromTarget(event.target);\n    if (targetCell !== null) {\n      editor.update(() => {\n        const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n        // We can't trust Firefox to do the right thing with the selection and\n        // we don't have a proper state machine to do this \"correctly\" but\n        // if we go ahead and make the table selection now it will work\n        if (IS_FIREFOX && event.shiftKey && $isSelectionInTable(prevSelection, tableNode) && ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) || $isTableSelection(prevSelection))) {\n          const prevAnchorNode = prevSelection.anchor.getNode();\n          const prevAnchorCell = $findParentTableCellNodeInTable(tableNode, prevSelection.anchor.getNode());\n          if (prevAnchorCell) {\n            tableObserver.$setAnchorCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, prevAnchorCell));\n            tableObserver.$setFocusCellForSelection(targetCell);\n            stopEvent(event);\n          } else {\n            const newSelection = tableNode.isBefore(prevAnchorNode) ? tableNode.selectStart() : tableNode.selectEnd();\n            newSelection.anchor.set(prevSelection.anchor.key, prevSelection.anchor.offset, prevSelection.anchor.type);\n          }\n        } else {\n          tableObserver.$setAnchorCellForSelection(targetCell);\n        }\n      });\n    }\n    createPointerHandlers();\n  };\n  tableElement.addEventListener('pointerdown', onPointerDown, tableObserver.listenerOptions);\n  tableObserver.listenersToRemove.add(() => {\n    tableElement.removeEventListener('pointerdown', onPointerDown);\n  });\n  const onTripleClick = event => {\n    if (event.detail >= 3 && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target)) {\n      const targetCell = getDOMCellFromTarget(event.target);\n      if (targetCell !== null) {\n        event.preventDefault();\n      }\n    }\n  };\n  tableElement.addEventListener('mousedown', onTripleClick, tableObserver.listenerOptions);\n  tableObserver.listenersToRemove.add(() => {\n    tableElement.removeEventListener('mousedown', onTripleClick);\n  });\n\n  // Clear selection when clicking outside of dom.\n  const pointerDownCallback = event => {\n    const target = event.target;\n    if (event.button !== 0 || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(target)) {\n      return;\n    }\n    editor.update(() => {\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if ($isTableSelection(selection) && selection.tableKey === tableObserver.tableNodeKey && rootElement.contains(target)) {\n        tableObserver.$clearHighlight();\n      }\n    });\n  };\n  editorWindow.addEventListener('pointerdown', pointerDownCallback, tableObserver.listenerOptions);\n  tableObserver.listenersToRemove.add(() => {\n    editorWindow.removeEventListener('pointerdown', pointerDownCallback);\n  });\n  for (const [command, direction] of ARROW_KEY_COMMANDS_WITH_DIRECTION) {\n    tableObserver.listenersToRemove.add(editor.registerCommand(command, event => $handleArrowKey(editor, event, direction, tableNode, tableObserver), lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_HIGH));\n  }\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ESCAPE_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ($isTableSelection(selection)) {\n      const focusCellNode = $findParentTableCellNodeInTable(tableNode, selection.focus.getNode());\n      if (focusCellNode !== null) {\n        stopEvent(event);\n        focusCellNode.selectEnd();\n        return true;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_HIGH));\n  const deleteTextHandler = command => () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    if ($isTableSelection(selection)) {\n      tableObserver.$clearText();\n      return true;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const tableCellNode = $findParentTableCellNodeInTable(tableNode, selection.anchor.getNode());\n      if (!$isTableCellNode(tableCellNode)) {\n        return false;\n      }\n      const anchorNode = selection.anchor.getNode();\n      const focusNode = selection.focus.getNode();\n      const isAnchorInside = tableNode.isParentOf(anchorNode);\n      const isFocusInside = tableNode.isParentOf(focusNode);\n      const selectionContainsPartialTable = isAnchorInside && !isFocusInside || isFocusInside && !isAnchorInside;\n      if (selectionContainsPartialTable) {\n        tableObserver.$clearText();\n        return true;\n      }\n      const nearestElementNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(n));\n      const topLevelCellElementNode = nearestElementNode && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(nearestElementNode, n => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(n) && $isTableCellNode(n.getParent()));\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(topLevelCellElementNode) || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(nearestElementNode)) {\n        return false;\n      }\n      if (command === lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_LINE_COMMAND && topLevelCellElementNode.getPreviousSibling() === null) {\n        // TODO: Fix Delete Line in Table Cells.\n        return true;\n      }\n    }\n    return false;\n  };\n  for (const command of DELETE_TEXT_COMMANDS) {\n    tableObserver.listenersToRemove.add(editor.registerCommand(command, deleteTextHandler(command), lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  }\n  const $deleteCellHandler = event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!($isTableSelection(selection) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection))) {\n      return false;\n    }\n\n    // If the selection is inside the table but should remove the whole table\n    // we expand the selection so that both the anchor and focus are outside\n    // the table and the editor's command listener will handle the delete\n    const isAnchorInside = tableNode.isParentOf(selection.anchor.getNode());\n    const isFocusInside = tableNode.isParentOf(selection.focus.getNode());\n    if (isAnchorInside !== isFocusInside) {\n      const tablePoint = isAnchorInside ? 'anchor' : 'focus';\n      const outerPoint = isAnchorInside ? 'focus' : 'anchor';\n      // Preserve the outer point\n      const {\n        key,\n        offset,\n        type\n      } = selection[outerPoint];\n      // Expand the selection around the table\n      const newSelection = tableNode[selection[tablePoint].isBefore(selection[outerPoint]) ? 'selectPrevious' : 'selectNext']();\n      // Restore the outer point of the selection\n      newSelection[outerPoint].set(key, offset, type);\n      // Let the base implementation handle the rest\n      return false;\n    }\n    if (!$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    if ($isTableSelection(selection)) {\n      if (event) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n      tableObserver.$clearText();\n      return true;\n    }\n    return false;\n  };\n  for (const command of DELETE_KEY_COMMANDS) {\n    tableObserver.listenersToRemove.add(editor.registerCommand(command, $deleteCellHandler, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  }\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CUT_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (selection) {\n      if (!($isTableSelection(selection) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection))) {\n        return false;\n      }\n      // Copying to the clipboard is async so we must capture the data\n      // before we delete it\n      void (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.copyToClipboard)(editor, (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent) ? event : null, (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.$getClipboardDataFromSelection)(selection));\n      const intercepted = $deleteCellHandler(event);\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n        selection.removeText();\n        return true;\n      }\n      return intercepted;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_TEXT_COMMAND, payload => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    if ($isTableSelection(selection)) {\n      tableObserver.$formatCells(payload);\n      return true;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const tableCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => $isTableCellNode(n));\n      if (!$isTableCellNode(tableCellNode)) {\n        return false;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_ELEMENT_COMMAND, formatType => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isTableSelection(selection) || !$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    const anchorNode = selection.anchor.getNode();\n    const focusNode = selection.focus.getNode();\n    if (!$isTableCellNode(anchorNode) || !$isTableCellNode(focusNode)) {\n      return false;\n    }\n\n    // Align the table if the entire table is selected\n    if ($isFullTableSelection(selection, tableNode)) {\n      tableNode.setFormat(formatType);\n      return true;\n    }\n    const [tableMap, anchorCell, focusCell] = $computeTableMap(tableNode, anchorNode, focusNode);\n    const maxRow = Math.max(anchorCell.startRow + anchorCell.cell.__rowSpan - 1, focusCell.startRow + focusCell.cell.__rowSpan - 1);\n    const maxColumn = Math.max(anchorCell.startColumn + anchorCell.cell.__colSpan - 1, focusCell.startColumn + focusCell.cell.__colSpan - 1);\n    const minRow = Math.min(anchorCell.startRow, focusCell.startRow);\n    const minColumn = Math.min(anchorCell.startColumn, focusCell.startColumn);\n    const visited = new Set();\n    for (let i = minRow; i <= maxRow; i++) {\n      for (let j = minColumn; j <= maxColumn; j++) {\n        const cell = tableMap[i][j].cell;\n        if (visited.has(cell)) {\n          continue;\n        }\n        visited.add(cell);\n        cell.setFormat(formatType);\n        const cellChildren = cell.getChildren();\n        for (let k = 0; k < cellChildren.length; k++) {\n          const child = cellChildren[k];\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child) && !child.isInline()) {\n            child.setFormat(formatType);\n          }\n        }\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CONTROLLED_TEXT_INSERTION_COMMAND, payload => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    if ($isTableSelection(selection)) {\n      tableObserver.$clearHighlight();\n      return false;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const tableCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => $isTableCellNode(n));\n      if (!$isTableCellNode(tableCellNode)) {\n        return false;\n      }\n      if (typeof payload === 'string') {\n        const edgePosition = $getTableEdgeCursorPosition(editor, selection, tableNode);\n        if (edgePosition) {\n          $insertParagraphAtTableEdge(edgePosition, tableNode, [(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(payload)]);\n          return true;\n        }\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  if (hasTabHandler) {\n    tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_TAB_COMMAND, event => {\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || !selection.isCollapsed() || !$isSelectionInTable(selection, tableNode)) {\n        return false;\n      }\n      const tableCellNode = $findCellNode(selection.anchor.getNode());\n      if (tableCellNode === null || !tableNode.is($findTableNode(tableCellNode))) {\n        return false;\n      }\n      stopEvent(event);\n      $selectAdjacentCell(tableCellNode, event.shiftKey ? 'previous' : 'next');\n      return true;\n    }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  }\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FOCUS_COMMAND, payload => {\n    return tableNode.isSelected();\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_HIGH));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND, selectionPayload => {\n    const {\n      nodes,\n      selection\n    } = selectionPayload;\n    const anchorAndFocus = selection.getStartEndPoints();\n    const isTableSelection = $isTableSelection(selection);\n    const isRangeSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection);\n    const isSelectionInsideOfGrid = isRangeSelection && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => $isTableCellNode(n)) !== null && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.focus.getNode(), n => $isTableCellNode(n)) !== null || isTableSelection;\n    if (nodes.length !== 1 || !$isTableNode(nodes[0]) || !isSelectionInsideOfGrid || anchorAndFocus === null) {\n      return false;\n    }\n    const [anchor] = anchorAndFocus;\n    const newGrid = nodes[0];\n    const newGridRows = newGrid.getChildren();\n    const newColumnCount = newGrid.getFirstChildOrThrow().getChildrenSize();\n    const newRowCount = newGrid.getChildrenSize();\n    const gridCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchor.getNode(), n => $isTableCellNode(n));\n    const gridRowNode = gridCellNode && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(gridCellNode, n => $isTableRowNode(n));\n    const gridNode = gridRowNode && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(gridRowNode, n => $isTableNode(n));\n    if (!$isTableCellNode(gridCellNode) || !$isTableRowNode(gridRowNode) || !$isTableNode(gridNode)) {\n      return false;\n    }\n    const startY = gridRowNode.getIndexWithinParent();\n    const stopY = Math.min(gridNode.getChildrenSize() - 1, startY + newRowCount - 1);\n    const startX = gridCellNode.getIndexWithinParent();\n    const stopX = Math.min(gridRowNode.getChildrenSize() - 1, startX + newColumnCount - 1);\n    const fromX = Math.min(startX, stopX);\n    const fromY = Math.min(startY, stopY);\n    const toX = Math.max(startX, stopX);\n    const toY = Math.max(startY, stopY);\n    const gridRowNodes = gridNode.getChildren();\n    let newRowIdx = 0;\n    for (let r = fromY; r <= toY; r++) {\n      const currentGridRowNode = gridRowNodes[r];\n      if (!$isTableRowNode(currentGridRowNode)) {\n        return false;\n      }\n      const newGridRowNode = newGridRows[newRowIdx];\n      if (!$isTableRowNode(newGridRowNode)) {\n        return false;\n      }\n      const gridCellNodes = currentGridRowNode.getChildren();\n      const newGridCellNodes = newGridRowNode.getChildren();\n      let newColumnIdx = 0;\n      for (let c = fromX; c <= toX; c++) {\n        const currentGridCellNode = gridCellNodes[c];\n        if (!$isTableCellNode(currentGridCellNode)) {\n          return false;\n        }\n        const newGridCellNode = newGridCellNodes[newColumnIdx];\n        if (!$isTableCellNode(newGridCellNode)) {\n          return false;\n        }\n        const originalChildren = currentGridCellNode.getChildren();\n        newGridCellNode.getChildren().forEach(child => {\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(child)) {\n            const paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n            paragraphNode.append(child);\n            currentGridCellNode.append(child);\n          } else {\n            currentGridCellNode.append(child);\n          }\n        });\n        originalChildren.forEach(n => n.remove());\n        newColumnIdx++;\n      }\n      newRowIdx++;\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n    const nextFocus = tableObserver.getAndClearNextFocus();\n    if (nextFocus !== null) {\n      const {\n        focusCell\n      } = nextFocus;\n      if ($isTableSelection(selection) && selection.tableKey === tableObserver.tableNodeKey) {\n        if (focusCell.x === tableObserver.focusX && focusCell.y === tableObserver.focusY) {\n          // The selection is already the correct table selection\n          return false;\n        } else {\n          tableObserver.$setFocusCellForSelection(focusCell);\n          return true;\n        }\n      } else if (focusCell !== tableObserver.anchorCell && $isSelectionInTable(selection, tableNode)) {\n        // The selection has crossed cells\n        tableObserver.$setFocusCellForSelection(focusCell);\n        return true;\n      }\n    }\n    const shouldCheckSelection = tableObserver.getAndClearShouldCheckSelection();\n    // If they pressed the down arrow with the selection outside of the\n    // table, and then the selection ends up in the table but not in the\n    // first cell, then move the selection to the first cell.\n    if (shouldCheckSelection && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed()) {\n      const anchor = selection.anchor.getNode();\n      const firstRow = tableNode.getFirstChild();\n      const anchorCell = $findCellNode(anchor);\n      if (anchorCell !== null && $isTableRowNode(firstRow)) {\n        const firstCell = firstRow.getFirstChild();\n        if ($isTableCellNode(firstCell) && tableNode.is((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchorCell, node => node.is(tableNode) || node.is(firstCell)))) {\n          // The selection moved to the table, but not in the first cell\n          firstCell.selectStart();\n          return true;\n        }\n      }\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const {\n        anchor,\n        focus\n      } = selection;\n      const anchorNode = anchor.getNode();\n      const focusNode = focus.getNode();\n      // Using explicit comparison with table node to ensure it's not a nested table\n      // as in that case we'll leave selection resolving to that table\n      const anchorCellNode = $findCellNode(anchorNode);\n      const focusCellNode = $findCellNode(focusNode);\n      const isAnchorInside = !!(anchorCellNode && tableNode.is($findTableNode(anchorCellNode)));\n      const isFocusInside = !!(focusCellNode && tableNode.is($findTableNode(focusCellNode)));\n      const isPartiallyWithinTable = isAnchorInside !== isFocusInside;\n      const isWithinTable = isAnchorInside && isFocusInside;\n      const isBackward = selection.isBackward();\n      if (isPartiallyWithinTable) {\n        const newSelection = selection.clone();\n        if (isFocusInside) {\n          const [tableMap] = $computeTableMap(tableNode, focusCellNode, focusCellNode);\n          const firstCell = tableMap[0][0].cell;\n          const lastCell = tableMap[tableMap.length - 1].at(-1).cell;\n          newSelection.focus.set(isBackward ? firstCell.getKey() : lastCell.getKey(), isBackward ? firstCell.getChildrenSize() : lastCell.getChildrenSize(), 'element');\n        } else if (isAnchorInside) {\n          const [tableMap] = $computeTableMap(tableNode, anchorCellNode, anchorCellNode);\n          const firstCell = tableMap[0][0].cell;\n          const lastCell = tableMap[tableMap.length - 1].at(-1).cell;\n          /**\n           * If isBackward, set the anchor to be at the end of the table so that when the cursor moves outside of\n           * the table in the backward direction, the entire table will be selected from its end.\n           * Otherwise, if forward, set the anchor to be at the start of the table so that when the focus is dragged\n           * outside th end of the table, it will start from the beginning of the table.\n           */\n          newSelection.anchor.set(isBackward ? lastCell.getKey() : firstCell.getKey(), isBackward ? lastCell.getChildrenSize() : 0, 'element');\n        }\n        (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n        $addHighlightStyleToTable(editor, tableObserver);\n      } else if (isWithinTable) {\n        // Handle case when selection spans across multiple cells but still\n        // has range selection, then we convert it into table selection\n        if (!anchorCellNode.is(focusCellNode)) {\n          tableObserver.$setAnchorCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, anchorCellNode));\n          tableObserver.$setFocusCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, focusCellNode), true);\n        }\n\n        // Handle case when the pointer type is touch and the current and\n        // previous selection are collapsed, and the previous anchor and current\n        // focus cell nodes are different, then we convert it into table selection\n        if (tableObserver.pointerType === 'touch' && selection.isCollapsed() && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && prevSelection.isCollapsed()) {\n          const prevAnchorCellNode = $findCellNode(prevSelection.anchor.getNode());\n          if (prevAnchorCellNode && !prevAnchorCellNode.is(focusCellNode)) {\n            tableObserver.$setAnchorCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, prevAnchorCellNode));\n            tableObserver.$setFocusCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, focusCellNode), true);\n            tableObserver.pointerType = null;\n          }\n        }\n      }\n    } else if (selection && $isTableSelection(selection) && selection.is(prevSelection) && selection.tableKey === tableNode.getKey()) {\n      // if selection goes outside of the table we need to change it to Range selection\n      const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(editorWindow);\n      if (domSelection && domSelection.anchorNode && domSelection.focusNode) {\n        const focusNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(domSelection.focusNode);\n        const isFocusOutside = focusNode && !tableNode.isParentOf(focusNode);\n        const anchorNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(domSelection.anchorNode);\n        const isAnchorInside = anchorNode && tableNode.isParentOf(anchorNode);\n        if (isFocusOutside && isAnchorInside && domSelection.rangeCount > 0) {\n          const newSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelectionFromDom)(domSelection, editor);\n          if (newSelection) {\n            newSelection.anchor.set(tableNode.getKey(), selection.isBackward() ? tableNode.getChildrenSize() : 0, 'element');\n            domSelection.removeAllRanges();\n            (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n          }\n        }\n      }\n    }\n    if (selection && !selection.is(prevSelection) && ($isTableSelection(selection) || $isTableSelection(prevSelection)) && tableObserver.tableSelection && !tableObserver.tableSelection.is(prevSelection)) {\n      if ($isTableSelection(selection) && selection.tableKey === tableObserver.tableNodeKey) {\n        tableObserver.$updateTableTableSelection(selection);\n      } else if (!$isTableSelection(selection) && $isTableSelection(prevSelection) && prevSelection.tableKey === tableObserver.tableNodeKey) {\n        tableObserver.$updateTableTableSelection(null);\n      }\n      return false;\n    }\n    if (tableObserver.hasHijackedSelectionStyles && !tableNode.isSelected()) {\n      $removeHighlightStyleToTable(editor, tableObserver);\n    } else if (!tableObserver.hasHijackedSelectionStyles && tableNode.isSelected()) {\n      $addHighlightStyleToTable(editor, tableObserver);\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || !selection.isCollapsed() || !$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    const edgePosition = $getTableEdgeCursorPosition(editor, selection, tableNode);\n    if (edgePosition) {\n      $insertParagraphAtTableEdge(edgePosition, tableNode);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  return tableObserver;\n}\nfunction detatchTableObserverFromTableElement(tableElement, tableObserver) {\n  if (getTableObserverFromTableElement(tableElement) === tableObserver) {\n    delete tableElement[LEXICAL_ELEMENT_KEY];\n  }\n}\nfunction attachTableObserverToTableElement(tableElement, tableObserver) {\n  if (!(getTableObserverFromTableElement(tableElement) === null)) {\n    formatDevErrorMessage(`tableElement already has an attached TableObserver`);\n  }\n  tableElement[LEXICAL_ELEMENT_KEY] = tableObserver;\n}\nfunction getTableObserverFromTableElement(tableElement) {\n  return tableElement[LEXICAL_ELEMENT_KEY] || null;\n}\nfunction getDOMCellFromTarget(node) {\n  let currentNode = node;\n  while (currentNode != null) {\n    const nodeName = currentNode.nodeName;\n    if (nodeName === 'TD' || nodeName === 'TH') {\n      // @ts-expect-error: internal field\n      const cell = currentNode._cell;\n      if (cell === undefined) {\n        return null;\n      }\n      return cell;\n    }\n    currentNode = currentNode.parentNode;\n  }\n  return null;\n}\nfunction getDOMCellInTableFromTarget(table, node) {\n  if (!table.contains(node)) {\n    return null;\n  }\n  let cell = null;\n  for (let currentNode = node; currentNode != null; currentNode = currentNode.parentNode) {\n    if (currentNode === table) {\n      return cell;\n    }\n    const nodeName = currentNode.nodeName;\n    if (nodeName === 'TD' || nodeName === 'TH') {\n      // @ts-expect-error: internal field\n      cell = currentNode._cell || null;\n    }\n  }\n  return null;\n}\nfunction getTable(tableNode, dom) {\n  const tableElement = getTableElement(tableNode, dom);\n  const domRows = [];\n  const grid = {\n    columns: 0,\n    domRows,\n    rows: 0\n  };\n  let currentNode = tableElement.querySelector('tr');\n  let x = 0;\n  let y = 0;\n  domRows.length = 0;\n  while (currentNode != null) {\n    const nodeMame = currentNode.nodeName;\n    if (nodeMame === 'TD' || nodeMame === 'TH') {\n      const elem = currentNode;\n      const cell = {\n        elem,\n        hasBackgroundColor: elem.style.backgroundColor !== '',\n        highlighted: false,\n        x,\n        y\n      };\n\n      // @ts-expect-error: internal field\n      currentNode._cell = cell;\n      let row = domRows[y];\n      if (row === undefined) {\n        row = domRows[y] = [];\n      }\n      row[x] = cell;\n    } else {\n      const child = currentNode.firstChild;\n      if (child != null) {\n        currentNode = child;\n        continue;\n      }\n    }\n    const sibling = currentNode.nextSibling;\n    if (sibling != null) {\n      x++;\n      currentNode = sibling;\n      continue;\n    }\n    const parent = currentNode.parentNode;\n    if (parent != null) {\n      const parentSibling = parent.nextSibling;\n      if (parentSibling == null) {\n        break;\n      }\n      y++;\n      x = 0;\n      currentNode = parentSibling;\n    }\n  }\n  grid.columns = x + 1;\n  grid.rows = y + 1;\n  return grid;\n}\nfunction $updateDOMForSelection(editor, table, selection) {\n  const selectedCellNodes = new Set(selection ? selection.getNodes() : []);\n  $forEachTableCell(table, (cell, lexicalNode) => {\n    const elem = cell.elem;\n    if (selectedCellNodes.has(lexicalNode)) {\n      cell.highlighted = true;\n      $addHighlightToDOM(editor, cell);\n    } else {\n      cell.highlighted = false;\n      $removeHighlightFromDOM(editor, cell);\n      if (!elem.getAttribute('style')) {\n        elem.removeAttribute('style');\n      }\n    }\n  });\n}\nfunction $forEachTableCell(grid, cb) {\n  const {\n    domRows\n  } = grid;\n  for (let y = 0; y < domRows.length; y++) {\n    const row = domRows[y];\n    if (!row) {\n      continue;\n    }\n    for (let x = 0; x < row.length; x++) {\n      const cell = row[x];\n      if (!cell) {\n        continue;\n      }\n      const lexicalNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(cell.elem);\n      if (lexicalNode !== null) {\n        cb(cell, lexicalNode, {\n          x,\n          y\n        });\n      }\n    }\n  }\n}\nfunction $addHighlightStyleToTable(editor, tableSelection) {\n  tableSelection.$disableHighlightStyle();\n  $forEachTableCell(tableSelection.table, cell => {\n    cell.highlighted = true;\n    $addHighlightToDOM(editor, cell);\n  });\n}\nfunction $removeHighlightStyleToTable(editor, tableObserver) {\n  tableObserver.$enableHighlightStyle();\n  $forEachTableCell(tableObserver.table, cell => {\n    const elem = cell.elem;\n    cell.highlighted = false;\n    $removeHighlightFromDOM(editor, cell);\n    if (!elem.getAttribute('style')) {\n      elem.removeAttribute('style');\n    }\n  });\n}\nfunction $selectAdjacentCell(tableCellNode, direction) {\n  const siblingMethod = direction === 'next' ? 'getNextSibling' : 'getPreviousSibling';\n  const childMethod = direction === 'next' ? 'getFirstChild' : 'getLastChild';\n  const sibling = tableCellNode[siblingMethod]();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(sibling)) {\n    return sibling.selectEnd();\n  }\n  const parentRow = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(tableCellNode, $isTableRowNode);\n  if (!(parentRow !== null)) {\n    formatDevErrorMessage(`selectAdjacentCell: Cell not in table row`);\n  }\n  for (let nextRow = parentRow[siblingMethod](); $isTableRowNode(nextRow); nextRow = nextRow[siblingMethod]()) {\n    const child = nextRow[childMethod]();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child)) {\n      return child.selectEnd();\n    }\n  }\n  const parentTable = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(parentRow, $isTableNode);\n  if (!(parentTable !== null)) {\n    formatDevErrorMessage(`selectAdjacentCell: Row not in table`);\n  }\n  return direction === 'next' ? parentTable.selectNext() : parentTable.selectPrevious();\n}\nconst selectTableNodeInDirection = (tableObserver, tableNode, x, y, direction) => {\n  const isForward = direction === 'forward';\n  switch (direction) {\n    case 'backward':\n    case 'forward':\n      if (x !== (isForward ? tableObserver.table.columns - 1 : 0)) {\n        selectTableCellNode(tableNode.getCellNodeFromCordsOrThrow(x + (isForward ? 1 : -1), y, tableObserver.table), isForward);\n      } else {\n        if (y !== (isForward ? tableObserver.table.rows - 1 : 0)) {\n          selectTableCellNode(tableNode.getCellNodeFromCordsOrThrow(isForward ? 0 : tableObserver.table.columns - 1, y + (isForward ? 1 : -1), tableObserver.table), isForward);\n        } else if (!isForward) {\n          tableNode.selectPrevious();\n        } else {\n          tableNode.selectNext();\n        }\n      }\n      return true;\n    case 'up':\n      if (y !== 0) {\n        selectTableCellNode(tableNode.getCellNodeFromCordsOrThrow(x, y - 1, tableObserver.table), false);\n      } else {\n        tableNode.selectPrevious();\n      }\n      return true;\n    case 'down':\n      if (y !== tableObserver.table.rows - 1) {\n        selectTableCellNode(tableNode.getCellNodeFromCordsOrThrow(x, y + 1, tableObserver.table), true);\n      } else {\n        tableNode.selectNext();\n      }\n      return true;\n    default:\n      return false;\n  }\n};\nfunction getCorner(rect, cellValue) {\n  let colName;\n  let rowName;\n  if (cellValue.startColumn === rect.minColumn) {\n    colName = 'minColumn';\n  } else if (cellValue.startColumn + cellValue.cell.__colSpan - 1 === rect.maxColumn) {\n    colName = 'maxColumn';\n  } else {\n    return null;\n  }\n  if (cellValue.startRow === rect.minRow) {\n    rowName = 'minRow';\n  } else if (cellValue.startRow + cellValue.cell.__rowSpan - 1 === rect.maxRow) {\n    rowName = 'maxRow';\n  } else {\n    return null;\n  }\n  return [colName, rowName];\n}\nfunction getCornerOrThrow(rect, cellValue) {\n  const corner = getCorner(rect, cellValue);\n  if (!(corner !== null)) {\n    formatDevErrorMessage(`getCornerOrThrow: cell ${cellValue.cell.getKey()} is not at a corner of rect`);\n  }\n  return corner;\n}\nfunction oppositeCorner([colName, rowName]) {\n  return [colName === 'minColumn' ? 'maxColumn' : 'minColumn', rowName === 'minRow' ? 'maxRow' : 'minRow'];\n}\nfunction cellAtCornerOrThrow(tableMap, rect, [colName, rowName]) {\n  const rowNum = rect[rowName];\n  const rowMap = tableMap[rowNum];\n  if (!(rowMap !== undefined)) {\n    formatDevErrorMessage(`cellAtCornerOrThrow: ${rowName} = ${String(rowNum)} missing in tableMap`);\n  }\n  const colNum = rect[colName];\n  const cell = rowMap[colNum];\n  if (!(cell !== undefined)) {\n    formatDevErrorMessage(`cellAtCornerOrThrow: ${colName} = ${String(colNum)} missing in tableMap`);\n  }\n  return cell;\n}\nfunction $extractRectCorners(tableMap, anchorCellValue, newFocusCellValue) {\n  // We are sure that the focus now either contracts or expands the rect\n  // but both the anchor and focus might be moved to ensure a rectangle\n  // given a potentially ragged merge shape\n  const rect = $computeTableCellRectBoundary(tableMap, anchorCellValue, newFocusCellValue);\n  const anchorCorner = getCorner(rect, anchorCellValue);\n  if (anchorCorner) {\n    return [cellAtCornerOrThrow(tableMap, rect, anchorCorner), cellAtCornerOrThrow(tableMap, rect, oppositeCorner(anchorCorner))];\n  }\n  const newFocusCorner = getCorner(rect, newFocusCellValue);\n  if (newFocusCorner) {\n    return [cellAtCornerOrThrow(tableMap, rect, oppositeCorner(newFocusCorner)), cellAtCornerOrThrow(tableMap, rect, newFocusCorner)];\n  }\n  // TODO this doesn't have to be arbitrary, use the closest corner instead\n  const newAnchorCorner = ['minColumn', 'minRow'];\n  return [cellAtCornerOrThrow(tableMap, rect, newAnchorCorner), cellAtCornerOrThrow(tableMap, rect, oppositeCorner(newAnchorCorner))];\n}\nfunction $adjustFocusInDirection(tableObserver, tableMap, anchorCellValue, focusCellValue, direction) {\n  const rect = $computeTableCellRectBoundary(tableMap, anchorCellValue, focusCellValue);\n  const spans = $computeTableCellRectSpans(tableMap, rect);\n  const {\n    topSpan,\n    leftSpan,\n    bottomSpan,\n    rightSpan\n  } = spans;\n  const anchorCorner = getCornerOrThrow(rect, anchorCellValue);\n  const [focusColumn, focusRow] = oppositeCorner(anchorCorner);\n  let fCol = rect[focusColumn];\n  let fRow = rect[focusRow];\n  if (direction === 'forward') {\n    fCol += focusColumn === 'maxColumn' ? 1 : leftSpan;\n  } else if (direction === 'backward') {\n    fCol -= focusColumn === 'minColumn' ? 1 : rightSpan;\n  } else if (direction === 'down') {\n    fRow += focusRow === 'maxRow' ? 1 : topSpan;\n  } else if (direction === 'up') {\n    fRow -= focusRow === 'minRow' ? 1 : bottomSpan;\n  }\n  const targetRowMap = tableMap[fRow];\n  if (targetRowMap === undefined) {\n    return false;\n  }\n  const newFocusCellValue = targetRowMap[fCol];\n  if (newFocusCellValue === undefined) {\n    return false;\n  }\n  // We can be certain that anchorCellValue and newFocusCellValue are\n  // contained within the desired selection, but we are not certain if\n  // they need to be expanded or not to maintain a rectangular shape\n  const [finalAnchorCell, finalFocusCell] = $extractRectCorners(tableMap, anchorCellValue, newFocusCellValue);\n  const anchorDOM = $getObserverCellFromCellNodeOrThrow(tableObserver, finalAnchorCell.cell);\n  const focusDOM = $getObserverCellFromCellNodeOrThrow(tableObserver, finalFocusCell.cell);\n  tableObserver.$setAnchorCellForSelection(anchorDOM);\n  tableObserver.$setFocusCellForSelection(focusDOM, true);\n  return true;\n}\nfunction $isSelectionInTable(selection, tableNode) {\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection)) {\n    // TODO this should probably return false if there's an unrelated\n    //      shadow root between the node and the table (e.g. another table,\n    //      collapsible, etc.)\n    const isAnchorInside = tableNode.isParentOf(selection.anchor.getNode());\n    const isFocusInside = tableNode.isParentOf(selection.focus.getNode());\n    return isAnchorInside && isFocusInside;\n  }\n  return false;\n}\nfunction $isFullTableSelection(selection, tableNode) {\n  if ($isTableSelection(selection)) {\n    const anchorNode = selection.anchor.getNode();\n    const focusNode = selection.focus.getNode();\n    if (tableNode && anchorNode && focusNode) {\n      const [map] = $computeTableMap(tableNode, anchorNode, focusNode);\n      return anchorNode.getKey() === map[0][0].cell.getKey() && focusNode.getKey() === map[map.length - 1].at(-1).cell.getKey();\n    }\n  }\n  return false;\n}\nfunction selectTableCellNode(tableCell, fromStart) {\n  if (fromStart) {\n    tableCell.selectStart();\n  } else {\n    tableCell.selectEnd();\n  }\n}\nfunction $addHighlightToDOM(editor, cell) {\n  const element = cell.elem;\n  const editorThemeClasses = editor._config.theme;\n  const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(element);\n  if (!$isTableCellNode(node)) {\n    formatDevErrorMessage(`Expected to find LexicalNode from Table Cell DOMNode`);\n  }\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, editorThemeClasses.tableCellSelected);\n}\nfunction $removeHighlightFromDOM(editor, cell) {\n  const element = cell.elem;\n  const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(element);\n  if (!$isTableCellNode(node)) {\n    formatDevErrorMessage(`Expected to find LexicalNode from Table Cell DOMNode`);\n  }\n  const editorThemeClasses = editor._config.theme;\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(element, editorThemeClasses.tableCellSelected);\n}\nfunction $findCellNode(node) {\n  const cellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, $isTableCellNode);\n  return $isTableCellNode(cellNode) ? cellNode : null;\n}\nfunction $findTableNode(node) {\n  const tableNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, $isTableNode);\n  return $isTableNode(tableNode) ? tableNode : null;\n}\nfunction $getBlockParentIfFirstNode(node) {\n  for (let prevNode = node, currentNode = node; currentNode !== null; prevNode = currentNode, currentNode = currentNode.getParent()) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n      if (currentNode !== prevNode && currentNode.getFirstChild() !== prevNode) {\n        // Not the first child or the initial node\n        return null;\n      } else if (!currentNode.isInline()) {\n        return currentNode;\n      }\n    }\n  }\n  return null;\n}\nfunction $handleHorizontalArrowKeyRangeSelection(editor, event, selection, alter, isBackward, tableNode, tableObserver) {\n  const initialFocus = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretFromPoint)(selection.focus, isBackward ? 'previous' : 'next');\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isExtendableTextPointCaret)(initialFocus)) {\n    return false;\n  }\n  let lastCaret = initialFocus;\n  // TableCellNode is the only shadow root we are interested in piercing so\n  // we find the last internal caret and then check its parent\n  for (const nextCaret of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$extendCaretToRange)(initialFocus).iterNodeCarets('shadowRoot')) {\n    if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isSiblingCaret)(nextCaret) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(nextCaret.origin))) {\n      return false;\n    }\n    lastCaret = nextCaret;\n  }\n  const lastCaretParent = lastCaret.getParentAtCaret();\n  if (!$isTableCellNode(lastCaretParent)) {\n    return false;\n  }\n  const anchorCell = lastCaretParent;\n  const focusCaret = $findNextTableCell((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(anchorCell, lastCaret.direction));\n  const anchorCellTable = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchorCell, $isTableNode);\n  if (!(anchorCellTable && anchorCellTable.is(tableNode))) {\n    return false;\n  }\n  const anchorCellDOM = editor.getElementByKey(anchorCell.getKey());\n  const anchorDOMCell = getDOMCellFromTarget(anchorCellDOM);\n  if (!anchorCellDOM || !anchorDOMCell) {\n    return false;\n  }\n  const anchorCellTableElement = $getElementForTableNode(editor, anchorCellTable);\n  tableObserver.table = anchorCellTableElement;\n  if (!focusCaret) {\n    if (alter === 'extend') {\n      // extend the selection from a range inside the cell to a table selection of the cell\n      tableObserver.$setAnchorCellForSelection(anchorDOMCell);\n      tableObserver.$setFocusCellForSelection(anchorDOMCell, true);\n    } else {\n      // exit the table\n      const outerFocusCaret = $getTableExitCaret((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(anchorCellTable, initialFocus.direction));\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.anchor, outerFocusCaret);\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.focus, outerFocusCaret);\n    }\n  } else if (alter === 'extend') {\n    const focusDOMCell = getDOMCellFromTarget(editor.getElementByKey(focusCaret.origin.getKey()));\n    if (!focusDOMCell) {\n      return false;\n    }\n    tableObserver.$setAnchorCellForSelection(anchorDOMCell);\n    tableObserver.$setFocusCellForSelection(focusDOMCell, true);\n  } else {\n    // alter === 'move'\n    const innerFocusCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)(focusCaret);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.anchor, innerFocusCaret);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.focus, innerFocusCaret);\n  }\n  stopEvent(event);\n  return true;\n}\nfunction $getTableExitCaret(initialCaret) {\n  const adjacent = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(initialCaret);\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(adjacent) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)(adjacent) : initialCaret;\n}\nfunction $findNextTableCell(initialCaret) {\n  for (const nextCaret of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$extendCaretToRange)(initialCaret).iterNodeCarets('root')) {\n    const {\n      origin\n    } = nextCaret;\n    if ($isTableCellNode(origin)) {\n      // not sure why ts isn't narrowing here (even if the guard is on nextCaret.origin)\n      // but returning a new caret is fine\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(nextCaret)) {\n        return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(origin, initialCaret.direction);\n      }\n    } else if (!$isTableRowNode(origin)) {\n      break;\n    }\n  }\n  return null;\n}\nfunction $handleArrowKey(editor, event, direction, tableNode, tableObserver) {\n  if ((direction === 'up' || direction === 'down') && isTypeaheadMenuInView(editor)) {\n    return false;\n  }\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!$isSelectionInTable(selection, tableNode)) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      if (direction === 'backward') {\n        if (selection.focus.offset > 0) {\n          return false;\n        }\n        const parentNode = $getBlockParentIfFirstNode(selection.focus.getNode());\n        if (!parentNode) {\n          return false;\n        }\n        const siblingNode = parentNode.getPreviousSibling();\n        if (!$isTableNode(siblingNode)) {\n          return false;\n        }\n        stopEvent(event);\n        if (event.shiftKey) {\n          selection.focus.set(siblingNode.getParentOrThrow().getKey(), siblingNode.getIndexWithinParent(), 'element');\n        } else {\n          siblingNode.selectEnd();\n        }\n        return true;\n      } else if (event.shiftKey && (direction === 'up' || direction === 'down')) {\n        const focusNode = selection.focus.getNode();\n        const isTableUnselect = !selection.isCollapsed() && (direction === 'up' && !selection.isBackward() || direction === 'down' && selection.isBackward());\n        if (isTableUnselect) {\n          let focusParentNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focusNode, n => $isTableNode(n));\n          if ($isTableCellNode(focusParentNode)) {\n            focusParentNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focusParentNode, $isTableNode);\n          }\n          if (focusParentNode !== tableNode) {\n            return false;\n          }\n          if (!focusParentNode) {\n            return false;\n          }\n          const sibling = direction === 'down' ? focusParentNode.getNextSibling() : focusParentNode.getPreviousSibling();\n          if (!sibling) {\n            return false;\n          }\n          let newOffset = 0;\n          if (direction === 'up') {\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(sibling)) {\n              newOffset = sibling.getChildrenSize();\n            }\n          }\n          let newFocusNode = sibling;\n          if (direction === 'up') {\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(sibling)) {\n              const lastCell = sibling.getLastChild();\n              newFocusNode = lastCell ? lastCell : sibling;\n              newOffset = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(newFocusNode) ? newFocusNode.getTextContentSize() : 0;\n            }\n          }\n          const newSelection = selection.clone();\n          newSelection.focus.set(newFocusNode.getKey(), newOffset, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(newFocusNode) ? 'text' : 'element');\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n          stopEvent(event);\n          return true;\n        } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(focusNode)) {\n          const selectedNode = direction === 'up' ? selection.getNodes()[selection.getNodes().length - 1] : selection.getNodes()[0];\n          if (selectedNode) {\n            const tableCellNode = $findParentTableCellNodeInTable(tableNode, selectedNode);\n            if (tableCellNode !== null) {\n              const firstDescendant = tableNode.getFirstDescendant();\n              const lastDescendant = tableNode.getLastDescendant();\n              if (!firstDescendant || !lastDescendant) {\n                return false;\n              }\n              const [firstCellNode] = $getNodeTriplet(firstDescendant);\n              const [lastCellNode] = $getNodeTriplet(lastDescendant);\n              const firstCellCoords = tableNode.getCordsFromCellNode(firstCellNode, tableObserver.table);\n              const lastCellCoords = tableNode.getCordsFromCellNode(lastCellNode, tableObserver.table);\n              const firstCellDOM = tableNode.getDOMCellFromCordsOrThrow(firstCellCoords.x, firstCellCoords.y, tableObserver.table);\n              const lastCellDOM = tableNode.getDOMCellFromCordsOrThrow(lastCellCoords.x, lastCellCoords.y, tableObserver.table);\n              tableObserver.$setAnchorCellForSelection(firstCellDOM);\n              tableObserver.$setFocusCellForSelection(lastCellDOM, true);\n              return true;\n            }\n          }\n          return false;\n        } else {\n          let focusParentNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focusNode, n => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(n) && !n.isInline());\n          if ($isTableCellNode(focusParentNode)) {\n            focusParentNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focusParentNode, $isTableNode);\n          }\n          if (!focusParentNode) {\n            return false;\n          }\n          const sibling = direction === 'down' ? focusParentNode.getNextSibling() : focusParentNode.getPreviousSibling();\n          if ($isTableNode(sibling) && tableObserver.tableNodeKey === sibling.getKey()) {\n            const firstDescendant = sibling.getFirstDescendant();\n            const lastDescendant = sibling.getLastDescendant();\n            if (!firstDescendant || !lastDescendant) {\n              return false;\n            }\n            const [firstCellNode] = $getNodeTriplet(firstDescendant);\n            const [lastCellNode] = $getNodeTriplet(lastDescendant);\n            const newSelection = selection.clone();\n            newSelection.focus.set((direction === 'up' ? firstCellNode : lastCellNode).getKey(), direction === 'up' ? 0 : lastCellNode.getChildrenSize(), 'element');\n            stopEvent(event);\n            (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n            return true;\n          }\n        }\n      }\n    }\n    if (direction === 'down' && $isScrollableTablesActive(editor)) {\n      // Enable Firefox workaround\n      tableObserver.setShouldCheckSelection();\n    }\n    return false;\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    if (direction === 'backward' || direction === 'forward') {\n      const alter = event.shiftKey ? 'extend' : 'move';\n      return $handleHorizontalArrowKeyRangeSelection(editor, event, selection, alter, direction === 'backward', tableNode, tableObserver);\n    }\n    if (selection.isCollapsed()) {\n      const {\n        anchor,\n        focus\n      } = selection;\n      const anchorCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchor.getNode(), $isTableCellNode);\n      const focusCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focus.getNode(), $isTableCellNode);\n      if (!$isTableCellNode(anchorCellNode) || !anchorCellNode.is(focusCellNode)) {\n        return false;\n      }\n      const anchorCellTable = $findTableNode(anchorCellNode);\n      if (anchorCellTable !== tableNode && anchorCellTable != null) {\n        const anchorCellTableElement = getTableElement(anchorCellTable, editor.getElementByKey(anchorCellTable.getKey()));\n        if (anchorCellTableElement != null) {\n          tableObserver.table = getTable(anchorCellTable, anchorCellTableElement);\n          return $handleArrowKey(editor, event, direction, anchorCellTable, tableObserver);\n        }\n      }\n      const anchorCellDom = editor.getElementByKey(anchorCellNode.__key);\n      const anchorDOM = editor.getElementByKey(anchor.key);\n      if (anchorDOM == null || anchorCellDom == null) {\n        return false;\n      }\n      let edgeSelectionRect;\n      if (anchor.type === 'element') {\n        edgeSelectionRect = anchorDOM.getBoundingClientRect();\n      } else {\n        const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(getEditorWindow(editor));\n        if (domSelection === null || domSelection.rangeCount === 0) {\n          return false;\n        }\n        const range = domSelection.getRangeAt(0);\n        edgeSelectionRect = range.getBoundingClientRect();\n      }\n      const edgeChild = direction === 'up' ? anchorCellNode.getFirstChild() : anchorCellNode.getLastChild();\n      if (edgeChild == null) {\n        return false;\n      }\n      const edgeChildDOM = editor.getElementByKey(edgeChild.__key);\n      if (edgeChildDOM == null) {\n        return false;\n      }\n      const edgeRect = edgeChildDOM.getBoundingClientRect();\n      const isExiting = direction === 'up' ? edgeRect.top > edgeSelectionRect.top - edgeSelectionRect.height : edgeSelectionRect.bottom + edgeSelectionRect.height > edgeRect.bottom;\n      if (isExiting) {\n        stopEvent(event);\n        const cords = tableNode.getCordsFromCellNode(anchorCellNode, tableObserver.table);\n        if (event.shiftKey) {\n          const cell = tableNode.getDOMCellFromCordsOrThrow(cords.x, cords.y, tableObserver.table);\n          tableObserver.$setAnchorCellForSelection(cell);\n          tableObserver.$setFocusCellForSelection(cell, true);\n        } else {\n          return selectTableNodeInDirection(tableObserver, tableNode, cords.x, cords.y, direction);\n        }\n        return true;\n      }\n    }\n  } else if ($isTableSelection(selection)) {\n    const {\n      anchor,\n      focus\n    } = selection;\n    const anchorCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchor.getNode(), $isTableCellNode);\n    const focusCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focus.getNode(), $isTableCellNode);\n    const [tableNodeFromSelection] = selection.getNodes();\n    if (!$isTableNode(tableNodeFromSelection)) {\n      formatDevErrorMessage(`$handleArrowKey: TableSelection.getNodes()[0] expected to be TableNode`);\n    }\n    const tableElement = getTableElement(tableNodeFromSelection, editor.getElementByKey(tableNodeFromSelection.getKey()));\n    if (!$isTableCellNode(anchorCellNode) || !$isTableCellNode(focusCellNode) || !$isTableNode(tableNodeFromSelection) || tableElement == null) {\n      return false;\n    }\n    tableObserver.$updateTableTableSelection(selection);\n    const grid = getTable(tableNodeFromSelection, tableElement);\n    const cordsAnchor = tableNode.getCordsFromCellNode(anchorCellNode, grid);\n    const anchorCell = tableNode.getDOMCellFromCordsOrThrow(cordsAnchor.x, cordsAnchor.y, grid);\n    tableObserver.$setAnchorCellForSelection(anchorCell);\n    stopEvent(event);\n    if (event.shiftKey) {\n      const [tableMap, anchorValue, focusValue] = $computeTableMap(tableNode, anchorCellNode, focusCellNode);\n      return $adjustFocusInDirection(tableObserver, tableMap, anchorValue, focusValue, direction);\n    } else {\n      focusCellNode.selectEnd();\n    }\n    return true;\n  }\n  return false;\n}\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n  event.stopPropagation();\n}\nfunction isTypeaheadMenuInView(editor) {\n  // There is no inbuilt way to check if the component picker is in view\n  // but we can check if the root DOM element has the aria-controls attribute \"typeahead-menu\".\n  const root = editor.getRootElement();\n  if (!root) {\n    return false;\n  }\n  return root.hasAttribute('aria-controls') && root.getAttribute('aria-controls') === 'typeahead-menu';\n}\nfunction $insertParagraphAtTableEdge(edgePosition, tableNode, children) {\n  const paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n  if (edgePosition === 'first') {\n    tableNode.insertBefore(paragraphNode);\n  } else {\n    tableNode.insertAfter(paragraphNode);\n  }\n  paragraphNode.append(...(children || []));\n  paragraphNode.selectEnd();\n}\nfunction $getTableEdgeCursorPosition(editor, selection, tableNode) {\n  const tableNodeParent = tableNode.getParent();\n  if (!tableNodeParent) {\n    return undefined;\n  }\n\n  // TODO: Add support for nested tables\n  const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(getEditorWindow(editor));\n  if (!domSelection) {\n    return undefined;\n  }\n  const domAnchorNode = domSelection.anchorNode;\n  const tableNodeParentDOM = editor.getElementByKey(tableNodeParent.getKey());\n  const tableElement = getTableElement(tableNode, editor.getElementByKey(tableNode.getKey()));\n  // We are only interested in the scenario where the\n  // native selection anchor is:\n  // - at or inside the table's parent DOM\n  // - and NOT at or inside the table DOM\n  // It may be adjacent to the table DOM (e.g. in a wrapper)\n  if (!domAnchorNode || !tableNodeParentDOM || !tableElement || !tableNodeParentDOM.contains(domAnchorNode) || tableElement.contains(domAnchorNode)) {\n    return undefined;\n  }\n  const anchorCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => $isTableCellNode(n));\n  if (!anchorCellNode) {\n    return undefined;\n  }\n  const parentTable = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchorCellNode, n => $isTableNode(n));\n  if (!$isTableNode(parentTable) || !parentTable.is(tableNode)) {\n    return undefined;\n  }\n  const [tableMap, cellValue] = $computeTableMap(tableNode, anchorCellNode, anchorCellNode);\n  const firstCell = tableMap[0][0];\n  const lastCell = tableMap[tableMap.length - 1][tableMap[0].length - 1];\n  const {\n    startRow,\n    startColumn\n  } = cellValue;\n  const isAtFirstCell = startRow === firstCell.startRow && startColumn === firstCell.startColumn;\n  const isAtLastCell = startRow === lastCell.startRow && startColumn === lastCell.startColumn;\n  if (isAtFirstCell) {\n    return 'first';\n  } else if (isAtLastCell) {\n    return 'last';\n  } else {\n    return undefined;\n  }\n}\nfunction $getObserverCellFromCellNodeOrThrow(tableObserver, tableCellNode) {\n  const {\n    tableNode\n  } = tableObserver.$lookup();\n  const currentCords = tableNode.getCordsFromCellNode(tableCellNode, tableObserver.table);\n  return tableNode.getDOMCellFromCordsOrThrow(currentCords.x, currentCords.y, tableObserver.table);\n}\nfunction $getNearestTableCellInTableFromDOMNode(tableNode, startingDOM, editorState) {\n  return $findParentTableCellNodeInTable(tableNode, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(startingDOM, editorState));\n}\n\nfunction updateColgroup(dom, config, colCount, colWidths) {\n  const colGroup = dom.querySelector('colgroup');\n  if (!colGroup) {\n    return;\n  }\n  const cols = [];\n  for (let i = 0; i < colCount; i++) {\n    const col = document.createElement('col');\n    const width = colWidths && colWidths[i];\n    if (width) {\n      col.style.width = `${width}px`;\n    }\n    cols.push(col);\n  }\n  colGroup.replaceChildren(...cols);\n}\nfunction setRowStriping(dom, config, rowStriping) {\n  if (rowStriping) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, config.theme.tableRowStriping);\n    dom.setAttribute('data-lexical-row-striping', 'true');\n  } else {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, config.theme.tableRowStriping);\n    dom.removeAttribute('data-lexical-row-striping');\n  }\n}\nfunction setFrozenColumns(dom, config, frozenColumnCount) {\n  if (frozenColumnCount > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, config.theme.tableFrozenColumn);\n    dom.setAttribute('data-lexical-frozen-column', 'true');\n  } else {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, config.theme.tableFrozenColumn);\n    dom.removeAttribute('data-lexical-frozen-column');\n  }\n}\nfunction setFrozenRows(dom, config, frozenRowCount) {\n  if (frozenRowCount > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, config.theme.tableFrozenRow);\n    dom.setAttribute('data-lexical-frozen-row', 'true');\n  } else {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, config.theme.tableFrozenRow);\n    dom.removeAttribute('data-lexical-frozen-row');\n  }\n}\nfunction alignTableElement(dom, config, formatType) {\n  if (!config.theme.tableAlignment) {\n    return;\n  }\n  const removeClasses = [];\n  const addClasses = [];\n  for (const format of ['center', 'right']) {\n    const classes = config.theme.tableAlignment[format];\n    if (!classes) {\n      continue;\n    }\n    (format === formatType ? addClasses : removeClasses).push(classes);\n  }\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, ...removeClasses);\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, ...addClasses);\n}\nconst scrollableEditors = new WeakSet();\nfunction $isScrollableTablesActive(editor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getEditor)()) {\n  return scrollableEditors.has(editor);\n}\nfunction setScrollableTablesActive(editor, active) {\n  if (active) {\n    if (!editor._config.theme.tableScrollableWrapper) {\n      console.warn('TableNode: hasHorizontalScroll is active but theme.tableScrollableWrapper is not defined.');\n    }\n    scrollableEditors.add(editor);\n  } else {\n    scrollableEditors.delete(editor);\n  }\n}\n\n/** @noInheritDoc */\nclass TableNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  static getType() {\n    return 'table';\n  }\n  getColWidths() {\n    const self = this.getLatest();\n    return self.__colWidths;\n  }\n  setColWidths(colWidths) {\n    const self = this.getWritable();\n    // NOTE: Node properties should be immutable. Freeze to prevent accidental mutation.\n    self.__colWidths = colWidths !== undefined && true ? Object.freeze(colWidths) : colWidths;\n    return self;\n  }\n  static clone(node) {\n    return new TableNode(node.__key);\n  }\n  afterCloneFrom(prevNode) {\n    super.afterCloneFrom(prevNode);\n    this.__colWidths = prevNode.__colWidths;\n    this.__rowStriping = prevNode.__rowStriping;\n    this.__frozenColumnCount = prevNode.__frozenColumnCount;\n    this.__frozenRowCount = prevNode.__frozenRowCount;\n  }\n  static importDOM() {\n    return {\n      table: _node => ({\n        conversion: $convertTableElement,\n        priority: 1\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createTableNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setRowStriping(serializedNode.rowStriping || false).setFrozenColumns(serializedNode.frozenColumnCount || 0).setFrozenRows(serializedNode.frozenRowCount || 0).setColWidths(serializedNode.colWidths);\n  }\n  constructor(key) {\n    super(key);\n    this.__rowStriping = false;\n    this.__frozenColumnCount = 0;\n    this.__frozenRowCount = 0;\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      colWidths: this.getColWidths(),\n      frozenColumnCount: this.__frozenColumnCount ? this.__frozenColumnCount : undefined,\n      frozenRowCount: this.__frozenRowCount ? this.__frozenRowCount : undefined,\n      rowStriping: this.__rowStriping ? this.__rowStriping : undefined\n    };\n  }\n  extractWithChild(child, selection, destination) {\n    return destination === 'html';\n  }\n  getDOMSlot(element) {\n    const tableElement = !isHTMLTableElement(element) ? element.querySelector('table') : element;\n    if (!isHTMLTableElement(tableElement)) {\n      formatDevErrorMessage(`TableNode.getDOMSlot: createDOM() did not return a table`);\n    }\n    return super.getDOMSlot(element).withElement(tableElement).withAfter(tableElement.querySelector('colgroup'));\n  }\n  createDOM(config, editor) {\n    const tableElement = document.createElement('table');\n    if (this.__style) {\n      tableElement.style.cssText = this.__style;\n    }\n    const colGroup = document.createElement('colgroup');\n    tableElement.appendChild(colGroup);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.setDOMUnmanaged)(colGroup);\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(tableElement, config.theme.table);\n    this.updateTableElement(null, tableElement, config);\n    if ($isScrollableTablesActive(editor)) {\n      const wrapperElement = document.createElement('div');\n      const classes = config.theme.tableScrollableWrapper;\n      if (classes) {\n        (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(wrapperElement, classes);\n      } else {\n        wrapperElement.style.cssText = 'overflow-x: auto;';\n      }\n      wrapperElement.appendChild(tableElement);\n      return wrapperElement;\n    }\n    return tableElement;\n  }\n  updateTableElement(prevNode, tableElement, config) {\n    if (this.__style !== (prevNode ? prevNode.__style : '')) {\n      tableElement.style.cssText = this.__style;\n    }\n    if (this.__rowStriping !== (prevNode ? prevNode.__rowStriping : false)) {\n      setRowStriping(tableElement, config, this.__rowStriping);\n    }\n    if (this.__frozenColumnCount !== (prevNode ? prevNode.__frozenColumnCount : 0)) {\n      setFrozenColumns(tableElement, config, this.__frozenColumnCount);\n    }\n    if (this.__frozenRowCount !== (prevNode ? prevNode.__frozenRowCount : 0)) {\n      setFrozenRows(tableElement, config, this.__frozenRowCount);\n    }\n    updateColgroup(tableElement, config, this.getColumnCount(), this.getColWidths());\n    alignTableElement(tableElement, config, this.getFormatType());\n  }\n  updateDOM(prevNode, dom, config) {\n    this.updateTableElement(prevNode, this.getDOMSlot(dom).element, config);\n    return false;\n  }\n  exportDOM(editor) {\n    const superExport = super.exportDOM(editor);\n    const {\n      element\n    } = superExport;\n    return {\n      after: tableElement => {\n        if (superExport.after) {\n          tableElement = superExport.after(tableElement);\n        }\n        if (!isHTMLTableElement(tableElement) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(tableElement)) {\n          tableElement = tableElement.querySelector('table');\n        }\n        if (!isHTMLTableElement(tableElement)) {\n          return null;\n        }\n        alignTableElement(tableElement, editor._config, this.getFormatType());\n\n        // Scan the table map to build a map of table cell key to the columns it needs\n        const [tableMap] = $computeTableMapSkipCellCheck(this, null, null);\n        const cellValues = new Map();\n        for (const mapRow of tableMap) {\n          for (const mapValue of mapRow) {\n            const key = mapValue.cell.getKey();\n            if (!cellValues.has(key)) {\n              cellValues.set(key, {\n                colSpan: mapValue.cell.getColSpan(),\n                startColumn: mapValue.startColumn\n              });\n            }\n          }\n        }\n\n        // scan the DOM to find the table cell keys that were used and mark those columns\n        const knownColumns = new Set();\n        for (const cellDOM of tableElement.querySelectorAll(':scope > tr > [data-temporary-table-cell-lexical-key]')) {\n          const key = cellDOM.getAttribute('data-temporary-table-cell-lexical-key');\n          if (key) {\n            const cellSpan = cellValues.get(key);\n            cellDOM.removeAttribute('data-temporary-table-cell-lexical-key');\n            if (cellSpan) {\n              cellValues.delete(key);\n              for (let i = 0; i < cellSpan.colSpan; i++) {\n                knownColumns.add(i + cellSpan.startColumn);\n              }\n            }\n          }\n        }\n\n        // Compute the colgroup and columns in the export\n        const colGroup = tableElement.querySelector(':scope > colgroup');\n        if (colGroup) {\n          // Only include the <col /> for rows that are in the output\n          const cols = Array.from(tableElement.querySelectorAll(':scope > colgroup > col')).filter((dom, i) => knownColumns.has(i));\n          colGroup.replaceChildren(...cols);\n        }\n\n        // Wrap direct descendant rows in a tbody for export\n        const rows = tableElement.querySelectorAll(':scope > tr');\n        if (rows.length > 0) {\n          const tBody = document.createElement('tbody');\n          for (const row of rows) {\n            tBody.appendChild(row);\n          }\n          tableElement.append(tBody);\n        }\n        return tableElement;\n      },\n      element: !isHTMLTableElement(element) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) ? element.querySelector('table') : element\n    };\n  }\n  canBeEmpty() {\n    return false;\n  }\n  isShadowRoot() {\n    return true;\n  }\n  getCordsFromCellNode(tableCellNode, table) {\n    const {\n      rows,\n      domRows\n    } = table;\n    for (let y = 0; y < rows; y++) {\n      const row = domRows[y];\n      if (row == null) {\n        continue;\n      }\n      for (let x = 0; x < row.length; x++) {\n        const cell = row[x];\n        if (cell == null) {\n          continue;\n        }\n        const {\n          elem\n        } = cell;\n        const cellNode = $getNearestTableCellInTableFromDOMNode(this, elem);\n        if (cellNode !== null && tableCellNode.is(cellNode)) {\n          return {\n            x,\n            y\n          };\n        }\n      }\n    }\n    throw new Error('Cell not found in table.');\n  }\n  getDOMCellFromCords(x, y, table) {\n    const {\n      domRows\n    } = table;\n    const row = domRows[y];\n    if (row == null) {\n      return null;\n    }\n    const index = x < row.length ? x : row.length - 1;\n    const cell = row[index];\n    if (cell == null) {\n      return null;\n    }\n    return cell;\n  }\n  getDOMCellFromCordsOrThrow(x, y, table) {\n    const cell = this.getDOMCellFromCords(x, y, table);\n    if (!cell) {\n      throw new Error('Cell not found at cords.');\n    }\n    return cell;\n  }\n  getCellNodeFromCords(x, y, table) {\n    const cell = this.getDOMCellFromCords(x, y, table);\n    if (cell == null) {\n      return null;\n    }\n    const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(cell.elem);\n    if ($isTableCellNode(node)) {\n      return node;\n    }\n    return null;\n  }\n  getCellNodeFromCordsOrThrow(x, y, table) {\n    const node = this.getCellNodeFromCords(x, y, table);\n    if (!node) {\n      throw new Error('Node at cords not TableCellNode.');\n    }\n    return node;\n  }\n  getRowStriping() {\n    return Boolean(this.getLatest().__rowStriping);\n  }\n  setRowStriping(newRowStriping) {\n    const self = this.getWritable();\n    self.__rowStriping = newRowStriping;\n    return self;\n  }\n  setFrozenColumns(columnCount) {\n    const self = this.getWritable();\n    self.__frozenColumnCount = columnCount;\n    return self;\n  }\n  getFrozenColumns() {\n    return this.getLatest().__frozenColumnCount;\n  }\n  setFrozenRows(rowCount) {\n    const self = this.getWritable();\n    self.__frozenRowCount = rowCount;\n    return self;\n  }\n  getFrozenRows() {\n    return this.getLatest().__frozenRowCount;\n  }\n  canSelectBefore() {\n    return true;\n  }\n  canIndent() {\n    return false;\n  }\n  getColumnCount() {\n    const firstRow = this.getFirstChild();\n    if (!firstRow) {\n      return 0;\n    }\n    let columnCount = 0;\n    firstRow.getChildren().forEach(cell => {\n      if ($isTableCellNode(cell)) {\n        columnCount += cell.getColSpan();\n      }\n    });\n    return columnCount;\n  }\n}\nfunction $getElementForTableNode(editor, tableNode) {\n  const tableElement = editor.getElementByKey(tableNode.getKey());\n  if (!(tableElement !== null)) {\n    formatDevErrorMessage(`$getElementForTableNode: Table Element Not Found`);\n  }\n  return getTable(tableNode, tableElement);\n}\nfunction $convertTableElement(domNode) {\n  const tableNode = $createTableNode();\n  if (domNode.hasAttribute('data-lexical-row-striping')) {\n    tableNode.setRowStriping(true);\n  }\n  const colGroup = domNode.querySelector(':scope > colgroup');\n  if (colGroup) {\n    let columns = [];\n    for (const col of colGroup.querySelectorAll(':scope > col')) {\n      let width = col.style.width || '';\n      if (!PIXEL_VALUE_REG_EXP.test(width)) {\n        // Also support deprecated width attribute for google docs\n        width = col.getAttribute('width') || '';\n        if (!/^\\d+$/.test(width)) {\n          columns = undefined;\n          break;\n        }\n      }\n      columns.push(parseFloat(width));\n    }\n    if (columns) {\n      tableNode.setColWidths(columns);\n    }\n  }\n  return {\n    after: children => (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$descendantsMatching)(children, $isTableRowNode),\n    node: tableNode\n  };\n}\nfunction $createTableNode() {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new TableNode());\n}\nfunction $isTableNode(node) {\n  return node instanceof TableNode;\n}\n\nfunction $insertTableCommandListener({\n  rows,\n  columns,\n  includeHeaders\n}) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!selection || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    return false;\n  }\n\n  // Prevent nested tables by checking if we're already inside a table\n  if ($findTableNode(selection.anchor.getNode())) {\n    return false;\n  }\n  const tableNode = $createTableNodeWithDimensions(Number(rows), Number(columns), includeHeaders);\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$insertNodeToNearestRoot)(tableNode);\n  const firstDescendant = tableNode.getFirstDescendant();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(firstDescendant)) {\n    firstDescendant.select();\n  }\n  return true;\n}\nfunction $tableCellTransform(node) {\n  if (!$isTableRowNode(node.getParent())) {\n    // TableCellNode must be a child of TableRowNode.\n    node.remove();\n  } else if (node.isEmpty()) {\n    // TableCellNode should never be empty\n    node.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n  }\n}\nfunction $tableRowTransform(node) {\n  if (!$isTableNode(node.getParent())) {\n    // TableRowNode must be a child of TableNode.\n    // TODO: Future support of tbody/thead/tfoot may change this\n    node.remove();\n  } else {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$unwrapAndFilterDescendants)(node, $isTableCellNode);\n  }\n}\nfunction $tableTransform(node) {\n  // TableRowNode is the only valid child for TableNode\n  // TODO: Future support of tbody/thead/tfoot/caption may change this\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$unwrapAndFilterDescendants)(node, $isTableRowNode);\n  const [gridMap] = $computeTableMapSkipCellCheck(node, null, null);\n  const maxRowLength = gridMap.reduce((curLength, row) => {\n    return Math.max(curLength, row.length);\n  }, 0);\n  const rowNodes = node.getChildren();\n  for (let i = 0; i < gridMap.length; ++i) {\n    const rowNode = rowNodes[i];\n    if (!rowNode) {\n      continue;\n    }\n    if (!$isTableRowNode(rowNode)) {\n      formatDevErrorMessage(`TablePlugin: Expecting all children of TableNode to be TableRowNode, found ${rowNode.constructor.name} (type ${rowNode.getType()})`);\n    }\n    const rowLength = gridMap[i].reduce((acc, cell) => cell ? 1 + acc : acc, 0);\n    if (rowLength === maxRowLength) {\n      continue;\n    }\n    for (let j = rowLength; j < maxRowLength; ++j) {\n      // TODO: inherit header state from another header or body\n      const newCell = $createTableCellNode();\n      newCell.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n      rowNode.append(newCell);\n    }\n  }\n}\nfunction $tableClickCommand(event) {\n  if (event.detail < 3 || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target)) {\n    return false;\n  }\n  const startNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(event.target);\n  if (startNode === null) {\n    return false;\n  }\n  const blockNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(startNode, node => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && !node.isInline());\n  if (blockNode === null) {\n    return false;\n  }\n  const rootNode = blockNode.getParent();\n  if (!$isTableCellNode(rootNode)) {\n    return false;\n  }\n  blockNode.select(0);\n  return true;\n}\n\n/**\n * Register a transform to ensure that all TableCellNode have a colSpan and rowSpan of 1.\n * This should only be registered when you do not want to support merged cells.\n *\n * @param editor The editor\n * @returns An unregister callback\n */\nfunction registerTableCellUnmergeTransform(editor) {\n  return editor.registerNodeTransform(TableCellNode, node => {\n    if (node.getColSpan() > 1 || node.getRowSpan() > 1) {\n      // When we have rowSpan we have to map the entire Table to understand where the new Cells\n      // fit best; let's analyze all Cells at once to save us from further transform iterations\n      const [,, gridNode] = $getNodeTriplet(node);\n      const [gridMap] = $computeTableMap(gridNode, node, node);\n      // TODO this function expects Tables to be normalized. Look into this once it exists\n      const rowsCount = gridMap.length;\n      const columnsCount = gridMap[0].length;\n      let row = gridNode.getFirstChild();\n      if (!$isTableRowNode(row)) {\n        formatDevErrorMessage(`Expected TableNode first child to be a RowNode`);\n      }\n      const unmerged = [];\n      for (let i = 0; i < rowsCount; i++) {\n        if (i !== 0) {\n          row = row.getNextSibling();\n          if (!$isTableRowNode(row)) {\n            formatDevErrorMessage(`Expected TableNode first child to be a RowNode`);\n          }\n        }\n        let lastRowCell = null;\n        for (let j = 0; j < columnsCount; j++) {\n          const cellMap = gridMap[i][j];\n          const cell = cellMap.cell;\n          if (cellMap.startRow === i && cellMap.startColumn === j) {\n            lastRowCell = cell;\n            unmerged.push(cell);\n          } else if (cell.getColSpan() > 1 || cell.getRowSpan() > 1) {\n            if (!$isTableCellNode(cell)) {\n              formatDevErrorMessage(`Expected TableNode cell to be a TableCellNode`);\n            }\n            const newCell = $createTableCellNode(cell.__headerState);\n            if (lastRowCell !== null) {\n              lastRowCell.insertAfter(newCell);\n            } else {\n              (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$insertFirst)(row, newCell);\n            }\n          }\n        }\n      }\n      for (const cell of unmerged) {\n        cell.setColSpan(1);\n        cell.setRowSpan(1);\n      }\n    }\n  });\n}\nfunction registerTableSelectionObserver(editor, hasTabHandler = true) {\n  const tableSelections = new Map();\n  const initializeTableNode = (tableNode, nodeKey, dom) => {\n    const tableElement = getTableElement(tableNode, dom);\n    const tableSelection = applyTableHandlers(tableNode, tableElement, editor, hasTabHandler);\n    tableSelections.set(nodeKey, [tableSelection, tableElement]);\n  };\n  const unregisterMutationListener = editor.registerMutationListener(TableNode, nodeMutations => {\n    editor.getEditorState().read(() => {\n      for (const [nodeKey, mutation] of nodeMutations) {\n        const tableSelection = tableSelections.get(nodeKey);\n        if (mutation === 'created' || mutation === 'updated') {\n          const {\n            tableNode,\n            tableElement\n          } = $getTableAndElementByKey(nodeKey);\n          if (tableSelection === undefined) {\n            initializeTableNode(tableNode, nodeKey, tableElement);\n          } else if (tableElement !== tableSelection[1]) {\n            // The update created a new DOM node, destroy the existing TableObserver\n            tableSelection[0].removeListeners();\n            tableSelections.delete(nodeKey);\n            initializeTableNode(tableNode, nodeKey, tableElement);\n          }\n        } else if (mutation === 'destroyed') {\n          if (tableSelection !== undefined) {\n            tableSelection[0].removeListeners();\n            tableSelections.delete(nodeKey);\n          }\n        }\n      }\n    }, {\n      editor\n    });\n  }, {\n    skipInitialization: false\n  });\n  return () => {\n    unregisterMutationListener();\n    // Hook might be called multiple times so cleaning up tables listeners as well,\n    // as it'll be reinitialized during recurring call\n    for (const [, [tableSelection]] of tableSelections) {\n      tableSelection.removeListeners();\n    }\n  };\n}\n\n/**\n * Register the INSERT_TABLE_COMMAND listener and the table integrity transforms. The\n * table selection observer should be registered separately after this with\n * {@link registerTableSelectionObserver}.\n *\n * @param editor The editor\n * @returns An unregister callback\n */\nfunction registerTablePlugin(editor) {\n  if (!editor.hasNodes([TableNode])) {\n    {\n      formatDevErrorMessage(`TablePlugin: TableNode is not registered on editor`);\n    }\n  }\n  return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(INSERT_TABLE_COMMAND, $insertTableCommandListener, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND, ({\n    nodes,\n    selection\n  }) => {\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const isInsideTableCell = $findTableNode(selection.anchor.getNode()) !== null;\n    return isInsideTableCell && nodes.some($isTableNode);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLICK_COMMAND, $tableClickCommand, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerNodeTransform(TableNode, $tableTransform), editor.registerNodeTransform(TableRowNode, $tableRowTransform), editor.registerNodeTransform(TableCellNode, $tableCellTransform));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+table@0.28.0/node_modules/@lexical/table/LexicalTable.dev.mjs\n");

/***/ })

};
;