"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+html@0.28.0";
exports.ids = ["vendor-chunks/@lexical+html@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $generateHtmlFromNodes: () => (/* binding */ $generateHtmlFromNodes),\n/* harmony export */   $generateNodesFromDOM: () => (/* binding */ $generateNodesFromDOM)\n/* harmony export */ });\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n/**\n * How you parse your html string to get a document is left up to you. In the browser you can use the native\n * DOMParser API to generate a document (see clipboard.ts), but to use in a headless environment you can use JSDom\n * or an equivalent library and pass in the document here.\n */\nfunction $generateNodesFromDOM(editor, dom) {\n  const elements = dom.body ? dom.body.childNodes : [];\n  let lexicalNodes = [];\n  const allArtificialNodes = [];\n  for (let i = 0; i < elements.length; i++) {\n    const element = elements[i];\n    if (!IGNORE_TAGS.has(element.nodeName)) {\n      const lexicalNode = $createNodesFromDOM(element, editor, allArtificialNodes, false);\n      if (lexicalNode !== null) {\n        lexicalNodes = lexicalNodes.concat(lexicalNode);\n      }\n    }\n  }\n  $unwrapArtificalNodes(allArtificialNodes);\n  return lexicalNodes;\n}\nfunction $generateHtmlFromNodes(editor, selection) {\n  if (typeof document === 'undefined' || typeof window === 'undefined' && typeof global.window === 'undefined') {\n    throw new Error('To use $generateHtmlFromNodes in headless mode please initialize a headless browser implementation such as JSDom before calling this function.');\n  }\n  const container = document.createElement('div');\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const topLevelChildren = root.getChildren();\n  for (let i = 0; i < topLevelChildren.length; i++) {\n    const topLevelNode = topLevelChildren[i];\n    $appendNodesToHTML(editor, topLevelNode, container, selection);\n  }\n  return container.innerHTML;\n}\nfunction $appendNodesToHTML(editor, currentNode, parentElement, selection = null) {\n  let shouldInclude = selection !== null ? currentNode.isSelected(selection) : true;\n  const shouldExclude = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && currentNode.excludeFromCopy('html');\n  let target = currentNode;\n  if (selection !== null) {\n    let clone = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties)(currentNode);\n    clone = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(clone) && selection !== null ? (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_1__.$sliceSelectedTextNodeContent)(selection, clone) : clone;\n    target = clone;\n  }\n  const children = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(target) ? target.getChildren() : [];\n  const registeredNode = editor._nodes.get(target.getType());\n  let exportOutput;\n\n  // Use HTMLConfig overrides, if available.\n  if (registeredNode && registeredNode.exportDOM !== undefined) {\n    exportOutput = registeredNode.exportDOM(editor, target);\n  } else {\n    exportOutput = target.exportDOM(editor);\n  }\n  const {\n    element,\n    after\n  } = exportOutput;\n  if (!element) {\n    return false;\n  }\n  const fragment = document.createDocumentFragment();\n  for (let i = 0; i < children.length; i++) {\n    const childNode = children[i];\n    const shouldIncludeChild = $appendNodesToHTML(editor, childNode, fragment, selection);\n    if (!shouldInclude && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && shouldIncludeChild && currentNode.extractWithChild(childNode, selection, 'html')) {\n      shouldInclude = true;\n    }\n  }\n  if (shouldInclude && !shouldExclude) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDocumentFragment)(element)) {\n      element.append(fragment);\n    }\n    parentElement.append(element);\n    if (after) {\n      const newElement = after.call(target, element);\n      if (newElement) {\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDocumentFragment)(element)) {\n          element.replaceChildren(newElement);\n        } else {\n          element.replaceWith(newElement);\n        }\n      }\n    }\n  } else {\n    parentElement.append(fragment);\n  }\n  return shouldInclude;\n}\nfunction getConversionFunction(domNode, editor) {\n  const {\n    nodeName\n  } = domNode;\n  const cachedConversions = editor._htmlConversions.get(nodeName.toLowerCase());\n  let currentConversion = null;\n  if (cachedConversions !== undefined) {\n    for (const cachedConversion of cachedConversions) {\n      const domConversion = cachedConversion(domNode);\n      if (domConversion !== null && (currentConversion === null ||\n      // Given equal priority, prefer the last registered importer\n      // which is typically an application custom node or HTMLConfig['import']\n      (currentConversion.priority || 0) <= (domConversion.priority || 0))) {\n        currentConversion = domConversion;\n      }\n    }\n  }\n  return currentConversion !== null ? currentConversion.conversion : null;\n}\nconst IGNORE_TAGS = new Set(['STYLE', 'SCRIPT']);\nfunction $createNodesFromDOM(node, editor, allArtificialNodes, hasBlockAncestorLexicalNode, forChildMap = new Map(), parentLexicalNode) {\n  let lexicalNodes = [];\n  if (IGNORE_TAGS.has(node.nodeName)) {\n    return lexicalNodes;\n  }\n  let currentLexicalNode = null;\n  const transformFunction = getConversionFunction(node, editor);\n  const transformOutput = transformFunction ? transformFunction(node) : null;\n  let postTransform = null;\n  if (transformOutput !== null) {\n    postTransform = transformOutput.after;\n    const transformNodes = transformOutput.node;\n    currentLexicalNode = Array.isArray(transformNodes) ? transformNodes[transformNodes.length - 1] : transformNodes;\n    if (currentLexicalNode !== null) {\n      for (const [, forChildFunction] of forChildMap) {\n        currentLexicalNode = forChildFunction(currentLexicalNode, parentLexicalNode);\n        if (!currentLexicalNode) {\n          break;\n        }\n      }\n      if (currentLexicalNode) {\n        lexicalNodes.push(...(Array.isArray(transformNodes) ? transformNodes : [currentLexicalNode]));\n      }\n    }\n    if (transformOutput.forChild != null) {\n      forChildMap.set(node.nodeName, transformOutput.forChild);\n    }\n  }\n\n  // If the DOM node doesn't have a transformer, we don't know what\n  // to do with it but we still need to process any childNodes.\n  const children = node.childNodes;\n  let childLexicalNodes = [];\n  const hasBlockAncestorLexicalNodeForChildren = currentLexicalNode != null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(currentLexicalNode) ? false : currentLexicalNode != null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isBlockElementNode)(currentLexicalNode) || hasBlockAncestorLexicalNode;\n  for (let i = 0; i < children.length; i++) {\n    childLexicalNodes.push(...$createNodesFromDOM(children[i], editor, allArtificialNodes, hasBlockAncestorLexicalNodeForChildren, new Map(forChildMap), currentLexicalNode));\n  }\n  if (postTransform != null) {\n    childLexicalNodes = postTransform(childLexicalNodes);\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isBlockDomNode)(node)) {\n    if (!hasBlockAncestorLexicalNodeForChildren) {\n      childLexicalNodes = wrapContinuousInlines(node, childLexicalNodes, lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode);\n    } else {\n      childLexicalNodes = wrapContinuousInlines(node, childLexicalNodes, () => {\n        const artificialNode = new lexical__WEBPACK_IMPORTED_MODULE_0__.ArtificialNode__DO_NOT_USE();\n        allArtificialNodes.push(artificialNode);\n        return artificialNode;\n      });\n    }\n  }\n  if (currentLexicalNode == null) {\n    if (childLexicalNodes.length > 0) {\n      // If it hasn't been converted to a LexicalNode, we hoist its children\n      // up to the same level as it.\n      lexicalNodes = lexicalNodes.concat(childLexicalNodes);\n    } else {\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isBlockDomNode)(node) && isDomNodeBetweenTwoInlineNodes(node)) {\n        // Empty block dom node that hasnt been converted, we replace it with a linebreak if its between inline nodes\n        lexicalNodes = lexicalNodes.concat((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createLineBreakNode)());\n      }\n    }\n  } else {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentLexicalNode)) {\n      // If the current node is a ElementNode after conversion,\n      // we can append all the children to it.\n      currentLexicalNode.append(...childLexicalNodes);\n    }\n  }\n  return lexicalNodes;\n}\nfunction wrapContinuousInlines(domNode, nodes, createWrapperFn) {\n  const textAlign = domNode.style.textAlign;\n  const out = [];\n  let continuousInlines = [];\n  // wrap contiguous inline child nodes in para\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isBlockElementNode)(node)) {\n      if (textAlign && !node.getFormat()) {\n        node.setFormat(textAlign);\n      }\n      out.push(node);\n    } else {\n      continuousInlines.push(node);\n      if (i === nodes.length - 1 || i < nodes.length - 1 && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isBlockElementNode)(nodes[i + 1])) {\n        const wrapper = createWrapperFn();\n        wrapper.setFormat(textAlign);\n        wrapper.append(...continuousInlines);\n        out.push(wrapper);\n        continuousInlines = [];\n      }\n    }\n  }\n  return out;\n}\nfunction $unwrapArtificalNodes(allArtificialNodes) {\n  for (const node of allArtificialNodes) {\n    if (node.getNextSibling() instanceof lexical__WEBPACK_IMPORTED_MODULE_0__.ArtificialNode__DO_NOT_USE) {\n      node.insertAfter((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createLineBreakNode)());\n    }\n  }\n  // Replace artificial node with it's children\n  for (const node of allArtificialNodes) {\n    const children = node.getChildren();\n    for (const child of children) {\n      node.insertBefore(child);\n    }\n    node.remove();\n  }\n}\nfunction isDomNodeBetweenTwoInlineNodes(node) {\n  if (node.nextSibling == null || node.previousSibling == null) {\n    return false;\n  }\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isInlineDomNode)(node.nextSibling) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isInlineDomNode)(node.previousSibling);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs\n");

/***/ })

};
;