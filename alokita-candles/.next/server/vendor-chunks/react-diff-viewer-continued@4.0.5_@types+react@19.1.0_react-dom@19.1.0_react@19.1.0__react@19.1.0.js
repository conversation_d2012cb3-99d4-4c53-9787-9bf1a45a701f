"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0";
exports.ids = ["vendor-chunks/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/compute-hidden-blocks.js":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/compute-hidden-blocks.js ***!
  \************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   computeHiddenBlocks: () => (/* binding */ computeHiddenBlocks)\n/* harmony export */ });\nfunction computeHiddenBlocks(lineInformation, diffLines, extraLines) {\n    let newBlockIndex = 0;\n    let currentBlock;\n    const lineBlocks = {};\n    const blocks = [];\n    lineInformation.forEach((line, lineIndex) => {\n        const isDiffLine = diffLines.some((diffLine) => diffLine >= lineIndex - extraLines &&\n            diffLine <= lineIndex + extraLines);\n        if (!isDiffLine && currentBlock === undefined) {\n            // block begins\n            currentBlock = {\n                index: newBlockIndex,\n                startLine: lineIndex,\n                endLine: lineIndex,\n                lines: 1,\n            };\n            blocks.push(currentBlock);\n            lineBlocks[lineIndex] = currentBlock.index;\n            newBlockIndex++;\n        }\n        else if (!isDiffLine && currentBlock) {\n            // block continues\n            currentBlock.endLine = lineIndex;\n            currentBlock.lines++;\n            lineBlocks[lineIndex] = currentBlock.index;\n        }\n        else {\n            // not a block anymore\n            currentBlock = undefined;\n        }\n    });\n    return {\n        lineBlocks,\n        blocks: blocks,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/compute-hidden-blocks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/compute-lines.js":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/compute-lines.js ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffMethod: () => (/* binding */ DiffMethod),\n/* harmony export */   DiffType: () => (/* binding */ DiffType),\n/* harmony export */   computeLineInformation: () => (/* binding */ computeLineInformation)\n/* harmony export */ });\n/* harmony import */ var diff__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! diff */ \"(ssr)/./node_modules/.pnpm/diff@5.2.0/node_modules/diff/lib/index.mjs\");\n\nconst jsDiff = diff__WEBPACK_IMPORTED_MODULE_0__;\nvar DiffType;\n(function (DiffType) {\n    DiffType[DiffType[\"DEFAULT\"] = 0] = \"DEFAULT\";\n    DiffType[DiffType[\"ADDED\"] = 1] = \"ADDED\";\n    DiffType[DiffType[\"REMOVED\"] = 2] = \"REMOVED\";\n    DiffType[DiffType[\"CHANGED\"] = 3] = \"CHANGED\";\n})(DiffType || (DiffType = {}));\n// See https://github.com/kpdecker/jsdiff/tree/v4.0.1#api for more info on the below JsDiff methods\nvar DiffMethod;\n(function (DiffMethod) {\n    DiffMethod[\"CHARS\"] = \"diffChars\";\n    DiffMethod[\"WORDS\"] = \"diffWords\";\n    DiffMethod[\"WORDS_WITH_SPACE\"] = \"diffWordsWithSpace\";\n    DiffMethod[\"LINES\"] = \"diffLines\";\n    DiffMethod[\"TRIMMED_LINES\"] = \"diffTrimmedLines\";\n    DiffMethod[\"SENTENCES\"] = \"diffSentences\";\n    DiffMethod[\"CSS\"] = \"diffCss\";\n    DiffMethod[\"JSON\"] = \"diffJson\";\n})(DiffMethod || (DiffMethod = {}));\n/**\n * Splits diff text by new line and computes final list of diff lines based on\n * conditions.\n *\n * @param value Diff text from the js diff module.\n */\nconst constructLines = (value) => {\n    if (value === \"\")\n        return [];\n    const lines = value.replace(/\\n$/, \"\").split(\"\\n\");\n    return lines;\n};\n/**\n * Computes word diff information in the line.\n * [TODO]: Consider adding options argument for JsDiff text block comparison\n *\n * @param oldValue Old word in the line.\n * @param newValue New word in the line.\n * @param compareMethod JsDiff text diff method from https://github.com/kpdecker/jsdiff/tree/v4.0.1#api\n */\nconst computeDiff = (oldValue, newValue, compareMethod = DiffMethod.CHARS) => {\n    const compareFunc = typeof compareMethod === \"string\" ? jsDiff[compareMethod] : compareMethod;\n    const diffArray = compareFunc(oldValue, newValue);\n    const computedDiff = {\n        left: [],\n        right: [],\n    };\n    diffArray.forEach(({ added, removed, value }) => {\n        const diffInformation = {};\n        if (added) {\n            diffInformation.type = DiffType.ADDED;\n            diffInformation.value = value;\n            computedDiff.right.push(diffInformation);\n        }\n        if (removed) {\n            diffInformation.type = DiffType.REMOVED;\n            diffInformation.value = value;\n            computedDiff.left.push(diffInformation);\n        }\n        if (!removed && !added) {\n            diffInformation.type = DiffType.DEFAULT;\n            diffInformation.value = value;\n            computedDiff.right.push(diffInformation);\n            computedDiff.left.push(diffInformation);\n        }\n        return diffInformation;\n    });\n    return computedDiff;\n};\n/**\n * [TODO]: Think about moving common left and right value assignment to a\n * common place. Better readability?\n *\n * Computes line wise information based in the js diff information passed. Each\n * line contains information about left and right section. Left side denotes\n * deletion and right side denotes addition.\n *\n * @param oldString Old string to compare.\n * @param newString New string to compare with old string.\n * @param disableWordDiff Flag to enable/disable word diff.\n * @param lineCompareMethod JsDiff text diff method from https://github.com/kpdecker/jsdiff/tree/v4.0.1#api\n * @param linesOffset line number to start counting from\n * @param showLines lines that are always shown, regardless of diff\n */\nconst computeLineInformation = (oldString, newString, disableWordDiff = false, lineCompareMethod = DiffMethod.CHARS, linesOffset = 0, showLines = []) => {\n    let diffArray = [];\n    // Use diffLines for strings, and diffJson for objects...\n    if (typeof oldString === \"string\" && typeof newString === \"string\") {\n        diffArray = diff__WEBPACK_IMPORTED_MODULE_0__.diffLines(oldString, newString, {\n            newlineIsToken: false,\n            ignoreWhitespace: false,\n            ignoreCase: false,\n        });\n    }\n    else {\n        diffArray = diff__WEBPACK_IMPORTED_MODULE_0__.diffJson(oldString, newString);\n    }\n    let rightLineNumber = linesOffset;\n    let leftLineNumber = linesOffset;\n    let lineInformation = [];\n    let counter = 0;\n    const diffLines = [];\n    const ignoreDiffIndexes = [];\n    const getLineInformation = (value, diffIndex, added, removed, evaluateOnlyFirstLine) => {\n        const lines = constructLines(value);\n        return lines\n            .map((line, lineIndex) => {\n            const left = {};\n            const right = {};\n            if (ignoreDiffIndexes.includes(`${diffIndex}-${lineIndex}`) ||\n                (evaluateOnlyFirstLine && lineIndex !== 0)) {\n                return undefined;\n            }\n            if (added || removed) {\n                let countAsChange = true;\n                if (removed) {\n                    leftLineNumber += 1;\n                    left.lineNumber = leftLineNumber;\n                    left.type = DiffType.REMOVED;\n                    left.value = line || \" \";\n                    // When the current line is of type REMOVED, check the next item in\n                    // the diff array whether it is of type ADDED. If true, the current\n                    // diff will be marked as both REMOVED and ADDED. Meaning, the\n                    // current line is a modification.\n                    const nextDiff = diffArray[diffIndex + 1];\n                    if (nextDiff?.added) {\n                        const nextDiffLines = constructLines(nextDiff.value)[lineIndex];\n                        if (nextDiffLines) {\n                            const nextDiffLineInfo = getLineInformation(nextDiffLines, diffIndex, true, false, true);\n                            const { value: rightValue, lineNumber, type, } = nextDiffLineInfo[0].right;\n                            // When identified as modification, push the next diff to ignore\n                            // list as the next value will be added in this line computation as\n                            // right and left values.\n                            ignoreDiffIndexes.push(`${diffIndex + 1}-${lineIndex}`);\n                            right.lineNumber = lineNumber;\n                            if (left.value === rightValue) {\n                                // The new value is exactly the same as the old\n                                countAsChange = false;\n                                right.type = 0;\n                                left.type = 0;\n                                right.value = rightValue;\n                            }\n                            else {\n                                right.type = type;\n                                // Do char level diff and assign the corresponding values to the\n                                // left and right diff information object.\n                                if (disableWordDiff) {\n                                    right.value = rightValue;\n                                }\n                                else {\n                                    const computedDiff = computeDiff(line, rightValue, lineCompareMethod);\n                                    right.value = computedDiff.right;\n                                    left.value = computedDiff.left;\n                                }\n                            }\n                        }\n                    }\n                }\n                else {\n                    rightLineNumber += 1;\n                    right.lineNumber = rightLineNumber;\n                    right.type = DiffType.ADDED;\n                    right.value = line;\n                }\n                if (countAsChange && !evaluateOnlyFirstLine) {\n                    if (!diffLines.includes(counter)) {\n                        diffLines.push(counter);\n                    }\n                }\n            }\n            else {\n                leftLineNumber += 1;\n                rightLineNumber += 1;\n                left.lineNumber = leftLineNumber;\n                left.type = DiffType.DEFAULT;\n                left.value = line;\n                right.lineNumber = rightLineNumber;\n                right.type = DiffType.DEFAULT;\n                right.value = line;\n            }\n            if (showLines?.includes(`L-${left.lineNumber}`) ||\n                (showLines?.includes(`R-${right.lineNumber}`) &&\n                    !diffLines.includes(counter))) {\n                diffLines.push(counter);\n            }\n            if (!evaluateOnlyFirstLine) {\n                counter += 1;\n            }\n            return { right, left };\n        })\n            .filter(Boolean);\n    };\n    diffArray.forEach(({ added, removed, value }, index) => {\n        lineInformation = [\n            ...lineInformation,\n            ...getLineInformation(value, index, added, removed),\n        ];\n    });\n    return {\n        lineInformation,\n        diffLines,\n    };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/compute-lines.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/expand.js":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/expand.js ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Expand: () => (/* binding */ Expand)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\nfunction Expand() {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 16 16\", width: \"16\", height: \"16\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"title\", { children: \"expand\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"m8.177.677 2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25a.75.75 0 0 1-1.5 0V4H5.104a.25.25 0 0 1-.177-.427L7.823.677a.25.25 0 0 1 .354 0ZM7.25 10.75a.75.75 0 0 1 1.5 0V12h2.146a.25.25 0 0 1 .177.427l-2.896 2.896a.25.25 0 0 1-.354 0l-2.896-2.896A.25.25 0 0 1 5.104 12H7.25v-1.25Zm-5-2a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z\" })] }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/expand.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/fold.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/fold.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fold: () => (/* binding */ Fold)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\nfunction Fold() {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 16 16\", width: \"16\", height: \"16\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"title\", { children: \"fold\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M10.896 2H8.75V.75a.75.75 0 0 0-1.5 0V2H5.104a.25.25 0 0 0-.177.427l2.896 2.896a.25.25 0 0 0 .354 0l2.896-2.896A.25.25 0 0 0 10.896 2ZM8.75 15.25a.75.75 0 0 1-1.5 0V14H5.104a.25.25 0 0 1-.177-.427l2.896-2.896a.25.25 0 0 1 .354 0l2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25Zm-6.5-6.5a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z\" })] }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/fold.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/index.js":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/index.js ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffMethod: () => (/* reexport safe */ _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffMethod),\n/* harmony export */   LineNumberPrefix: () => (/* binding */ LineNumberPrefix),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var memoize_one__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! memoize-one */ \"(ssr)/./node_modules/.pnpm/memoize-one@6.0.0/node_modules/memoize-one/dist/memoize-one.esm.js\");\n/* harmony import */ var _compute_hidden_blocks_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./compute-hidden-blocks.js */ \"(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/compute-hidden-blocks.js\");\n/* harmony import */ var _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./compute-lines.js */ \"(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/compute-lines.js\");\n/* harmony import */ var _expand_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./expand.js */ \"(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/expand.js\");\n/* harmony import */ var _styles_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./styles.js */ \"(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/styles.js\");\n/* harmony import */ var _fold_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./fold.js */ \"(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/fold.js\");\n\n\n\n\n\n\n\n\n\nvar LineNumberPrefix;\n(function (LineNumberPrefix) {\n    LineNumberPrefix[\"LEFT\"] = \"L\";\n    LineNumberPrefix[\"RIGHT\"] = \"R\";\n})(LineNumberPrefix || (LineNumberPrefix = {}));\nclass DiffViewer extends react__WEBPACK_IMPORTED_MODULE_2__.Component {\n    styles;\n    static defaultProps = {\n        oldValue: \"\",\n        newValue: \"\",\n        splitView: true,\n        highlightLines: [],\n        disableWordDiff: false,\n        compareMethod: _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffMethod.CHARS,\n        styles: {},\n        hideLineNumbers: false,\n        extraLinesSurroundingDiff: 3,\n        showDiffOnly: true,\n        useDarkTheme: false,\n        linesOffset: 0,\n        nonce: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            expandedBlocks: [],\n            noSelect: undefined,\n        };\n    }\n    /**\n     * Resets code block expand to the initial stage. Will be exposed to the parent component via\n     * refs.\n     */\n    resetCodeBlocks = () => {\n        if (this.state.expandedBlocks.length > 0) {\n            this.setState({\n                expandedBlocks: [],\n            });\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Pushes the target expanded code block to the state. During the re-render,\n     * this value is used to expand/fold unmodified code.\n     */\n    onBlockExpand = (id) => {\n        const prevState = this.state.expandedBlocks.slice();\n        prevState.push(id);\n        this.setState({\n            expandedBlocks: prevState,\n        });\n    };\n    /**\n     * Computes final styles for the diff viewer. It combines the default styles with the user\n     * supplied overrides. The computed styles are cached with performance in mind.\n     *\n     * @param styles User supplied style overrides.\n     */\n    computeStyles = (0,memoize_one__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(_styles_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]);\n    /**\n     * Returns a function with clicked line number in the closure. Returns an no-op function when no\n     * onLineNumberClick handler is supplied.\n     *\n     * @param id Line id of a line.\n     */\n    onLineNumberClickProxy = (id) => {\n        if (this.props.onLineNumberClick) {\n            return (e) => this.props.onLineNumberClick(id, e);\n        }\n        return () => { };\n    };\n    /**\n     * Maps over the word diff and constructs the required React elements to show word diff.\n     *\n     * @param diffArray Word diff information derived from line information.\n     * @param renderer Optional renderer to format diff words. Useful for syntax highlighting.\n     */\n    renderWordDiff = (diffArray, renderer) => {\n        return diffArray.map((wordDiff, i) => {\n            const content = renderer\n                ? renderer(wordDiff.value)\n                : wordDiff.value;\n            if (typeof content !== \"string\")\n                return;\n            return wordDiff.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.ADDED ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ins\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.wordDiff, {\n                    [this.styles.wordAdded]: wordDiff.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.ADDED,\n                }), children: content }, i)) : wordDiff.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.REMOVED ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"del\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.wordDiff, {\n                    [this.styles.wordRemoved]: wordDiff.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.REMOVED,\n                }), children: content }, i)) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.wordDiff), children: content }, i));\n        });\n    };\n    /**\n     * Maps over the line diff and constructs the required react elements to show line diff. It calls\n     * renderWordDiff when encountering word diff. This takes care of both inline and split view line\n     * renders.\n     *\n     * @param lineNumber Line number of the current line.\n     * @param type Type of diff of the current line.\n     * @param prefix Unique id to prefix with the line numbers.\n     * @param value Content of the line. It can be a string or a word diff array.\n     * @param additionalLineNumber Additional line number to be shown. Useful for rendering inline\n     *  diff view. Right line number will be passed as additionalLineNumber.\n     * @param additionalPrefix Similar to prefix but for additional line number.\n     */\n    renderLine = (lineNumber, type, prefix, value, additionalLineNumber, additionalPrefix) => {\n        const lineNumberTemplate = `${prefix}-${lineNumber}`;\n        const additionalLineNumberTemplate = `${additionalPrefix}-${additionalLineNumber}`;\n        const highlightLine = this.props.highlightLines.includes(lineNumberTemplate) ||\n            this.props.highlightLines.includes(additionalLineNumberTemplate);\n        const added = type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.ADDED;\n        const removed = type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.REMOVED;\n        const changed = type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.CHANGED;\n        let content;\n        const hasWordDiff = Array.isArray(value);\n        if (hasWordDiff) {\n            content = this.renderWordDiff(value, this.props.renderContent);\n        }\n        else if (this.props.renderContent) {\n            content = this.props.renderContent(value);\n        }\n        else {\n            content = value;\n        }\n        let ElementType = \"div\";\n        if (added && !hasWordDiff) {\n            ElementType = \"ins\";\n        }\n        else if (removed && !hasWordDiff) {\n            ElementType = \"del\";\n        }\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [!this.props.hideLineNumbers && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { onClick: lineNumber && this.onLineNumberClickProxy(lineNumberTemplate), className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.gutter, {\n                        [this.styles.emptyGutter]: !lineNumber,\n                        [this.styles.diffAdded]: added,\n                        [this.styles.diffRemoved]: removed,\n                        [this.styles.diffChanged]: changed,\n                        [this.styles.highlightedGutter]: highlightLine,\n                    }), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"pre\", { className: this.styles.lineNumber, children: lineNumber }) })), !this.props.splitView && !this.props.hideLineNumbers && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { onClick: additionalLineNumber &&\n                        this.onLineNumberClickProxy(additionalLineNumberTemplate), className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.gutter, {\n                        [this.styles.emptyGutter]: !additionalLineNumber,\n                        [this.styles.diffAdded]: added,\n                        [this.styles.diffRemoved]: removed,\n                        [this.styles.diffChanged]: changed,\n                        [this.styles.highlightedGutter]: highlightLine,\n                    }), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"pre\", { className: this.styles.lineNumber, children: additionalLineNumber }) })), this.props.renderGutter\n                    ? this.props.renderGutter({\n                        lineNumber,\n                        type,\n                        prefix,\n                        value,\n                        additionalLineNumber,\n                        additionalPrefix,\n                        styles: this.styles,\n                    })\n                    : null, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.marker, {\n                        [this.styles.emptyLine]: !content,\n                        [this.styles.diffAdded]: added,\n                        [this.styles.diffRemoved]: removed,\n                        [this.styles.diffChanged]: changed,\n                        [this.styles.highlightedLine]: highlightLine,\n                    }), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"pre\", { children: [added && \"+\", removed && \"-\"] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.content, {\n                        [this.styles.emptyLine]: !content,\n                        [this.styles.diffAdded]: added,\n                        [this.styles.diffRemoved]: removed,\n                        [this.styles.diffChanged]: changed,\n                        [this.styles.highlightedLine]: highlightLine,\n                        left: prefix === LineNumberPrefix.LEFT,\n                        right: prefix === LineNumberPrefix.RIGHT,\n                    }), onMouseDown: () => {\n                        const elements = document.getElementsByClassName(prefix === LineNumberPrefix.LEFT ? \"right\" : \"left\");\n                        for (let i = 0; i < elements.length; i++) {\n                            const element = elements.item(i);\n                            element.classList.add(this.styles.noSelect);\n                        }\n                    }, title: added && !hasWordDiff\n                        ? \"Added line\"\n                        : removed && !hasWordDiff\n                            ? \"Removed line\"\n                            : undefined, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ElementType, { className: this.styles.contentText, children: content }) })] }));\n    };\n    /**\n     * Generates lines for split view.\n     *\n     * @param obj Line diff information.\n     * @param obj.left Life diff information for the left pane of the split view.\n     * @param obj.right Life diff information for the right pane of the split view.\n     * @param index React key for the lines.\n     */\n    renderSplitView = ({ left, right }, index) => {\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { className: this.styles.line, children: [this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value), this.renderLine(right.lineNumber, right.type, LineNumberPrefix.RIGHT, right.value)] }, index));\n    };\n    /**\n     * Generates lines for inline view.\n     *\n     * @param obj Line diff information.\n     * @param obj.left Life diff information for the added section of the inline view.\n     * @param obj.right Life diff information for the removed section of the inline view.\n     * @param index React key for the lines.\n     */\n    renderInlineView = ({ left, right }, index) => {\n        let content;\n        if (left.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.REMOVED && right.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.ADDED) {\n            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tr\", { className: this.styles.line, children: this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, null) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tr\", { className: this.styles.line, children: this.renderLine(null, right.type, LineNumberPrefix.RIGHT, right.value, right.lineNumber, LineNumberPrefix.RIGHT) })] }, index));\n        }\n        if (left.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.REMOVED) {\n            content = this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, null);\n        }\n        if (left.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.DEFAULT) {\n            content = this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, right.lineNumber, LineNumberPrefix.RIGHT);\n        }\n        if (right.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.ADDED) {\n            content = this.renderLine(null, right.type, LineNumberPrefix.RIGHT, right.value, right.lineNumber);\n        }\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tr\", { className: this.styles.line, children: content }, index));\n    };\n    /**\n     * Returns a function with clicked block number in the closure.\n     *\n     * @param id Cold fold block id.\n     */\n    onBlockClickProxy = (id) => () => this.onBlockExpand(id);\n    /**\n     * Generates cold fold block. It also uses the custom message renderer when available to show\n     * cold fold messages.\n     *\n     * @param num Number of skipped lines between two blocks.\n     * @param blockNumber Code fold block id.\n     * @param leftBlockLineNumber First left line number after the current code fold block.\n     * @param rightBlockLineNumber First right line number after the current code fold block.\n     */\n    renderSkippedLineIndicator = (num, blockNumber, leftBlockLineNumber, rightBlockLineNumber) => {\n        const { hideLineNumbers, splitView } = this.props;\n        const message = this.props.codeFoldMessageRenderer ? (this.props.codeFoldMessageRenderer(num, leftBlockLineNumber, rightBlockLineNumber)) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", { className: this.styles.codeFoldContent, children: [\"Expand \", num, \" lines ...\"] }));\n        const content = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: this.styles.codeFoldContentContainer, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", { type: \"button\", className: this.styles.codeFoldExpandButton, onClick: this.onBlockClickProxy(blockNumber), tabIndex: 0, children: message }) }));\n        const isUnifiedViewWithoutLineNumbers = !splitView && !hideLineNumbers;\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { className: this.styles.codeFold, children: [!hideLineNumbers && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: this.styles.codeFoldGutter }), this.props.renderGutter ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: this.styles.codeFoldGutter })) : null, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__({\n                        [this.styles.codeFoldGutter]: isUnifiedViewWithoutLineNumbers,\n                    }) }), isUnifiedViewWithoutLineNumbers ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", {}), content] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: [content, this.props.renderGutter ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", {}) : null, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", {}), !hideLineNumbers ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", {}) : null] }))] }, `${leftBlockLineNumber}-${rightBlockLineNumber}`));\n    };\n    /**\n     * Generates the entire diff view.\n     */\n    renderDiff = () => {\n        const { oldValue, newValue, splitView, disableWordDiff, compareMethod, linesOffset, } = this.props;\n        const { lineInformation, diffLines } = (0,_compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.computeLineInformation)(oldValue, newValue, disableWordDiff, compareMethod, linesOffset, this.props.alwaysShowLines);\n        const extraLines = this.props.extraLinesSurroundingDiff < 0\n            ? 0\n            : Math.round(this.props.extraLinesSurroundingDiff);\n        const { lineBlocks, blocks } = (0,_compute_hidden_blocks_js__WEBPACK_IMPORTED_MODULE_3__.computeHiddenBlocks)(lineInformation, diffLines, extraLines);\n        const diffNodes = lineInformation.map((line, lineIndex) => {\n            if (this.props.showDiffOnly) {\n                const blockIndex = lineBlocks[lineIndex];\n                if (blockIndex !== undefined) {\n                    const lastLineOfBlock = blocks[blockIndex].endLine === lineIndex;\n                    if (!this.state.expandedBlocks.includes(blockIndex) &&\n                        lastLineOfBlock) {\n                        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: this.renderSkippedLineIndicator(blocks[blockIndex].lines, blockIndex, line.left.lineNumber, line.right.lineNumber) }, lineIndex));\n                    }\n                    if (!this.state.expandedBlocks.includes(blockIndex)) {\n                        return null;\n                    }\n                }\n            }\n            return splitView\n                ? this.renderSplitView(line, lineIndex)\n                : this.renderInlineView(line, lineIndex);\n        });\n        return {\n            diffNodes,\n            blocks,\n            lineInformation,\n        };\n    };\n    render = () => {\n        const { oldValue, newValue, useDarkTheme, leftTitle, rightTitle, splitView, compareMethod, hideLineNumbers, nonce, } = this.props;\n        if (typeof compareMethod === \"string\" &&\n            compareMethod !== _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffMethod.JSON) {\n            if (typeof oldValue !== \"string\" || typeof newValue !== \"string\") {\n                throw Error('\"oldValue\" and \"newValue\" should be strings');\n            }\n        }\n        this.styles = this.computeStyles(this.props.styles, useDarkTheme, nonce);\n        const nodes = this.renderDiff();\n        let colSpanOnSplitView = 3;\n        let colSpanOnInlineView = 4;\n        if (hideLineNumbers) {\n            colSpanOnSplitView -= 1;\n            colSpanOnInlineView -= 1;\n        }\n        if (this.props.renderGutter) {\n            colSpanOnSplitView += 1;\n            colSpanOnInlineView += 1;\n        }\n        let deletions = 0;\n        let additions = 0;\n        for (const l of nodes.lineInformation) {\n            if (l.left.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.ADDED) {\n                additions++;\n            }\n            if (l.right.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.ADDED) {\n                additions++;\n            }\n            if (l.left.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.REMOVED) {\n                deletions++;\n            }\n            if (l.right.type === _compute_lines_js__WEBPACK_IMPORTED_MODULE_4__.DiffType.REMOVED) {\n                deletions++;\n            }\n        }\n        const totalChanges = deletions + additions;\n        const percentageAddition = Math.round((additions / totalChanges) * 100);\n        const blocks = [];\n        for (let i = 0; i < 5; i++) {\n            if (percentageAddition > i * 20) {\n                blocks.push((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.block, this.styles.blockAddition) }, i));\n            }\n            else {\n                blocks.push((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.block, this.styles.blockDeletion) }, i));\n            }\n        }\n        const allExpanded = this.state.expandedBlocks.length === nodes.blocks.length;\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: this.styles.summary, role: \"banner\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", { type: \"button\", className: this.styles.allExpandButton, onClick: () => {\n                                this.setState({\n                                    expandedBlocks: allExpanded\n                                        ? []\n                                        : nodes.blocks.map((b) => b.index),\n                                });\n                            }, children: allExpanded ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_fold_js__WEBPACK_IMPORTED_MODULE_7__.Fold, {}) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_expand_js__WEBPACK_IMPORTED_MODULE_5__.Expand, {}) }), \" \", totalChanges, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: { display: \"flex\", gap: \"1px\" }, children: blocks }), this.props.summary ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { children: this.props.summary }) : null] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"table\", { className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.diffContainer, {\n                        [this.styles.splitView]: splitView,\n                    }), onMouseUp: () => {\n                        const elements = document.getElementsByClassName(\"right\");\n                        for (let i = 0; i < elements.length; i++) {\n                            const element = elements.item(i);\n                            element.classList.remove(this.styles.noSelect);\n                        }\n                        const elementsLeft = document.getElementsByClassName(\"left\");\n                        for (let i = 0; i < elementsLeft.length; i++) {\n                            const element = elementsLeft.item(i);\n                            element.classList.remove(this.styles.noSelect);\n                        }\n                    }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tbody\", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { children: [!this.props.hideLineNumbers ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"50px\" }) : null, !splitView && !this.props.hideLineNumbers ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"50px\" })) : null, this.props.renderGutter ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"50px\" }) : null, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"28px\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"100%\" }), splitView ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [!this.props.hideLineNumbers ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"50px\" }) : null, this.props.renderGutter ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"50px\" }) : null, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"28px\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { width: \"100%\" })] })) : null] }), leftTitle || rightTitle ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"th\", { colSpan: splitView ? colSpanOnSplitView : colSpanOnInlineView, className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.titleBlock, this.styles.column), children: leftTitle ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"pre\", { className: this.styles.contentText, children: leftTitle })) : null }), splitView ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"th\", { colSpan: colSpanOnSplitView, className: classnames__WEBPACK_IMPORTED_MODULE_1__(this.styles.titleBlock, this.styles.column), children: rightTitle ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"pre\", { className: this.styles.contentText, children: rightTitle })) : null })) : null] })) : null, nodes.diffNodes] }) })] }));\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DiffViewer);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/styles.js":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/styles.js ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_css_create_instance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/css/create-instance */ \"(ssr)/./node_modules/.pnpm/@emotion+css@11.13.5/node_modules/@emotion/css/create-instance/dist/emotion-css-create-instance.development.esm.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((styleOverride, useDarkTheme = false, nonce = \"\") => {\n    const { variables: overrideVariables = {}, ...styles } = styleOverride;\n    const themeVariables = {\n        light: {\n            ...{\n                diffViewerBackground: \"#fff\",\n                diffViewerColor: \"#212529\",\n                addedBackground: \"#e6ffed\",\n                addedColor: \"#24292e\",\n                removedBackground: \"#ffeef0\",\n                removedColor: \"#24292e\",\n                changedBackground: \"#fffbdd\",\n                wordAddedBackground: \"#acf2bd\",\n                wordRemovedBackground: \"#fdb8c0\",\n                addedGutterBackground: \"#cdffd8\",\n                removedGutterBackground: \"#ffdce0\",\n                gutterBackground: \"#f7f7f7\",\n                gutterBackgroundDark: \"#f3f1f1\",\n                highlightBackground: \"#fffbdd\",\n                highlightGutterBackground: \"#fff5b1\",\n                codeFoldGutterBackground: \"#dbedff\",\n                codeFoldBackground: \"#f1f8ff\",\n                emptyLineBackground: \"#fafbfc\",\n                gutterColor: \"#212529\",\n                addedGutterColor: \"#212529\",\n                removedGutterColor: \"#212529\",\n                codeFoldContentColor: \"#212529\",\n                diffViewerTitleBackground: \"#fafbfc\",\n                diffViewerTitleColor: \"#212529\",\n                diffViewerTitleBorderColor: \"#eee\",\n            },\n            ...(overrideVariables.light || {}),\n        },\n        dark: {\n            ...{\n                diffViewerBackground: \"#2e303c\",\n                diffViewerColor: \"#FFF\",\n                addedBackground: \"#044B53\",\n                addedColor: \"white\",\n                removedBackground: \"#632F34\",\n                removedColor: \"white\",\n                changedBackground: \"#3e302c\",\n                wordAddedBackground: \"#055d67\",\n                wordRemovedBackground: \"#7d383f\",\n                addedGutterBackground: \"#034148\",\n                removedGutterBackground: \"#632b30\",\n                gutterBackground: \"#2c2f3a\",\n                gutterBackgroundDark: \"#262933\",\n                highlightBackground: \"#2a3967\",\n                highlightGutterBackground: \"#2d4077\",\n                codeFoldGutterBackground: \"#262831\",\n                codeFoldBackground: \"#262831\",\n                emptyLineBackground: \"#363946\",\n                gutterColor: \"#666c87\",\n                addedGutterColor: \"#8c8c8c\",\n                removedGutterColor: \"#8c8c8c\",\n                codeFoldContentColor: \"#656a8b\",\n                diffViewerTitleBackground: \"#2f323e\",\n                diffViewerTitleColor: \"#757a9b\",\n                diffViewerTitleBorderColor: \"#353846\",\n            },\n            ...(overrideVariables.dark || {}),\n        },\n    };\n    const variables = useDarkTheme ? themeVariables.dark : themeVariables.light;\n    const { css, cx } = (0,_emotion_css_create_instance__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({ key: \"react-diff\", nonce });\n    const content = css({\n        width: \"auto\",\n        label: \"content\",\n    });\n    const splitView = css({\n        label: \"split-view\",\n    });\n    const summary = css({\n        background: variables.diffViewerTitleBackground,\n        color: variables.diffViewerTitleColor,\n        padding: \"0.5em 1em\",\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: \"0.5em\",\n        fontFamily: \"monospace\",\n        fill: variables.diffViewerTitleColor,\n    });\n    const diffContainer = css({\n        width: \"100%\",\n        minWidth: \"1000px\",\n        overflowX: \"auto\",\n        tableLayout: \"fixed\",\n        background: variables.diffViewerBackground,\n        pre: {\n            margin: 0,\n            whiteSpace: \"pre-wrap\",\n            lineHeight: \"1.6em\",\n            width: \"fit-content\",\n        },\n        label: \"diff-container\",\n        borderCollapse: \"collapse\",\n    });\n    const lineContent = css({\n        overflow: \"hidden\",\n        width: \"100%\",\n    });\n    const contentText = css({\n        color: variables.diffViewerColor,\n        whiteSpace: \"pre-wrap\",\n        fontFamily: \"monospace\",\n        lineBreak: \"anywhere\",\n        textDecoration: \"none\",\n        label: \"content-text\",\n    });\n    const unselectable = css({\n        userSelect: \"none\",\n        label: \"unselectable\",\n    });\n    const allExpandButton = css({\n        background: \"transparent\",\n        border: \"none\",\n        cursor: \"pointer\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        margin: 0,\n        label: \"all-expand-button\",\n        \":hover\": {\n            fill: variables.addedGutterColor,\n        },\n        \":focus\": {\n            outline: `1px ${variables.addedGutterColor} solid`,\n        },\n    });\n    const titleBlock = css({\n        background: variables.diffViewerTitleBackground,\n        padding: \"0.5em\",\n        lineHeight: \"1.4em\",\n        height: \"2.4em\",\n        overflow: \"hidden\",\n        width: \"50%\",\n        borderBottom: `1px solid ${variables.diffViewerTitleBorderColor}`,\n        label: \"title-block\",\n        \":last-child\": {\n            borderLeft: `1px solid ${variables.diffViewerTitleBorderColor}`,\n        },\n        [`.${contentText}`]: {\n            color: variables.diffViewerTitleColor,\n        },\n    });\n    const lineNumber = css({\n        color: variables.gutterColor,\n        label: \"line-number\",\n    });\n    const diffRemoved = css({\n        background: variables.removedBackground,\n        color: variables.removedColor,\n        pre: {\n            color: variables.removedColor,\n        },\n        [`.${lineNumber}`]: {\n            color: variables.removedGutterColor,\n        },\n        label: \"diff-removed\",\n    });\n    const diffAdded = css({\n        background: variables.addedBackground,\n        color: variables.addedColor,\n        pre: {\n            color: variables.addedColor,\n        },\n        [`.${lineNumber}`]: {\n            color: variables.addedGutterColor,\n        },\n        label: \"diff-added\",\n    });\n    const diffChanged = css({\n        background: variables.changedBackground,\n        [`.${lineNumber}`]: {\n            color: variables.gutterColor,\n        },\n        label: \"diff-changed\",\n    });\n    const wordDiff = css({\n        padding: 2,\n        display: \"inline-flex\",\n        borderRadius: 4,\n        wordBreak: \"break-all\",\n        label: \"word-diff\",\n    });\n    const wordAdded = css({\n        background: variables.wordAddedBackground,\n        textDecoration: \"none\",\n        label: \"word-added\",\n    });\n    const wordRemoved = css({\n        background: variables.wordRemovedBackground,\n        textDecoration: \"none\",\n        label: \"word-removed\",\n    });\n    const codeFoldGutter = css({\n        backgroundColor: variables.codeFoldGutterBackground,\n        label: \"code-fold-gutter\",\n        minWidth: \"50px\",\n        width: \"50px\",\n    });\n    const codeFoldContentContainer = css({\n        padding: \"\",\n    });\n    const codeFoldExpandButton = css({\n        background: variables.codeFoldBackground,\n        cursor: \"pointer\",\n        display: \"inline\",\n        margin: 0,\n        border: \"none\",\n        label: \"code-fold-expand-button\",\n    });\n    const codeFoldContent = css({\n        color: variables.codeFoldContentColor,\n        fontFamily: \"monospace\",\n        label: \"code-fold-content\",\n    });\n    const block = css({\n        display: \"block\",\n        width: \"10px\",\n        height: \"10px\",\n        backgroundColor: \"#ddd\",\n        borderWidth: \"1px\",\n        borderStyle: \"solid\",\n        borderColor: variables.diffViewerTitleBorderColor,\n    });\n    const blockAddition = css({\n        backgroundColor: variables.wordAddedBackground,\n    });\n    const blockDeletion = css({\n        backgroundColor: variables.wordRemovedBackground,\n    });\n    const codeFold = css({\n        backgroundColor: variables.codeFoldBackground,\n        height: 40,\n        fontSize: 14,\n        alignItems: \"center\",\n        userSelect: \"none\",\n        fontWeight: 700,\n        label: \"code-fold\",\n        a: {\n            textDecoration: \"underline !important\",\n            cursor: \"pointer\",\n            pre: {\n                display: \"inline\",\n            },\n        },\n    });\n    const emptyLine = css({\n        backgroundColor: variables.emptyLineBackground,\n        label: \"empty-line\",\n    });\n    const marker = css({\n        width: 28,\n        paddingLeft: 10,\n        paddingRight: 10,\n        userSelect: \"none\",\n        label: \"marker\",\n        [`&.${diffAdded}`]: {\n            pre: {\n                color: variables.addedColor,\n            },\n        },\n        [`&.${diffRemoved}`]: {\n            pre: {\n                color: variables.removedColor,\n            },\n        },\n    });\n    const highlightedLine = css({\n        background: variables.highlightBackground,\n        label: \"highlighted-line\",\n        [`.${wordAdded}, .${wordRemoved}`]: {\n            backgroundColor: \"initial\",\n        },\n    });\n    const highlightedGutter = css({\n        label: \"highlighted-gutter\",\n    });\n    const gutter = css({\n        userSelect: \"none\",\n        minWidth: 50,\n        width: \"50px\",\n        padding: \"0 10px\",\n        whiteSpace: \"nowrap\",\n        label: \"gutter\",\n        textAlign: \"right\",\n        background: variables.gutterBackground,\n        \"&:hover\": {\n            cursor: \"pointer\",\n            background: variables.gutterBackgroundDark,\n            pre: {\n                opacity: 1,\n            },\n        },\n        pre: {\n            opacity: 0.5,\n        },\n        [`&.${diffAdded}`]: {\n            background: variables.addedGutterBackground,\n        },\n        [`&.${diffRemoved}`]: {\n            background: variables.removedGutterBackground,\n        },\n        [`&.${highlightedGutter}`]: {\n            background: variables.highlightGutterBackground,\n            \"&:hover\": {\n                background: variables.highlightGutterBackground,\n            },\n        },\n    });\n    const emptyGutter = css({\n        \"&:hover\": {\n            background: variables.gutterBackground,\n            cursor: \"initial\",\n        },\n        label: \"empty-gutter\",\n    });\n    const line = css({\n        verticalAlign: \"baseline\",\n        label: \"line\",\n        textDecoration: \"none\",\n    });\n    const column = css({});\n    const defaultStyles = {\n        diffContainer,\n        diffRemoved,\n        diffAdded,\n        diffChanged,\n        splitView,\n        marker,\n        highlightedGutter,\n        highlightedLine,\n        gutter,\n        line,\n        lineContent,\n        wordDiff,\n        wordAdded,\n        summary,\n        block,\n        blockAddition,\n        blockDeletion,\n        wordRemoved,\n        noSelect: unselectable,\n        codeFoldGutter,\n        codeFoldExpandButton,\n        codeFoldContentContainer,\n        codeFold,\n        emptyGutter,\n        emptyLine,\n        lineNumber,\n        contentText,\n        content,\n        column,\n        codeFoldContent,\n        titleBlock,\n        allExpandButton,\n    };\n    const computerOverrideStyles = Object.keys(styles).reduce((acc, key) => ({\n        ...acc,\n        ...{\n            [key]: css(styles[key]),\n        },\n    }), {});\n    return Object.keys(defaultStyles).reduce((acc, key) => ({\n        ...acc,\n        ...{\n            [key]: computerOverrideStyles[key]\n                ? cx(defaultStyles[key], computerOverrideStyles[key])\n                : defaultStyles[key],\n        },\n    }), {});\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-diff-viewer-continued@4.0.5_@types+react@19.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-diff-viewer-continued/lib/esm/src/styles.js\n");

/***/ })

};
;