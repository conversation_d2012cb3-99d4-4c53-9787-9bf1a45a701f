"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit+accessibility@3.1.1_react@19.1.0";
exports.ids = ["vendor-chunks/@dnd-kit+accessibility@3.1.1_react@19.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@19.1.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@19.1.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HiddenText: () => (/* binding */ HiddenText),\n/* harmony export */   LiveRegion: () => (/* binding */ LiveRegion),\n/* harmony export */   useAnnouncement: () => (/* binding */ useAnnouncement)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const announce = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\n\n//# sourceMappingURL=accessibility.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@19.1.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\n");

/***/ })

};
;