import { getPayload } from 'payload'
import config from '../payload.config'

const createRichText = (text: string) => ({
  root: {
    type: 'root',
    children: [
      {
        type: 'paragraph',
        version: 1,
        children: [
          {
            type: 'text',
            text,
            version: 1,
          },
        ],
      },
    ],
    direction: 'ltr' as const,
    format: '' as const,
    indent: 0,
    version: 1,
  },
})

const seed = async (): Promise<void> => {
  const payload = await getPayload({ config })

  console.log('Seeding database...')

  try {
    // Create categories
    const categories = [
      {
        name: 'Floral',
        description: 'Delicate and romantic scents inspired by blooming flowers',
        slug: 'floral',
        isActive: true,
        sortOrder: 1,
      },
      {
        name: '<PERSON>',
        description: 'Warm and earthy fragrances with notes of cedar, sandalwood, and oak',
        slug: 'woody',
        isActive: true,
        sortOrder: 2,
      },
      {
        name: 'Festive',
        description: 'Special occasion candles perfect for celebrations and holidays',
        slug: 'festive',
        isActive: true,
        sortOrder: 3,
      },
      {
        name: 'Fresh',
        description: 'Clean and invigorating scents that refresh any space',
        slug: 'fresh',
        isActive: true,
        sortOrder: 4,
      },
    ]

    const createdCategories = []
    for (const category of categories) {
      const created = await payload.create({
        collection: 'categories',
        data: category,
      })
      createdCategories.push(created)
      console.log(`Created category: ${category.name}`)
    }

    // Create sample products
    const products = [
      {
        name: 'Rose Petal Bliss',
        description: createRichText('A romantic floral scent in a frosted glass jar. This luxurious candle combines the essence of fresh rose petals with subtle hints of jasmine and peony.'),
        price: 899,
        scent: 'Rose, Jasmine, Peony',
        scentNotes: [
          { note: 'Rose Petals', type: 'top' as const },
          { note: 'Jasmine', type: 'middle' as const },
          { note: 'Peony', type: 'base' as const },
        ],
        glassType: 'frosted-jar' as const,
        glassDescription: 'Elegant frosted glass jar with gold rim, perfect for repurposing as a decorative vase',
        images: [],
        categories: [createdCategories.find(c => c.slug === 'floral')?.id].filter((id): id is string => Boolean(id)),
        isFeatured: true,
        inStock: true,
        stockQuantity: 25,
        weight: '200g',
        burnTime: '45-50 hours',
        dimensions: {
          height: '10cm',
          diameter: '8cm',
        },
        slug: 'rose-petal-bliss',
      },
      {
        name: 'Sandalwood Serenity',
        description: createRichText('A warm woody fragrance that brings tranquility to any space. Crafted with premium sandalwood and cedar notes.'),
        price: 1099,
        scent: 'Sandalwood, Cedar, Vanilla',
        scentNotes: [
          { note: 'Bergamot', type: 'top' as const },
          { note: 'Sandalwood', type: 'middle' as const },
          { note: 'Cedar & Vanilla', type: 'base' as const },
        ],
        glassType: 'clear-jar' as const,
        glassDescription: 'Clear glass jar with wooden lid, showcasing the natural wax color',
        images: [],
        categories: [createdCategories.find(c => c.slug === 'woody')?.id].filter((id): id is string => Boolean(id)),
        isFeatured: true,
        inStock: true,
        stockQuantity: 18,
        weight: '250g',
        burnTime: '55-60 hours',
        dimensions: {
          height: '12cm',
          diameter: '9cm',
        },
        slug: 'sandalwood-serenity',
      },
      {
        name: 'Festive Spice',
        description: createRichText('A celebration in a candle! Perfect for special occasions with warm spices and citrus notes.'),
        price: 1299,
        scent: 'Cinnamon, Orange, Clove',
        scentNotes: [
          { note: 'Orange Zest', type: 'top' as const },
          { note: 'Cinnamon', type: 'middle' as const },
          { note: 'Clove & Nutmeg', type: 'base' as const },
        ],
        glassType: 'colored-jar' as const,
        glassDescription: 'Rich amber colored glass jar that glows beautifully when lit',
        images: [],
        categories: [createdCategories.find(c => c.slug === 'festive')?.id].filter((id): id is string => Boolean(id)),
        isFeatured: true,
        inStock: true,
        stockQuantity: 12,
        weight: '300g',
        burnTime: '65-70 hours',
        dimensions: {
          height: '14cm',
          diameter: '10cm',
        },
        slug: 'festive-spice',
      },
      {
        name: 'Ocean Breeze',
        description: createRichText('Fresh and invigorating like a morning by the sea. Clean scent with marine and citrus notes.'),
        price: 799,
        scent: 'Sea Salt, Lemon, Eucalyptus',
        scentNotes: [
          { note: 'Lemon & Lime', type: 'top' as const },
          { note: 'Sea Salt', type: 'middle' as const },
          { note: 'Eucalyptus', type: 'base' as const },
        ],
        glassType: 'tumbler' as const,
        glassDescription: 'Modern glass tumbler design, perfect for contemporary spaces',
        images: [],
        categories: [createdCategories.find(c => c.slug === 'fresh')?.id].filter((id): id is string => Boolean(id)),
        isFeatured: false,
        inStock: true,
        stockQuantity: 30,
        weight: '180g',
        burnTime: '40-45 hours',
        dimensions: {
          height: '9cm',
          diameter: '7cm',
        },
        slug: 'ocean-breeze',
      },
      {
        name: 'Lavender Dreams',
        description: createRichText('Soothing lavender candle perfect for relaxation and bedtime routines.'),
        price: 849,
        scent: 'Lavender, Chamomile, Vanilla',
        scentNotes: [
          { note: 'French Lavender', type: 'top' as const },
          { note: 'Chamomile', type: 'middle' as const },
          { note: 'Vanilla', type: 'base' as const },
        ],
        glassType: 'votive' as const,
        glassDescription: 'Delicate glass votive holder with etched pattern',
        images: [],
        categories: [createdCategories.find(c => c.slug === 'floral')?.id].filter((id): id is string => Boolean(id)),
        isFeatured: false,
        inStock: true,
        stockQuantity: 22,
        weight: '150g',
        burnTime: '35-40 hours',
        dimensions: {
          height: '8cm',
          diameter: '6cm',
        },
        slug: 'lavender-dreams',
      },
    ]

    for (const product of products) {
      const created = await payload.create({
        collection: 'products',
        data: product,
      })
      console.log(`Created product: ${product.name}`)
    }

    // Create homepage
    const homepage = await payload.create({
      collection: 'pages',
      data: {
        title: 'Home',
        slug: 'home',
        status: 'published',
        metaTitle: 'Alokita Candles | Handcrafted Scented Candles from Ahmedabad',
        metaDescription: 'Discover premium handcrafted scented candles from Ahmedabad. Elegant glass containers, natural ingredients, and captivating fragrances for every mood.',
        hero: {
          enabled: true,
          headline: 'Illuminate Your Space with Alokita Candles',
          subheadline: 'Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.',
          ctaText: 'Shop Now',
          ctaLink: '/products',
        },
      },
    })
    console.log('Created homepage')

    // Create about page
    const aboutPage = await payload.create({
      collection: 'pages',
      data: {
        title: 'About Us',
        slug: 'about',
        status: 'published',
        metaTitle: 'About Alokita Candles | Our Story',
        metaDescription: 'Learn about Alokita Candles, our passion for handcrafted scented candles, and our journey from Ahmedabad to your home.',
        blocks: [
          {
            blockType: 'content',
            content: [
              {
                children: [
                  {
                    text: 'Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.',
                  },
                ],
              },
            ],
          },
        ],
      },
    })
    console.log('Created about page')

    console.log('Database seeded successfully!')
  } catch (error) {
    console.error('Error seeding database:', error)
    throw error
  }
}

export default seed
