'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <nav style={{
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(8px)',
      borderBottom: '1px solid rgba(234, 179, 8, 0.2)',
      position: 'sticky',
      top: 0,
      zIndex: 50,
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', height: '4rem' }}>
          {/* Logo */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <a href="/" style={{ textDecoration: 'none' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ fontSize: '1.5rem' }}>🕯️</span>
                <span className="text-gradient-gold" style={{
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  letterSpacing: '-0.025em'
                }}>
                  Alokita Candles
                </span>
              </div>
            </a>
          </div>

          {/* Desktop Navigation */}
          <div style={{ display: 'none' }} className="md:block">
            <div style={{ display: 'flex', alignItems: 'center', gap: '2rem', marginLeft: '2.5rem' }}>
              <a
                href="/"
                style={{
                  color: '#374151',
                  textDecoration: 'none',
                  padding: '0.5rem 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  transition: 'color 0.2s',
                  borderRadius: '0.375rem'
                }}
                onMouseEnter={(e) => e.target.style.color = '#eab308'}
                onMouseLeave={(e) => e.target.style.color = '#374151'}
              >
                Home
              </a>
              <a
                href="/products"
                style={{
                  color: '#374151',
                  textDecoration: 'none',
                  padding: '0.5rem 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  transition: 'color 0.2s',
                  borderRadius: '0.375rem'
                }}
                onMouseEnter={(e) => e.target.style.color = '#eab308'}
                onMouseLeave={(e) => e.target.style.color = '#374151'}
              >
                Products
              </a>
              <a
                href="/categories"
                style={{
                  color: '#374151',
                  textDecoration: 'none',
                  padding: '0.5rem 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  transition: 'color 0.2s',
                  borderRadius: '0.375rem'
                }}
                onMouseEnter={(e) => e.target.style.color = '#eab308'}
                onMouseLeave={(e) => e.target.style.color = '#374151'}
              >
                Categories
              </a>
              <a
                href="/about"
                style={{
                  color: '#374151',
                  textDecoration: 'none',
                  padding: '0.5rem 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  transition: 'color 0.2s',
                  borderRadius: '0.375rem'
                }}
                onMouseEnter={(e) => e.target.style.color = '#eab308'}
                onMouseLeave={(e) => e.target.style.color = '#374151'}
              >
                About
              </a>
              <a
                href="/contact"
                style={{
                  color: '#374151',
                  textDecoration: 'none',
                  padding: '0.5rem 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  transition: 'color 0.2s',
                  borderRadius: '0.375rem'
                }}
                onMouseEnter={(e) => e.target.style.color = '#eab308'}
                onMouseLeave={(e) => e.target.style.color = '#374151'}
              >
                Contact
              </a>
            </div>
          </div>

          {/* Cart and Admin */}
          <div style={{ display: 'none', alignItems: 'center', gap: '1rem' }} className="md:flex">
            <button style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '0.5rem 1rem',
              border: '1px solid #d1d5db',
              borderRadius: '0.375rem',
              backgroundColor: 'transparent',
              color: '#374151',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}>
              🛒 Cart
              <Badge style={{
                backgroundColor: '#eab308',
                color: 'white',
                fontSize: '0.75rem',
                padding: '0.125rem 0.375rem'
              }}>
                0
              </Badge>
            </button>
            <button style={{
              backgroundColor: '#eab308',
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: '0.375rem',
              border: 'none',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}>
              <a href="/admin" style={{ color: 'inherit', textDecoration: 'none' }}>
                Admin
              </a>
            </button>
          </div>

          {/* Mobile menu button */}
          <div style={{ display: 'block' }} className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              style={{
                color: '#374151',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '0.5rem',
                borderRadius: '0.375rem',
                transition: 'color 0.2s'
              }}
            >
              <svg style={{ width: '1.5rem', height: '1.5rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div style={{ display: 'block' }} className="md:hidden">
            <div style={{
              padding: '1rem',
              backgroundColor: 'white',
              borderTop: '1px solid rgba(234, 179, 8, 0.2)',
              borderRadius: '0 0 0.5rem 0.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              {['Home', 'Products', 'Categories', 'About', 'Contact'].map((item) => (
                <a
                  key={item}
                  href={item === 'Home' ? '/' : `/${item.toLowerCase()}`}
                  style={{
                    display: 'block',
                    color: '#374151',
                    textDecoration: 'none',
                    padding: '0.75rem',
                    fontSize: '1rem',
                    fontWeight: '500',
                    borderRadius: '0.375rem',
                    transition: 'all 0.2s',
                    marginBottom: '0.25rem'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#fef7cd'
                    e.target.style.color = '#eab308'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'transparent'
                    e.target.style.color = '#374151'
                  }}
                >
                  {item}
                </a>
              ))}
              <div style={{ display: 'flex', gap: '0.5rem', marginTop: '1rem' }}>
                <button style={{
                  flex: 1,
                  padding: '0.5rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.375rem',
                  backgroundColor: 'transparent',
                  color: '#374151',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  🛒 Cart (0)
                </button>
                <button style={{
                  flex: 1,
                  backgroundColor: '#eab308',
                  color: 'white',
                  padding: '0.5rem',
                  borderRadius: '0.375rem',
                  border: 'none',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  <a href="/admin" style={{ color: 'inherit', textDecoration: 'none' }}>
                    Admin
                  </a>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
