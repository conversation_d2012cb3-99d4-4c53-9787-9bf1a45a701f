import React from 'react'
import { Button } from '@/components/ui/button'

interface AboutProps {
  title?: string
  content?: string
  image?: string
  className?: string
}

export function About({
  title = "Our Story",
  content = "Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.",
  image,
  className = ""
}: AboutProps) {
  return (
    <section className={`py-16 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-6">
            <h2 className="text-3xl lg:text-4xl font-bold text-gradient-gold">
              {title}
            </h2>
            
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 leading-relaxed">
                {content}
              </p>
              
              <p className="text-gray-700 leading-relaxed">
                Each candle is carefully crafted with premium soy wax and housed in beautiful glass containers 
                that can be repurposed long after the candle has burned. From floral to woody scents, our 
                collection captures the essence of different moods and seasons.
              </p>
              
              <p className="text-gray-700 leading-relaxed">
                Based in the heart of Ahmedabad, we draw inspiration from the city's rich textile heritage 
                and vibrant colors, translating these elements into fragrances that tell a story.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button variant="gold" size="lg" asChild>
                <a href="/products">Explore Our Collection</a>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <a href="/contact">Get in Touch</a>
              </Button>
            </div>
          </div>

          {/* Image */}
          <div className="relative">
            {image ? (
              <img
                src={image}
                alt="About Alokita Candles"
                className="w-full h-96 lg:h-[500px] object-cover rounded-lg shadow-lg"
              />
            ) : (
              <div className="w-full h-96 lg:h-[500px] bg-gradient-to-br from-gold-100 via-rose-100 to-gold-200 rounded-lg shadow-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">🕯️</div>
                  <p className="text-gold-600 font-medium">Handcrafted with Love</p>
                </div>
              </div>
            )}
            
            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-gold-200/30 rounded-full blur-xl" />
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-rose-200/30 rounded-full blur-xl" />
          </div>
        </div>

        {/* Features */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl">🌿</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">Natural Ingredients</h3>
            <p className="text-muted-foreground">Made with premium soy wax and natural fragrances for a clean burn.</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-rose-400 to-rose-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl">🏺</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">Elegant Glass</h3>
            <p className="text-muted-foreground">Beautiful glass containers that can be repurposed after use.</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-gold-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl">✨</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">Handcrafted</h3>
            <p className="text-muted-foreground">Each candle is carefully made by hand with attention to detail.</p>
          </div>
        </div>
      </div>
    </section>
  )
}
