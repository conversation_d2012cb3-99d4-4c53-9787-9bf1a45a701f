import React from 'react'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface ProductCardProps {
  id: string
  name: string
  price: number
  scent: string
  image?: {
    url: string
    alt: string
  }
  isFeatured?: boolean
  inStock?: boolean
  slug: string
}

export function ProductCard({
  id,
  name,
  price,
  scent,
  image,
  isFeatured = false,
  inStock = true,
  slug
}: ProductCardProps) {
  return (
    <Card className="group overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
      <div className="relative aspect-square overflow-hidden">
        {image ? (
          <img
            src={image.url}
            alt={image.alt}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gold-100 to-rose-100 flex items-center justify-center">
            <div className="text-gold-600 text-4xl">🕯️</div>
          </div>
        )}
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {isFeatured && (
            <Badge variant="gold" className="shadow-sm">
              Featured
            </Badge>
          )}
          {!inStock && (
            <Badge variant="destructive" className="shadow-sm">
              Out of Stock
            </Badge>
          )}
        </div>

        {/* Quick view overlay */}
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <Button 
            variant="outline" 
            className="bg-white/90 hover:bg-white text-black border-white/50"
            asChild
          >
            <a href={`/products/${slug}`}>Quick View</a>
          </Button>
        </div>
      </div>

      <CardContent className="p-4">
        <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-gold-600 transition-colors">
          <a href={`/products/${slug}`} className="hover:underline">
            {name}
          </a>
        </h3>
        
        <p className="text-sm text-muted-foreground mb-3 line-clamp-1">
          {scent}
        </p>
        
        <div className="flex items-center justify-between">
          <span className="text-xl font-bold text-gold-600">
            ₹{price.toLocaleString('en-IN')}
          </span>
          
          <Button 
            size="sm" 
            variant={inStock ? "default" : "outline"}
            disabled={!inStock}
            className="ml-2"
          >
            {inStock ? "Add to Cart" : "Notify Me"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
