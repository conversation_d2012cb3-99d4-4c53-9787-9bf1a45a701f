import React from 'react'
import { Button } from '@/components/ui/button'

interface HeroProps {
  headline?: string
  subheadline?: string
  backgroundImage?: string
  ctaText?: string
  ctaLink?: string
}

export function Hero({
  headline = "Illuminate Your Space with Alokita Candles",
  subheadline = "Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.",
  backgroundImage,
  ctaText = "Shop Now",
  ctaLink = "/products"
}: HeroProps) {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 z-0">
        {backgroundImage ? (
          <img
            src={backgroundImage}
            alt="Alokita Candles Hero"
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-rose-50 via-white to-gold-50" />
        )}
        <div className="absolute inset-0 bg-black/20" />
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
          <span className="block">{headline}</span>
        </h1>
        
        <p className="text-lg sm:text-xl lg:text-2xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed">
          {subheadline}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button 
            variant="gold" 
            size="lg"
            className="text-lg px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
            asChild
          >
            <a href={ctaLink}>{ctaText}</a>
          </Button>
          
          <Button 
            variant="outline" 
            size="lg"
            className="text-lg px-8 py-3 bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
            asChild
          >
            <a href="/about">Our Story</a>
          </Button>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-10 -left-10 w-20 h-20 bg-gold-300/20 rounded-full blur-xl" />
        <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-rose-300/20 rounded-full blur-xl" />
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce" />
        </div>
      </div>
    </section>
  )
}
