'use client'

import React from 'react'

export function Footer() {
  return (
    <footer style={{
      background: 'linear-gradient(135deg, #111827 0%, #1f2937 100%)',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '3rem 2rem' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '2rem'
        }}>
          {/* Brand */}
          <div style={{ gridColumn: 'span 2' }} className="md:col-span-2">
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
              <span style={{ fontSize: '2rem' }}>🕯️</span>
              <h3 className="text-gradient-gold" style={{
                fontSize: '1.75rem',
                fontWeight: 'bold'
              }}>
                Alokita Candles
              </h3>
            </div>
            <p style={{
              color: '#d1d5db',
              marginBottom: '1.5rem',
              maxWidth: '400px',
              lineHeight: '1.6'
            }}>
              Handcrafted scented candles from Ahmedabad, cased in elegant glass.
              Each candle is made with premium ingredients and designed to elevate your space.
            </p>

            {/* Social Links */}
            <div style={{ display: 'flex', gap: '1rem' }}>
              {[
                { name: 'Facebook', icon: '📘' },
                { name: 'Instagram', icon: '📷' },
                { name: 'Twitter', icon: '🐦' },
                { name: 'WhatsApp', icon: '💬' }
              ].map((social) => (
                <a
                  key={social.name}
                  href="#"
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '2.5rem',
                    height: '2.5rem',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    borderRadius: '50%',
                    color: '#d1d5db',
                    textDecoration: 'none',
                    transition: 'all 0.2s',
                    border: '1px solid rgba(255, 255, 255, 0.1)'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#eab308'
                    e.target.style.transform = 'translateY(-2px)'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
                    e.target.style.transform = 'translateY(0)'
                  }}
                >
                  <span style={{ fontSize: '1.25rem' }}>{social.icon}</span>
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <a href="/" className="text-gray-300 hover:text-gold-400 transition-colors">
                  Home
                </a>
              </li>
              <li>
                <a href="/products" className="text-gray-300 hover:text-gold-400 transition-colors">
                  All Products
                </a>
              </li>
              <li>
                <a href="/categories/floral" className="text-gray-300 hover:text-gold-400 transition-colors">
                  Floral Candles
                </a>
              </li>
              <li>
                <a href="/categories/woody" className="text-gray-300 hover:text-gold-400 transition-colors">
                  Woody Candles
                </a>
              </li>
              <li>
                <a href="/about" className="text-gray-300 hover:text-gold-400 transition-colors">
                  About Us
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact</h4>
            <div className="space-y-2 text-gray-300">
              <p>📍 Ahmedabad, Gujarat, India</p>
              <p>📞 +91 98765 43210</p>
              <p>✉️ <EMAIL></p>
              <p>🕒 Mon-Sat: 9AM-6PM</p>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 Alokita Candles. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="/privacy" className="text-gray-400 hover:text-gold-400 text-sm transition-colors">
              Privacy Policy
            </a>
            <a href="/terms" className="text-gray-400 hover:text-gold-400 text-sm transition-colors">
              Terms of Service
            </a>
            <a href="/shipping" className="text-gray-400 hover:text-gold-400 text-sm transition-colors">
              Shipping Info
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}
