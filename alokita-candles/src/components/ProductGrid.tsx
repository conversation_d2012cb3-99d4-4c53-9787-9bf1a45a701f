import React from 'react'
import { ProductCard } from './ProductCard'

interface Product {
  id: string
  name: string
  price: number
  scent: string
  images?: Array<{
    image: string | {
      url: string
      alt: string
    }
    alt: string
  }>
  isFeatured?: boolean
  inStock?: boolean
  slug: string
}

interface ProductGridProps {
  products: Product[]
  title?: string
  className?: string
}

export function ProductGrid({ products, title, className = "" }: ProductGridProps) {
  if (!products || products.length === 0) {
    return (
      <section className={`py-16 ${className}`}>
        <div className="container mx-auto px-4">
          {title && (
            <h2 className="text-3xl font-bold text-center mb-12 text-gradient-gold">
              {title}
            </h2>
          )}
          <div className="text-center py-12">
            <p className="text-muted-foreground text-lg">No products available at the moment.</p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className={`py-16 ${className}`}>
      <div className="container mx-auto px-4">
        {title && (
          <h2 className="text-3xl font-bold text-center mb-12 text-gradient-gold">
            {title}
          </h2>
        )}
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => {
            const firstImage = product.images?.[0]
            const imageData = firstImage ? (
              typeof firstImage.image === 'string'
                ? { url: firstImage.image, alt: firstImage.alt }
                : firstImage.image
            ) : undefined

            return (
              <ProductCard
                key={product.id}
                id={product.id}
                name={product.name}
                price={product.price}
                scent={product.scent}
                image={imageData}
                isFeatured={product.isFeatured}
                inStock={product.inStock}
                slug={product.slug}
              />
            )
          })}
        </div>
      </div>
    </section>
  )
}
