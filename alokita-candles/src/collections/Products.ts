import type { CollectionConfig } from 'payload'

export const Products: CollectionConfig = {
  slug: 'products',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'price', 'scent', 'isFeatured', 'inStock'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Product Name',
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      label: 'Description',
    },
    {
      name: 'price',
      type: 'number',
      required: true,
      label: 'Price (₹)',
      min: 0,
    },
    {
      name: 'scent',
      type: 'text',
      required: true,
      label: 'Scent Profile',
    },
    {
      name: 'scentNotes',
      type: 'array',
      label: 'Scent Notes',
      fields: [
        {
          name: 'note',
          type: 'text',
          required: true,
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Top Note', value: 'top' },
            { label: 'Middle Note', value: 'middle' },
            { label: 'Base Note', value: 'base' },
          ],
          required: true,
        },
      ],
    },
    {
      name: 'glassType',
      type: 'select',
      required: true,
      label: 'Glass Type',
      options: [
        { label: 'Clear Glass Jar', value: 'clear-jar' },
        { label: 'Frosted Glass Jar', value: 'frosted-jar' },
        { label: 'Colored Glass Jar', value: 'colored-jar' },
        { label: 'Glass Tumbler', value: 'tumbler' },
        { label: 'Glass Votive', value: 'votive' },
      ],
    },
    {
      name: 'glassDescription',
      type: 'textarea',
      label: 'Glass Description',
    },
    {
      name: 'images',
      type: 'array',
      required: true,
      minRows: 1,
      maxRows: 10,
      label: 'Product Images',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'alt',
          type: 'text',
          required: true,
          label: 'Alt Text',
        },
      ],
    },
    {
      name: 'categories',
      type: 'relationship',
      relationTo: 'categories',
      hasMany: true,
      label: 'Categories',
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      label: 'Featured Product',
      defaultValue: false,
    },
    {
      name: 'inStock',
      type: 'checkbox',
      label: 'In Stock',
      defaultValue: true,
    },
    {
      name: 'stockQuantity',
      type: 'number',
      label: 'Stock Quantity',
      min: 0,
      defaultValue: 0,
    },
    {
      name: 'weight',
      type: 'text',
      label: 'Weight',
    },
    {
      name: 'burnTime',
      type: 'text',
      label: 'Burn Time',
    },
    {
      name: 'dimensions',
      type: 'group',
      label: 'Dimensions',
      fields: [
        {
          name: 'height',
          type: 'text',
          label: 'Height',
        },
        {
          name: 'diameter',
          type: 'text',
          label: 'Diameter',
        },
      ],
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'URL Slug',
      admin: {
        position: 'sidebar',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        if (data.name && !data.slug) {
          data.slug = data.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        return data
      },
    ],
  },
}
