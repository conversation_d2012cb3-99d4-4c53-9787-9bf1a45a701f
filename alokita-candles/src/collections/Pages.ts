import type { CollectionConfig } from 'payload'

export const Pages: CollectionConfig = {
  slug: 'pages',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'slug', 'status'],
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Page Title',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'URL Slug',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      label: 'Status',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
      ],
      defaultValue: 'draft',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'metaTitle',
      type: 'text',
      label: 'Meta Title',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'metaDescription',
      type: 'textarea',
      label: 'Meta Description',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'hero',
      type: 'group',
      label: 'Hero Section',
      fields: [
        {
          name: 'enabled',
          type: 'checkbox',
          label: 'Enable Hero Section',
          defaultValue: false,
        },
        {
          name: 'headline',
          type: 'text',
          label: 'Headline',
          admin: {
            condition: (data) => data.hero?.enabled,
          },
        },
        {
          name: 'subheadline',
          type: 'text',
          label: 'Subheadline',
          admin: {
            condition: (data) => data.hero?.enabled,
          },
        },
        {
          name: 'backgroundImage',
          type: 'upload',
          relationTo: 'media',
          label: 'Background Image',
          admin: {
            condition: (data) => data.hero?.enabled,
          },
        },
        {
          name: 'ctaText',
          type: 'text',
          label: 'CTA Button Text',
          admin: {
            condition: (data) => data.hero?.enabled,
          },
        },
        {
          name: 'ctaLink',
          type: 'text',
          label: 'CTA Button Link',
          admin: {
            condition: (data) => data.hero?.enabled,
          },
        },
      ],
    },
    {
      name: 'blocks',
      type: 'blocks',
      label: 'Page Blocks',
      blocks: [
        {
          slug: 'content',
          labels: {
            singular: 'Content Block',
            plural: 'Content Blocks',
          },
          fields: [
            {
              name: 'content',
              type: 'richText',
              required: true,
            },
          ],
        },
        {
          slug: 'productGrid',
          labels: {
            singular: 'Product Grid',
            plural: 'Product Grids',
          },
          fields: [
            {
              name: 'title',
              type: 'text',
              label: 'Section Title',
            },
            {
              name: 'products',
              type: 'relationship',
              relationTo: 'products',
              hasMany: true,
              label: 'Products to Display',
            },
            {
              name: 'showFeaturedOnly',
              type: 'checkbox',
              label: 'Show Featured Products Only',
              defaultValue: false,
            },
            {
              name: 'limit',
              type: 'number',
              label: 'Number of Products to Show',
              defaultValue: 6,
              min: 1,
              max: 20,
            },
          ],
        },
        {
          slug: 'testimonials',
          labels: {
            singular: 'Testimonials Section',
            plural: 'Testimonials Sections',
          },
          fields: [
            {
              name: 'title',
              type: 'text',
              label: 'Section Title',
              defaultValue: 'What Our Customers Say',
            },
            {
              name: 'testimonials',
              type: 'array',
              label: 'Testimonials',
              fields: [
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                  label: 'Customer Name',
                },
                {
                  name: 'location',
                  type: 'text',
                  label: 'Location',
                },
                {
                  name: 'review',
                  type: 'textarea',
                  required: true,
                  label: 'Review Text',
                },
                {
                  name: 'rating',
                  type: 'number',
                  required: true,
                  label: 'Rating (1-5)',
                  min: 1,
                  max: 5,
                },
                {
                  name: 'image',
                  type: 'upload',
                  relationTo: 'media',
                  label: 'Customer Photo',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        if (data.title && !data.slug) {
          data.slug = data.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        return data
      },
    ],
  },
}
