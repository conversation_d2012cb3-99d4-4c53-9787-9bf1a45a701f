import React from 'react'
import { <PERSON> } from '@/components/Hero'
import { ProductGrid } from '@/components/ProductGrid'
import { About } from '@/components/About'
import { Testimonials } from '@/components/Testimonials'

// Mock data for development when database is not available
const mockProducts = [
  {
    id: '1',
    name: 'Rose Petal Bliss',
    price: 899,
    scent: '<PERSON>, Jasmine, Peony',
    isFeatured: true,
    inStock: true,
    slug: 'rose-petal-bliss',
  },
  {
    id: '2',
    name: 'Sandalwood Serenity',
    price: 1099,
    scent: 'Sandalwood, Cedar, Vanilla',
    isFeatured: true,
    inStock: true,
    slug: 'sandalwood-serenity',
  },
  {
    id: '3',
    name: 'Festive Spice',
    price: 1299,
    scent: 'Cinnamon, Orange, Clove',
    isFeatured: true,
    inStock: true,
    slug: 'festive-spice',
  },
]

export default async function HomePage() {
  // Use mock data for development (when database is not available)
  const featuredProducts = mockProducts

  // Sample testimonials data (you can move this to CMS later)
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      location: "Mumbai",
      review: "The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!",
      rating: 5,
    },
    {
      name: "Arjun Patel",
      location: "Ahmedabad",
      review: "Amazing quality and the scents are so authentic. Love supporting local artisans!",
      rating: 5,
    },
    {
      name: "Kavya Reddy",
      location: "Bangalore",
      review: "Perfect for my meditation space. The woody scent collection is my favorite.",
      rating: 4,
    },
  ]

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <Hero />

      {/* Featured Products */}
      <ProductGrid
        products={featuredProducts}
        title="Featured Candles"
        className="bg-white"
      />

      {/* About Section */}
      <About className="bg-gradient-to-br from-rose-50 to-gold-50" />

      {/* Testimonials */}
      <Testimonials
        testimonials={testimonials}
        className="bg-white"
      />
    </main>
  )
}
