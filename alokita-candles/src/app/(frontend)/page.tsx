import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { Hero } from '@/components/Hero'
import { ProductGrid } from '@/components/ProductGrid'
import { About } from '@/components/About'
import { Testimonials } from '@/components/Testimonials'

export default async function HomePage() {
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  // Fetch featured products
  const { docs: featuredProducts } = await payload.find({
    collection: 'products',
    where: {
      isFeatured: {
        equals: true,
      },
      inStock: {
        equals: true,
      },
    },
    limit: 6,
  })

  // Sample testimonials data (you can move this to CMS later)
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      location: "Mumbai",
      review: "The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!",
      rating: 5,
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      location: "Ahmedabad",
      review: "Amazing quality and the scents are so authentic. Love supporting local artisans!",
      rating: 5,
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      location: "Bangalore",
      review: "Perfect for my meditation space. The woody scent collection is my favorite.",
      rating: 4,
    },
  ]

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <Hero />

      {/* Featured Products */}
      <ProductGrid
        products={featuredProducts}
        title="Featured Candles"
        className="bg-white"
      />

      {/* About Section */}
      <About className="bg-gradient-to-br from-rose-50 to-gold-50" />

      {/* Testimonials */}
      <Testimonials
        testimonials={testimonials}
        className="bg-white"
      />
    </main>
  )
}
