'use client'

import React from 'react'
import { <PERSON> } from '@/components/Hero'
import { ProductGrid } from '@/components/ProductGrid'
import { About } from '@/components/About'
import { Testimonials } from '@/components/Testimonials'

// Mock data for development when database is not available
const mockProducts = [
  {
    id: '1',
    name: 'Rose Petal Bliss',
    price: 899,
    scent: '<PERSON>, <PERSON>, Peony',
    isFeatured: true,
    inStock: true,
    slug: 'rose-petal-bliss',
  },
  {
    id: '2',
    name: 'Sandalwood Serenity',
    price: 1099,
    scent: 'Sandalwood, Cedar, Vanilla',
    isFeatured: true,
    inStock: true,
    slug: 'sandalwood-serenity',
  },
  {
    id: '3',
    name: 'Festive Spice',
    price: 1299,
    scent: 'Cinnamon, Orange, Clove',
    isFeatured: true,
    inStock: true,
    slug: 'festive-spice',
  },
]

export default async function HomePage() {
  // Use mock data for development (when database is not available)
  const featuredProducts = mockProducts

  // Sample testimonials data (you can move this to CMS later)
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      location: "Mumbai",
      review: "The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!",
      rating: 5,
    },
    {
      name: "Arjun Patel",
      location: "Ahmedabad",
      review: "Amazing quality and the scents are so authentic. Love supporting local artisans!",
      rating: 5,
    },
    {
      name: "Kavya Reddy",
      location: "Bangalore",
      review: "Perfect for my meditation space. The woody scent collection is my favorite.",
      rating: 4,
    },
  ]

  return (
    <div style={{ minHeight: '100vh', fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif' }}>
      {/* Test Section */}
      <div className="test-tailwind">
        TAILWIND TEST - If this is blue with white text, Tailwind is working!
      </div>

      {/* Hero Section */}
      <section style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #fdf2f8 0%, #ffffff 50%, #fef7cd 100%)',
        padding: '2rem'
      }}>
        <div style={{ textAlign: 'center', maxWidth: '1200px' }}>
          <h1 className="text-gradient-gold" style={{
            fontSize: 'clamp(2rem, 5vw, 4rem)',
            fontWeight: 'bold',
            marginBottom: '1.5rem',
            lineHeight: '1.2'
          }}>
            Illuminate Your Space with Alokita Candles
          </h1>
          <p style={{
            fontSize: 'clamp(1rem, 2.5vw, 1.5rem)',
            color: '#6b7280',
            marginBottom: '2rem',
            lineHeight: '1.6'
          }}>
            Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.
          </p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            <button style={{
              backgroundColor: '#eab308',
              color: 'white',
              padding: '0.75rem 2rem',
              borderRadius: '0.5rem',
              border: 'none',
              fontSize: '1.125rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}>
              Shop Now
            </button>
            <button style={{
              backgroundColor: 'transparent',
              color: '#374151',
              padding: '0.75rem 2rem',
              borderRadius: '0.5rem',
              border: '2px solid #d1d5db',
              fontSize: '1.125rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}>
              Our Story
            </button>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section style={{ padding: '4rem 2rem', backgroundColor: '#ffffff' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <h2 className="text-gradient-gold" style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '3rem'
          }}>
            Featured Candles
          </h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem'
          }}>
            {featuredProducts.map((product) => (
              <div key={product.id} style={{
                backgroundColor: 'white',
                borderRadius: '0.5rem',
                border: '1px solid #e5e7eb',
                boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                overflow: 'hidden',
                transition: 'transform 0.2s, box-shadow 0.2s'
              }}>
                <div style={{
                  aspectRatio: '1',
                  background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <span style={{ fontSize: '4rem' }}>🕯️</span>
                </div>
                <div style={{ padding: '1.5rem' }}>
                  <h3 style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    marginBottom: '0.5rem',
                    color: '#111827'
                  }}>
                    {product.name}
                  </h3>
                  <p style={{
                    color: '#6b7280',
                    marginBottom: '1rem',
                    fontSize: '0.875rem'
                  }}>
                    {product.scent}
                  </p>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}>
                    <span style={{
                      fontSize: '1.5rem',
                      fontWeight: 'bold',
                      color: '#eab308'
                    }}>
                      ₹{product.price.toLocaleString('en-IN')}
                    </span>
                    <button style={{
                      backgroundColor: '#eab308',
                      color: 'white',
                      padding: '0.5rem 1rem',
                      borderRadius: '0.375rem',
                      border: 'none',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}>
                      Add to Cart
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section style={{
        padding: '5rem 2rem',
        background: 'linear-gradient(135deg, #fdf2f8 0%, #ffffff 50%, #fef7cd 100%)'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr',
            gap: '3rem',
            alignItems: 'center'
          }} className="lg:grid-cols-2">
            <div>
              <h2 className="text-gradient-gold" style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                marginBottom: '1.5rem',
                lineHeight: '1.2'
              }}>
                Our Story
              </h2>
              <p style={{
                fontSize: '1.125rem',
                color: '#6b7280',
                marginBottom: '1.5rem',
                lineHeight: '1.7'
              }}>
                Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.
              </p>
              <p style={{
                color: '#6b7280',
                marginBottom: '2rem',
                lineHeight: '1.6'
              }}>
                Each candle is carefully crafted with premium soy wax and housed in beautiful glass containers that can be repurposed long after the candle has burned.
              </p>

              {/* Feature Cards */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem',
                marginBottom: '2rem'
              }}>
                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  padding: '1.5rem',
                  borderRadius: '0.75rem',
                  border: '1px solid rgba(234, 179, 8, 0.2)',
                  textAlign: 'center',
                  backdropFilter: 'blur(8px)'
                }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🌿</div>
                  <h4 style={{ fontWeight: '600', marginBottom: '0.5rem', color: '#111827' }}>Natural Ingredients</h4>
                  <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>Premium soy wax & natural fragrances</p>
                </div>
                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  padding: '1.5rem',
                  borderRadius: '0.75rem',
                  border: '1px solid rgba(234, 179, 8, 0.2)',
                  textAlign: 'center',
                  backdropFilter: 'blur(8px)'
                }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🏺</div>
                  <h4 style={{ fontWeight: '600', marginBottom: '0.5rem', color: '#111827' }}>Elegant Glass</h4>
                  <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>Reusable glass containers</p>
                </div>
                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  padding: '1.5rem',
                  borderRadius: '0.75rem',
                  border: '1px solid rgba(234, 179, 8, 0.2)',
                  textAlign: 'center',
                  backdropFilter: 'blur(8px)'
                }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>✨</div>
                  <h4 style={{ fontWeight: '600', marginBottom: '0.5rem', color: '#111827' }}>Handcrafted</h4>
                  <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>Made with love in Ahmedabad</p>
                </div>
              </div>

              <button style={{
                backgroundColor: '#eab308',
                color: 'white',
                padding: '0.75rem 1.5rem',
                borderRadius: '0.5rem',
                border: 'none',
                fontSize: '1rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                Learn More About Us
              </button>
            </div>
            <div style={{
              aspectRatio: '1',
              background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',
              borderRadius: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              overflow: 'hidden',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
            }}>
              <div style={{
                position: 'absolute',
                top: '10%',
                left: '10%',
                width: '20px',
                height: '20px',
                backgroundColor: 'rgba(234, 179, 8, 0.3)',
                borderRadius: '50%',
                animation: 'float 3s ease-in-out infinite'
              }} />
              <div style={{
                position: 'absolute',
                bottom: '20%',
                right: '15%',
                width: '15px',
                height: '15px',
                backgroundColor: 'rgba(236, 72, 153, 0.3)',
                borderRadius: '50%',
                animation: 'float 4s ease-in-out infinite reverse'
              }} />
              <span style={{ fontSize: '6rem', filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))' }}>🕯️</span>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section style={{ padding: '5rem 2rem', backgroundColor: '#ffffff' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
            <h2 className="text-gradient-gold" style={{
              fontSize: '2.5rem',
              fontWeight: 'bold',
              marginBottom: '1rem'
            }}>
              What Our Customers Say
            </h2>
            <p style={{
              fontSize: '1.125rem',
              color: '#6b7280',
              maxWidth: '600px',
              margin: '0 auto'
            }}>
              Don't just take our word for it. Here's what our customers have to say about their Alokita Candles experience.
            </p>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
            gap: '2rem'
          }}>
            {testimonials.map((testimonial, index) => (
              <div key={index} style={{
                backgroundColor: 'white',
                borderRadius: '1rem',
                border: '1px solid #e5e7eb',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                padding: '2rem',
                transition: 'all 0.3s ease',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)'
                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.15)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
              }}>
                {/* Decorative Quote */}
                <div style={{
                  position: 'absolute',
                  top: '1rem',
                  right: '1rem',
                  fontSize: '3rem',
                  color: 'rgba(234, 179, 8, 0.1)',
                  fontFamily: 'serif'
                }}>
                  "
                </div>

                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1.5rem' }}>
                  <div style={{
                    width: '3.5rem',
                    height: '3.5rem',
                    background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '1rem',
                    border: '2px solid rgba(234, 179, 8, 0.2)'
                  }}>
                    <span style={{
                      fontWeight: '600',
                      color: '#eab308',
                      fontSize: '1.25rem'
                    }}>
                      {testimonial.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 style={{
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: '0.25rem',
                      fontSize: '1.125rem'
                    }}>
                      {testimonial.name}
                    </h4>
                    <p style={{
                      fontSize: '0.875rem',
                      color: '#6b7280',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem'
                    }}>
                      📍 {testimonial.location}
                    </p>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  marginBottom: '1.5rem',
                  gap: '0.25rem'
                }}>
                  {[...Array(5)].map((_, i) => (
                    <span key={i} style={{
                      color: i < testimonial.rating ? '#fbbf24' : '#e5e7eb',
                      fontSize: '1.25rem'
                    }}>
                      ★
                    </span>
                  ))}
                  <span style={{
                    marginLeft: '0.5rem',
                    fontSize: '0.875rem',
                    color: '#6b7280',
                    fontWeight: '500'
                  }}>
                    {testimonial.rating}/5
                  </span>
                </div>

                <blockquote style={{
                  color: '#374151',
                  fontStyle: 'italic',
                  fontSize: '1rem',
                  lineHeight: '1.6',
                  position: 'relative',
                  paddingLeft: '1rem',
                  borderLeft: '3px solid #eab308'
                }}>
                  "{testimonial.review}"
                </blockquote>

                {/* Verified Badge */}
                <div style={{
                  marginTop: '1.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.875rem',
                  color: '#059669',
                  fontWeight: '500'
                }}>
                  <span>✓</span>
                  Verified Purchase
                </div>
              </div>
            ))}
          </div>

          {/* CTA Section */}
          <div style={{
            textAlign: 'center',
            marginTop: '4rem',
            padding: '2rem',
            background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',
            borderRadius: '1rem',
            border: '1px solid rgba(234, 179, 8, 0.2)'
          }}>
            <h3 style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              marginBottom: '1rem',
              color: '#111827'
            }}>
              Join thousands of happy customers
            </h3>
            <p style={{
              color: '#6b7280',
              marginBottom: '1.5rem',
              fontSize: '1rem'
            }}>
              Experience the magic of Alokita Candles in your own home
            </p>
            <button style={{
              backgroundColor: '#eab308',
              color: 'white',
              padding: '0.75rem 2rem',
              borderRadius: '0.5rem',
              border: 'none',
              fontSize: '1rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              Shop Our Collection
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}
