import React from 'react'
import { <PERSON> } from '@/components/Hero'
import { ProductGrid } from '@/components/ProductGrid'
import { About } from '@/components/About'
import { Testimonials } from '@/components/Testimonials'

// Mock data for development when database is not available
const mockProducts = [
  {
    id: '1',
    name: 'Rose Petal Bliss',
    price: 899,
    scent: '<PERSON>, Jasmine, Peony',
    isFeatured: true,
    inStock: true,
    slug: 'rose-petal-bliss',
  },
  {
    id: '2',
    name: 'Sandalwood Serenity',
    price: 1099,
    scent: 'Sandalwood, Cedar, Vanilla',
    isFeatured: true,
    inStock: true,
    slug: 'sandalwood-serenity',
  },
  {
    id: '3',
    name: 'Festive Spice',
    price: 1299,
    scent: 'Cinnamon, Orange, Clove',
    isFeatured: true,
    inStock: true,
    slug: 'festive-spice',
  },
]

export default async function HomePage() {
  // Use mock data for development (when database is not available)
  const featuredProducts = mockProducts

  // Sample testimonials data (you can move this to CMS later)
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      location: "Mumbai",
      review: "The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!",
      rating: 5,
    },
    {
      name: "Arjun Patel",
      location: "Ahmedabad",
      review: "Amazing quality and the scents are so authentic. Love supporting local artisans!",
      rating: 5,
    },
    {
      name: "Kavya Reddy",
      location: "Bangalore",
      review: "Perfect for my meditation space. The woody scent collection is my favorite.",
      rating: 4,
    },
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-rose-50 via-white to-amber-50">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gradient-gold">
            Illuminate Your Space with Alokita Candles
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-3 rounded-md text-lg font-medium transition-colors">
              Shop Now
            </button>
            <button className="border border-input bg-background hover:bg-accent hover:text-accent-foreground px-8 py-3 rounded-md text-lg font-medium transition-colors">
              Our Story
            </button>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gradient-gold">
            Featured Candles
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredProducts.map((product) => (
              <div key={product.id} className="bg-card text-card-foreground rounded-lg border shadow-sm overflow-hidden">
                <div className="aspect-square bg-gradient-to-br from-amber-100 to-rose-100 flex items-center justify-center">
                  <span className="text-6xl">🕯️</span>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                  <p className="text-muted-foreground mb-4">{product.scent}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-primary">₹{product.price.toLocaleString('en-IN')}</span>
                    <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors">
                      Add to Cart
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6 text-gradient-gold">Our Story</h2>
              <p className="text-lg text-muted-foreground mb-6">
                Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.
              </p>
              <p className="text-muted-foreground mb-8">
                Each candle is carefully crafted with premium soy wax and housed in beautiful glass containers that can be repurposed long after the candle has burned.
              </p>
              <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3 rounded-md font-medium transition-colors">
                Learn More
              </button>
            </div>
            <div className="aspect-square bg-gradient-to-br from-amber-100 to-rose-100 rounded-lg flex items-center justify-center">
              <span className="text-8xl">🕯️</span>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gradient-gold">
            What Our Customers Say
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-card text-card-foreground rounded-lg border shadow-sm p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-200 to-rose-200 rounded-full flex items-center justify-center mr-4">
                    <span className="font-semibold text-primary">{testimonial.name.charAt(0)}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">{testimonial.name}</h4>
                    <p className="text-sm text-muted-foreground">{testimonial.location}</p>
                  </div>
                </div>
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i} className="text-amber-400">★</span>
                  ))}
                </div>
                <p className="text-muted-foreground italic">"{testimonial.review}"</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
