import React from 'react'
import { <PERSON> } from '@/components/Hero'
import { ProductGrid } from '@/components/ProductGrid'
import { About } from '@/components/About'
import { Testimonials } from '@/components/Testimonials'

// Mock data for development when database is not available
const mockProducts = [
  {
    id: '1',
    name: 'Rose Petal Bliss',
    price: 899,
    scent: '<PERSON>, Jasmine, Peony',
    isFeatured: true,
    inStock: true,
    slug: 'rose-petal-bliss',
  },
  {
    id: '2',
    name: 'Sandalwood Serenity',
    price: 1099,
    scent: 'Sandalwood, Cedar, Vanilla',
    isFeatured: true,
    inStock: true,
    slug: 'sandalwood-serenity',
  },
  {
    id: '3',
    name: 'Festive Spice',
    price: 1299,
    scent: 'Cinnamon, Orange, Clove',
    isFeatured: true,
    inStock: true,
    slug: 'festive-spice',
  },
]

export default async function HomePage() {
  // Use mock data for development (when database is not available)
  const featuredProducts = mockProducts

  // Sample testimonials data (you can move this to CMS later)
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      location: "Mumbai",
      review: "The Rose Petal Bliss candle transformed my living room into a serene sanctuary. The glass jar is so elegant!",
      rating: 5,
    },
    {
      name: "Arjun Patel",
      location: "Ahmedabad",
      review: "Amazing quality and the scents are so authentic. Love supporting local artisans!",
      rating: 5,
    },
    {
      name: "Kavya Reddy",
      location: "Bangalore",
      review: "Perfect for my meditation space. The woody scent collection is my favorite.",
      rating: 4,
    },
  ]

  return (
    <div style={{ minHeight: '100vh', fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif' }}>
      {/* Test Section */}
      <div className="test-tailwind">
        TAILWIND TEST - If this is blue with white text, Tailwind is working!
      </div>

      {/* Hero Section */}
      <section style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #fdf2f8 0%, #ffffff 50%, #fef7cd 100%)',
        padding: '2rem'
      }}>
        <div style={{ textAlign: 'center', maxWidth: '1200px' }}>
          <h1 className="text-gradient-gold" style={{
            fontSize: 'clamp(2rem, 5vw, 4rem)',
            fontWeight: 'bold',
            marginBottom: '1.5rem',
            lineHeight: '1.2'
          }}>
            Illuminate Your Space with Alokita Candles
          </h1>
          <p style={{
            fontSize: 'clamp(1rem, 2.5vw, 1.5rem)',
            color: '#6b7280',
            marginBottom: '2rem',
            lineHeight: '1.6'
          }}>
            Handcrafted scented candles from Ahmedabad, cased in elegant glass — perfect for every room.
          </p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            <button style={{
              backgroundColor: '#eab308',
              color: 'white',
              padding: '0.75rem 2rem',
              borderRadius: '0.5rem',
              border: 'none',
              fontSize: '1.125rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}>
              Shop Now
            </button>
            <button style={{
              backgroundColor: 'transparent',
              color: '#374151',
              padding: '0.75rem 2rem',
              borderRadius: '0.5rem',
              border: '2px solid #d1d5db',
              fontSize: '1.125rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}>
              Our Story
            </button>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section style={{ padding: '4rem 2rem', backgroundColor: '#ffffff' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <h2 className="text-gradient-gold" style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '3rem'
          }}>
            Featured Candles
          </h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem'
          }}>
            {featuredProducts.map((product) => (
              <div key={product.id} style={{
                backgroundColor: 'white',
                borderRadius: '0.5rem',
                border: '1px solid #e5e7eb',
                boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                overflow: 'hidden',
                transition: 'transform 0.2s, box-shadow 0.2s'
              }}>
                <div style={{
                  aspectRatio: '1',
                  background: 'linear-gradient(135deg, #fef7cd 0%, #fce7f3 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <span style={{ fontSize: '4rem' }}>🕯️</span>
                </div>
                <div style={{ padding: '1.5rem' }}>
                  <h3 style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    marginBottom: '0.5rem',
                    color: '#111827'
                  }}>
                    {product.name}
                  </h3>
                  <p style={{
                    color: '#6b7280',
                    marginBottom: '1rem',
                    fontSize: '0.875rem'
                  }}>
                    {product.scent}
                  </p>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}>
                    <span style={{
                      fontSize: '1.5rem',
                      fontWeight: 'bold',
                      color: '#eab308'
                    }}>
                      ₹{product.price.toLocaleString('en-IN')}
                    </span>
                    <button style={{
                      backgroundColor: '#eab308',
                      color: 'white',
                      padding: '0.5rem 1rem',
                      borderRadius: '0.375rem',
                      border: 'none',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}>
                      Add to Cart
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6 text-gradient-gold">Our Story</h2>
              <p className="text-lg text-muted-foreground mb-6">
                Inspired by the vibrant culture of Ahmedabad, Alokita Candles blend premium fragrances with artisanal craftsmanship. Our candles are poured in glass, designed to elevate your ambiance and mood.
              </p>
              <p className="text-muted-foreground mb-8">
                Each candle is carefully crafted with premium soy wax and housed in beautiful glass containers that can be repurposed long after the candle has burned.
              </p>
              <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3 rounded-md font-medium transition-colors">
                Learn More
              </button>
            </div>
            <div className="aspect-square bg-gradient-to-br from-amber-100 to-rose-100 rounded-lg flex items-center justify-center">
              <span className="text-8xl">🕯️</span>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gradient-gold">
            What Our Customers Say
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-card text-card-foreground rounded-lg border shadow-sm p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-200 to-rose-200 rounded-full flex items-center justify-center mr-4">
                    <span className="font-semibold text-primary">{testimonial.name.charAt(0)}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">{testimonial.name}</h4>
                    <p className="text-sm text-muted-foreground">{testimonial.location}</p>
                  </div>
                </div>
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i} className="text-amber-400">★</span>
                  ))}
                </div>
                <p className="text-muted-foreground italic">"{testimonial.review}"</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
