@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force proper fonts and basic styling */
* {
  box-sizing: border-box;
}

html, body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, sans-serif !important;
  margin: 0;
  padding: 0;
  background-color: white;
  color: #1f2937;
}

/* Test class to verify CSS is working */
.test-tailwind {
  background-color: #3b82f6 !important;
  color: white !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  font-size: 1.25rem !important;
  font-weight: bold !important;
}

/* Alokita Candles Custom Styles */
.text-gradient-gold {
  background: linear-gradient(135deg, #ca8a04 0%, #facc15 50%, #eab308 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}
