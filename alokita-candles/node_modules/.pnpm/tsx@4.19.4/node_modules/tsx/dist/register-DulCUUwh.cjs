"use strict";var g=Object.defineProperty;var o=(r,s)=>g(r,"name",{value:s,configurable:!0});var p=require("node:module"),v=require("node:worker_threads"),f=require("./register-D2KMMyKp.cjs"),h=require("node:url"),m=typeof document<"u"?document.currentScript:null;const w=o(r=>(s,e)=>{if(!e)throw new Error("The current file path (import.meta.url) must be provided in the second argument of tsImport()");const n=e.startsWith(f.fileUrlPrefix)?e:h.pathToFileURL(e).toString();return import(`tsx://${JSON.stringify({specifier:s,parentURL:n,namespace:r})}`)},"createScopedImport");let l=!1;const _=o(r=>{if(!p.register)throw new Error(`This version of Node.js (${process.version}) does not support module.register(). Please upgrade to Node v18.19 or v20.6 and above.`);if(!l){const{_resolveFilename:t}=p;p._resolveFilename=(c,...u)=>t(f.interopCjsExports(c),...u),l=!0}const{sourceMapsEnabled:s}=process;process.setSourceMapsEnabled(!0);const{port1:e,port2:n}=new v.MessageChannel;p.register(`./esm/index.mjs?${Date.now()}`,{parentURL:typeof document>"u"?require("url").pathToFileURL(__filename).href:m&&m.src||new URL("register-DulCUUwh.cjs",document.baseURI).href,data:{port:n,namespace:r?.namespace,tsconfig:r?.tsconfig},transferList:[n]});const d=r?.onImport,a=d&&(t=>{t.type==="load"&&d(t.url)});a&&(e.on("message",a),e.unref());const i=o(()=>(s===!1&&process.setSourceMapsEnabled(!1),a&&e.off("message",a),e.postMessage("deactivate"),new Promise(t=>{const c=o(u=>{u.type==="deactivated"&&(t(),e.off("message",c))},"onDeactivated");e.on("message",c)})),"unregister");return r?.namespace&&(i.import=w(r.namespace),i.unregister=i),i},"register");exports.register=_;
