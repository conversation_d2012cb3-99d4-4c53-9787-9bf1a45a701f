{"version": 3, "file": "make-command.js", "sourceRoot": "", "sources": ["../../src/make-command.ts"], "names": [], "mappings": ";;;AAAA,6CAoBqB;AA2Id,MAAM,WAAW,GAAG,CAIzB,QAA8D,EAC9D,SAIkB,EAClB,UAGc,EACd,WAGe,EACf,QAAwD,EACrB,EAAE;IACrC,OAAO,MAAM,CAAC,MAAM,CAClB,CACE,OAAyC,EAAE,EAC3C,OAAuB,EACvB,EAAO,EACP,EAAE;QACF,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,GAAG,IAAI,CAAA;YACd,IAAI,GAAG,EAAE,CAAA;QACX,CAAC;QAED,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;YAClC,EAAE,GAAG,OAAO,CAAA;YACZ,OAAO,GAAG,SAAS,CAAA;QACrB,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,CAAA;QACd,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC/B,CAAC;QAED,MAAM,GAAG,GAAG,IAAA,oBAAO,EAAC,IAAI,CAAC,CAAA;QAEzB,QAAQ,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QAExB,IAAI,IAAA,uBAAU,EAAC,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE,CAAC;gBAC7B,MAAM,IAAI,SAAS,CACjB,+CAA+C,CAChD,CAAA;YACH,CAAC;YACD,OAAO,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QAC/B,CAAC;aAAM,IAAI,IAAA,wBAAW,EAAC,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;YACjC,6BAA6B;YAC7B,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAA;YAC7B,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACrC,CAAC;aAAM,IAAI,IAAA,yBAAY,EAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE,CAAC;gBAC7B,MAAM,IAAI,SAAS,CACjB,+CAA+C,CAChD,CAAA;YACH,CAAC;YACD,OAAO,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QACjC,CAAC;aAAM,IAAI,IAAA,0BAAa,EAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE,CAAC;gBAC7B,MAAM,IAAI,SAAS,CACjB,0CAA0C,CAC3C,CAAA;YACH,CAAC;YACD,OAAO,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;YAChC,qBAAqB;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;QACzC,CAAC;QACD,oBAAoB;IACtB,CAAC,EACD;QACE,QAAQ;QACR,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ;KACT,CACmC,CAAA;AACxC,CAAC,CAAA;AAtFY,QAAA,WAAW,eAsFvB", "sourcesContent": ["import {\n  dealias,\n  isAsyncFile,\n  isAsyncNoFile,\n  isSyncFile,\n  isSyncNoFile,\n  TarOptions,\n  TarOptionsAsyncFile,\n  TarOptionsAsyncNoFile,\n  TarOptionsSyncFile,\n  TarOptionsSyncNoFile,\n  TarOptionsWithAliases,\n  TarOptionsWithAliasesAsync,\n  TarOptionsWithAliasesAsyncFile,\n  TarOptionsWithAliasesAsyncNoFile,\n  TarOptionsWithAliasesFile,\n  TarOptionsWithAliasesNoFile,\n  TarOptionsWithAliasesSync,\n  TarOptionsWithAliasesSyncFile,\n  TarOptionsWithAliasesSyncNoFile,\n} from './options.js'\n\nexport type CB = (er?: Error) => any\n\nexport type TarCommand<\n  AsyncClass,\n  SyncClass extends { sync: true },\n> = {\n  // async and no file specified\n  (): AsyncClass\n  (opt: TarOptionsWithAliasesAsyncNoFile): AsyncClass\n  (entries: string[]): AsyncClass\n  (\n    opt: TarOptionsWithAliasesAsyncNoFile,\n    entries: string[],\n  ): AsyncClass\n} & {\n  // sync and no file\n  (opt: TarOptionsWithAliasesSyncNoFile): SyncClass\n  (opt: TarOptionsWithAliasesSyncNoFile, entries: string[]): SyncClass\n} & {\n  // async and file\n  (opt: TarOptionsWithAliasesAsyncFile): Promise<void>\n  (\n    opt: TarOptionsWithAliasesAsyncFile,\n    entries: string[],\n  ): Promise<void>\n  (opt: TarOptionsWithAliasesAsyncFile, cb: CB): Promise<void>\n  (\n    opt: TarOptionsWithAliasesAsyncFile,\n    entries: string[],\n    cb: CB,\n  ): Promise<void>\n} & {\n  // sync and file\n  (opt: TarOptionsWithAliasesSyncFile): void\n  (opt: TarOptionsWithAliasesSyncFile, entries: string[]): void\n} & {\n  // sync, maybe file\n  (opt: TarOptionsWithAliasesSync): typeof opt extends (\n    TarOptionsWithAliasesFile\n  ) ?\n    void\n  : typeof opt extends TarOptionsWithAliasesNoFile ? SyncClass\n  : void | SyncClass\n  (\n    opt: TarOptionsWithAliasesSync,\n    entries: string[],\n  ): typeof opt extends TarOptionsWithAliasesFile ? void\n  : typeof opt extends TarOptionsWithAliasesNoFile ? SyncClass\n  : void | SyncClass\n} & {\n  // async, maybe file\n  (opt: TarOptionsWithAliasesAsync): typeof opt extends (\n    TarOptionsWithAliasesFile\n  ) ?\n    Promise<void>\n  : typeof opt extends TarOptionsWithAliasesNoFile ? AsyncClass\n  : Promise<void> | AsyncClass\n  (\n    opt: TarOptionsWithAliasesAsync,\n    entries: string[],\n  ): typeof opt extends TarOptionsWithAliasesFile ? Promise<void>\n  : typeof opt extends TarOptionsWithAliasesNoFile ? AsyncClass\n  : Promise<void> | AsyncClass\n  (opt: TarOptionsWithAliasesAsync, cb: CB): Promise<void>\n  (\n    opt: TarOptionsWithAliasesAsync,\n    entries: string[],\n    cb: CB,\n  ): typeof opt extends TarOptionsWithAliasesFile ? Promise<void>\n  : typeof opt extends TarOptionsWithAliasesNoFile ? never\n  : Promise<void>\n} & {\n  // maybe sync, file\n  (opt: TarOptionsWithAliasesFile): Promise<void> | void\n  (\n    opt: TarOptionsWithAliasesFile,\n    entries: string[],\n  ): typeof opt extends TarOptionsWithAliasesSync ? void\n  : typeof opt extends TarOptionsWithAliasesAsync ? Promise<void>\n  : Promise<void> | void\n  (opt: TarOptionsWithAliasesFile, cb: CB): Promise<void>\n  (\n    opt: TarOptionsWithAliasesFile,\n    entries: string[],\n    cb: CB,\n  ): typeof opt extends TarOptionsWithAliasesSync ? never\n  : typeof opt extends TarOptionsWithAliasesAsync ? Promise<void>\n  : Promise<void>\n} & {\n  // maybe sync, no file\n  (opt: TarOptionsWithAliasesNoFile): typeof opt extends (\n    TarOptionsWithAliasesSync\n  ) ?\n    SyncClass\n  : typeof opt extends TarOptionsWithAliasesAsync ? AsyncClass\n  : SyncClass | AsyncClass\n  (\n    opt: TarOptionsWithAliasesNoFile,\n    entries: string[],\n  ): typeof opt extends TarOptionsWithAliasesSync ? SyncClass\n  : typeof opt extends TarOptionsWithAliasesAsync ? AsyncClass\n  : SyncClass | AsyncClass\n} & {\n  // maybe sync, maybe file\n  (opt: TarOptionsWithAliases): typeof opt extends (\n    TarOptionsWithAliasesFile\n  ) ?\n    typeof opt extends TarOptionsWithAliasesSync ? void\n    : typeof opt extends TarOptionsWithAliasesAsync ? Promise<void>\n    : void | Promise<void>\n  : typeof opt extends TarOptionsWithAliasesNoFile ?\n    typeof opt extends TarOptionsWithAliasesSync ? SyncClass\n    : typeof opt extends TarOptionsWithAliasesAsync ? AsyncClass\n    : SyncClass | AsyncClass\n  : typeof opt extends TarOptionsWithAliasesSync ? SyncClass | void\n  : typeof opt extends TarOptionsWithAliasesAsync ?\n    AsyncClass | Promise<void>\n  : SyncClass | void | AsyncClass | Promise<void>\n} & {\n  // extras\n  syncFile: (opt: TarOptionsSyncFile, entries: string[]) => void\n  asyncFile: (\n    opt: TarOptionsAsyncFile,\n    entries: string[],\n    cb?: CB,\n  ) => Promise<void>\n  syncNoFile: (\n    opt: TarOptionsSyncNoFile,\n    entries: string[],\n  ) => SyncClass\n  asyncNoFile: (\n    opt: TarOptionsAsyncNoFile,\n    entries: string[],\n  ) => AsyncClass\n  validate?: (opt: TarOptions, entries?: string[]) => void\n}\n\nexport const makeCommand = <\n  AsyncClass,\n  SyncClass extends { sync: true },\n>(\n  syncFile: (opt: TarOptionsSyncFile, entries: string[]) => void,\n  asyncFile: (\n    opt: TarOptionsAsyncFile,\n    entries: string[],\n    cb?: CB,\n  ) => Promise<void>,\n  syncNoFile: (\n    opt: TarOptionsSyncNoFile,\n    entries: string[],\n  ) => SyncClass,\n  asyncNoFile: (\n    opt: TarOptionsAsyncNoFile,\n    entries: string[],\n  ) => AsyncClass,\n  validate?: (opt: TarOptions, entries?: string[]) => void,\n): TarCommand<AsyncClass, SyncClass> => {\n  return Object.assign(\n    (\n      opt_: TarOptionsWithAliases | string[] = [],\n      entries?: string[] | CB,\n      cb?: CB,\n    ) => {\n      if (Array.isArray(opt_)) {\n        entries = opt_\n        opt_ = {}\n      }\n\n      if (typeof entries === 'function') {\n        cb = entries\n        entries = undefined\n      }\n\n      if (!entries) {\n        entries = []\n      } else {\n        entries = Array.from(entries)\n      }\n\n      const opt = dealias(opt_)\n\n      validate?.(opt, entries)\n\n      if (isSyncFile(opt)) {\n        if (typeof cb === 'function') {\n          throw new TypeError(\n            'callback not supported for sync tar functions',\n          )\n        }\n        return syncFile(opt, entries)\n      } else if (isAsyncFile(opt)) {\n        const p = asyncFile(opt, entries)\n        // weirdness to make TS happy\n        const c = cb ? cb : undefined\n        return c ? p.then(() => c(), c) : p\n      } else if (isSyncNoFile(opt)) {\n        if (typeof cb === 'function') {\n          throw new TypeError(\n            'callback not supported for sync tar functions',\n          )\n        }\n        return syncNoFile(opt, entries)\n      } else if (isAsyncNoFile(opt)) {\n        if (typeof cb === 'function') {\n          throw new TypeError(\n            'callback only supported with file option',\n          )\n        }\n        return asyncNoFile(opt, entries)\n        /* c8 ignore start */\n      } else {\n        throw new Error('impossible options??')\n      }\n      /* c8 ignore stop */\n    },\n    {\n      syncFile,\n      asyncFile,\n      syncNoFile,\n      asyncNoFile,\n      validate,\n    },\n  ) as TarCommand<AsyncClass, SyncClass>\n}\n"]}