hoistPattern:
  - '*'
hoistedDependencies:
  '@apidevtools/json-schema-ref-parser@11.9.3':
    '@apidevtools/json-schema-ref-parser': private
  '@aws-crypto/crc32@5.2.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/crc32c@5.2.0':
    '@aws-crypto/crc32c': private
  '@aws-crypto/sha1-browser@5.2.0':
    '@aws-crypto/sha1-browser': private
  '@aws-crypto/sha256-browser@5.2.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@5.2.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@5.2.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@5.2.0':
    '@aws-crypto/util': private
  '@aws-sdk/client-cognito-identity@3.772.0':
    '@aws-sdk/client-cognito-identity': private
  '@aws-sdk/client-s3@3.772.0':
    '@aws-sdk/client-s3': private
  '@aws-sdk/client-sso@3.772.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/core@3.758.0':
    '@aws-sdk/core': private
  '@aws-sdk/credential-provider-cognito-identity@3.772.0':
    '@aws-sdk/credential-provider-cognito-identity': private
  '@aws-sdk/credential-provider-env@3.758.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-http@3.758.0':
    '@aws-sdk/credential-provider-http': private
  '@aws-sdk/credential-provider-ini@3.772.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.772.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.758.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.772.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.772.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/credential-providers@3.772.0':
    '@aws-sdk/credential-providers': private
  '@aws-sdk/lib-storage@3.772.0(@aws-sdk/client-s3@3.772.0)':
    '@aws-sdk/lib-storage': private
  '@aws-sdk/middleware-bucket-endpoint@3.734.0':
    '@aws-sdk/middleware-bucket-endpoint': private
  '@aws-sdk/middleware-expect-continue@3.734.0':
    '@aws-sdk/middleware-expect-continue': private
  '@aws-sdk/middleware-flexible-checksums@3.758.0':
    '@aws-sdk/middleware-flexible-checksums': private
  '@aws-sdk/middleware-host-header@3.734.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-location-constraint@3.734.0':
    '@aws-sdk/middleware-location-constraint': private
  '@aws-sdk/middleware-logger@3.734.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.772.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-sdk-s3@3.758.0':
    '@aws-sdk/middleware-sdk-s3': private
  '@aws-sdk/middleware-ssec@3.734.0':
    '@aws-sdk/middleware-ssec': private
  '@aws-sdk/middleware-user-agent@3.758.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/nested-clients@3.772.0':
    '@aws-sdk/nested-clients': private
  '@aws-sdk/region-config-resolver@3.734.0':
    '@aws-sdk/region-config-resolver': private
  '@aws-sdk/signature-v4-multi-region@3.758.0':
    '@aws-sdk/signature-v4-multi-region': private
  '@aws-sdk/token-providers@3.772.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.734.0':
    '@aws-sdk/types': private
  '@aws-sdk/util-arn-parser@3.723.0':
    '@aws-sdk/util-arn-parser': private
  '@aws-sdk/util-endpoints@3.743.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-locate-window@3.723.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-user-agent-browser@3.734.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.758.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/util-utf8-browser@3.259.0':
    '@aws-sdk/util-utf8-browser': private
  '@aws-sdk/xml-builder@3.734.0':
    '@aws-sdk/xml-builder': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/generator@7.26.10':
    '@babel/generator': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.26.10':
    '@babel/parser': private
  '@babel/runtime@7.26.10':
    '@babel/runtime': private
  '@babel/template@7.26.9':
    '@babel/template': private
  '@babel/traverse@7.26.10':
    '@babel/traverse': private
  '@babel/types@7.26.10':
    '@babel/types': private
  '@date-fns/tz@1.2.0':
    '@date-fns/tz': private
  '@dnd-kit/accessibility@3.1.1(react@19.1.0)':
    '@dnd-kit/accessibility': private
  '@dnd-kit/core@6.0.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@dnd-kit/core': private
  '@dnd-kit/sortable@7.0.2(@dnd-kit/core@6.0.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react@19.1.0)':
    '@dnd-kit/sortable': private
  '@dnd-kit/utilities@3.2.2(react@19.1.0)':
    '@dnd-kit/utilities': private
  '@emotion/babel-plugin@11.13.5':
    '@emotion/babel-plugin': private
  '@emotion/cache@11.14.0':
    '@emotion/cache': private
  '@emotion/css@11.13.5':
    '@emotion/css': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@emotion/memoize@0.9.0':
    '@emotion/memoize': private
  '@emotion/react@11.14.0(@types/react@19.1.0)(react@19.1.0)':
    '@emotion/react': private
  '@emotion/serialize@1.3.3':
    '@emotion/serialize': private
  '@emotion/sheet@1.4.0':
    '@emotion/sheet': private
  '@emotion/unitless@0.10.0':
    '@emotion/unitless': private
  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@19.1.0)':
    '@emotion/use-insertion-effect-with-fallbacks': private
  '@emotion/utils@1.4.2':
    '@emotion/utils': private
  '@emotion/weak-memoize@0.4.0':
    '@emotion/weak-memoize': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.5.1(eslint@9.23.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.19.2':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.12.0':
    '@eslint/core': private
  '@eslint/js@9.23.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.7':
    '@eslint/plugin-kit': private
  '@faceless-ui/modal@3.0.0-beta.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@faceless-ui/modal': private
  '@faceless-ui/scroll-info@2.0.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@faceless-ui/scroll-info': private
  '@faceless-ui/window-info@3.0.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@faceless-ui/window-info': private
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/react@0.27.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.1':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.1':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.1':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.1':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.1':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.1':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.1':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.1':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.1':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.34.1':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.1':
    '@img/sharp-win32-x64': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@jsdevtools/ono@7.1.3':
    '@jsdevtools/ono': private
  '@lexical/clipboard@0.28.0':
    '@lexical/clipboard': private
  '@lexical/code@0.28.0':
    '@lexical/code': private
  '@lexical/devtools-core@0.28.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@lexical/devtools-core': private
  '@lexical/dragon@0.28.0':
    '@lexical/dragon': private
  '@lexical/hashtag@0.28.0':
    '@lexical/hashtag': private
  '@lexical/headless@0.28.0':
    '@lexical/headless': private
  '@lexical/history@0.28.0':
    '@lexical/history': private
  '@lexical/html@0.28.0':
    '@lexical/html': private
  '@lexical/link@0.28.0':
    '@lexical/link': private
  '@lexical/list@0.28.0':
    '@lexical/list': private
  '@lexical/mark@0.28.0':
    '@lexical/mark': private
  '@lexical/markdown@0.28.0':
    '@lexical/markdown': private
  '@lexical/offset@0.28.0':
    '@lexical/offset': private
  '@lexical/overflow@0.28.0':
    '@lexical/overflow': private
  '@lexical/plain-text@0.28.0':
    '@lexical/plain-text': private
  '@lexical/react@0.28.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.24)':
    '@lexical/react': private
  '@lexical/rich-text@0.28.0':
    '@lexical/rich-text': private
  '@lexical/selection@0.28.0':
    '@lexical/selection': private
  '@lexical/table@0.28.0':
    '@lexical/table': private
  '@lexical/text@0.28.0':
    '@lexical/text': private
  '@lexical/utils@0.28.0':
    '@lexical/utils': private
  '@lexical/yjs@0.28.0(yjs@13.6.24)':
    '@lexical/yjs': private
  '@monaco-editor/loader@1.5.0':
    '@monaco-editor/loader': private
  '@monaco-editor/react@4.7.0(monaco-editor@0.52.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@monaco-editor/react': private
  '@mongodb-js/saslprep@1.2.0':
    '@mongodb-js/saslprep': private
  '@next/env@15.3.0':
    '@next/env': private
  '@next/eslint-plugin-next@15.3.0':
    '@next/eslint-plugin-next': private
  '@next/swc-darwin-arm64@15.3.0':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.0':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.0':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.0':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.0':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.0':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.0':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.0':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@payloadcms/email-nodemailer@3.40.0(payload@3.40.0(graphql@16.10.0)(typescript@5.7.3))':
    '@payloadcms/email-nodemailer': private
  '@payloadcms/graphql@3.40.0(graphql@16.10.0)(payload@3.40.0(graphql@16.10.0)(typescript@5.7.3))(typescript@5.7.3)':
    '@payloadcms/graphql': private
  '@payloadcms/translations@3.40.0':
    '@payloadcms/translations': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.0)(react@19.1.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.2(@types/react@19.1.0))(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.11.0':
    '@rushstack/eslint-patch': private
  '@smithy/abort-controller@4.0.1':
    '@smithy/abort-controller': private
  '@smithy/chunked-blob-reader-native@4.0.0':
    '@smithy/chunked-blob-reader-native': private
  '@smithy/chunked-blob-reader@5.0.0':
    '@smithy/chunked-blob-reader': private
  '@smithy/config-resolver@4.0.1':
    '@smithy/config-resolver': private
  '@smithy/core@3.1.5':
    '@smithy/core': private
  '@smithy/credential-provider-imds@4.0.1':
    '@smithy/credential-provider-imds': private
  '@smithy/eventstream-codec@4.0.1':
    '@smithy/eventstream-codec': private
  '@smithy/eventstream-serde-browser@4.0.1':
    '@smithy/eventstream-serde-browser': private
  '@smithy/eventstream-serde-config-resolver@4.0.1':
    '@smithy/eventstream-serde-config-resolver': private
  '@smithy/eventstream-serde-node@4.0.1':
    '@smithy/eventstream-serde-node': private
  '@smithy/eventstream-serde-universal@4.0.1':
    '@smithy/eventstream-serde-universal': private
  '@smithy/fetch-http-handler@5.0.1':
    '@smithy/fetch-http-handler': private
  '@smithy/hash-blob-browser@4.0.1':
    '@smithy/hash-blob-browser': private
  '@smithy/hash-node@4.0.1':
    '@smithy/hash-node': private
  '@smithy/hash-stream-node@4.0.1':
    '@smithy/hash-stream-node': private
  '@smithy/invalid-dependency@4.0.1':
    '@smithy/invalid-dependency': private
  '@smithy/is-array-buffer@4.0.0':
    '@smithy/is-array-buffer': private
  '@smithy/md5-js@4.0.1':
    '@smithy/md5-js': private
  '@smithy/middleware-content-length@4.0.1':
    '@smithy/middleware-content-length': private
  '@smithy/middleware-endpoint@4.0.6':
    '@smithy/middleware-endpoint': private
  '@smithy/middleware-retry@4.0.7':
    '@smithy/middleware-retry': private
  '@smithy/middleware-serde@4.0.2':
    '@smithy/middleware-serde': private
  '@smithy/middleware-stack@4.0.1':
    '@smithy/middleware-stack': private
  '@smithy/node-config-provider@4.0.1':
    '@smithy/node-config-provider': private
  '@smithy/node-http-handler@4.0.3':
    '@smithy/node-http-handler': private
  '@smithy/property-provider@4.0.1':
    '@smithy/property-provider': private
  '@smithy/protocol-http@5.0.1':
    '@smithy/protocol-http': private
  '@smithy/querystring-builder@4.0.1':
    '@smithy/querystring-builder': private
  '@smithy/querystring-parser@4.0.1':
    '@smithy/querystring-parser': private
  '@smithy/service-error-classification@4.0.1':
    '@smithy/service-error-classification': private
  '@smithy/shared-ini-file-loader@4.0.1':
    '@smithy/shared-ini-file-loader': private
  '@smithy/signature-v4@5.0.1':
    '@smithy/signature-v4': private
  '@smithy/smithy-client@4.1.6':
    '@smithy/smithy-client': private
  '@smithy/types@4.1.0':
    '@smithy/types': private
  '@smithy/url-parser@4.0.1':
    '@smithy/url-parser': private
  '@smithy/util-base64@4.0.0':
    '@smithy/util-base64': private
  '@smithy/util-body-length-browser@4.0.0':
    '@smithy/util-body-length-browser': private
  '@smithy/util-body-length-node@4.0.0':
    '@smithy/util-body-length-node': private
  '@smithy/util-buffer-from@4.0.0':
    '@smithy/util-buffer-from': private
  '@smithy/util-config-provider@4.0.0':
    '@smithy/util-config-provider': private
  '@smithy/util-defaults-mode-browser@4.0.7':
    '@smithy/util-defaults-mode-browser': private
  '@smithy/util-defaults-mode-node@4.0.7':
    '@smithy/util-defaults-mode-node': private
  '@smithy/util-endpoints@3.0.1':
    '@smithy/util-endpoints': private
  '@smithy/util-hex-encoding@4.0.0':
    '@smithy/util-hex-encoding': private
  '@smithy/util-middleware@4.0.1':
    '@smithy/util-middleware': private
  '@smithy/util-retry@4.0.1':
    '@smithy/util-retry': private
  '@smithy/util-stream@4.1.2':
    '@smithy/util-stream': private
  '@smithy/util-uri-escape@4.0.0':
    '@smithy/util-uri-escape': private
  '@smithy/util-utf8@4.0.0':
    '@smithy/util-utf8': private
  '@smithy/util-waiter@4.0.2':
    '@smithy/util-waiter': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@types/acorn@4.0.6':
    '@types/acorn': private
  '@types/busboy@1.5.4':
    '@types/busboy': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/lodash@4.17.16':
    '@types/lodash': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/react-transition-group@4.4.12(@types/react@19.1.0)':
    '@types/react-transition-group': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': private
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': private
  '@typescript-eslint/eslint-plugin@8.27.0(@typescript-eslint/parser@8.27.0(eslint@9.23.0)(typescript@5.7.3))(eslint@9.23.0)(typescript@5.7.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.27.0(eslint@9.23.0)(typescript@5.7.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.27.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.27.0(eslint@9.23.0)(typescript@5.7.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.27.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.27.0(typescript@5.7.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.27.0(eslint@9.23.0)(typescript@5.7.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.27.0':
    '@typescript-eslint/visitor-keys': private
  '@unrs/rspack-resolver-binding-darwin-arm64@1.2.2':
    '@unrs/rspack-resolver-binding-darwin-arm64': private
  '@unrs/rspack-resolver-binding-darwin-x64@1.2.2':
    '@unrs/rspack-resolver-binding-darwin-x64': private
  '@unrs/rspack-resolver-binding-freebsd-x64@1.2.2':
    '@unrs/rspack-resolver-binding-freebsd-x64': private
  '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.2.2':
    '@unrs/rspack-resolver-binding-linux-arm-gnueabihf': private
  '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.2.2':
    '@unrs/rspack-resolver-binding-linux-arm64-gnu': private
  '@unrs/rspack-resolver-binding-linux-arm64-musl@1.2.2':
    '@unrs/rspack-resolver-binding-linux-arm64-musl': private
  '@unrs/rspack-resolver-binding-linux-x64-gnu@1.2.2':
    '@unrs/rspack-resolver-binding-linux-x64-gnu': private
  '@unrs/rspack-resolver-binding-linux-x64-musl@1.2.2':
    '@unrs/rspack-resolver-binding-linux-x64-musl': private
  '@unrs/rspack-resolver-binding-wasm32-wasi@1.2.2':
    '@unrs/rspack-resolver-binding-wasm32-wasi': private
  '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.2.2':
    '@unrs/rspack-resolver-binding-win32-arm64-msvc': private
  '@unrs/rspack-resolver-binding-win32-x64-msvc@1.2.2':
    '@unrs/rspack-resolver-binding-win32-x64-msvc': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.12.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  amazon-cognito-identity-js@6.3.12:
    amazon-cognito-identity-js: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  b4a@1.6.7:
    b4a: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  bare-fs@4.0.1:
    bare-fs: private
  bare-os@3.6.0:
    bare-os: private
  bare-path@3.0.0:
    bare-path: private
  bare-stream@2.6.5(bare-events@2.5.4):
    bare-stream: private
  base64-js@1.5.1:
    base64-js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  body-scroll-lock@4.0.0-beta.0:
    body-scroll-lock: private
  bowser@2.11.0:
    bowser: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  bson-objectid@2.0.4:
    bson-objectid: private
  bson@6.10.3:
    bson: private
  buffer@5.6.0:
    buffer: private
  busboy@1.6.0:
    busboy: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001706:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  character-reference-invalid@2.0.1:
    character-reference-invalid: private
  charenc@0.0.2:
    charenc: private
  chokidar@3.6.0:
    chokidar: private
  chownr@1.1.4:
    chownr: private
  ci-info@4.2.0:
    ci-info: private
  classnames@2.5.1:
    classnames: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colorette@2.0.20:
    colorette: private
  commander@2.20.3:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  console-table-printer@2.12.1:
    console-table-printer: private
  convert-source-map@1.9.0:
    convert-source-map: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  croner@9.0.0:
    croner: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypt@0.0.2:
    crypt: private
  cssfilter@0.0.10:
    cssfilter: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dataloader@2.2.3:
    dataloader: private
  date-fns@4.1.0:
    date-fns: private
  dateformat@4.6.3:
    dateformat: private
  debug@4.4.0:
    debug: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.3:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devlop@1.1.0:
    devlop: private
  diff@5.2.0:
    diff: private
  doctrine@2.1.0:
    doctrine: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dunder-proto@1.0.1:
    dunder-proto: private
  electron-to-chromium@1.5.165:
    electron-to-chromium: private
  emoji-regex@9.2.2:
    emoji-regex: private
  end-of-stream@1.4.4:
    end-of-stream: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.9.1(eslint-plugin-import@2.31.0)(eslint@9.23.0):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.27.0(eslint@9.23.0)(typescript@5.7.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.9.1)(eslint@9.23.0):
    eslint-module-utils: private
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.27.0(eslint@9.23.0)(typescript@5.7.3))(eslint-import-resolver-typescript@3.9.1)(eslint@9.23.0):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.23.0):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.2.0(eslint@9.23.0):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.4(eslint@9.23.0):
    eslint-plugin-react: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  estree-util-visit@2.0.0:
    estree-util-visit: private
  esutils@2.0.3:
    esutils: private
  events@3.3.0:
    events: private
  expand-template@2.0.3:
    expand-template: private
  fast-base64-decode@1.0.0:
    fast-base64-decode: private
  fast-copy@3.0.2:
    fast-copy: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-redact@3.5.0:
    fast-redact: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fast-xml-parser@4.4.1:
    fast-xml-parser: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.3(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-type@19.3.0:
    file-type: private
  fill-range@7.1.1:
    fill-range: private
  find-root@1.1.0:
    find-root: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  focus-trap@7.5.4:
    focus-trap: private
  for-each@0.3.5:
    for-each: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-constants@1.0.0:
    fs-constants: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.8.1:
    get-tsconfig: private
  github-from-package@0.0.0:
    github-from-package: private
  glob-parent@6.0.2:
    glob-parent: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  graphql-http@1.22.4(graphql@16.10.0):
    graphql-http: private
  graphql-playground-html@1.6.30:
    graphql-playground-html: private
  graphql-scalars@1.22.2(graphql@16.10.0):
    graphql-scalars: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  help-me@5.0.0:
    help-me: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  http-status@2.1.0:
    http-status: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  image-size@2.0.2:
    image-size: private
  immutable@4.3.7:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  internal-slot@1.1.0:
    internal-slot: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-bun-module@1.3.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@2.0.1:
    is-decimal: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@2.0.1:
    is-hexadecimal: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isomorphic-unfetch@3.1.0:
    isomorphic-unfetch: private
  isomorphic.js@0.2.5:
    isomorphic.js: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jose@5.9.6:
    jose: private
  joycon@3.1.1:
    joycon: private
  js-cookie@2.2.1:
    js-cookie: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-to-typescript@15.0.3:
    json-schema-to-typescript: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsox@1.2.121:
    jsox: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  kareem@2.6.3:
    kareem: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lexical@0.28.0:
    lexical: private
  lib0@0.2.100:
    lib0: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  md5@2.3.0:
    md5: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-mdx-jsx@3.1.3:
    mdast-util-mdx-jsx: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  memoize-one@6.0.0:
    memoize-one: private
  memory-pager@1.5.0:
    memory-pager: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-mdx-jsx@3.0.1:
    micromark-extension-mdx-jsx: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-mdx-expression@2.0.2:
    micromark-factory-mdx-expression: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-events-to-acorn@2.0.2:
    micromark-util-events-to-acorn: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mimic-response@3.1.0:
    mimic-response: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  monaco-editor@0.52.2:
    monaco-editor: private
  mongodb-connection-string-url@3.0.2:
    mongodb-connection-string-url: private
  mongodb@6.12.0(@aws-sdk/credential-providers@3.772.0):
    mongodb: private
  mongoose-paginate-v2@1.8.5:
    mongoose-paginate-v2: private
  mongoose@8.9.5(@aws-sdk/credential-providers@3.772.0):
    mongoose: private
  mpath@0.9.0:
    mpath: private
  mquery@5.0.0:
    mquery: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  natural-compare@1.4.0:
    natural-compare: private
  node-abi@3.74.0:
    node-abi: private
  node-addon-api@6.1.0:
    node-addon-api: private
  node-fetch@2.7.0:
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  nodemailer@6.9.16:
    nodemailer: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object-to-formdata@4.5.1:
    object-to-formdata: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  once@1.4.0:
    once: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@4.0.2:
    parse-entities: private
  parse-json@5.2.0:
    parse-json: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  peek-readable@5.4.2:
    peek-readable: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pino-abstract-transport@2.0.0:
    pino-abstract-transport: private
  pino-pretty@13.0.0:
    pino-pretty: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pino@9.5.0:
    pino: private
  pluralize@8.0.0:
    pluralize: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prebuild-install@7.1.3:
    prebuild-install: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prismjs@1.30.0:
    prismjs: private
  process-warning@4.0.1:
    process-warning: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  pump@3.0.2:
    pump: private
  punycode@2.3.1:
    punycode: private
  qs-esm@7.0.2:
    qs-esm: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  rc@1.2.8:
    rc: private
  react-datepicker@7.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-datepicker: private
  react-diff-viewer-continued@4.0.5(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-diff-viewer-continued: private
  react-error-boundary@4.1.2(react@19.1.0):
    react-error-boundary: private
  react-image-crop@10.1.8(react@19.1.0):
    react-image-crop: private
  react-is@16.13.1:
    react-is: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.0)(react@19.1.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@19.1.0)(react@19.1.0):
    react-remove-scroll: private
  react-select@5.9.0(@types/react@19.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-select: private
  react-style-singleton@2.2.3(@types/react@19.1.0)(react@19.1.0):
    react-style-singleton: private
  react-transition-group@4.4.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-transition-group: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  real-require@0.2.0:
    real-require: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rspack-resolver@1.2.2:
    rspack-resolver: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  sanitize-filename@1.6.3:
    sanitize-filename: private
  sass@1.77.4:
    sass: private
  scheduler@0.25.0:
    scheduler: private
  scmp@2.1.0:
    scmp: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@7.7.1:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  sift@17.1.3:
    sift: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  simple-wcswidth@1.0.1:
    simple-wcswidth: private
  sisteransi@1.0.5:
    sisteransi: private
  sonic-boom@4.2.0:
    sonic-boom: private
  sonner@1.7.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    sonner: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.5.7:
    source-map: private
  sparse-bitfield@3.0.3:
    sparse-bitfield: private
  split2@4.2.0:
    split2: private
  stable-hash@0.0.5:
    stable-hash: private
  state-local@1.0.7:
    state-local: private
  stream-browserify@3.0.0:
    stream-browserify: private
  streamsearch@1.1.0:
    streamsearch: private
  streamx@2.22.0:
    streamx: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strnum@1.1.2:
    strnum: private
  strtok3@8.1.0:
    strtok3: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  stylis@4.2.0:
    stylis: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tabbable@6.2.0:
    tabbable: private
  tar-fs@3.0.8:
    tar-fs: private
  tar-stream@3.1.7:
    tar-stream: private
  text-decoder@1.2.3:
    text-decoder: private
  thread-stream@3.1.0:
    thread-stream: private
  tinyglobby@0.2.12:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  token-types@6.0.0:
    token-types: private
  tr46@5.1.0:
    tr46: private
  truncate-utf8-bytes@1.0.2:
    truncate-utf8-bytes: private
  ts-api-utils@2.1.0(typescript@5.7.3):
    ts-api-utils: private
  ts-essentials@10.0.3(typescript@5.7.3):
    ts-essentials: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  type-check@0.4.0:
    type-check: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  uint8array-extras@1.4.0:
    uint8array-extras: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.20.0:
    undici-types: private
  unfetch@4.2.0:
    unfetch: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position-from-estree@2.0.0:
    unist-util-position-from-estree: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@19.1.0)(react@19.1.0):
    use-callback-ref: private
  use-context-selector@2.0.0(react@19.1.0)(scheduler@0.25.0):
    use-context-selector: private
  use-isomorphic-layout-effect@1.2.0(@types/react@19.1.0)(react@19.1.0):
    use-isomorphic-layout-effect: private
  use-sidecar@1.1.3(@types/react@19.1.0)(react@19.1.0):
    use-sidecar: private
  utf8-byte-length@1.0.5:
    utf8-byte-length: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@10.0.0:
    uuid: private
  vfile-message@4.0.2:
    vfile-message: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-url@14.2.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.1:
    ws: private
  xss@1.0.15:
    xss: private
  yaml@1.10.2:
    yaml: private
  yjs@13.6.24:
    yjs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.7.0
pendingBuilds: []
prunedAt: Sun, 08 Jun 2025 18:30:39 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.3.1'
  - '@emnapi/runtime@1.3.1'
  - '@emnapi/runtime@1.4.0'
  - '@emnapi/wasi-threads@1.0.1'
  - '@esbuild/aix-ppc64@0.23.1'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.23.1'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.23.1'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.23.1'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-x64@0.23.1'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.23.1'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.23.1'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.23.1'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.23.1'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.23.1'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.23.1'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.23.1'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.23.1'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.23.1'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.23.1'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.23.1'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.23.1'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.23.1'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.23.1'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.23.1'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.23.1'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.23.1'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.23.1'
  - '@esbuild/win32-x64@0.25.5'
  - '@img/sharp-darwin-x64@0.34.1'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.1'
  - '@img/sharp-linux-arm@0.34.1'
  - '@img/sharp-linux-s390x@0.34.1'
  - '@img/sharp-linux-x64@0.34.1'
  - '@img/sharp-linuxmusl-arm64@0.34.1'
  - '@img/sharp-linuxmusl-x64@0.34.1'
  - '@img/sharp-wasm32@0.34.1'
  - '@img/sharp-win32-ia32@0.34.1'
  - '@img/sharp-win32-x64@0.34.1'
  - '@napi-rs/wasm-runtime@0.2.7'
  - '@next/swc-darwin-x64@15.3.0'
  - '@next/swc-linux-arm64-gnu@15.3.0'
  - '@next/swc-linux-arm64-musl@15.3.0'
  - '@next/swc-linux-x64-gnu@15.3.0'
  - '@next/swc-linux-x64-musl@15.3.0'
  - '@next/swc-win32-arm64-msvc@15.3.0'
  - '@next/swc-win32-x64-msvc@15.3.0'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/rspack-resolver-binding-darwin-x64@1.2.2'
  - '@unrs/rspack-resolver-binding-freebsd-x64@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-arm64-musl@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-x64-gnu@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-x64-musl@1.2.2'
  - '@unrs/rspack-resolver-binding-wasm32-wasi@1.2.2'
  - '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.2.2'
  - '@unrs/rspack-resolver-binding-win32-x64-msvc@1.2.2'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
