{"name": "alokita-candles", "version": "1.0.0", "description": "Alokita Candles - Handcrafted scented candles from Ahmedabad", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "seed": "cross-env NODE_OPTIONS=--no-deprecation tsx scripts/seed.ts", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@payloadcms/db-mongodb": "3.40.0", "@payloadcms/next": "3.40.0", "@payloadcms/payload-cloud": "3.40.0", "@payloadcms/richtext-lexical": "3.40.0", "@payloadcms/ui": "3.40.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "graphql": "^16.8.1", "lucide-react": "^0.513.0", "next": "15.3.0", "payload": "3.40.0", "postcss": "^8.5.4", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "0.32.6", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^22.5.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "prettier": "^3.4.2", "tailwindcss": "3", "tsx": "^4.19.4", "typescript": "5.7.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}